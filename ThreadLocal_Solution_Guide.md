# 多线程环境下ThreadLocal用户信息传递解决方案

## 问题描述

在多线程环境中，子线程无法获取到父线程中ThreadLocal存储的用户信息，导致 `LoginUtil.getLoginUserId()` 返回null或抛出异常。

## 解决方案

### 方案一：使用InheritableThreadLocal（推荐）

如果您可以修改 `LoginUtil` 类，将ThreadLocal改为InheritableThreadLocal：

```java
public class LoginUtil {
    // 将ThreadLocal改为InheritableThreadLocal
    private static final InheritableThreadLocal<Long> USER_ID_HOLDER = new InheritableThreadLocal<>();
    
    public static void setLoginUserId(Long userId) {
        USER_ID_HOLDER.set(userId);
    }
    
    public static Long getLoginUserId() {
        return USER_ID_HOLDER.get();
    }
    
    public static void clear() {
        USER_ID_HOLDER.remove();
    }
}
```

**优点**：
- 子线程自动继承父线程的ThreadLocal值
- 代码改动最小
- 对现有业务逻辑无影响

**缺点**：
- 需要修改LoginUtil类
- 在线程池环境下可能有内存泄漏风险

### 方案二：手动传递用户ID（最安全）

在启动子线程前，先获取用户ID，然后传递给子线程：

```java
// 在父线程中获取用户ID
Long currentUserId = LoginUtil.getLoginUserId();

// 启动子线程时传递用户ID
CompletableFuture.runAsync(() -> {
    try {
        // 在子线程中手动设置用户ID
        LoginUtil.setLoginUserId(currentUserId);
        
        // 执行业务逻辑
        req.setUserId(LoginUtil.getLoginUserId());
        // ... 其他业务代码
        
    } finally {
        // 清理ThreadLocal，防止内存泄漏
        LoginUtil.clear();
    }
});
```

**优点**：
- 最安全，不会有内存泄漏
- 不需要修改LoginUtil类
- 显式控制，逻辑清晰

**缺点**：
- 需要在每个子线程中手动处理
- 代码稍微复杂一些

### 方案三：使用TransmittableThreadLocal（阿里巴巴方案）

如果您使用线程池，推荐使用阿里巴巴的TransmittableThreadLocal：

1. 添加依赖：
```xml
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>transmittable-thread-local</artifactId>
    <version>2.14.2</version>
</dependency>
```

2. 修改LoginUtil：
```java
import com.alibaba.ttl.TransmittableThreadLocal;

public class LoginUtil {
    private static final TransmittableThreadLocal<Long> USER_ID_HOLDER = new TransmittableThreadLocal<>();
    
    public static void setLoginUserId(Long userId) {
        USER_ID_HOLDER.set(userId);
    }
    
    public static Long getLoginUserId() {
        return USER_ID_HOLDER.get();
    }
    
    public static void clear() {
        USER_ID_HOLDER.remove();
    }
}
```

3. 装饰线程池：
```java
import com.alibaba.ttl.threadpool.TtlExecutors;

// 装饰现有线程池
ExecutorService executorService = TtlExecutors.getTtlExecutorService(originalExecutorService);
```

**优点**：
- 专门解决线程池环境下的ThreadLocal传递问题
- 性能优秀，阿里巴巴生产环境验证
- 支持各种线程池场景

**缺点**：
- 需要引入额外依赖
- 需要装饰线程池

### 方案四：创建ThreadLocal上下文传递工具类

创建一个专门的工具类来处理ThreadLocal传递：

```java
import java.util.concurrent.Callable;
import java.util.function.Supplier;

public class ThreadLocalContextUtil {
    
    /**
     * 在子线程中执行任务，自动传递父线程的用户信息
     */
    public static <T> T executeWithContext(Supplier<T> task) {
        Long currentUserId = LoginUtil.getLoginUserId();
        return executeWithUserId(currentUserId, task);
    }
    
    /**
     * 在子线程中执行任务，使用指定的用户ID
     */
    public static <T> T executeWithUserId(Long userId, Supplier<T> task) {
        Long originalUserId = LoginUtil.getLoginUserId();
        try {
            LoginUtil.setLoginUserId(userId);
            return task.get();
        } finally {
            if (originalUserId != null) {
                LoginUtil.setLoginUserId(originalUserId);
            } else {
                LoginUtil.clear();
            }
        }
    }
    
    /**
     * 包装Runnable，自动传递ThreadLocal上下文
     */
    public static Runnable wrapWithContext(Runnable task) {
        Long currentUserId = LoginUtil.getLoginUserId();
        return () -> executeWithUserId(currentUserId, () -> {
            task.run();
            return null;
        });
    }
    
    /**
     * 包装Callable，自动传递ThreadLocal上下文
     */
    public static <T> Callable<T> wrapWithContext(Callable<T> task) {
        Long currentUserId = LoginUtil.getLoginUserId();
        return () -> executeWithUserId(currentUserId, () -> {
            try {
                return task.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
}
```

使用示例：
```java
// 方式1：直接执行
ThreadLocalContextUtil.executeWithContext(() -> {
    req.setUserId(LoginUtil.getLoginUserId());
    // 其他业务逻辑
    return result;
});

// 方式2：包装任务
CompletableFuture.runAsync(
    ThreadLocalContextUtil.wrapWithContext(() -> {
        req.setUserId(LoginUtil.getLoginUserId());
        // 其他业务逻辑
    })
);
```

## 推荐方案

根据您的具体情况，我推荐以下方案：

1. **如果可以修改LoginUtil类**：使用方案一（InheritableThreadLocal）
2. **如果不能修改LoginUtil类**：使用方案二（手动传递）或方案四（工具类）
3. **如果大量使用线程池**：使用方案三（TransmittableThreadLocal）

## 注意事项

1. **内存泄漏防护**：无论使用哪种方案，都要确保在线程结束时清理ThreadLocal
2. **线程池复用**：在线程池环境下，要特别注意ThreadLocal的清理
3. **异常处理**：在finally块中进行清理，确保异常情况下也能正确清理
4. **性能考虑**：InheritableThreadLocal在创建子线程时会复制所有父线程的ThreadLocal，如果数据量大可能影响性能

## 具体实现建议

基于您的代码，我建议使用方案二（手动传递），修改如下：

```java
// 在需要启动子线程的地方
Long currentUserId = LoginUtil.getLoginUserId();

CompletableFuture.runAsync(() -> {
    try {
        // 在子线程开始时设置用户ID
        LoginUtil.setLoginUserId(currentUserId);
        
        // 现在可以正常使用了
        req.setUserId(LoginUtil.getLoginUserId());
        
        // 执行其他业务逻辑...
        
    } finally {
        // 清理ThreadLocal
        LoginUtil.clear();
    }
});
```

这种方式最安全，不需要修改现有的LoginUtil类，也不会引入额外的依赖。