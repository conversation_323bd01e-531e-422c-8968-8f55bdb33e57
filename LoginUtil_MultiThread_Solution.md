# LoginUtil多线程解决方案

## 问题分析

您的LoginUtil类使用了以下机制获取用户信息：
1. `RequestContextHolder` - 获取HTTP请求上下文
2. `MDC.get(Constant.TRACEID)` - 获取链路追踪ID
3. HTTP请求头中的token - 从Redis获取用户信息

这些机制在子线程中都无法正常工作，因为：
- RequestContextHolder在子线程中无法获取父线程的请求上下文
- MDC在子线程中无法获取父线程的值
- 子线程没有HTTP请求上下文

## 解决方案

### 方案一：手动传递用户信息（推荐）

在启动子线程前，先获取用户信息，然后传递给子线程：

```java
// 在父线程中获取用户信息
Long currentUserId = LoginUtil.getLoginUserId();
RedisUser currentUser = LoginUtil.getLoginUser();

// 启动子线程
CompletableFuture.runAsync(() -> {
    // 在子线程中直接使用传递的用户ID
    req.setUserId(currentUserId);
    
    // 如果需要完整的用户信息，也可以传递整个用户对象
    // 然后在业务逻辑中使用
});
```

### 方案二：创建多线程上下文传递工具类

```java
package com.jusha.caselibrary.common.util;

import com.jusha.caselibrary.system.dto.RedisUser;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 多线程上下文传递工具类
 */
public class ThreadContextUtil {
    
    /**
     * 上下文信息包装类
     */
    public static class ContextInfo {
        private final Long userId;
        private final RedisUser user;
        private final String traceId;
        private final ServletRequestAttributes requestAttributes;
        private final Map<String, String> mdcContext;
        
        public ContextInfo(Long userId, RedisUser user, String traceId, 
                          ServletRequestAttributes requestAttributes, Map<String, String> mdcContext) {
            this.userId = userId;
            this.user = user;
            this.traceId = traceId;
            this.requestAttributes = requestAttributes;
            this.mdcContext = mdcContext;
        }
        
        // getters...
        public Long getUserId() { return userId; }
        public RedisUser getUser() { return user; }
        public String getTraceId() { return traceId; }
        public ServletRequestAttributes getRequestAttributes() { return requestAttributes; }
        public Map<String, String> getMdcContext() { return mdcContext; }
    }
    
    /**
     * 获取当前线程的上下文信息
     */
    public static ContextInfo captureContext() {
        try {
            Long userId = LoginUtil.getLoginUserId();
            RedisUser user = LoginUtil.getLoginUser();
            String traceId = MDC.get("traceId");
            ServletRequestAttributes requestAttributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            Map<String, String> mdcContext = MDC.getCopyOfContextMap();
            
            return new ContextInfo(userId, user, traceId, requestAttributes, mdcContext);
        } catch (Exception e) {
            // 如果获取失败，返回null，让调用方处理
            return null;
        }
    }
    
    /**
     * 在子线程中执行任务，自动传递上下文
     */
    public static <T> T executeWithContext(ContextInfo context, Supplier<T> task) {
        if (context == null) {
            return task.get();
        }
        
        // 保存原始上下文
        ServletRequestAttributes originalAttributes = 
            (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Map<String, String> originalMdcContext = MDC.getCopyOfContextMap();
        
        try {
            // 设置上下文
            if (context.getRequestAttributes() != null) {
                RequestContextHolder.setRequestAttributes(context.getRequestAttributes());
            }
            if (context.getMdcContext() != null) {
                MDC.setContextMap(context.getMdcContext());
            }
            
            return task.get();
        } finally {
            // 恢复原始上下文
            if (originalAttributes != null) {
                RequestContextHolder.setRequestAttributes(originalAttributes);
            } else {
                RequestContextHolder.resetRequestAttributes();
            }
            
            if (originalMdcContext != null) {
                MDC.setContextMap(originalMdcContext);
            } else {
                MDC.clear();
            }
        }
    }
    
    /**
     * 包装Runnable，自动传递上下文
     */
    public static Runnable wrapWithContext(Runnable task) {
        ContextInfo context = captureContext();
        return () -> executeWithContext(context, () -> {
            task.run();
            return null;
        });
    }
    
    /**
     * 包装Callable，自动传递上下文
     */
    public static <T> Callable<T> wrapWithContext(Callable<T> task) {
        ContextInfo context = captureContext();
        return () -> executeWithContext(context, () -> {
            try {
                return task.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 简化版本：只传递用户ID
     */
    public static Runnable wrapWithUserId(Runnable task) {
        try {
            Long userId = LoginUtil.getLoginUserId();
            return () -> {
                // 在子线程中，直接使用传递的userId
                // 注意：这种方式下，子线程中不能调用LoginUtil.getLoginUserId()
                // 需要在业务代码中直接使用传递的userId
                task.run();
            };
        } catch (Exception e) {
            // 如果获取用户ID失败，返回原始任务
            return task;
        }
    }
}
```

### 方案三：使用InheritableThreadLocal增强MDC

创建一个增强的MDC工具类：

```java
package com.jusha.caselibrary.common.util;

import org.slf4j.MDC;
import java.util.Map;

/**
 * 支持父子线程传递的MDC工具类
 */
public class InheritableMDC {
    
    private static final InheritableThreadLocal<Map<String, String>> INHERITABLE_MDC = 
        new InheritableThreadLocal<Map<String, String>>() {
            @Override
            protected Map<String, String> childValue(Map<String, String> parentValue) {
                return parentValue == null ? null : Map.copyOf(parentValue);
            }
        };
    
    /**
     * 设置MDC值，同时设置到InheritableThreadLocal
     */
    public static void put(String key, String value) {
        MDC.put(key, value);
        
        Map<String, String> context = INHERITABLE_MDC.get();
        if (context == null) {
            context = new HashMap<>();
            INHERITABLE_MDC.set(context);
        }
        context.put(key, value);
    }
    
    /**
     * 获取MDC值，优先从InheritableThreadLocal获取
     */
    public static String get(String key) {
        // 先尝试从普通MDC获取
        String value = MDC.get(key);
        if (value != null) {
            return value;
        }
        
        // 如果普通MDC没有，从InheritableThreadLocal获取
        Map<String, String> context = INHERITABLE_MDC.get();
        return context != null ? context.get(key) : null;
    }
    
    /**
     * 清理
     */
    public static void clear() {
        MDC.clear();
        INHERITABLE_MDC.remove();
    }
}
```

## 推荐使用方式

### 最简单的方式（推荐）

```java
// 在需要启动子线程的地方
Long currentUserId = LoginUtil.getLoginUserId();

CompletableFuture.runAsync(() -> {
    // 直接使用传递的用户ID，不要调用LoginUtil.getLoginUserId()
    req.setUserId(currentUserId);
    
    // 执行其他业务逻辑
    // 注意：在子线程中不要调用LoginUtil的其他方法
});
```

### 使用工具类的方式

```java
// 使用ThreadContextUtil包装任务
CompletableFuture.runAsync(
    ThreadContextUtil.wrapWithContext(() -> {
        // 在这里可以正常使用LoginUtil.getLoginUserId()
        req.setUserId(LoginUtil.getLoginUserId());
        
        // 执行其他业务逻辑
    })
);
```

### 如果需要完整的用户信息

```java
// 在父线程中获取完整用户信息
Long currentUserId = LoginUtil.getLoginUserId();
RedisUser currentUser = LoginUtil.getLoginUser();

CompletableFuture.runAsync(() -> {
    // 使用传递的用户信息
    req.setUserId(currentUserId);
    
    // 如果需要其他用户信息，使用传递的currentUser对象
    String userName = currentUser.getSysUser().getUserName();
    
    // 执行业务逻辑
});
```

## 注意事项

1. **最简单的方案**：直接传递userId，在子线程中不调用LoginUtil方法
2. **避免在子线程中调用LoginUtil**：除非使用了上下文传递工具类
3. **异常处理**：获取用户信息时要做好异常处理
4. **性能考虑**：上下文传递会有一定的性能开销，简单传递userId是最高效的方式

## 具体实现建议

基于您的代码结构，我建议使用最简单的方式：

```java
// 在PersonalCaseServiceImpl或其他需要启动子线程的地方
public void someMethodWithAsyncTask() {
    // 在父线程中获取用户ID
    Long currentUserId = LoginUtil.getLoginUserId();
    
    // 启动异步任务
    CompletableFuture.runAsync(() -> {
        try {
            // 直接使用传递的用户ID
            PersonalCaseSearchReq req = new PersonalCaseSearchReq();
            req.setUserId(currentUserId);
            
            // 执行业务逻辑
            // ...
            
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
        }
    });
}
```

这种方式最简单、最安全，不需要修改现有的LoginUtil类，也不会有内存泄漏的风险。