{"bool": {"must": [{"multi_match": {"query": "和", "fields": ["caseAnalysis^1.5", "followInfoList.followupResult^1.5", "patientName^2.0", "remark^1.2", "studyInfoList.accessNumber^2.0", "studyInfoList.inPatientNo^2.0", "studyInfoList.outPatientNo^2.0", "studyInfoList.reportDescribe^1.8", "studyInfoList.reportDiagnose^1.8"], "type": "best_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1.0}}, {"nested": {"query": {"bool": {"should": [{"terms": {"studyInfoList.deviceType": ["CT"], "boost": 1.0}}, {"terms": {"studyInfoList.modalities": ["CT"], "boost": 1.0}}], "adjust_pure_negative": true, "minimum_should_match": "1", "boost": 1.0}}, "path": "studyInfoList", "ignore_unmapped": false, "score_mode": "none", "boost": 1.0}}, {"bool": {"should": [{"bool": {"must": [{"nested": {"query": {"bool": {"must": [{"term": {"caseTypeInfoList.caseTypeId": {"value": 1, "boost": 1.0}}}, {"term": {"caseTypeInfoList.audit": {"value": "0", "boost": 1.0}}}], "adjust_pure_negative": true, "boost": 1.0}}, "path": "caseTypeInfoList", "ignore_unmapped": false, "score_mode": "none", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}, {"bool": {"must": [{"nested": {"query": {"bool": {"must": [{"term": {"caseTypeInfoList.caseTypeId": {"value": 1, "boost": 1.0}}}, {"term": {"caseTypeInfoList.audit": {"value": "1", "boost": 1.0}}}], "adjust_pure_negative": true, "boost": 1.0}}, "path": "caseTypeInfoList", "ignore_unmapped": false, "score_mode": "none", "boost": 1.0}}, {"nested": {"query": {"bool": {"must": [{"term": {"auditInfoList.caseTypeId": {"value": 1, "boost": 1.0}}}, {"term": {"auditInfoList.status": {"value": "1", "boost": 1.0}}}], "adjust_pure_negative": true, "boost": 1.0}}, "path": "auditInfoList", "ignore_unmapped": false, "score_mode": "none", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}], "adjust_pure_negative": true, "minimum_should_match": "1", "boost": 1.0}}], "filter": [{"nested": {"query": {"term": {"caseTypeInfoList.caseTypeId": {"value": 1, "boost": 1.0}}}, "path": "caseTypeInfoList", "ignore_unmapped": false, "score_mode": "none", "boost": 1.0}}], "adjust_pure_negative": true, "boost": 1.0}}