"bool" : {
"must" : [
{
"bool" : {
"should" : [
{
"match_phrase" : {
"patientName" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 2.0
}
}
},
{
"match_phrase" : {
"caseAnalysis" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.5
}
}
},
{
"match_phrase" : {
"remark" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.2
}
}
},
{
"match_phrase" : {
"caseName" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.8
}
}
},
{
"match_phrase" : {
"diagnosis" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.8
}
}
},
{
"match_phrase" : {
"selfComplaints" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.3
}
}
},
{
"nested" : {
"query" : {
"bool" : {
"should" : [
{
"match_phrase" : {
"tagInfoList.tagName" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.5
}
}
}
],
"adjust_pure_negative" : true,
"boost" : 1.0
}
},
"path" : "tagInfoList",
"ignore_unmapped" : false,
"score_mode" : "none",
"boost" : 1.0
}
},
{
"nested" : {
"query" : {
"bool" : {
"should" : [
{
"term" : {
"studyInfoList.accessNumber" : {
"value" : "龙从蓉",
"boost" : 2.0
}
}
},
{
"term" : {
"studyInfoList.inPatientNo" : {
"value" : "龙从蓉",
"boost" : 2.0
}
}
},
{
"term" : {
"studyInfoList.outPatientNo" : {
"value" : "龙从蓉",
"boost" : 2.0
}
}
},
{
"match_phrase" : {
"studyInfoList.reportDescribe" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.8
}
}
},
{
"match_phrase" : {
"studyInfoList.reportDiagnose" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.8
}
}
}
],
"adjust_pure_negative" : true,
"minimum_should_match" : "1",
"boost" : 1.0
}
},
"path" : "studyInfoList",
"ignore_unmapped" : false,
"score_mode" : "none",
"boost" : 1.0
}
},
{
"nested" : {
"query" : {
"bool" : {
"should" : [
{
"match_phrase" : {
"studyInfoList.followInfoList.followupResult" : {
"query" : "龙从蓉",
"slop" : 0,
"zero_terms_query" : "NONE",
"boost" : 1.5
}
}
}
],
"adjust_pure_negative" : true,
"boost" : 1.0
}
},
"path" : "studyInfoList.followInfoList",
"ignore_unmapped" : false,
"score_mode" : "none",
"boost" : 1.0
}
}
],
"adjust_pure_negative" : true,
"minimum_should_match" : "1",
"boost" : 1.0
}
}
],
"filter" : [
{
"term" : {
"followStatus" : {
"value" : "0",
"boost" : 1.0
}
}
}
],
"adjust_pure_negative" : true,
"boost" : 1.0
}