#proguard混淆共四个阶段：1、Shrink:收缩，删除没有被使用的类和方法；2、Optimize:对代码指令进行优化；
######                  3、Obfuscate:对代码名称进行混淆；4、Preverify:对class进行预校验，校验StackMap/StackMapTable属性。

#JDK版本1.8
-target 1.8

#不做收缩
-dontshrink
#不做代码指令优化
-dontoptimize

#不用大小写混合类名机制（类名都混淆为小写,因为windows大小写不敏感）
-dontusemixedcaseclassnames
#类和成员混淆的时候，使用唯一的名字
-useuniqueclassmembernames


#不混淆：包名
-keeppackagenames
#不混淆：特殊的属性（内部类InnerClasses，泛型Signature，注解Annotation等）
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,LocalVariable*Table,*Annotation*,Synthetic,EnclosingMethod
#若类中包含指定的方法，则不混淆类名、类成员（比如保留Springboot启动类）
-keepclasseswithmembers class * {
    public static void main(java.lang.String[]);
}
-keepclasseswithmembernames class * {
    native <methods>;
}

#类名--不混淆，类成员--不混淆 （mybatis/mybatisPlus的mapper层）
-keep class com.jusha.**.*mapper.** {*;}
-keep class com.jusha.**.dao.** {*;}
#类名--不混淆，类成员--不混淆 （数据库表映射实体、前后端传参实体）
-keep class com.jusha.**.entity.** {*;}
-keep class com.jusha.**.model.** {*;}
-keep class com.jusha.**.beans.** {*;}
-keep class com.jusha.**.dto.** {*;}
-keep class com.jusha.**.req.** {*;}
-keep class com.jusha.**.resp.** {*;}
#类名--不混淆，类成员--不混淆 （注解）
-keep @interface * {*;}
#类名--不混淆，类成员--不混淆 （框架代码）
-keep class com.jusha.*.common.util.LoginUtil {*;}
-keep class com.jusha.**.aop.** {*;}
-keep class com.jusha.**.feign.** {*;}

#类名--混淆，类成员--不混淆 （枚举）
-keepclassmembers enum * {*;}
#类名--混淆，类成员--部分不混淆 （get、set、equals、hashCode、toString）
-keepclassmembers class * {
    public *** get*();
    public *** set*(***);
    public int hashCode();
    public boolean equals(***);
    protected boolean canEqual(***);
    public String toString();
}
