package com.jusha.ris.docking.study.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.exception.BusinessException;
import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.common.util.HttpClientUtil;
import com.jusha.ris.docking.common.util.LoginUtil;
import com.jusha.ris.docking.feign.RisBaseClient;
import com.jusha.ris.docking.feign.StudyClient;
import com.jusha.ris.docking.study.dto.req.StudyReq;
import com.jusha.ris.docking.study.dto.resp.StudyApplyResp;
import com.jusha.ris.docking.study.dto.resp.StudyReportResp;
import com.jusha.ris.docking.study.dto.resp.StudyResp;
import com.jusha.ris.docking.study.service.StudyService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
@Service
@RequiredArgsConstructor
public class StudyImplService implements StudyService{

    private static final Logger log = LoggerFactory.getLogger(StudyImplService.class);

    @Autowired
    private StudyClient studyClient;

    @Autowired
    private RisBaseClient risBaseClient;

    @Value("${ris.studyList.url:}")
    private String studyListUrl;

    @Value("${ris.reportList.url:}")
    private String reportListUrl;

    @Value("${PLAT_ID:}")
    private String PLAT_ID;

    /**
     * 模拟对接数据
     *
     * @param studyReq
     */
    public List<StudyResp> queryStudyList(StudyReq studyReq) throws Exception{
        studyReq.setOutInPatientNo(studyReq.getKeyword());
        Map<String, String> header = new HashMap<>();
        header.put("platId",PLAT_ID);
        header.put("authorization", LoginUtil.getRequestHeader(Constant.TOKEN_KEY));
        if(studyListUrl == null || studyListUrl.isEmpty()){
            return new ArrayList<>();
        }
        List<StudyResp> studyRespList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(studyReq);
        String studyText = HttpClientUtil.doPostDefaultSecurity(studyListUrl,json,header);
        log.info("入参===================="+json);
        log.info("studyText================="+studyText);
        ResultBean<PageInfo<StudyApplyResp>> studyApplyResultBean = objectMapper.readValue(studyText, new TypeReference<ResultBean<PageInfo<StudyApplyResp>>>(){});
        if(studyApplyResultBean.getState()!=null && studyApplyResultBean.getState()){
            List<StudyApplyResp> studyApplyRespList = studyApplyResultBean.getData().getList();

            for(StudyApplyResp studyApplyResp : studyApplyRespList){
                StudyResp studyResp = new StudyResp();
                studyResp.setStudyId(studyApplyResp.getStudyId()).setApplyNumber(studyApplyResp.getApplyNo())
                        .setStudyNo(studyApplyResp.getStudyNo()).setStudyUid(studyApplyResp.getStudyUid())
                        .setAccessNumber(studyApplyResp.getAccessNumber()).setPatientId(studyApplyResp.getPatientId())
                        .setPatientName(studyApplyResp.getPatientName())
                        .setPatientNo(studyApplyResp.getPatientNo()).setPatientAge(studyApplyResp.getPatientAge())
                        .setPatientType(studyApplyResp.getPatientType()).setStudyTime(studyApplyResp.getStudyTime())
                        .setDeviceType(studyApplyResp.getStudyType())
                        .setStudyItemName(studyApplyResp.getStudyItems()).setPartName(studyApplyResp.getStudyParts())
                        .setOutPatientNo(studyApplyResp.getOutInPatientNo()).setInPatientNo(studyApplyResp.getOutInPatientNo());

                if("0".equals(studyApplyResp.getPatientSex()) || "M".equals(studyApplyResp.getPatientSex())){
                    studyResp.setPatientSex("男");
                }else if("1".equals(studyApplyResp.getPatientSex()) || "F".equals(studyApplyResp.getPatientSex())){
                    studyResp.setPatientSex("女");
                }else{
                    studyResp.setPatientSex("未知");
                }

                String reportText = HttpClientUtil.doGetDefaultSecurity(reportListUrl+"234633307525189",header);
//                log.info("reportText================="+reportText);
                if(reportText!=null){
                    ResultBean<StudyReportResp> studyReportResultBean = objectMapper.readValue(reportText, new TypeReference<ResultBean<StudyReportResp>>(){});
                    if(studyReportResultBean.getState()!=null && studyReportResultBean.getState()){
                        StudyReportResp studyReportResp = studyReportResultBean.getData();
                        studyResp.setStudyPurpose(studyReportResp.getStudyPurpose()).setPhysicalSign(studyReportResp.getPhysicalSign())
                                .setApplyDepartment(studyReportResp.getApplyDepartment())
                                .setApplyDoctor("").setArtificer("").setVisitDate("").setRegisterTime("")
                                .setSelfReportedSymptom(studyReportResp.getSelfComplaints()).setMedicalHistory(studyReportResp.getHistoryDisease())
                                .setMedicalHistory(studyReportResp.getHistoryDisease()).setClinicalDiagnosis(studyReportResp.getClinicalDiagnosis())
                                .setReporter(studyReportResp.getReportDoctor()).setReportTime(studyReportResp.getReportTime())
                                .setChecker(studyReportResp.getCheckDoctor()).setCheckTime(studyReportResp.getCheckTime())
                                .setReportId(studyReportResp.getReportId()).setReportDescribe(studyReportResp.getReportDescribe())
                                .setReportDiagnose(studyReportResp.getReportDiagnosis()).setIsPostive(studyReportResp.getPositiveStatus());
                    }
                }
                studyRespList.add(studyResp);
            }
           return studyRespList;
        }
        throw new BusinessException(studyApplyResultBean.getMessage());
    }
}
