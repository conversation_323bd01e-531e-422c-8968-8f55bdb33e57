package com.jusha.ris.docking.common.exception;

import lombok.Getter;

/**
 * 业务异常
 * ps: 大多数情况下，用通用错误码即可，因为前端大多情况下只需要用返回的错误信息展示
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 错误码
     */
    private int errCode;

    /**
     * 错误信息
     */
    private String errMsg;


    /**
     * 通用错误码
     * @param message
     */
    public BusinessException(String message) {
        super(message);
        this.errCode = BusinessCode.COMMON_ERROR;
        this.errMsg = message;
    }

    /**
     * 其它错误码
     * @param code
     * @param message
     */
    public BusinessException(int code, String message) {
        super(message);
        this.errCode = code;
        this.errMsg = message;
    }

}