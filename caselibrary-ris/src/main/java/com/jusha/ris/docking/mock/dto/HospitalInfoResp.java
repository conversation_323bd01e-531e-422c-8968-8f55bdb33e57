package com.jusha.ris.docking.mock.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title HospitalInfoResp
 * @description
 * @date 2025/2/27
 */
@Data
public class HospitalInfoResp {

    /**
     * 医院ID
     */
    @ApiModelProperty(value = "医院ID", dataType = "Long")
    private Long hospitalId;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称", dataType = "String")
    private String hospitalName;

    /**
     * 分组ID
     */
    @ApiModelProperty(value = "分组ID", dataType = "Long")
    private Long groupId;

    /**
     * 分组名称名称
     */
    @ApiModelProperty(value = "分组名称", dataType = "String")
    private String groupName;

    /**
     * 医院类型（0-社区医院，1-中心医院）
     */
    @ApiModelProperty(value = "医院类型（0-社区医院，1-中心医院）", dataType = "Integer")
    private Integer hospitalType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", dataType = "DateTime")
    private LocalDateTime createTime;

}
