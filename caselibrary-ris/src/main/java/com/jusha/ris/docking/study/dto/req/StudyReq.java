package com.jusha.ris.docking.study.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 检查请求类
 * @date 2025/7/12
 */

@ApiModel
@Data
public class StudyReq {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("申请单号")
    private String applyNo;

    @NotNull
    @ApiModelProperty("检查开始时间")
    private String studyStartTime;

    @NotNull
    @ApiModelProperty("检查结束时间")
    private String studyEndTime;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("关键字：门诊/住院/体检号")
    private String outInPatientNo;

    @ApiModelProperty("检查类型s")
    private List<String> studyTypes;

}
