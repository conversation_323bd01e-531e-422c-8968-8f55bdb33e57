package com.jusha.ris.docking.common.locale;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * LocaleResolver组件
 */
@Slf4j
@Component("localeResolver")
public class LocaleResolverService implements LocaleResolver {

    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        //默认中文
        Locale locale = Locale.SIMPLIFIED_CHINESE;

        //请求头中是否携带了语言参数
        if(request != null){
            String language = request.getHeader("Accept-Language");
            if (StringUtils.isNotBlank(language)) {
                String[] split = language.trim().replace("-", "_").replace(",", "_").replace("，", "_").split("_");   //zh_CN
                if(split.length > 1){
                    locale = new Locale(split[0], split[1]);  // 国家，地区
                }
            }
        }

        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Locale locale) {
    }

}