package com.jusha.ris.docking.mock.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @title RandomNumberStringUtil
 * @description
 * @date 2025/2/24
 */
public class RandomNumberStringUtil {

    private static final String DIGITS = "0123456789";
    private static final Random random = new Random();

    public static String generateRandomNumberString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 随机选择一个数字并追加到 StringBuilder
            sb.append(DIGITS.charAt(random.nextInt(DIGITS.length())));
        }
        return sb.toString();
    }

}
