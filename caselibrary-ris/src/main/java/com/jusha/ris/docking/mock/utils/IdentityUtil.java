package com.jusha.ris.docking.mock.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * <AUTHOR>
 * @title IdentityUtil
 * @description
 * @date 2023/6/9
 */

public class IdentityUtil {

    /**
     * 省、自治区、直辖市代码
     */
    private static String provinces[] = { "11", "12", "13", "14", "15", "21", "22", "23",
            "31", "32", "33", "34", "35", "36", "37", "41", "42", "43",
            "44", "45", "46", "50", "51", "52", "53", "54", "61", "62",
            "63", "64", "65", "71", "81", "82" };

    /**
     * 地级市、盟、自治州代码
     */
    private static String citys[] = { "01", "02", "03", "04", "05", "06", "07", "08",
            "09", "10", "21", "22", "23", "24", "25", "26", "27", "28" };

    /**
     * 县、县级市、区代码
     */
    private static String countys[] = { "01", "02", "03", "04", "05", "06", "07", "08",
            "09", "10", "21", "22", "23", "24", "25", "26", "27", "28",
            "29", "30", "31", "32", "33", "34", "35", "36", "37", "38" };

    public static String getRandomID() {
        StringBuffer identityNo = new StringBuffer();

        // 随机生成省、自治区、直辖市代码 1-2
        identityNo.append(provinces[new Random().nextInt(provinces.length - 1)]);

        // 随机生成地级市、盟、自治州代码 3-4
        identityNo.append(citys[new Random().nextInt(citys.length - 1)]);

        // 随机生成县、县级市、区代码 5-6
        identityNo.append(countys[new Random().nextInt(countys.length - 1)]);

        // 随机生成出生年月 7-14
        SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
        Date beginDate = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(beginDate);
        date.set(Calendar.DATE, date.get(Calendar.DATE) - new Random().nextInt(365 * 100));
        identityNo.append(dft.format(date.getTime()));

        // 随机生成顺序号 15-17
        identityNo.append(new Random().nextInt(999));

        // 生成校验码 18
        identityNo.append(getVerifyCode(identityNo));
//        identityNo.append('1');
        return identityNo.toString();
    }

    /**
     * 计算校验码
     * @param cardId
     * @return
     */
    private static char getVerifyCode(StringBuffer cardId) {
        char[] ValCodeArr = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        int[] Wi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        int tmp = 0;
        for (int i = 0; i < Wi.length; i++) {
            tmp += Integer.parseInt(String.valueOf(cardId.charAt(i))) * Wi[i];
        }
        return ValCodeArr[tmp % 11];
    }


}
