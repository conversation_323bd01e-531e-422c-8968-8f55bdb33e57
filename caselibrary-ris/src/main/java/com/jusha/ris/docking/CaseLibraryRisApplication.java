package com.jusha.ris.docking;

import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.StringUtils;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@ComponentScan({"com.jusha.ris.docking.common", "com.jusha.ris.docking"})
@SpringBootApplication
@EnableTransactionManagement
@EnableScheduling
@EnableAsync
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients
public class CaseLibraryRisApplication {

    public static void main(String[] args) {
        SpringApplicationBuilder springApplicationBuilder = new SpringApplicationBuilder(CaseLibraryRisApplication.class)
                .beanNameGenerator(new CustomGenerator());
        springApplicationBuilder.run(args);
    }


    /**
     * 自定义Spring的BeanName生成器
     */
    public static class CustomGenerator extends AnnotationBeanNameGenerator {

        @Override
        public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {

            String fullClassName = definition.getBeanClassName();
            if (fullClassName.contains("com.jusha.")) {   //项目代码

                //如果自己配了beanName，则用配的
                if (definition instanceof AnnotatedBeanDefinition) {
                    String beanName = super.determineBeanNameFromAnnotation((AnnotatedBeanDefinition) definition);
                    if (StringUtils.hasText(beanName)) {
                        return beanName;
                    }
                }
                //否则用全名
                return fullClassName;
            } else {   //依赖框架jar里代码
                return super.generateBeanName(definition, registry);
            }

        }

    }

}
