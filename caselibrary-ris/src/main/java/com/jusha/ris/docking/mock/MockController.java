package com.jusha.ris.docking.mock;

import com.jusha.ris.docking.common.aop.NoDuplicate;
import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.mock.dto.req.StudyInfoReq;
import com.jusha.ris.docking.mock.service.MockDockingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title MockController
 * @description
 * @date 2025/3/14
 */

@RestController
@Api(tags = "模拟数据对接")
@RequiredArgsConstructor
public class MockController {

    private final MockDockingService mockService;

    @ApiOperation("添加检查、申请单信息")
    @PostMapping("/open/study/add/mock")
    @NoDuplicate(keys = {"#studyInfoReq.studyNo"})
    public ResultBean<Void> addStudyApplyMock(@RequestBody @Validated StudyInfoReq studyInfoReq) {
        mockService.addStudyApplyMock(studyInfoReq);
        return ResultBean.success();
    }


}
