package com.jusha.ris.docking.common.beans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class RedisUser {

    /**
     * sysUserId
     */
    private Long userId;

    @ApiModelProperty(value = "角色类型列表")
    private Set<String> roleTypes;

    @ApiModelProperty(value = "医院分组ID")
    private Long sysGroupId;

    @ApiModelProperty(value = "当前用户的所属联盟ID")
    private Long lmGroupId;

    @ApiModelProperty(value = "中心医院分组ID")
    private Long centerGroupId;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     *  SysUser
     */
    private SysUser sysUser;


    @Data
    public static class SysUser {

        /**
         * sysUserId
         */
        private Long userId;

        /**
         * 用户名
         */
        private String nickName;

        /**
         * 手机号码
         */
        private String phoneNumber;

        /**
         * 工号
         */
        private String workNumber;

        /**
         * 帐号状态（0正常 1停用）
         */
        private String status;

        /** 角色对象 */
        private List<SysRole> roles;

    }

}
