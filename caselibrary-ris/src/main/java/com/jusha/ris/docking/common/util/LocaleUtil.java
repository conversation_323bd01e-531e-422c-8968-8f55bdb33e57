package com.jusha.ris.docking.common.util;

import com.jusha.ris.docking.common.acHolder.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 国际化工具类
 */
@Slf4j
public class LocaleUtil {

    private static final MessageSource messageSource = ContextHolder.getBean(MessageSource.class);

    private LocaleUtil(){ }


    public static String getLocale(String msg) {
        Locale locale = LocaleContextHolder.getLocale();
        //log.info("getLocale -- language:{}, country:{}", locale.getLanguage(), locale.getCountry());

        String result = null;
        try {
            result = messageSource.getMessage(msg, null, locale);
        }
        catch (Exception e) {
            result = messageSource.getMessage(msg, null, Locale.SIMPLIFIED_CHINESE);
        }
        return result;
    }

}