package com.jusha.ris.docking.common.exception;

import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.common.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

/**
 * 全局异常处理
 **/
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * ---------------- 校验 -----------------------------
     */
    @ExceptionHandler(BindException.class)
    public ResultBean bindException(HttpServletRequest req, BindException e) {
        String requestUri = req.getRequestURI();
        //errMsg
        String errMsg = StringUtils.join(e.getBindingResult().getFieldErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).toArray(), ";");

        log.warn(StringUtils.join(requestUri, "-BindException异常:", errMsg));
        return ResultBean.error(errMsg);
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResultBean methodArgumentNotValidException(HttpServletRequest req, MethodArgumentNotValidException e) {
        String requestUri = req.getRequestURI();
        //errMsg
        ObjectError objectError = e.getBindingResult().getAllErrors().stream().findFirst().get();
        String errMsg = objectError.getDefaultMessage();

        log.warn(StringUtils.join(requestUri, "-MethodArgumentNotValidException异常:", errMsg));
        return ResultBean.error(errMsg);
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResultBean constraintViolationException(HttpServletRequest req, ConstraintViolationException e) {
        String requestUri = req.getRequestURI();
        //errMsg
        String errMsg;
        if (CollectionUtils.isNotEmpty(e.getConstraintViolations())) {
            errMsg = StringUtils.join(e.getConstraintViolations().stream().map(constraintViolation -> constraintViolation.getMessage()).toArray(), ";");
        }
        else {
            errMsg = e.getMessage();
        }

        log.warn(StringUtils.join(requestUri, "-ConstraintViolationException异常:", errMsg));
        return ResultBean.error(errMsg);
    }

    /**
     * ---------------- 空指针: NullPointerException -----------------------------
     */
    @ExceptionHandler(NullPointerException.class)
    public ResultBean nullPointerException(HttpServletRequest req, NullPointerException e) {
        String requestUri = req.getRequestURI();
        log.error(StringUtils.join(requestUri, "-NullPointerException:"), e);

        return ResultBean.error(LocaleUtil.getLocale("common.resource.not.exists"));
    }


    /**
     * -----------自定义业务异常: BusinessException --------------------------------------
     */
    @ExceptionHandler(value = BusinessException.class)
    public ResultBean businessException(BusinessException e) {
        log.info(e.getErrMsg());
        return ResultBean.error(e.getErrCode(), e.getErrMsg());
    }

    /**
     * -----------自定义业务正常: BusinessNormal --------------------------------------
     */
    @ExceptionHandler(value = BusinessNormal.class)
    public ResultBean businessNormal(BusinessNormal e) {
        log.info(e.getMsg());
        return new ResultBean<>(true, BusinessCode.SUCCESS, e.getMsg(), null);
    }


    /**
     * -----------其它异常：输出日志--------------------------------------
     */
    @ExceptionHandler(Throwable.class)
    public ResultBean throwable(HttpServletRequest req, Throwable e) {
        String requestUri = req.getRequestURI();

        log.error(StringUtils.join(requestUri, "-Exception:"), e);
        return ResultBean.error(LocaleUtil.getLocale("common.bad.request"));
    }

}
