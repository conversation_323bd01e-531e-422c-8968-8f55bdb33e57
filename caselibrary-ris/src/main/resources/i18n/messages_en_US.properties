#common
common.bad.request = Bad request
common.getlock.time.out = Acquire lock time out
common.login.out = Login failure
common.repeat.request = duplicate request
common.param.error = Error in request parameters
common.not.permitted = not permitted
common.data.is.null = data is null
common.resource.not.exists = resource does not exist
common.field.max.length = the maximum length limit of %s is %s
common.excel.date.error = Excel parsing exception, please fill in the time according to the specified format
common.not.exists = %s do not exist
common.cannot.null = %s can not be null
common.error.type = Parameter request type is wrong
common.operate.permit.user = Only teaching secretaries or administrators have permission to operate

#login
login.username.password.error = Incorrect username or password input
login.username.exist = The user already exists
login.username.multiple = There are multiple accounts for this username. Please contact the administrator for processing

#file
file.minio.upload.fail = Upload to file service failed

#excel
excel.date.error = Excel analysis exception, please fill in the time according to the specified format
excel.dynamic.head.null = The dynamic header is empty