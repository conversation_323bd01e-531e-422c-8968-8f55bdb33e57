package com.jusha.caselibrary.search.migration;

import com.jusha.caselibrary.search.migration.dto.MigrationPlan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;

/**
 * 字段类型转换器
 * 
 * 核心功能：
 * 1. 支持text到keyword、数值类型转换等常见场景
 * 2. 提供安全的类型转换机制
 * 3. 处理转换异常和数据清洗
 * 4. 支持自定义转换规则
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FieldTypeConverter {

    // 日期格式化器
    private static final List<DateTimeFormatter> DATE_FORMATTERS = Arrays.asList(
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd")
    );

    // 类型转换器映射
    private final Map<String, Map<String, Function<Object, Object>>> converters;

    public FieldTypeConverter() {
        this.converters = initializeConverters();
    }

    /**
     * 转换字段值
     * 
     * @param value 原始值
     * @param sourceType 源类型
     * @param targetType 目标类型
     * @param conversionRules 转换规则
     * @return 转换后的值
     */
    public Object convertValue(Object value, String sourceType, String targetType, 
                              Map<String, Object> conversionRules) {
        if (value == null) {
            return null;
        }

        try {
            // 如果类型相同，直接返回
            if (sourceType.equals(targetType)) {
                return value;
            }

            // 获取转换器
            Function<Object, Object> converter = getConverter(sourceType, targetType);
            if (converter != null) {
                Object convertedValue = converter.apply(value);
                
                // 应用自定义转换规则
                if (conversionRules != null && !conversionRules.isEmpty()) {
                    convertedValue = applyCustomRules(convertedValue, conversionRules);
                }
                
                return convertedValue;
            }

            log.warn("不支持的类型转换: {} -> {}, 值: {}", sourceType, targetType, value);
            return value;

        } catch (Exception e) {
            log.error("字段值转换失败: {} -> {}, 值: {}", sourceType, targetType, value, e);
            return getDefaultValue(targetType);
        }
    }

    /**
     * 批量转换文档字段
     * 
     * @param document 文档数据
     * @param migrationPlan 迁移计划
     * @return 转换后的文档
     */
    public Map<String, Object> convertDocument(Map<String, Object> document, MigrationPlan migrationPlan) {
        if (document == null || migrationPlan == null || migrationPlan.getFieldChanges() == null) {
            return document;
        }

        Map<String, Object> convertedDocument = new HashMap<>(document);

        // 处理字段变更
        migrationPlan.getFieldChanges().forEach((fieldPath, fieldConfig) -> {
            try {
                convertDocumentField(convertedDocument, fieldPath, fieldConfig, migrationPlan);
            } catch (Exception e) {
                log.error("转换文档字段失败: {}", fieldPath, e);
            }
        });

        return convertedDocument;
    }

    /**
     * 验证转换结果
     * 
     * @param originalValue 原始值
     * @param convertedValue 转换后的值
     * @param targetType 目标类型
     * @return 是否转换成功
     */
    public boolean validateConversion(Object originalValue, Object convertedValue, String targetType) {
        if (originalValue == null && convertedValue == null) {
            return true;
        }

        if (originalValue == null || convertedValue == null) {
            return false;
        }

        try {
            switch (targetType.toLowerCase()) {
                case "keyword":
                case "text":
                    return convertedValue instanceof String;
                case "long":
                    return convertedValue instanceof Long;
                case "integer":
                    return convertedValue instanceof Integer;
                case "double":
                    return convertedValue instanceof Double;
                case "float":
                    return convertedValue instanceof Float;
                case "boolean":
                    return convertedValue instanceof Boolean;
                case "date":
                    return convertedValue instanceof String || convertedValue instanceof Date;
                default:
                    return true;
            }
        } catch (Exception e) {
            log.error("验证转换结果失败", e);
            return false;
        }
    }

    /**
     * 获取支持的转换类型
     * 
     * @param sourceType 源类型
     * @return 支持的目标类型列表
     */
    public Set<String> getSupportedTargetTypes(String sourceType) {
        Map<String, Function<Object, Object>> typeConverters = converters.get(sourceType.toLowerCase());
        return typeConverters != null ? typeConverters.keySet() : Collections.emptySet();
    }

    // ==================== 私有方法 ====================

    /**
     * 初始化转换器
     */
    private Map<String, Map<String, Function<Object, Object>>> initializeConverters() {
        Map<String, Map<String, Function<Object, Object>>> converterMap = new HashMap<>();

        // Text类型转换器
        Map<String, Function<Object, Object>> textConverters = new HashMap<>();
        textConverters.put("keyword", this::textToKeyword);
        textConverters.put("text", Function.identity());
        converterMap.put("text", textConverters);

        // Keyword类型转换器
        Map<String, Function<Object, Object>> keywordConverters = new HashMap<>();
        keywordConverters.put("text", this::keywordToText);
        keywordConverters.put("keyword", Function.identity());
        converterMap.put("keyword", keywordConverters);

        // Long类型转换器
        Map<String, Function<Object, Object>> longConverters = new HashMap<>();
        longConverters.put("integer", this::longToInteger);
        longConverters.put("double", this::longToDouble);
        longConverters.put("float", this::longToFloat);
        longConverters.put("text", this::numberToText);
        longConverters.put("keyword", this::numberToKeyword);
        longConverters.put("long", Function.identity());
        converterMap.put("long", longConverters);

        // Integer类型转换器
        Map<String, Function<Object, Object>> integerConverters = new HashMap<>();
        integerConverters.put("long", this::integerToLong);
        integerConverters.put("double", this::integerToDouble);
        integerConverters.put("float", this::integerToFloat);
        integerConverters.put("text", this::numberToText);
        integerConverters.put("keyword", this::numberToKeyword);
        integerConverters.put("integer", Function.identity());
        converterMap.put("integer", integerConverters);

        // Double类型转换器
        Map<String, Function<Object, Object>> doubleConverters = new HashMap<>();
        doubleConverters.put("float", this::doubleToFloat);
        doubleConverters.put("long", this::doubleToLong);
        doubleConverters.put("integer", this::doubleToInteger);
        doubleConverters.put("text", this::numberToText);
        doubleConverters.put("keyword", this::numberToKeyword);
        doubleConverters.put("double", Function.identity());
        converterMap.put("double", doubleConverters);

        // Float类型转换器
        Map<String, Function<Object, Object>> floatConverters = new HashMap<>();
        floatConverters.put("double", this::floatToDouble);
        floatConverters.put("long", this::floatToLong);
        floatConverters.put("integer", this::floatToInteger);
        floatConverters.put("text", this::numberToText);
        floatConverters.put("keyword", this::numberToKeyword);
        floatConverters.put("float", Function.identity());
        converterMap.put("float", floatConverters);

        // Boolean类型转换器
        Map<String, Function<Object, Object>> booleanConverters = new HashMap<>();
        booleanConverters.put("text", this::booleanToText);
        booleanConverters.put("keyword", this::booleanToKeyword);
        booleanConverters.put("boolean", Function.identity());
        converterMap.put("boolean", booleanConverters);

        // Date类型转换器
        Map<String, Function<Object, Object>> dateConverters = new HashMap<>();
        dateConverters.put("text", this::dateToText);
        dateConverters.put("keyword", this::dateToKeyword);
        dateConverters.put("date", Function.identity());
        converterMap.put("date", dateConverters);

        return converterMap;
    }

    /**
     * 获取转换器
     */
    private Function<Object, Object> getConverter(String sourceType, String targetType) {
        Map<String, Function<Object, Object>> typeConverters = converters.get(sourceType.toLowerCase());
        if (typeConverters != null) {
            return typeConverters.get(targetType.toLowerCase());
        }
        return null;
    }

    /**
     * 转换文档字段
     */
    @SuppressWarnings("unchecked")
    private void convertDocumentField(Map<String, Object> document, String fieldPath, 
                                    Object fieldConfig, MigrationPlan migrationPlan) {
        if (!(fieldConfig instanceof Map)) {
            return;
        }

        Map<String, Object> config = (Map<String, Object>) fieldConfig;
        String sourceType = (String) config.get("sourceType");
        String targetType = (String) config.get("targetType");

        if (sourceType == null || targetType == null) {
            return;
        }

        // 获取字段值
        Object fieldValue = getFieldValue(document, fieldPath);
        if (fieldValue == null) {
            return;
        }

        // 转换字段值
        Map<String, Object> conversionRules = (Map<String, Object>) config.get("conversionRules");
        Object convertedValue = convertValue(fieldValue, sourceType, targetType, conversionRules);

        // 设置转换后的值
        setFieldValue(document, fieldPath, convertedValue);
    }

    /**
     * 获取字段值
     */
    private Object getFieldValue(Map<String, Object> document, String fieldPath) {
        String[] pathParts = fieldPath.split("\\.");
        Object current = document;

        for (String part : pathParts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 设置字段值
     */
    @SuppressWarnings("unchecked")
    private void setFieldValue(Map<String, Object> document, String fieldPath, Object value) {
        String[] pathParts = fieldPath.split("\\.");
        Map<String, Object> current = document;

        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];
            Object next = current.get(part);
            if (!(next instanceof Map)) {
                next = new HashMap<String, Object>();
                current.put(part, next);
            }
            current = (Map<String, Object>) next;
        }

        current.put(pathParts[pathParts.length - 1], value);
    }

    /**
     * 应用自定义转换规则
     */
    private Object applyCustomRules(Object value, Map<String, Object> rules) {
        // 实现自定义转换规则逻辑
        // 例如：格式化、截断、替换等
        return value;
    }

    /**
     * 获取默认值
     */
    private Object getDefaultValue(String targetType) {
        switch (targetType.toLowerCase()) {
            case "text":
            case "keyword":
                return "";
            case "long":
                return 0L;
            case "integer":
                return 0;
            case "double":
                return 0.0;
            case "float":
                return 0.0f;
            case "boolean":
                return false;
            case "date":
                return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            default:
                return null;
        }
    }

    // ==================== 具体转换方法 ====================

    private Object textToKeyword(Object value) {
        if (value == null) return null;
        String text = value.toString().trim();
        // Keyword字段通常有长度限制，这里限制为256字符
        return text.length() > 256 ? text.substring(0, 256) : text;
    }

    private Object keywordToText(Object value) {
        return value != null ? value.toString() : null;
    }

    private Object longToInteger(Object value) {
        if (value == null) return null;
        Long longValue = (Long) value;
        if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
            throw new IllegalArgumentException("Long值超出Integer范围: " + longValue);
        }
        return longValue.intValue();
    }

    private Object longToDouble(Object value) {
        return value != null ? ((Long) value).doubleValue() : null;
    }

    private Object longToFloat(Object value) {
        return value != null ? ((Long) value).floatValue() : null;
    }

    private Object integerToLong(Object value) {
        return value != null ? ((Integer) value).longValue() : null;
    }

    private Object integerToDouble(Object value) {
        return value != null ? ((Integer) value).doubleValue() : null;
    }

    private Object integerToFloat(Object value) {
        return value != null ? ((Integer) value).floatValue() : null;
    }

    private Object doubleToFloat(Object value) {
        if (value == null) return null;
        Double doubleValue = (Double) value;
        if (doubleValue > Float.MAX_VALUE || doubleValue < -Float.MAX_VALUE) {
            throw new IllegalArgumentException("Double值超出Float范围: " + doubleValue);
        }
        return doubleValue.floatValue();
    }

    private Object doubleToLong(Object value) {
        if (value == null) return null;
        return Math.round((Double) value);
    }

    private Object doubleToInteger(Object value) {
        if (value == null) return null;
        long longValue = Math.round((Double) value);
        if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
            throw new IllegalArgumentException("Double转换后的值超出Integer范围: " + longValue);
        }
        return (int) longValue;
    }

    private Object floatToDouble(Object value) {
        return value != null ? ((Float) value).doubleValue() : null;
    }

    private Object floatToLong(Object value) {
        if (value == null) return null;
        return Math.round((Float) value);
    }

    private Object floatToInteger(Object value) {
        if (value == null) return null;
        long longValue = Math.round((Float) value);
        if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
            throw new IllegalArgumentException("Float转换后的值超出Integer范围: " + longValue);
        }
        return (int) longValue;
    }

    private Object numberToText(Object value) {
        return value != null ? value.toString() : null;
    }

    private Object numberToKeyword(Object value) {
        return value != null ? value.toString() : null;
    }

    private Object booleanToText(Object value) {
        return value != null ? value.toString() : null;
    }

    private Object booleanToKeyword(Object value) {
        return value != null ? value.toString() : null;
    }

    private Object dateToText(Object value) {
        if (value == null) return null;
        if (value instanceof String) {
            return value;
        }
        if (value instanceof Date) {
            return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    .format(((Date) value).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        }
        return value.toString();
    }

    private Object dateToKeyword(Object value) {
        return dateToText(value);
    }
}