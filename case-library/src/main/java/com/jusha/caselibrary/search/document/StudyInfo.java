package com.jusha.caselibrary.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * @ClassName StudyInfo
 * @Description 报告信息
 * <AUTHOR>
 * @Date 2025/7/8 17:25
 **/
@Data
public class StudyInfo {

    @ApiModelProperty(value = "检查UID")
    @Field(type = FieldType.Keyword)
    private String studyUid;

    @ApiModelProperty(value = "检查流水号/影像号")
    @Field(type = FieldType.Keyword)
    private String accessNumber;

    @ApiModelProperty(value = "检查时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DateUtil.DEFAULT_PATTERN)
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyTime;

    @ApiModelProperty(value = "RIS中的检查号")
    @Field(type = FieldType.Keyword)
    private String studyNo;

    @ApiModelProperty(value = "患者ID")
    @Field(type = FieldType.Keyword)
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    @Field(type = FieldType.Keyword)
    private String patientSex;

    @ApiModelProperty(value = "患者出生日期")
    @Field(type = FieldType.Keyword)
    private String patientBirthDate;

    @ApiModelProperty(value = "患者年龄")
    @Field(type = FieldType.Keyword)
    private String patientAge;

    @ApiModelProperty(value = "就诊类型（门诊、住院、体检）")
    @Field(type = FieldType.Keyword)
    private String patientType;

    @ApiModelProperty(value = "就诊日期")
    @Field(type = FieldType.Keyword)
    private String visitDate;

    @ApiModelProperty(value = "门诊号")
    @Field(type = FieldType.Keyword)
    private String outPatientNo;

    @ApiModelProperty(value = "住院号")
    @Field(type = FieldType.Keyword)
    private String inPatientNo;

    @ApiModelProperty(value = "症状/体征")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String physicalSign;

    @ApiModelProperty(value = "临床诊断")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String clinicalDiagnosis;

    @ApiModelProperty(value = "检查项目名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String studyItemName;

    @ApiModelProperty(value = "检查部位名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String partName;

    @ApiModelProperty(value = "设备类型")
    @Field(type = FieldType.Keyword)
    private String deviceType;

    @ApiModelProperty(value = "检查状态")
    @Field(type = FieldType.Keyword)
    private String studyState;

    @ApiModelProperty(value = "设备名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String deviceName;

    @ApiModelProperty(value = "病史")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String medicalHistory;

    @ApiModelProperty(value = "患者主诉")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String selfReportedSymptom;

    @ApiModelProperty(value = "影像学表现")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String reportDescribe;

    @ApiModelProperty(value = "影像学诊断")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String reportDiagnose;

    @ApiModelProperty(value = "阳性/阴性")
    @Field(type = FieldType.Keyword)
    private String isPostive;

    @ApiModelProperty(value = "登记时间")
    @Field(type = FieldType.Keyword)
    private String registerTime;

    @ApiModelProperty(value = "报告时间")
    @Field(type = FieldType.Keyword)
    private String reportTime;

    @ApiModelProperty(value = "报告人")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String reporter;

    @ApiModelProperty(value = "审核人")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String checker;

    @ApiModelProperty(value = "审核时间")
    @Field(type = FieldType.Keyword)
    private String checkTime;

    @ApiModelProperty(value = "申请号")
    @Field(type = FieldType.Keyword)
    private String applyNumber;

    @ApiModelProperty(value = "申请科室")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String applyDepartment;

    @ApiModelProperty(value = "申请医生")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String applyDoctor;

    @ApiModelProperty(value = "技师")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String artificer;

    @ApiModelProperty(value = "是否公有")
    @Field(type = FieldType.Keyword)
    private String isPublic;

    /**
     * 序列信息
     */
    @ApiModelProperty(value = "检查模态列表")
    @Field(type = FieldType.Keyword)
    private Set<String> modalities;

/*    @ApiModelProperty(value = "序列列表")
    @Field(type = FieldType.Nested)
    private List<SeriesInfo> seriesList;*/
}
