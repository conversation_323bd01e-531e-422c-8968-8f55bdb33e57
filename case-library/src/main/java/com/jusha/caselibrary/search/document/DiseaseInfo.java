package com.jusha.caselibrary.search.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName DiseaseInfo
 * @Description 疾病信息
 * <AUTHOR>
 * @Date 2025/8/6 08:45
 **/
@Data
public class DiseaseInfo {

    @ApiModelProperty(value = "疾病id")
    @Field(type = FieldType.Keyword)
    private Long diseaseId;

    @ApiModelProperty(value = "疾病名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String diseaseName;

}
