package com.jusha.caselibrary.search.migration.enums;

/**
 * 兼容性级别枚举
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
public enum CompatibilityLevel {

    /**
     * 完全兼容
     */
    COMPATIBLE("完全兼容", 1),

    /**
     * 需要数据转换
     */
    REQUIRES_CONVERSION("需要数据转换", 2),

    /**
     * 警告级别（可能有风险）
     */
    WARNING("警告", 3),

    /**
     * 不兼容
     */
    INCOMPATIBLE("不兼容", 4),

    /**
     * 未知状态
     */
    UNKNOWN("未知", 5);

    private final String description;
    private final int riskLevel;

    CompatibilityLevel(String description, int riskLevel) {
        this.description = description;
        this.riskLevel = riskLevel;
    }

    public String getDescription() {
        return description;
    }

    public int getRiskLevel() {
        return riskLevel;
    }

    /**
     * 是否可以进行迁移
     */
    public boolean canMigrate() {
        return this != INCOMPATIBLE && this != UNKNOWN;
    }

    /**
     * 是否需要人工干预
     */
    public boolean requiresManualIntervention() {
        return this == INCOMPATIBLE || this == WARNING;
    }

    /**
     * 是否为安全级别
     */
    public boolean isSafe() {
        return this == COMPATIBLE;
    }

    /**
     * 获取建议操作
     */
    public String getRecommendedAction() {
        switch (this) {
            case COMPATIBLE:
                return "可以直接进行迁移";
            case REQUIRES_CONVERSION:
                return "需要配置数据转换规则后进行迁移";
            case WARNING:
                return "建议仔细评估风险后进行迁移";
            case INCOMPATIBLE:
                return "不建议进行迁移，需要重新设计字段结构";
            case UNKNOWN:
                return "需要进一步分析兼容性";
            default:
                return "未知操作";
        }
    }

    /**
     * 比较兼容性级别
     * 
     * @param other 另一个兼容性级别
     * @return 当前级别是否比另一个级别更安全
     */
    public boolean isSaferThan(CompatibilityLevel other) {
        return this.riskLevel < other.riskLevel;
    }

    /**
     * 获取最严重的兼容性级别
     * 
     * @param level1 级别1
     * @param level2 级别2
     * @return 更严重的级别
     */
    public static CompatibilityLevel getMostSevere(CompatibilityLevel level1, CompatibilityLevel level2) {
        return level1.riskLevel > level2.riskLevel ? level1 : level2;
    }
}