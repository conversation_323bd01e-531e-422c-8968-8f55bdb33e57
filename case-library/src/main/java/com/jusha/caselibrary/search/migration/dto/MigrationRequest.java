package com.jusha.caselibrary.search.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 迁移请求
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 索引类型
     */
    private String indexType;

    /**
     * 迁移计划
     */
    private MigrationPlan migrationPlan;

    /**
     * 请求者
     */
    private String requestedBy;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    /**
     * 是否异步执行
     */
    @Builder.Default
    private Boolean async = true;

    /**
     * 优先级（1-10，数字越大优先级越高）
     */
    @Builder.Default
    private Integer priority = 5;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 验证请求是否有效
     */
    public boolean isValid() {
        if (indexType == null || indexType.trim().isEmpty()) {
            return false;
        }
        
        if (migrationPlan == null || !migrationPlan.isValid()) {
            return false;
        }
        
        if (priority == null || priority < 1 || priority > 10) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取预计执行时间
     */
    public LocalDateTime getEstimatedExecutionTime() {
        if (migrationPlan != null && migrationPlan.getScheduledTime() != null) {
            return migrationPlan.getScheduledTime();
        }
        return LocalDateTime.now();
    }

    /**
     * 是否为高优先级请求
     */
    public boolean isHighPriority() {
        return priority != null && priority >= 8;
    }

    /**
     * 是否需要立即执行
     */
    public boolean needsImmediateExecution() {
        LocalDateTime scheduledTime = getEstimatedExecutionTime();
        return scheduledTime.isBefore(LocalDateTime.now().plusMinutes(5));
    }
}