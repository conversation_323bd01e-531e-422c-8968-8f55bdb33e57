package com.jusha.caselibrary.search.document;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName StringToLongListTypeHandler
 * @Description 逗号分隔字符串与Long列表转换的类型处理器
 * <AUTHOR>
 * @Date 2025/8/5 15:30
 **/
public class StringToLongListTypeHandler extends BaseTypeHandler<List<Long>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType) throws SQLException {
        // 将List<Long>转换为逗号分隔的字符串
        if (parameter == null || parameter.isEmpty()) {
            ps.setString(i, null);
        } else {
            String result = parameter.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            ps.setString(i, result);
        }
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return convertStringToLongList(value);
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return convertStringToLongList(value);
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return convertStringToLongList(value);
    }

    /**
     * 将逗号分隔的字符串转换为Long列表
     * @param value 逗号分隔的字符串
     * @return Long列表
     */
    private List<Long> convertStringToLongList(String value) {
        if (!StringUtils.hasText(value)) {
            return Collections.emptyList();
        }

        try {
            return Arrays.stream(value.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            // 如果转换失败，返回空列表
            return Collections.emptyList();
        }
    }
}
