package com.jusha.caselibrary.search.consumer;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.dto.SyncMessage;
import com.jusha.caselibrary.search.service.ESSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.alibaba.nacos.api.config.ConfigType.JSON;

/**
 * @ClassName ESSyncConsumer
 * @Description ES同步消息消费者
 *  * 异步消费Redis队列中的ES同步消息
 * <AUTHOR>
 * @Date 2025/7/7 15:27
 **/
@Slf4j
@Component
public class ESSyncConsumer {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ESSyncService esSyncService;

    @Autowired
    private SearchConfig searchConfig;

    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 启动消费者 - 在应用完全启动后再启动消费者
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startConsumer() {
        if (running.compareAndSet(false, true)) {
            log.info("启动ES同步消息消费者");

            // 启动普通队列消费者
            consumeNormalQueue();

            // 启动延迟队列消费者
            consumeDelayQueue();

            // 启动重试队列消费者
            consumeRetryQueue();
        }
    }

    /**
     * 停止消费者
     */
    @PreDestroy
    public void stopConsumer() {
        if (running.compareAndSet(true, false)) {
            log.info("停止ES同步消息消费者");
        }
    }

    /**
     * 消费普通队列
     */
    @Async
    public void consumeNormalQueue() {
        log.info("启动普通队列消费者线程");

        while (running.get()) {
            try {
                // 阻塞式获取消息，超时时间5秒
                Object message = redisTemplate.opsForList()
                        .rightPop(searchConfig.getMq().getQueues().getSyncQueue(), 5, TimeUnit.SECONDS);
                if (message != null) {
                    processMessage(message);
                }
            } catch (Exception e) {
                log.error("消费普通队列消息异常", e);
                // 短暂休眠后继续
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("普通队列消费者线程已停止");
    }

    /**
     * 消费延迟队列
     */
    @Async
    public void consumeDelayQueue() {
        log.info("启动延迟队列消费者线程");

        while (running.get()) {
            try {
                // 阻塞式获取消息，超时时间10秒
                Object message = redisTemplate.opsForList()
                        .rightPop(searchConfig.getMq().getQueues().getDelayQueue(), 10, TimeUnit.SECONDS);
                if (message != null) {
                    SyncMessage syncMessage = convertToSyncMessage(message);
                    if (syncMessage != null && shouldProcessDelayMessage(syncMessage)) {
                        processMessage(message);
                    } else if (syncMessage != null) {
                        // 重新放回延迟队列
                        redisTemplate.opsForList().leftPush(searchConfig.getMq().getQueues().getDelayQueue(), message);
                        // 避免频繁检查
                        Thread.sleep(1000);
                    }
                }
            } catch (Exception e) {
                log.error("消费延迟队列消息异常", e);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("延迟队列消费者线程已停止");
    }

    /**
     * 消费重试队列
     */
    @Async
    public void consumeRetryQueue() {
        log.info("启动重试队列消费者线程");

        while (running.get()) {
            try {
                // 阻塞式获取消息，超时时间15秒
                Object message = redisTemplate.opsForList()
                        .rightPop(searchConfig.getMq().getQueues().getRetryQueue(), 15, TimeUnit.SECONDS);
                if (message != null) {
                    processMessage(message);
                }
            } catch (Exception e) {
                log.error("消费重试队列消息异常", e);
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("重试队列消费者线程已停止");
    }

    /**
     * 处理消息
     */
    private void processMessage(Object message) {
        SyncMessage syncMessage = null;
        try {
            syncMessage = convertToSyncMessage(message);
            if (syncMessage == null) {
                log.warn("无法转换ES同步消息：{}", message);
                return;
            }

            log.info("开始处理ES同步消息，ID：{}，病例ID：{}，类型：{}，操作：{}",
                    syncMessage.getMessageId(), syncMessage.getIndexId(),
                    syncMessage.getIndexType(), syncMessage.getOperation());

            // 调用同步服务处理消息
            esSyncService.processSyncMessage(syncMessage);

            log.info("ES同步消息处理成功，ID：{}", syncMessage.getMessageId());

        } catch (Exception e) {
            log.error("处理ES同步消息异常，ID：{}",
                    syncMessage != null ? syncMessage.getMessageId() : "unknown", e);

            if (syncMessage != null) {
                syncMessage.setErrorMessage(e.getMessage());
                handleProcessFailure(syncMessage);
            }
        }
    }

    /**
     * 处理消息处理失败的情况
     */
    private void handleProcessFailure(SyncMessage syncMessage) {
        if (syncMessage.needRetry()) {
            syncMessage.incrementRetry();

            // 放入重试队列
            redisTemplate.opsForList().leftPush(searchConfig.getMq().getQueues().getRetryQueue(), syncMessage);

            log.warn("ES同步消息处理失败，已放入重试队列，ID：{}，重试次数：{}/{}",
                    syncMessage.getMessageId(), syncMessage.getRetryCount(), syncMessage.getMaxRetryCount());
        } else {
            log.error("ES同步消息处理失败，已达到最大重试次数，ID：{}，错误：{}",
                    syncMessage.getMessageId(), syncMessage.getErrorMessage());

            // 可以考虑将失败消息存储到数据库或发送告警
            handleFinalFailure(syncMessage);
        }
    }

    /**
     * 处理最终失败的消息
     */
    private void handleFinalFailure(SyncMessage syncMessage) {
        // 这里可以实现失败消息的持久化存储或告警通知
        // 例如：存储到数据库的失败消息表，或发送邮件/短信告警
        log.error("ES同步消息最终处理失败，需要人工介入，消息详情：{}", syncMessage);
    }

    /**
     * 转换为同步消息对象
     */
    private SyncMessage convertToSyncMessage(Object message) {
        try {
            // 统一转换为JSON字符串后解析
            String jsonString = message instanceof String ?
                    (String) message : JSONObject.toJSONString(message);
            return JSONObject.parseObject(jsonString, SyncMessage.class);
        } catch (Exception e) {
            log.error("转换ES同步消息异常", e);
            return null;
        }
    }

    /**
     * 判断延迟消息是否应该处理
     */
    private boolean shouldProcessDelayMessage(SyncMessage syncMessage) {
        if (syncMessage.getDelayTime() == null || syncMessage.getDelayTime() <= 0) {
            return true;
        }

        long currentTime = System.currentTimeMillis();
        long createTime = syncMessage.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

        return (currentTime - createTime) >= syncMessage.getDelayTime();
    }
}