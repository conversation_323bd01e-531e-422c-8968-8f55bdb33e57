package com.jusha.caselibrary.search.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 迁移计划
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计划ID
     */
    private String planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 源索引类型
     */
    private String sourceIndexType;

    /**
     * 目标索引类型
     */
    private String targetIndexType;

    /**
     * 字段变更配置
     * key: 字段路径, value: 字段配置
     */
    private Map<String, Object> fieldChanges;

    /**
     * 索引设置
     */
    private Map<String, Object> indexSettings;

    /**
     * 数据转换规则
     */
    private List<DataTransformRule> transformRules;

    /**
     * 批量大小
     */
    @Builder.Default
    private Integer batchSize = 1000;

    /**
     * 并发线程数
     */
    @Builder.Default
    private Integer concurrency = 2;

    /**
     * 是否需要全量迁移
     */
    @Builder.Default
    private Boolean fullMigration = true;

    /**
     * 是否启用双写
     */
    @Builder.Default
    private Boolean enableDualWrite = true;

    /**
     * 性能限制配置
     */
    private PerformanceConfig performanceConfig;

    /**
     * 计划创建时间
     */
    private LocalDateTime createTime;

    /**
     * 计划创建者
     */
    private String createdBy;

    /**
     * 预计执行时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 数据转换规则
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataTransformRule implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 源字段路径
         */
        private String sourceField;

        /**
         * 目标字段路径
         */
        private String targetField;

        /**
         * 转换类型
         */
        private String transformType;

        /**
         * 转换参数
         */
        private Map<String, Object> parameters;

        /**
         * 是否必需
         */
        @Builder.Default
        private Boolean required = false;
    }

    /**
     * 性能配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceConfig implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 最大内存使用率（百分比）
         */
        @Builder.Default
        private Integer maxMemoryUsage = 70;

        /**
         * 最大CPU使用率（百分比）
         */
        @Builder.Default
        private Integer maxCpuUsage = 50;

        /**
         * 查询超时时间（毫秒）
         */
        @Builder.Default
        private Long queryTimeout = 30000L;

        /**
         * 批处理间隔（毫秒）
         */
        @Builder.Default
        private Long batchInterval = 100L;

        /**
         * 错误重试次数
         */
        @Builder.Default
        private Integer retryCount = 3;

        /**
         * 重试间隔（毫秒）
         */
        @Builder.Default
        private Long retryInterval = 1000L;
    }

    /**
     * 验证迁移计划是否有效
     */
    public boolean isValid() {
        if (sourceIndexType == null || targetIndexType == null) {
            return false;
        }
        
        if (batchSize == null || batchSize <= 0) {
            return false;
        }
        
        if (concurrency == null || concurrency <= 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取预计迁移时间（分钟）
     */
    public long getEstimatedDurationMinutes(long documentCount) {
        if (batchSize == null || concurrency == null) {
            return -1;
        }
        
        // 简单估算：文档数量 / (批量大小 * 并发数) * 每批处理时间
        long batchCount = (documentCount + batchSize - 1) / batchSize;
        long parallelBatchCount = (batchCount + concurrency - 1) / concurrency;
        
        // 假设每批处理需要2秒
        return (parallelBatchCount * 2) / 60;
    }

    /**
     * 是否包含字段类型变更
     */
    public boolean hasFieldTypeChanges() {
        if (fieldChanges == null || fieldChanges.isEmpty()) {
            return false;
        }
        
        return fieldChanges.values().stream()
                .anyMatch(config -> config instanceof Map && 
                         ((Map<?, ?>) config).containsKey("type"));
    }

    /**
     * 是否需要数据转换
     */
    public boolean needsDataTransformation() {
        return transformRules != null && !transformRules.isEmpty();
    }
}