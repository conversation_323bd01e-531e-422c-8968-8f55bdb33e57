package com.jusha.caselibrary.search.migration.enums;

/**
 * 字段变更类型枚举
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
public enum FieldChangeType {

    /**
     * 新增字段
     */
    ADDED("新增字段"),

    /**
     * 删除字段
     */
    REMOVED("删除字段"),

    /**
     * 字段类型变更
     */
    TYPE_CHANGED("字段类型变更"),

    /**
     * 字段属性变更（如analyzer、format等）
     */
    PROPERTIES_CHANGED("字段属性变更"),

    /**
     * 字段重命名
     */
    RENAMED("字段重命名"),

    /**
     * 嵌套结构变更
     */
    NESTED_STRUCTURE_CHANGED("嵌套结构变更");

    private final String description;

    FieldChangeType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 是否为破坏性变更
     */
    public boolean isBreakingChange() {
        return this == REMOVED || this == TYPE_CHANGED || this == RENAMED;
    }

    /**
     * 是否需要数据迁移
     */
    public boolean requiresDataMigration() {
        return this == TYPE_CHANGED || this == RENAMED || this == NESTED_STRUCTURE_CHANGED;
    }

    /**
     * 获取变更风险级别
     */
    public int getRiskLevel() {
        switch (this) {
            case ADDED:
                return 1; // 低风险
            case PROPERTIES_CHANGED:
                return 2; // 中低风险
            case NESTED_STRUCTURE_CHANGED:
                return 3; // 中风险
            case TYPE_CHANGED:
                return 4; // 高风险
            case RENAMED:
                return 4; // 高风险
            case REMOVED:
                return 5; // 最高风险
            default:
                return 3;
        }
    }
}