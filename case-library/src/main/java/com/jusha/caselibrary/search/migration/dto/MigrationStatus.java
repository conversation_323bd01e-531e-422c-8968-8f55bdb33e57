package com.jusha.caselibrary.search.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 迁移状态
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 迁移ID
     */
    private String migrationId;

    /**
     * 索引类型
     */
    private String indexType;

    /**
     * 迁移状态
     * STARTED - 已开始
     * ANALYZING - 分析中
     * CREATING_INDEX - 创建索引中
     * MIGRATING_DATA - 数据迁移中
     * SWITCHING_ALIAS - 切换别名中
     * COMPLETED - 已完成
     * FAILED - 失败
     * CANCELLED - 已取消
     * INTERRUPTED - 中断
     * RESUMED - 已恢复
     */
    private String status;

    /**
     * 当前阶段
     */
    private String currentPhase;

    /**
     * 进度百分比（0-100）
     */
    private Integer progress;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 源版本
     */
    private String sourceVersion;

    /**
     * 目标版本
     */
    private String targetVersion;

    /**
     * 迁移计划
     */
    private MigrationPlan migrationPlan;

    /**
     * 字段分析结果
     */
    private FieldChangeAnalysis fieldAnalysis;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 警告消息
     */
    private String warningMessage;

    /**
     * 已处理文档数
     */
    private Long processedDocuments;

    /**
     * 总文档数
     */
    private Long totalDocuments;

    /**
     * 失败文档数
     */
    private Long failedDocuments;

    /**
     * 跳过文档数
     */
    private Long skippedDocuments;

    /**
     * 执行者
     */
    private String executedBy;

    /**
     * 是否正在运行
     */
    public boolean isRunning() {
        return "STARTED".equals(status) || 
               "ANALYZING".equals(status) ||
               "CREATING_INDEX".equals(status) ||
               "MIGRATING_DATA".equals(status) ||
               "SWITCHING_ALIAS".equals(status) ||
               "RESUMED".equals(status);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    /**
     * 是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 是否被取消
     */
    public boolean isCancelled() {
        return "CANCELLED".equals(status);
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return isRunning() && !"SWITCHING_ALIAS".equals(currentPhase);
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return "INTERRUPTED".equals(status);
    }

    /**
     * 获取执行时长（秒）
     */
    public Long getExecutionDurationSeconds() {
        if (startTime == null) {
            return null;
        }
        
        LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTimeToUse).getSeconds();
    }

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (totalDocuments == null || totalDocuments == 0) {
            return null;
        }
        
        long successful = processedDocuments != null ? processedDocuments : 0;
        long failed = failedDocuments != null ? failedDocuments : 0;
        long processed = successful + failed;
        
        if (processed == 0) {
            return null;
        }
        
        return (double) successful / processed * 100;
    }

    /**
     * 获取预计剩余时间（秒）
     */
    public Long getEstimatedRemainingSeconds() {
        if (progress == null || progress <= 0 || progress >= 100) {
            return null;
        }
        
        Long duration = getExecutionDurationSeconds();
        if (duration == null || duration <= 0) {
            return null;
        }
        
        // 基于当前进度估算剩余时间
        double remainingProgress = 100.0 - progress;
        double timePerPercent = (double) duration / progress;
        
        return Math.round(remainingProgress * timePerPercent);
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        switch (status) {
            case "STARTED":
                return "迁移已开始";
            case "ANALYZING":
                return "正在分析字段兼容性";
            case "CREATING_INDEX":
                return "正在创建新版本索引";
            case "MIGRATING_DATA":
                return "正在迁移数据";
            case "SWITCHING_ALIAS":
                return "正在切换索引别名";
            case "COMPLETED":
                return "迁移已完成";
            case "FAILED":
                return "迁移失败";
            case "CANCELLED":
                return "迁移已取消";
            case "INTERRUPTED":
                return "迁移被中断";
            case "RESUMED":
                return "迁移已恢复";
            default:
                return "未知状态";
        }
    }

    /**
     * 更新最后更新时间
     */
    public void updateLastUpdateTime() {
        this.lastUpdateTime = LocalDateTime.now();
    }
}