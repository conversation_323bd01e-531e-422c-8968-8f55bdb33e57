package com.jusha.caselibrary.search.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.entity.DepCaseClassify;
import com.jusha.caselibrary.mybatisplus.service.DepCaseService;
import com.jusha.caselibrary.mybatisplus.service.UserCaseService;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.mybatisplus.service.DepCaseClassifyService;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.mapper.ESDocumentMapper;
import com.jusha.caselibrary.search.service.CaseDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 病例数据服务实现类
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseDataServiceImpl implements CaseDataService {

    private final DepCaseService depCaseService;

    private final UserCaseService userCaseService;

    private final ESDocumentMapper esDocumentMapper;
    
    private final UserCaseCatalogService userCaseCatalogService;
    
    private final DepCaseClassifyService depCaseClassifyService;

    @Override
    public DepCase getDepartmentCaseById(Long caseId) {
        if (caseId == null) {
            return null;
        }
        return depCaseService.getById(caseId);
    }

    @Override
    public UserCase getPersonalCaseById(Long userCaseId) {
        if (userCaseId == null) {
            return null;
        }
        return userCaseService.getById(userCaseId);
    }

    @Override
    public List<DepCase> getDepartmentCasesByIds(List<Long> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        return depCaseService.listByIds(caseIds);
    }

    @Override
    public List<UserCase> getPersonalCasesByIds(List<Long> userCaseIds) {
        if (userCaseIds == null || userCaseIds.isEmpty()) {
            return Collections.emptyList();
        }
        return userCaseService.listByIds(userCaseIds);
    }

    @Override
    public List<DepCase> getAllDepartmentCases() {
        // 只查询未删除的记录
        return depCaseService.list();
    }

    @Override
    public List<UserCase> getAllPersonalCases() {
        return userCaseService.list();
    }

    @Override
    public List<UserCase> getPersonalCasesByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCase::getCreateBy, userId);
        return userCaseService.list(queryWrapper);
    }

    @Override
    public List<Long> getAllDepartmentCaseIds() {
        LambdaQueryWrapper<DepCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DepCase::getCaseId);
        List<DepCase> cases = depCaseService.list(queryWrapper);
        return cases.stream().map(DepCase::getCaseId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getAllPersonalCaseIds(Long userId) {
        LambdaQueryWrapper<UserCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(UserCase::getUserCaseId);
        if (userId != null) {
            queryWrapper.eq(UserCase::getCreateBy, userId);
        }
        List<UserCase> cases = userCaseService.list(queryWrapper);
        return cases.stream().map(UserCase::getUserCaseId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getDeptCasesByCreateTimeRange(String startTime, String endTime) {
        LambdaQueryWrapper<DepCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DepCase::getCaseId)
                .ge(DepCase::getCreateTime, startTime)
                .le(DepCase::getCreateTime, endTime);
        List<DepCase> cases = depCaseService.list(queryWrapper);
        if (!cases.isEmpty()) {
            return cases.stream().map(DepCase::getCaseId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> getPersonalCasesByCreateTimeRange(String startTime, String endTime) {
        LambdaQueryWrapper<UserCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(UserCase::getUserCaseId)
                .ge(UserCase::getCreateTime, startTime)
                .le(UserCase::getCreateTime, endTime);
        List<UserCase> cases = userCaseService.list(queryWrapper);
        if (!cases.isEmpty()) {
            return cases.stream().map(UserCase::getUserCaseId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public DepartmentCaseDocument buildDepartmentCaseDocument(Long caseId) {
        try {
            DepartmentCaseDocument document = esDocumentMapper.buildDeptCaseDocument(caseId);
            if (document != null) {
                // 构建被收藏目录map：Map<userId, List<catalogId>>
                Map<Long, List<Long>> favoriteCatalogMap = buildFavoriteCatalogMap(caseId);
                document.setFavoriteCatalogMap(favoriteCatalogMap);
            }
            log.debug("构建科室病例ES文档成功，caseId: {}", caseId);
            return document;
        } catch (Exception e) {
            log.error("构建科室病例ES文档异常，caseId: {}", caseId, e);
            return null;
        }
    }

    @Override
    public PersonalCaseDocument buildPersonalCaseDocument(Long userCaseId) {
        try {
            PersonalCaseDocument document = esDocumentMapper.buildPersonalCaseDocument(userCaseId);
            if (document != null) {
                // 构建被发送到科室病例库类型map：Map<userId, List<caseTypeId>>
                Map<Long, List<Long>> sentDepartmentIdMap = buildSentDepartmentIdMap(document.getCaseId());
                document.setSentDepartmentIdMap(sentDepartmentIdMap);
            }
            log.debug("构建个人病例ES文档成功，userCaseId: {}", userCaseId);
            return document;
        } catch (Exception e) {
            log.error("构建个人病例ES文档异常，userCaseId: {}", userCaseId, e);
            return null;
        }
    }

    @Override
    public List<DepartmentCaseDocument> buildSyncDepartmentCasesByIds(List<Long> caseIdList) {
        List<DepartmentCaseDocument> departmentCaseDocumentList = esDocumentMapper.buildSyncDeptCasesByIds(caseIdList);
        if (CollectionUtils.isNotEmpty(departmentCaseDocumentList)) {
            departmentCaseDocumentList.forEach(document -> {
                // 构建被收藏目录map：Map<userId, List<catalogId>>
                Map<Long, List<Long>> favoriteCatalogMap = buildFavoriteCatalogMap(document.getCaseId());
                document.setFavoriteCatalogMap(favoriteCatalogMap);
            });
        }
        return departmentCaseDocumentList;
    }

    @Override
    public List<PersonalCaseDocument> buildSyncPersonalCasesByUserCaseIds(List<Long> caseIdList) {
        List<PersonalCaseDocument> personalCaseDocumentList = esDocumentMapper.buildSyncPersonalCasesByUserCaseIds(caseIdList);
        if (CollectionUtils.isNotEmpty(personalCaseDocumentList)) {
            personalCaseDocumentList.forEach(document -> {
                // 构建被发送到科室病例库类型map：Map<userId, List<caseTypeId>>
                Map<Long, List<Long>> sentDepartmentIdMap = buildSentDepartmentIdMap(document.getCaseId());
                document.setSentDepartmentIdMap(sentDepartmentIdMap);
            });
        }
        return personalCaseDocumentList;
    }
    
    /**
     * 构建被收藏目录map
     * 查询该病例被哪些用户收藏到了哪些目录
     * @param caseId 病例ID
     * @return Map<userId, List<catalogId>>
     */
    private Map<Long, List<Long>> buildFavoriteCatalogMap(Long caseId) {
        Map<Long, List<Long>> favoriteCatalogMap = new HashMap<>();
        
        // 查询该病例在个人病例库中的所有记录
        List<UserCase> userCases = userCaseService.lambdaQuery()
                .eq(UserCase::getCaseId, caseId)
                .eq(UserCase::getDelFlag, 0)
                .list();
        
        if (!userCases.isEmpty()) {
            List<Long> userCaseIds = userCases.stream()
                    .map(UserCase::getUserCaseId)
                    .collect(Collectors.toList());
            
            // 查询这些个人病例对应的目录关联
            List<UserCaseCatalog> catalogs = userCaseCatalogService.lambdaQuery()
                    .in(UserCaseCatalog::getUserCaseId, userCaseIds)
                    .list();
            
            // 按用户分组构建map
            for (UserCase userCase : userCases) {
                Long userId = userCase.getCreateBy();
                List<Long> catalogIds = catalogs.stream()
                        .filter(catalog -> catalog.getUserCaseId().equals(userCase.getUserCaseId()))
                        .map(UserCaseCatalog::getCatalogId)
                        .collect(Collectors.toList());
                
                if (!catalogIds.isEmpty()) {
                    favoriteCatalogMap.put(userId, catalogIds);
                }
            }
        }
        
        return favoriteCatalogMap;
    }
    
    /**
     * 构建被发送到科室病例库类型map
     * 查询该病例被哪些用户发送到了哪些科室病例库类型
     * @param caseId 病例ID
     * @return Map<userId, List<caseTypeId>>
     */
    private Map<Long, List<Long>> buildSentDepartmentIdMap(Long caseId) {
        Map<Long, List<Long>> sentDepartmentIdMap = new HashMap<>();
        
        // 查询该病例在科室病例库中的记录
        List<DepCase> depCases = depCaseService.lambdaQuery()
                .eq(DepCase::getCaseId, caseId)
                .eq(DepCase::getDelFlag, 0)
                .list();
        
        if (!depCases.isEmpty()) {
            List<Long> depCaseIds = depCases.stream()
                    .map(DepCase::getCaseId)
                    .collect(Collectors.toList());
            
            // 查询这些科室病例对应的类型关联
            List<DepCaseClassify> classifies = depCaseClassifyService.lambdaQuery()
                    .in(DepCaseClassify::getCaseId, depCaseIds)
                    .list();
            
            // 按用户分组构建map
            for (DepCase depCase : depCases) {
                Long userId = depCase.getCreateBy();
                List<Long> caseTypeIds = classifies.stream()
                        .filter(classify -> classify.getCaseId().equals(depCase.getCaseId()))
                        .map(DepCaseClassify::getCaseTypeId)
                        .collect(Collectors.toList());
                
                if (!caseTypeIds.isEmpty()) {
                    sentDepartmentIdMap.put(userId, caseTypeIds);
                }
            }
        }
        
        return sentDepartmentIdMap;
    }
}