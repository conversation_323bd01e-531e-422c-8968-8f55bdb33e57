package com.jusha.caselibrary.search.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.mapper.DepCaseMapper;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseMapper;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.mapper.ESDocumentMapper;
import com.jusha.caselibrary.search.service.CaseDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 病例数据服务实现类
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseDataServiceImpl implements CaseDataService {

    private final DepCaseMapper depCaseMapper;

    private final UserCaseMapper userCaseMapper;

    private final ESDocumentMapper esDocumentMapper;

    @Override
    public DepCase getDepartmentCaseById(Long caseId) {
        if (caseId == null) {
            return null;
        }
        return depCaseMapper.selectById(caseId);
    }

    @Override
    public UserCase getPersonalCaseById(Long caseId) {
        if (caseId == null) {
            return null;
        }
        return userCaseMapper.selectById(caseId);
    }

    @Override
    public List<DepCase> getDepartmentCasesByIds(List<Long> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        return depCaseMapper.selectBatchIds(caseIds);
    }

    @Override
    public List<UserCase> getPersonalCasesByIds(List<Long> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        return userCaseMapper.selectBatchIds(caseIds);
    }

    @Override
    public List<DepCase> getAllDepartmentCases() {
        QueryWrapper<DepCase> queryWrapper = new QueryWrapper<>();
        // 只查询未删除的记录
        queryWrapper.eq("del_flag", "0");
        return depCaseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserCase> getAllPersonalCases() {
        QueryWrapper<UserCase> queryWrapper = new QueryWrapper<>();
        // 只查询未删除的记录
        queryWrapper.eq("del_flag", "0");
        return userCaseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserCase> getPersonalCasesByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        QueryWrapper<UserCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_by", userId)
                .eq("del_flag", "0");
        return userCaseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Long> getAllDepartmentCaseIds() {
        QueryWrapper<DepCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("case_id")
                .eq("del_flag", "0");
        List<DepCase> cases = depCaseMapper.selectList(queryWrapper);
        return cases.stream().map(DepCase::getCaseId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getAllPersonalCaseIds(Long userId) {
        QueryWrapper<UserCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("user_case_id")
                .eq("del_flag", "0");
        if (userId != null) {
            queryWrapper.eq("create_by", userId);
        }
        List<UserCase> cases = userCaseMapper.selectList(queryWrapper);
        return cases.stream().map(UserCase::getUserCaseId).collect(Collectors.toList());
    }

    @Override
    public DepartmentCaseDocument buildDepartmentCaseDocument(Long caseId) {
        try {
            DepartmentCaseDocument document = esDocumentMapper.buildDeptCaseDocument(caseId);
            log.debug("构建科室病例ES文档成功，caseId: {}", caseId);
            return document;
        } catch (Exception e) {
            log.error("构建科室病例ES文档异常，caseId: {}", caseId, e);
            return null;
        }
    }

    @Override
    public PersonalCaseDocument buildPersonalCaseDocument(Long userCaseId) {
        try {
            PersonalCaseDocument document = esDocumentMapper.buildPersonalCaseDocument(userCaseId);
            log.debug("构建个人病例ES文档成功，userCaseId: {}", userCaseId);
            return document;
        } catch (Exception e) {
            log.error("构建个人病例ES文档异常，userCaseId: {}", userCaseId, e);
            return null;
        }
    }

    @Override
    public List<DepartmentCaseDocument> buildSyncDepartmentCasesByIds(List<Long> caseIdList) {
        return esDocumentMapper.buildSyncDeptCasesByIds(caseIdList);
    }

    @Override
    public List<PersonalCaseDocument> buildSyncPersonalCasesByUserCaseIds(List<Long> caseIdList) {
        return esDocumentMapper.buildSyncPersonalCasesByUserCaseIds(caseIdList);
    }
}