package com.jusha.caselibrary.search.dto;

import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName AdvancedSearchResponse
 * @Description 高级检索响应DTO
 * <AUTHOR>
 * @Date 2025/7/7 15:57
 **/
@Data
public class AdvancedSearchResponse<T> {

    /**
     * 检索结果列表
     */
    private List<SearchResult<T>> results;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 搜索耗时（毫秒）
     */
    private Long took;

    /**
     * 最大评分
     */
    private Float maxScore;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否成功
     */
    private Boolean success = true;

    /**
     * 搜索结果项
     */
    @Data
    public static class SearchResult<T> {
        /**
         * 文档ID
         */
        private String id;

        /**
         * 评分
         */
        private Float score;

        /**
         * 文档数据
         */
        private T source;

        /**
         * 高亮字段
         */
        private Map<String, List<String>> highlight;

        /**
         * 排序值
         */
        private Object[] sort;
    }

    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.totalPages = 0;
        }
    }

    /**
     * 计算是否有下一页
     */
    public void calculateHasNext() {
        if (pageNum != null && totalPages != null) {
            this.hasNext = pageNum < totalPages;
        } else {
            this.hasNext = false;
        }
    }

    /**
     * 计算是否有上一页
     */
    public void calculateHasPrevious() {
        if (pageNum != null) {
            this.hasPrevious = pageNum > 1;
        } else {
            this.hasPrevious = false;
        }
    }

    /**
     * 设置分页信息并自动计算相关字段
     */
    public void setPaginationInfo(Long total, Integer pageNum, Integer pageSize) {
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        calculateTotalPages();
        calculateHasNext();
        calculateHasPrevious();
    }

    /**
     * 创建成功响应
     */
    public static <T> AdvancedSearchResponse<T> success(List<SearchResult<T>> results, Long total,
                                                        Integer pageNum, Integer pageSize, Long took) {
        AdvancedSearchResponse<T> response = new AdvancedSearchResponse<>();
        response.setResults(results);
        response.setPaginationInfo(total, pageNum, pageSize);
        response.setTook(took);
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static <T> AdvancedSearchResponse<T> failure(String errorMessage) {
        AdvancedSearchResponse<T> response = new AdvancedSearchResponse<>();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        return response;
    }

    /**
     * 创建空结果响应
     */
    public static <T> AdvancedSearchResponse<T> empty(Integer pageNum, Integer pageSize) {
        AdvancedSearchResponse<T> response = new AdvancedSearchResponse<>();
        response.setResults(Collections.emptyList());
        response.setPaginationInfo(0L, pageNum, pageSize);
        response.setTook(0L);
        response.setSuccess(true);
        return response;
    }

    /**
     * 是否为空结果
     */
    public boolean isEmpty() {
        return results == null || results.isEmpty();
    }

    /**
     * 设置最大评分
     */
    public void calculateMaxScore() {
        if (results != null && !results.isEmpty()) {
            this.maxScore = results.stream()
                    .map(SearchResult::getScore)
                    .filter(Objects::nonNull)
                    .max(Float::compareTo)
                    .orElse(0.0f);
        } else {
            this.maxScore = 0.0f;
        }
    }
}
