package com.jusha.caselibrary.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @ClassName SeriesInfo
 * @Description 序列信息
 * <AUTHOR>
 * @Date 2025/7/29 08:45
 **/
@Data
public class    SeriesInfo {

    @ApiModelProperty(value = "序列UID")
    @Field(type = FieldType.Keyword)
    private String seriesUid;

    @ApiModelProperty(value = "患者id")
    @Field(type = FieldType.Keyword)
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    @Field(type = FieldType.Keyword)
    private String patientName;

    @ApiModelProperty(value = "性别")
    @Field(type = FieldType.Keyword)
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    @Field(type = FieldType.Keyword)
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    @Field(type = FieldType.Keyword)
    private String patientAge;

    @ApiModelProperty(value = "检查UID")
    @Field(type = FieldType.Keyword)
    private String studyUid;

    @ApiModelProperty(value = "检查流水号")
    @Field(type = FieldType.Keyword)
    private String accessNumber;

    @ApiModelProperty(value = "序列描述")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String seriesDescription;

    @ApiModelProperty(value = "检查模态")
    @Field(type = FieldType.Keyword)
    private String modality;

    @ApiModelProperty(value = "是否关键帧: 0-不是 1-是")
    @Field(type = FieldType.Integer)
    private int isKeyframe;

    @ApiModelProperty(value = "创建人")
    @Field(type = FieldType.Long)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DateUtil.DEFAULT_PATTERN)
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private Date createTime;
}
