package com.jusha.caselibrary.search.migration;

import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.migration.dto.IndexVersionInfo;
import com.jusha.caselibrary.search.migration.dto.MigrationPlan;
import com.jusha.caselibrary.search.migration.enums.IndexVersionStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 索引版本管理服务
 * 
 * 核心功能：
 * 1. 索引版本控制和生命周期管理
 * 2. 索引别名动态切换
 * 3. 字段类型变更支持
 * 4. 版本回滚机制
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndexVersionManager {

    private final ElasticsearchRestTemplate elasticsearchTemplate;
//    private final RestHighLevelClient restHighLevelClient;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SearchConfig searchConfig;

    private static final String VERSION_CACHE_PREFIX = "index_version:";
    private static final String MIGRATION_LOCK_PREFIX = "migration_lock:";
    private static final DateTimeFormatter VERSION_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 创建新版本索引
     * 
     * @param indexType 索引类型（department_cases 或 personal_cases）
     * @param documentClass 文档类型
     * @param migrationPlan 迁移计划
     * @return 新版本索引信息
     */
    public IndexVersionInfo createNewVersion(String indexType, Class<?> documentClass, MigrationPlan migrationPlan) {
        try {
            log.info("开始创建新版本索引，类型: {}", indexType);
            
            // 1. 生成新版本号
            String newVersion = generateNewVersion(indexType);
            String newIndexName = buildIndexName(indexType, newVersion);
            
            // 2. 创建索引版本信息
            IndexVersionInfo versionInfo = IndexVersionInfo.builder()
                    .indexType(indexType)
                    .version(newVersion)
                    .indexName(newIndexName)
                    .aliasName(getAliasName(indexType))
                    .status(IndexVersionStatus.CREATING)
                    .createTime(LocalDateTime.now())
                    .migrationPlan(migrationPlan)
                    .build();
            
            // 3. 创建物理索引
            createPhysicalIndex(newIndexName, documentClass, migrationPlan);
            
            // 4. 更新状态并缓存
            versionInfo.setStatus(IndexVersionStatus.CREATED);
            cacheVersionInfo(versionInfo);
            
            log.info("新版本索引创建成功: {}", newIndexName);
            return versionInfo;
            
        } catch (Exception e) {
            log.error("创建新版本索引失败，类型: {}", indexType, e);
            throw new RuntimeException("创建新版本索引失败", e);
        }
    }

    /**
     * 切换索引别名
     * 
     * @param versionInfo 版本信息
     * @param removeOldAlias 是否移除旧别名
     */
    public void switchAlias(IndexVersionInfo versionInfo, boolean removeOldAlias) {
        try {
            log.info("开始切换索引别名: {} -> {}", versionInfo.getAliasName(), versionInfo.getIndexName());
            
            IndicesAliasesRequest request = new IndicesAliasesRequest();
            
            if (removeOldAlias) {
                // 获取当前别名指向的索引
                String currentIndex = getCurrentIndexByAlias(versionInfo.getAliasName());
                if (currentIndex != null) {
                    request.addAliasAction(
                        IndicesAliasesRequest.AliasActions.remove()
                            .index(currentIndex)
                            .alias(versionInfo.getAliasName())
                    );
                }
            }
            
            // 添加新别名
            request.addAliasAction(
                IndicesAliasesRequest.AliasActions.add()
                    .index(versionInfo.getIndexName())
                    .alias(versionInfo.getAliasName())
            );
            
//            restHighLevelClient.indices().updateAliases(request, RequestOptions.DEFAULT);
            
            // 更新版本状态
            versionInfo.setStatus(IndexVersionStatus.ACTIVE);
            versionInfo.setSwitchTime(LocalDateTime.now());
            cacheVersionInfo(versionInfo);
            
            log.info("索引别名切换成功: {}", versionInfo.getAliasName());
            
        } catch (Exception e) {
            log.error("切换索引别名失败: {}", versionInfo.getIndexName(), e);
            throw new RuntimeException("切换索引别名失败", e);
        }
    }

    /**
     * 回滚到指定版本
     * 
     * @param indexType 索引类型
     * @param targetVersion 目标版本
     */
    public void rollbackToVersion(String indexType, String targetVersion) {
        try {
            log.info("开始回滚索引版本: {} -> {}", indexType, targetVersion);
            
            // 1. 获取目标版本信息
            IndexVersionInfo targetVersionInfo = getVersionInfo(indexType, targetVersion);
            if (targetVersionInfo == null) {
                throw new IllegalArgumentException("目标版本不存在: " + targetVersion);
            }
            
            // 2. 检查目标索引是否存在
            String targetIndexName = targetVersionInfo.getIndexName();
            if (!indexExists(targetIndexName)) {
                throw new IllegalStateException("目标索引不存在: " + targetIndexName);
            }
            
            // 3. 切换别名到目标版本
            switchAlias(targetVersionInfo, true);
            
            // 4. 更新版本状态
            targetVersionInfo.setStatus(IndexVersionStatus.ACTIVE);
            targetVersionInfo.setRollbackTime(LocalDateTime.now());
            cacheVersionInfo(targetVersionInfo);
            
            log.info("索引版本回滚成功: {} -> {}", indexType, targetVersion);
            
        } catch (Exception e) {
            log.error("回滚索引版本失败: {} -> {}", indexType, targetVersion, e);
            throw new RuntimeException("回滚索引版本失败", e);
        }
    }

    /**
     * 删除旧版本索引
     * 
     * @param indexType 索引类型
     * @param keepVersions 保留版本数量
     */
    public void cleanupOldVersions(String indexType, int keepVersions) {
        try {
            log.info("开始清理旧版本索引: {}, 保留数量: {}", indexType, keepVersions);
            
            // 1. 获取所有版本
            List<IndexVersionInfo> allVersions = getAllVersions(indexType);
            if (allVersions.size() <= keepVersions) {
                log.info("版本数量未超过保留数量，无需清理");
                return;
            }
            
            // 2. 按创建时间排序，保留最新的版本
            allVersions.sort((v1, v2) -> v2.getCreateTime().compareTo(v1.getCreateTime()));
            List<IndexVersionInfo> versionsToDelete = allVersions.subList(keepVersions, allVersions.size());
            
            // 3. 删除旧版本（跳过当前活跃版本）
            String activeVersion = getCurrentActiveVersion(indexType);
            for (IndexVersionInfo versionInfo : versionsToDelete) {
                if (!versionInfo.getVersion().equals(activeVersion)) {
                    deleteVersion(versionInfo);
                }
            }
            
            log.info("旧版本索引清理完成，删除数量: {}", versionsToDelete.size());
            
        } catch (Exception e) {
            log.error("清理旧版本索引失败: {}", indexType, e);
            throw new RuntimeException("清理旧版本索引失败", e);
        }
    }

    /**
     * 获取当前活跃版本
     * 
     * @param indexType 索引类型
     * @return 当前活跃版本号
     */
    public String getCurrentActiveVersion(String indexType) {
        try {
            String aliasName = getAliasName(indexType);
            String currentIndex = getCurrentIndexByAlias(aliasName);
            if (currentIndex != null) {
                return extractVersionFromIndexName(currentIndex);
            }
            return null;
        } catch (Exception e) {
            log.error("获取当前活跃版本失败: {}", indexType, e);
            return null;
        }
    }

    /**
     * 获取版本信息
     * 
     * @param indexType 索引类型
     * @param version 版本号
     * @return 版本信息
     */
    public IndexVersionInfo getVersionInfo(String indexType, String version) {
        String cacheKey = VERSION_CACHE_PREFIX + indexType + ":" + version;
        return (IndexVersionInfo) redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 获取所有版本信息
     * 
     * @param indexType 索引类型
     * @return 版本信息列表
     */
    public List<IndexVersionInfo> getAllVersions(String indexType) {
        String pattern = VERSION_CACHE_PREFIX + indexType + ":*";
        Set<String> keys = redisTemplate.keys(pattern);
        List<IndexVersionInfo> versions = new ArrayList<>();
        
        if (keys != null) {
            for (String key : keys) {
                IndexVersionInfo versionInfo = (IndexVersionInfo) redisTemplate.opsForValue().get(key);
                if (versionInfo != null) {
                    versions.add(versionInfo);
                }
            }
        }
        
        return versions;
    }

    /**
     * 检查是否可以进行迁移
     * 
     * @param indexType 索引类型
     * @return 是否可以迁移
     */
    public boolean canMigrate(String indexType) {
        String lockKey = MIGRATION_LOCK_PREFIX + indexType;
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 2, TimeUnit.HOURS);
        return Boolean.TRUE.equals(lockAcquired);
    }

    /**
     * 释放迁移锁
     * 
     * @param indexType 索引类型
     */
    public void releaseMigrationLock(String indexType) {
        String lockKey = MIGRATION_LOCK_PREFIX + indexType;
        redisTemplate.delete(lockKey);
    }

    // ==================== 私有方法 ====================

    /**
     * 生成新版本号
     */
    private String generateNewVersion(String indexType) {
        String timestamp = LocalDateTime.now().format(VERSION_FORMATTER);
        return "v" + timestamp;
    }

    /**
     * 构建索引名称
     */
    private String buildIndexName(String indexType, String version) {
        return indexType + "_" + version;
    }

    /**
     * 获取别名名称
     */
    private String getAliasName(String indexType) {
        if ("department_cases".equals(indexType)) {
            return searchConfig.getIndexes().getDepartmentCases();
        } else if ("personal_cases".equals(indexType)) {
            return searchConfig.getIndexes().getPersonalCases();
        }
        throw new IllegalArgumentException("不支持的索引类型: " + indexType);
    }

    /**
     * 创建物理索引
     */
    private void createPhysicalIndex(String indexName, Class<?> documentClass, MigrationPlan migrationPlan) throws IOException {
        CreateIndexRequest request = new CreateIndexRequest(indexName);
        
        // 设置索引配置
        Settings.Builder settingsBuilder = Settings.builder()
                .put("index.number_of_shards", 1)
                .put("index.number_of_replicas", 0)
                .put("index.max_result_window", 10000);
        
        // 应用迁移计划中的配置
        if (migrationPlan.getIndexSettings() != null) {
//            migrationPlan.getIndexSettings().forEach(settingsBuilder::put);
        }
        
        request.settings(settingsBuilder);
        
        // 设置映射
        IndexOperations indexOps = elasticsearchTemplate.indexOps(documentClass);
        Document mapping = indexOps.createMapping(documentClass);
        
        // 应用字段变更
        if (migrationPlan.getFieldChanges() != null) {
            applyFieldChangesToMapping(mapping, migrationPlan.getFieldChanges());
        }
        
//        request.mapping(mapping.toJson());
        
//        restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
    }

    /**
     * 应用字段变更到映射
     */
    private void applyFieldChangesToMapping(Document mapping, Map<String, Object> fieldChanges) {
        // 这里实现字段变更逻辑
        // 例如：新增字段、修改字段类型等
        fieldChanges.forEach((fieldPath, fieldConfig) -> {
            // 解析字段路径并应用变更
            applyFieldChange(mapping, fieldPath, fieldConfig);
        });
    }

    /**
     * 应用单个字段变更
     */
    private void applyFieldChange(Document mapping, String fieldPath, Object fieldConfig) {
        // 实现具体的字段变更逻辑
        log.debug("应用字段变更: {} -> {}", fieldPath, fieldConfig);
        // TODO: 实现字段变更逻辑
    }

    /**
     * 获取当前别名指向的索引
     */
    private String getCurrentIndexByAlias(String aliasName) throws IOException {
        GetAliasesRequest request = new GetAliasesRequest(aliasName);
//        var response = restHighLevelClient.indices().getAlias(request, RequestOptions.DEFAULT);
        
//        if (!response.getAliases().isEmpty()) {
//            return response.getAliases().keySet().iterator().next();
//        }
        return null;
    }

    /**
     * 检查索引是否存在
     */
    private boolean indexExists(String indexName) throws IOException {
        GetIndexRequest request = new GetIndexRequest(indexName);
//        return restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        return false;
    }

    /**
     * 从索引名称中提取版本号
     */
    private String extractVersionFromIndexName(String indexName) {
        int lastUnderscoreIndex = indexName.lastIndexOf('_');
        if (lastUnderscoreIndex > 0) {
            return indexName.substring(lastUnderscoreIndex + 1);
        }
        return null;
    }

    /**
     * 删除版本
     */
    private void deleteVersion(IndexVersionInfo versionInfo) throws IOException {
        // 删除物理索引
        DeleteIndexRequest request = new DeleteIndexRequest(versionInfo.getIndexName());
//        restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
        
        // 删除缓存
        String cacheKey = VERSION_CACHE_PREFIX + versionInfo.getIndexType() + ":" + versionInfo.getVersion();
        redisTemplate.delete(cacheKey);
        
        log.info("删除版本成功: {}", versionInfo.getIndexName());
    }

    /**
     * 缓存版本信息
     */
    private void cacheVersionInfo(IndexVersionInfo versionInfo) {
        String cacheKey = VERSION_CACHE_PREFIX + versionInfo.getIndexType() + ":" + versionInfo.getVersion();
        redisTemplate.opsForValue().set(cacheKey, versionInfo, 30, TimeUnit.DAYS);
    }
}