package com.jusha.caselibrary.search.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移进度
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationProgress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 迁移ID
     */
    private String migrationId;

    /**
     * 当前阶段
     */
    private String currentPhase;

    /**
     * 总体进度百分比（0-100）
     */
    private Integer overallProgress;

    /**
     * 当前阶段进度百分比（0-100）
     */
    private Integer phaseProgress;

    /**
     * 已处理文档数
     */
    private Long processedDocuments;

    /**
     * 总文档数
     */
    private Long totalDocuments;

    /**
     * 成功处理文档数
     */
    private Long successfulDocuments;

    /**
     * 失败文档数
     */
    private Long failedDocuments;

    /**
     * 跳过文档数
     */
    private Long skippedDocuments;

    /**
     * 当前处理速度（文档/秒）
     */
    private Double processingRate;

    /**
     * 平均处理速度（文档/秒）
     */
    private Double averageProcessingRate;

    /**
     * 预计剩余时间（秒）
     */
    private Long estimatedRemainingSeconds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 阶段列表
     */
    private List<PhaseProgress> phases;

    /**
     * 错误信息列表
     */
    private List<String> errors;

    /**
     * 警告信息列表
     */
    private List<String> warnings;

    /**
     * 阶段进度
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhaseProgress implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 阶段名称
         */
        private String phaseName;

        /**
         * 阶段描述
         */
        private String description;

        /**
         * 阶段状态
         */
        private String status;

        /**
         * 阶段进度（0-100）
         */
        private Integer progress;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 耗时（秒）
         */
        private Long durationSeconds;

        /**
         * 是否已完成
         */
        public boolean isCompleted() {
            return "COMPLETED".equals(status);
        }

        /**
         * 是否正在进行
         */
        public boolean isInProgress() {
            return "IN_PROGRESS".equals(status);
        }

        /**
         * 是否失败
         */
        public boolean isFailed() {
            return "FAILED".equals(status);
        }
    }

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (processedDocuments == null || processedDocuments == 0) {
            return null;
        }
        
        long successful = successfulDocuments != null ? successfulDocuments : 0;
        return (double) successful / processedDocuments * 100;
    }

    /**
     * 获取失败率
     */
    public Double getFailureRate() {
        if (processedDocuments == null || processedDocuments == 0) {
            return null;
        }
        
        long failed = failedDocuments != null ? failedDocuments : 0;
        return (double) failed / processedDocuments * 100;
    }

    /**
     * 获取跳过率
     */
    public Double getSkipRate() {
        if (processedDocuments == null || processedDocuments == 0) {
            return null;
        }
        
        long skipped = skippedDocuments != null ? skippedDocuments : 0;
        return (double) skipped / processedDocuments * 100;
    }

    /**
     * 获取已用时间（秒）
     */
    public Long getElapsedSeconds() {
        if (startTime == null) {
            return null;
        }
        
        LocalDateTime endTime = lastUpdateTime != null ? lastUpdateTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTime).getSeconds();
    }

    /**
     * 获取预计总时间（秒）
     */
    public Long getEstimatedTotalSeconds() {
        Long elapsed = getElapsedSeconds();
        Long remaining = estimatedRemainingSeconds;
        
        if (elapsed == null) {
            return remaining;
        }
        
        if (remaining == null) {
            return elapsed;
        }
        
        return elapsed + remaining;
    }

    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }

    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }

    /**
     * 获取当前阶段信息
     */
    public PhaseProgress getCurrentPhaseInfo() {
        if (phases == null || currentPhase == null) {
            return null;
        }
        
        return phases.stream()
                .filter(phase -> currentPhase.equals(phase.getPhaseName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取已完成阶段数
     */
    public int getCompletedPhasesCount() {
        if (phases == null) {
            return 0;
        }
        
        return (int) phases.stream()
                .filter(PhaseProgress::isCompleted)
                .count();
    }

    /**
     * 获取总阶段数
     */
    public int getTotalPhasesCount() {
        return phases != null ? phases.size() : 0;
    }

    /**
     * 获取进度摘要
     */
    public String getProgressSummary() {
        StringBuilder summary = new StringBuilder();
        
        summary.append("总体进度: ").append(overallProgress != null ? overallProgress : 0).append("%");
        
        if (processedDocuments != null && totalDocuments != null) {
            summary.append(", 文档: ").append(processedDocuments).append("/").append(totalDocuments);
        }
        
        if (averageProcessingRate != null) {
            summary.append(", 速度: ").append(String.format("%.1f", averageProcessingRate)).append("文档/秒");
        }
        
        if (estimatedRemainingSeconds != null) {
            long minutes = estimatedRemainingSeconds / 60;
            long seconds = estimatedRemainingSeconds % 60;
            summary.append(", 预计剩余: ").append(minutes).append("分").append(seconds).append("秒");
        }
        
        return summary.toString();
    }

    /**
     * 更新最后更新时间
     */
    public void updateLastUpdateTime() {
        this.lastUpdateTime = LocalDateTime.now();
    }
}