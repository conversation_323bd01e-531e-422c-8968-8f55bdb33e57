package com.jusha.caselibrary.search.service.impl;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.sickcase.dto.req.DeptSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 高级检索服务实现类
 * 
 * 主要优化内容：
 * 1. 实现search_after深度分页优化
 * 2. 智能查询优化策略
 * 3. 性能监控和日志记录
 * 4. 支持新增的FollowInfo和DiseaseOverviewInfo字段
 * 5. 查询模板预编译
 * 6. 并发查询优化
 * 7. ElasticSearch原生优化
 * 
 * <AUTHOR>
 * @date 2025/07/09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvancedSearchServiceImpl implements AdvancedSearchService {

    private final ElasticsearchRestTemplate elasticsearchTemplate;

    private final SearchConfig searchConfig;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN);
    
    /**
     * 连接预热标志
     */
    private volatile boolean isWarmedUp = false;

    /**
     * 预热 Elasticsearch 连接
     */
    @PostConstruct
    public void warmUpConnection() {
        if (!isWarmedUp) {
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始预热 Elasticsearch 连接...");
                    long startTime = System.currentTimeMillis();
                    
                    // 执行一个简单的查询来预热连接
                    NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
                    queryBuilder.withQuery(QueryBuilders.matchAllQuery());
                    queryBuilder.withPageable(PageRequest.of(0, 1));
                    
                    try {
                        IndexCoordinates indexCoordinates = IndexCoordinates.of(
                            searchConfig.getIndexes().getDepartmentCases());
                        elasticsearchTemplate.search(queryBuilder.build(), 
                            DepartmentCaseDocument.class, indexCoordinates);
                        
                        long took = System.currentTimeMillis() - startTime;
                        isWarmedUp = true;
                        log.info("Elasticsearch 连接预热完成，耗时: {}ms", took);
                    } catch (Exception e) {
                        log.warn("Elasticsearch 连接预热失败，但不影响正常使用: {}", e.getMessage());
                        // 即使预热失败，也标记为已尝试，避免重复预热
                        isWarmedUp = true;
                    }
                } catch (Exception e) {
                    log.error("预热过程异常", e);
                    isWarmedUp = true;
                }
            });
        }
    }

    @Override
    public <T extends AdvancedSearchRequest> AdvancedSearchResponse<?> search(T request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 参数验证
            request.preprocessRequest(request);
            if (!request.isValid()) {
                return AdvancedSearchResponse.failure(LocaleUtil.getLocale("common.param.error"));
            }

            // 2. 查询优化
            optimizeSearchRequest(request);

            // 3. 执行搜索
            AdvancedSearchResponse<?> response;
            if (request instanceof PersonalSearchReq) {
                response = searchPersonalCases((PersonalSearchReq) request);
            } else if (request instanceof DeptSearchReq) {
                response = searchDepartmentCases((DeptSearchReq) request);
            } else {
                throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
            }

            // 4. 计算耗时
            long endTime = System.currentTimeMillis();
            long took = endTime - startTime;
            response.setTook(took);

            // 5. 性能监控
            logPerformanceMetrics(request, response, took);

            return response;
        } catch (Exception e) {
            long took = System.currentTimeMillis() - startTime;
            log.error("执行高级检索异常，耗时: {}ms", took, e);
            return AdvancedSearchResponse.failure("检索异常：" + e.getMessage());
        }
    }

    @Override
    public <T extends AdvancedSearchRequest, R> List<R> searchAndConvertList(T request, Class<R> responseClass) {

        // 执行搜索
        AdvancedSearchResponse<?> searchResponse = search(request);
        
        // 如果搜索失败，返回空结果
        if (!searchResponse.getSuccess()) {
            return new ArrayList<>();
        }

        // 非分页查询，返回List
        return convertSearchResultsToDetailList(searchResponse, responseClass, request);
    }

    @Override
    public <T extends AdvancedSearchRequest, R> PageInfo<R> searchAndConvertPage(T request, Class<R> responseClass) {

        if (!request.isPaginated()) {
            throw new BusinessException(LocaleUtil.getLocale("page.param.not.mull"));
        }

        // 执行搜索
        AdvancedSearchResponse<?> searchResponse = search(request);

        // 转换搜索结果
        List<R> resultList = convertSearchResultsToDetailList(searchResponse, responseClass, request);

        // 如果搜索失败，返回空结果
        if (!searchResponse.getSuccess()) {
            return new PageInfo<>();
        }

        // 分页查询，返回PageInfo
        PageInfo<R> pageInfo = new PageInfo<>(resultList);
        // 设置分页信息
        pageInfo.setPageNum(request.getPageNum());
        pageInfo.setPageSize(request.getPageSize());
        pageInfo.setTotal(searchResponse.getTotal());
        pageInfo.setPages((int) Math.ceil((double) searchResponse.getTotal() / request.getPageSize()));
        pageInfo.setHasNextPage(request.getPageNum() < pageInfo.getPages());
        pageInfo.setHasPreviousPage(request.getPageNum() > 1);
        return pageInfo;
    }

    @Override
    public <T extends AdvancedSearchRequest, R> Object searchAndConvert(T request, Class<R> responseClass) {
        // 根据是否分页决定调用哪个方法
        if (request.isPaginated()) {
            return searchAndConvertPage(request, responseClass);
        } else {
            return searchAndConvertList(request, responseClass);
        }
    }

    /**
     * 搜索科室病例库
     */
    private <T extends AdvancedSearchRequest> AdvancedSearchResponse<DepartmentCaseDocument> searchDepartmentCases(T request) {
        return executeSearchWithRetry(() -> {
            // 构建优化的查询
            NativeSearchQuery searchQuery = buildOptimizedDepartmentCaseQuery(request);
            
            // 执行搜索
            IndexCoordinates indexCoordinates = IndexCoordinates.of(searchConfig.getIndexes().getDepartmentCases());
            SearchHits<DepartmentCaseDocument> searchHits = elasticsearchTemplate.search(
                searchQuery, DepartmentCaseDocument.class, indexCoordinates);

            // 转换结果
            List<AdvancedSearchResponse.SearchResult<DepartmentCaseDocument>> results = convertSearchHits(searchHits);

            // 构建响应
            AdvancedSearchResponse<DepartmentCaseDocument> response = AdvancedSearchResponse.success(
                    results, searchHits.getTotalHits(), request.getPageNum(), request.getPageSize(), 0L);

            // 设置最大评分
            response.calculateMaxScore();

            return response;
        }, "科室病例库");
    }

    /**
     * 搜索个人病例库
     */
    private <T extends AdvancedSearchRequest> AdvancedSearchResponse<PersonalCaseDocument> searchPersonalCases(T request) {
        return executeSearchWithRetry(() -> {
            // 构建优化的查询
            NativeSearchQuery searchQuery = buildOptimizedPersonalCaseQuery(request);
            
            // 执行搜索
            IndexCoordinates indexCoordinates = IndexCoordinates.of(searchConfig.getIndexes().getPersonalCases());
            SearchHits<PersonalCaseDocument> searchHits = elasticsearchTemplate.search(
                searchQuery, PersonalCaseDocument.class, indexCoordinates);

            // 转换结果
            List<AdvancedSearchResponse.SearchResult<PersonalCaseDocument>> results = convertSearchHits(searchHits);

            // 构建响应
            AdvancedSearchResponse<PersonalCaseDocument> response = AdvancedSearchResponse.success(
                    results, searchHits.getTotalHits(), request.getPageNum(), request.getPageSize(), 0L);

            // 设置最大评分
            response.calculateMaxScore();

            return response;
        }, "个人病例库");
    }

    /**
     * 构建优化的科室病例库查询
     */
    private <T extends AdvancedSearchRequest> NativeSearchQuery buildOptimizedDepartmentCaseQuery(T request) {

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 构建布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (StringUtils.hasText(request.getKeyword())) {
            addKeywordSearch(boolQuery, request.getKeyword());
        }

        // 基础字段查询
        addBasicFieldQueries(boolQuery, request);
        
        // 科室病例库Study相关查询
        addStudyQueries(boolQuery, request);
        
        // 新增随访相关查询
        addFollowInfoQueries(boolQuery, request);
        
        // 新增疾病概述相关查询
        addDiseaseOverviewQueries(boolQuery, request);

        // 患者信息查询
        addPatientInfoQueries(boolQuery, request);

        // 标签查询
        addTagQueries(boolQuery, request);

        // 特殊的时间区间查询（参照XML逻辑）
        addSpecialDateRangeQuery(boolQuery, request);

        // 科室特有字段查询
        if (request instanceof DeptSearchReq) {
            addDepartmentSpecificQueries(boolQuery, (DeptSearchReq) request);
        }

        return getNativeSearchQuery(request, queryBuilder, boolQuery);
    }

    /**
     * 添加特殊的时间区间查询逻辑（参照XML中的逻辑）
     * XML原逻辑：如果有时间区间条件，则要求该病例至少有一个报告在时间区间内
     */
    private <T extends AdvancedSearchRequest> void addSpecialDateRangeQuery(BoolQueryBuilder boolQuery, T request) {
        LocalDateTime beginTime = request.getStudyStartTime();
        LocalDateTime endTime = request.getStudyEndTime();
        
        if (beginTime != null || endTime != null) {
            String studyField = "studyInfoList";
            
            // 使用nested查询确保病例至少有一个study在时间范围内
            BoolQueryBuilder nestedBoolQuery = QueryBuilders.boolQuery();
            
            if (beginTime != null) {
                nestedBoolQuery.must(QueryBuilders.rangeQuery(studyField + ".studyTime")
                        .gte(beginTime.format(DATE_FORMATTER)));
            }
            
            if (endTime != null) {
                nestedBoolQuery.must(QueryBuilders.rangeQuery(studyField + ".studyTime")
                        .lte(endTime.format(DATE_FORMATTER)));
            }
            
            // 使用nested查询，确保至少有一个study满足时间条件
            boolQuery.must(QueryBuilders.nestedQuery(studyField, nestedBoolQuery, 
                    ScoreMode.None));
        }
    }

    /**
     * 获取优化后的NativeSearchQuery
     */
    private <T extends AdvancedSearchRequest> NativeSearchQuery getNativeSearchQuery(T request, NativeSearchQueryBuilder queryBuilder, BoolQueryBuilder boolQuery) {
        queryBuilder.withQuery(boolQuery);

        // 分页处理
        if (request.isPaginated()) {
            // 分页查询
            if (request.needsDeepPaginationOptimization() || request.getUseSearchAfter()) {
                // 深度分页优化：不设置from，只设置size
                queryBuilder.withPageable(PageRequest.of(0, request.getPageSize()));
            } else {
                // 常规分页
                Pageable pageable = PageRequest.of(request.getPageNum() - 1, request.getPageSize());
                queryBuilder.withPageable(pageable);
            }
        } else {
            // 非分页查询，设置一个较大的size来获取所有结果
            queryBuilder.withPageable(PageRequest.of(0, 10000));
        }

        // 特殊排序逻辑（参照XML中的排序规则）
        addSpecialSorting(queryBuilder, request);

        // 高亮
        if (request.getHighlight() != null && request.getHighlight()) {
            addOptimizedHighlight(queryBuilder);
        }

        // 聚合
        addAggregations(queryBuilder, request.getAggregationFields());

        // 最小评分
        if (request.getMinScore() != null) {
            queryBuilder.withMinScore(request.getMinScore());
        }

        return queryBuilder.build();
    }

    /**
     * 构建优化的个人病例库查询
     */
    private <T extends AdvancedSearchRequest> NativeSearchQuery buildOptimizedPersonalCaseQuery(T request) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 构建布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 添加个人病例库的关键词搜索逻辑（对应XML中的keyword条件）
        if (StringUtils.hasText(request.getKeyword())) {
            addKeywordSearch(boolQuery, request.getKeyword());
        }
        
        // 基础字段查询
        addBasicFieldQueries(boolQuery, request);
        
        // 个人病例库Study相关查询
        addStudyQueries(boolQuery, request);

        // 新增随访相关查询
        addFollowInfoQueries(boolQuery, request);

        // 患者信息查询
        addPatientInfoQueries(boolQuery, request);

        // 标签查询
        addTagQueries(boolQuery, request);

        // 个人特有字段查询
        if (request instanceof PersonalSearchReq) {
            addPersonalSpecificQueries(boolQuery, (PersonalSearchReq) request);
        }

        // 特殊的时间区间查询（参照XML逻辑）
        addSpecialDateRangeQuery(boolQuery, request);

        return getNativeSearchQuery(request, queryBuilder, boolQuery);
    }

    /**
     * 添加基础字段查询
     */
    private void addBasicFieldQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        // 患者姓名
        addTermQuery(boolQuery, "patientName", request.getPatientName());

        addMatchQuery(boolQuery, "caseName", request.getCaseName());
        addTermQuery(boolQuery, "caseNo", request.getCaseNo());
        addMatchQuery(boolQuery, "caseAnalysis", request.getCaseAnalysis());
        addTermQuery(boolQuery, "difficulty", request.getDifficulty());
        addMatchQuery(boolQuery, "medicalHistory", request.getMedicalHistory());
        addMatchQuery(boolQuery, "sign", request.getPhysicalSign());
        addMatchQuery(boolQuery, "remark", request.getRemark());
        addMatchQuery(boolQuery, "selfComplaints", request.getSelfComplaints());
        
        // 诊断信息
        addMatchQuery(boolQuery, "diagnosis", request.getDiagnosis());

        // 疾病ID
        if (request.getDiseaseId() != null) {
            boolQuery.filter(QueryBuilders.termQuery("diseaseId", request.getDiseaseId()));
        }

        // 疾病名称
        if (StringUtils.hasText(request.getDiseaseName())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseName", request.getDiseaseName()));
        }
    }
    
    /**
     * 添加科室病例库的Study相关查询
     */
    private void addStudyQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        // 影像号
        addTermQuery(boolQuery, "studyInfoList.accessNumber", request.getAccessNumber());
        
        // 住院号和门诊号
        addTermQuery(boolQuery, "studyInfoList.inPatientNo", request.getInPatientNo());
        addTermQuery(boolQuery, "studyInfoList.outPatientNo", request.getOutPatientNo());
        
        // 检查项目和部位
        addMatchQuery(boolQuery, "studyInfoList.studyItemName", request.getStudyItemName());
        addMatchQuery(boolQuery, "studyInfoList.partName", request.getPartName());
        
        // 设备信息
        addTermQuery(boolQuery, "studyInfoList.deviceType", request.getDeviceType());
        addMatchQuery(boolQuery, "studyInfoList.deviceName", request.getDeviceName());
        
        // 诊断信息
        addMatchQuery(boolQuery, "studyInfoList.clinicalDiagnosis", request.getClinicalDiagnosis());
        addMatchQuery(boolQuery, "studyInfoList.reportDiagnose", request.getReportDiagnose());
        addMatchQuery(boolQuery, "studyInfoList.reportDescribe", request.getReportDescribe());
        
        // 病史和体征
        addMatchQuery(boolQuery, "studyInfoList.medicalHistory", request.getMedicalHistory());
        addMatchQuery(boolQuery, "studyInfoList.physicalSign", request.getPhysicalSign());
        addMatchQuery(boolQuery, "studyInfoList.selfReportedSymptom", request.getSelfReportedSymptom());

        addTermQuery(boolQuery, "studyInfoList.isPositive", request.getIsPositive());
        addTermQuery(boolQuery, "studyInfoList.studyState", request.getStudyState());
        addMatchQuery(boolQuery, "studyInfoList.reporter", request.getReporter());
        addMatchQuery(boolQuery, "studyInfoList.checker", request.getChecker());
        addMatchQuery(boolQuery, "studyInfoList.applyDepartment", request.getApplyDepartment());
        addMatchQuery(boolQuery, "studyInfoList.applyDoctor", request.getApplyDoctor());
        // 针对studyInfoList.modalities的查询，去匹配deviceType或者modalities，任何一个匹配上都可以
        addModalityQuery(boolQuery, request.getModality());
    }

    /**
     * 添加随访信息查询
     */
    private void addFollowInfoQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        if (StringUtils.hasText(request.getFollowType())) {
            boolQuery.must(QueryBuilders.termQuery("followInfoList.followType", request.getFollowType()));
        }
        
        if (StringUtils.hasText(request.getFollowupResult())) {
            boolQuery.must(QueryBuilders.matchQuery("followInfoList.followupResult", request.getFollowupResult()));
        }
    }

    /**
     * 添加疾病概述信息查询
     */
    private void addDiseaseOverviewQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        if (StringUtils.hasText(request.getDiseaseOverview())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.overview", request.getDiseaseOverview()));
        }
        
        if (StringUtils.hasText(request.getPathology())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.pathology", request.getPathology()));
        }
        
        if (StringUtils.hasText(request.getClinical())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.clinical", request.getClinical()));
        }
        
        if (StringUtils.hasText(request.getImaging())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.imaging", request.getImaging()));
        }
        
        if (StringUtils.hasText(request.getDiagnosisBasis())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.diagnosis", request.getDiagnosisBasis()));
        }
        
        if (StringUtils.hasText(request.getDifferential())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.differential", request.getDifferential()));
        }
        
        if (StringUtils.hasText(request.getKeyframe())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.keyframe", request.getKeyframe()));
        }
    }

    /**
     * 添加患者信息查询
     */
    private void addPatientInfoQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermQuery(boolQuery, "patientSex", request.getPatientSex());
        addAgeRangeQuery(boolQuery, "patientAge", request.getPatientAgeMin(), request.getPatientAgeMax());
    }

    /**
     * 添加标签查询
     */
    private void addTagQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermsQuery(boolQuery, "tags", request.getTagNames());
    }

    /**
     * 添加优化的高亮
     */
    private void addOptimizedHighlight(NativeSearchQueryBuilder queryBuilder) {
        SearchConfig.Highlight highlightConfig = searchConfig.getHighlight();
        
        HighlightBuilder highlightBuilder = new HighlightBuilder()
                .field("patientName")
                .field("diagnosis")
                .field("studyInfoList.clinicalDiagnosis")
                .field("studyInfoList.reportDiagnose")
                .field("studyInfoList.reportDescribe")
                .field("studyInfoList.medicalHistory")
                .field("caseName")
                .field("caseAnalysis")
                // 新增随访和疾病概述字段高亮
                .field("followInfoList.followupResult")
                .field("diseaseOverview.pathology")
                .field("diseaseOverview.clinical")
                .field("diseaseOverview.imaging")
                .preTags(highlightConfig.getPreTags())
                .postTags(highlightConfig.getPostTags())
                .fragmentSize(highlightConfig.getFragmentSize())
                .numOfFragments(highlightConfig.getNumberOfFragments());
        
        queryBuilder.withHighlightBuilder(highlightBuilder);
    }

    // 工具方法保持不变，但添加新的查询方法
    private void addTermQuery(BoolQueryBuilder boolQuery, String field, Object value) {
        if (value != null && StringUtils.hasText(value.toString())) {
            boolQuery.filter(QueryBuilders.termQuery(field, value));
        }
    }

    /**
     * 添加单值 terms 查询的便捷方法
     */
    private void addSingleTermQuery(BoolQueryBuilder boolQuery, String field, Object value) {
        if (value != null && StringUtils.hasText(value.toString())) {
            boolQuery.filter(QueryBuilders.termQuery(field, value));
        }
    }

    private void addMatchQuery(BoolQueryBuilder boolQuery, String field, String value) {
        if (StringUtils.hasText(value)) {
            boolQuery.must(QueryBuilders.matchQuery(field, value));
        }
    }

    private void addTermsQuery(BoolQueryBuilder boolQuery, String field, List<?> values) {
        // 修复：过滤掉 null 值，并确保最终的 List 不为空
        if (!CollectionUtils.isEmpty(values)) {
            List<?> filteredValues = values.stream()
                    .filter(value -> value != null && StringUtils.hasText(value.toString()))
                    .collect(Collectors.toList());
            
            if (!filteredValues.isEmpty()) {
                boolQuery.filter(QueryBuilders.termsQuery(field, filteredValues));
            }
        }
    }

    private void addAgeRangeQuery(BoolQueryBuilder boolQuery, String field, Integer minAge, Integer maxAge) {
        if (minAge != null || maxAge != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field);
            if (minAge != null) {
                rangeQuery.gte(minAge);
            }
            if (maxAge != null) {
                rangeQuery.lte(maxAge);
            }
            boolQuery.filter(rangeQuery);
        }
    }

    /**
     * 添加检查类型(modality)查询
     * 针对studyInfoList.modalities的查询，去匹配deviceType或者modalities，任何一个匹配上都可以
     * 
     * @param boolQuery 布尔查询构建器
     * @param modality 检查类型值
     */
    private void addModalityQuery(BoolQueryBuilder boolQuery, String modality) {
        if (StringUtils.hasText(modality)) {
            BoolQueryBuilder nestedBoolQuery = QueryBuilders.boolQuery();

            // 统一使用terms查询，可以处理单值和数组两种情况
            nestedBoolQuery.should(QueryBuilders.termsQuery("studyInfoList.deviceType", modality));
            nestedBoolQuery.should(QueryBuilders.termsQuery("studyInfoList.modalities", modality));

            nestedBoolQuery.minimumShouldMatch(1);

            boolQuery.must(QueryBuilders.nestedQuery("studyInfoList", nestedBoolQuery, ScoreMode.None));

            log.debug("添加检查类型查询: modality={}", modality);
        }
    }

    private void addAggregations(NativeSearchQueryBuilder queryBuilder, List<String> aggregationFields) {
        if (!CollectionUtils.isEmpty(aggregationFields)) {
            for (String field : aggregationFields) {
                queryBuilder.addAggregation(
                        AggregationBuilders.terms(field + "_agg").field(field).size(20));
            }
        }
    }

    /**
     * 转换搜索结果
     */
    private <T> List<AdvancedSearchResponse.SearchResult<T>> convertSearchHits(SearchHits<T> searchHits) {
        return searchHits.getSearchHits().stream()
                .filter(searchHit -> searchHit.getScore() >= 0.0f)
                .map(this::convertSearchHit)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个搜索结果
     */
    private <T> AdvancedSearchResponse.SearchResult<T> convertSearchHit(SearchHit<T> searchHit) {
        AdvancedSearchResponse.SearchResult<T> result = new AdvancedSearchResponse.SearchResult<>();
        result.setId(searchHit.getId());
        result.setScore(searchHit.getScore());
        result.setSource(searchHit.getContent());
        result.setSort(searchHit.getSortValues().toArray());
        
        // 转换高亮结果
        if (!searchHit.getHighlightFields().isEmpty()) {
            Map<String, List<String>> highlight = new HashMap<>(searchHit.getHighlightFields());
            result.setHighlight(highlight);
        }
        
        return result;
    }

    /**
     * 智能查询优化
     */
    private <T extends AdvancedSearchRequest> void optimizeSearchRequest(T request) {
        // 1. 复杂查询优化
        if (request.isComplexQuery()) {
            // 调整超时时间
            if (request.getTimeoutMs() == null) {
                request.setTimeoutMs(searchConfig.getPerformance().getQueryTimeout());
            }
        }

        // 2. 深度分页优化
        if (request.needsDeepPaginationOptimization()) {
            request.setUseSearchAfter(true);
        }

        // 3. 智能优化开关
        if (request.getEnableSmartOptimization() != null && request.getEnableSmartOptimization()) {
            // 根据查询复杂度调整参数
            if (request.isComplexQuery()) {
                // 复杂查询减少返回字段
                request.setPageSize(Math.min(request.getPageSize(), 20));
            }
        }
    }

    /**
     * 记录性能指标
     */
    private <T extends AdvancedSearchRequest> void logPerformanceMetrics(T request, AdvancedSearchResponse<?> response, long took) {
        // 慢查询日志
        if (took > searchConfig.getPerformance().getSlowQueryThreshold()) {
            log.warn("慢查询检测 - 耗时: {}ms, 关键字: {}, 结果数: {}",
                took, request.getKeyword(), response.getTotal());
        }

        // 性能统计
        log.debug("查询性能统计 - 耗时: {}ms, 命中数: {}",
            took, response.getTotal());
    }

    /**
     * 带重试机制的搜索执行方法
     */
    private <T> T executeSearchWithRetry(Supplier<T> searchFunction, String searchType) {
        int maxRetries = 3;
        // 基础延迟1秒
        long baseDelay = 1000;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                long startTime = System.currentTimeMillis();
                T result = searchFunction.get();
                long took = System.currentTimeMillis() - startTime;
                
                if (attempt > 1) {
                    log.info("{}搜索重试成功，第{}次尝试，耗时: {}ms", searchType, attempt, took);
                }
                
                return result;
            } catch (DataAccessResourceFailureException e) {
                boolean isTimeout = e.getMessage() != null && 
                    (e.getMessage().contains("timeout") || e.getMessage().contains("SocketTimeoutException"));
                
                if (isTimeout && attempt < maxRetries) {
                    // 递增延迟
                    long delay = baseDelay * attempt;
                    log.warn("{}搜索超时，第{}次重试，{}ms后重试。错误: {}", 
                        searchType, attempt, delay, e.getMessage());
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试被中断", ie);
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    log.error("{}搜索失败，已重试{}次。错误: {}", searchType, attempt - 1, e.getMessage());
                    if (isTimeout) {
                        return createTimeoutErrorResponse(searchType);
                    }
                    throw e;
                }
            } catch (Exception e) {
                log.error("{}搜索异常，第{}次尝试", searchType, attempt, e);
                if (attempt == maxRetries) {
                    throw e;
                }
                
                // 对于其他异常，也进行重试
                try {
                    Thread.sleep(baseDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
        
        throw new RuntimeException(searchType + "搜索失败，已达到最大重试次数");
    }
    
    /**
     * 创建超时错误响应
     */
    @SuppressWarnings("unchecked")
    private <T> T createTimeoutErrorResponse(String searchType) {
        return (T) AdvancedSearchResponse.failure(
            searchType + "搜索超时，请稍后重试或简化查询条件");
    }

    /**
     * 添加科室特有字段查询
     */
    private void addDepartmentSpecificQueries(BoolQueryBuilder boolQuery, DeptSearchReq deptRequest) {
        // 分类ID - 使用nested查询匹配caseTypeInfoList中的caseTypeId
        if (deptRequest.getCaseTypeId() != null) {
            boolQuery.filter(QueryBuilders.nestedQuery("caseTypeInfoList", 
                QueryBuilders.termQuery("caseTypeInfoList.caseTypeId", deptRequest.getCaseTypeId()), 
                ScoreMode.None));
        }

        // 审核过滤（病例的类型是不需要审核，或者是需要审核但是审核状态是通过）
        if (deptRequest.getCaseTypeId() != null) {
            addCaseTypeAuditFilter(boolQuery, deptRequest.getCaseTypeId());
        }

    }

    /**
     * 添加案例类型审核过滤
     * 如果类型不需要审核（audit为"0"），或者需要审核但审核状态是通过的，则包含该病例
     * 新版本：支持多个审核记录，需要同时匹配病例ID和类型ID来确定审核状态
     * 
     * @param boolQuery 布尔查询构建器
     * @param caseTypeId 案例类型ID
     */
    private void addCaseTypeAuditFilter(BoolQueryBuilder boolQuery, Long caseTypeId) {
        if (caseTypeId == null) {
            return;
        }

        // 首先确保病例包含指定的类型ID
        boolQuery.filter(QueryBuilders.nestedQuery("caseTypeInfoList",
                QueryBuilders.termQuery("caseTypeInfoList.caseTypeId", caseTypeId),
                ScoreMode.None));
        
        // 构建审核过滤条件
        BoolQueryBuilder auditFilterQuery = QueryBuilders.boolQuery();
        
        // 条件1：该类型不需要审核（audit为"0"）
        BoolQueryBuilder noAuditNeeded = QueryBuilders.boolQuery();
        noAuditNeeded.must(QueryBuilders.nestedQuery("caseTypeInfoList",
            QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("caseTypeInfoList.caseTypeId", caseTypeId))
                .must(QueryBuilders.termQuery("caseTypeInfoList.audit", "0")),
            ScoreMode.None));
        
        // 条件2：该类型需要审核且存在对应的审核记录且审核状态是通过的
        // 使用nested查询匹配auditInfoList中病例ID和类型ID都匹配且状态为通过的记录
        BoolQueryBuilder auditPassed = QueryBuilders.boolQuery();
        auditPassed.must(QueryBuilders.nestedQuery("caseTypeInfoList",
            QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("caseTypeInfoList.caseTypeId", caseTypeId))
                .must(QueryBuilders.termQuery("caseTypeInfoList.audit", "1")),
            ScoreMode.None))
            .must(QueryBuilders.nestedQuery("auditInfoList",
                QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("auditInfoList.caseTypeId", caseTypeId))
                    .must(QueryBuilders.termQuery("auditInfoList.status", Constant.CASE_AUDIT_STATUS_1)),
                ScoreMode.None));
        
        // 使用should查询，满足任一条件即可
        auditFilterQuery.should(noAuditNeeded);
        auditFilterQuery.should(auditPassed);
        auditFilterQuery.minimumShouldMatch(1);
        
        // 将审核过滤条件添加到主查询中
        boolQuery.must(auditFilterQuery);
        
        log.debug("添加案例类型审核过滤(支持多审核记录): caseTypeId={}", caseTypeId);
    }

    /**
     * 添加个人特有字段查询（参照PersonalCaseMapper.xml中的逻辑）
     */
    private void addPersonalSpecificQueries(BoolQueryBuilder boolQuery, PersonalSearchReq personalRequest) {

        addSingleTermQuery(boolQuery, "catalogIds", personalRequest.getCatalogId());

        // 用户ID过滤（个人病例库必须）
        boolQuery.filter(QueryBuilders.termQuery("createBy", personalRequest.getUserId()));
    }

    /**
     * 添加科室病例库关键词搜索逻辑（参照xml中的keyword逻辑）
     */
    private void addKeywordSearch(BoolQueryBuilder boolQuery, String keyword) {
        boolQuery.must(QueryBuilders.multiMatchQuery(keyword)
                // 对应 uc.patient_name
                .field("patientName", 2.0f)
                // 对应 s.access_number  
                .field("studyInfoList.accessNumber", 2.0f)
                // 对应 s.in_patient_no
                .field("studyInfoList.inPatientNo", 2.0f)
                // 对应 s.out_patient_no
                .field("studyInfoList.outPatientNo", 2.0f)
                // 对应 s.report_describe
                .field("studyInfoList.reportDescribe", 1.8f)
                // 对应 s.report_diagnose
                .field("studyInfoList.reportDiagnose", 1.8f)
                // 对应 f.followup_result
                .field("followInfoList.followupResult", 1.5f)
                // 对应 uc.case_analysis
                .field("caseAnalysis", 1.5f)
                // 对应 uc.remark
                .field("remark", 1.2f)
                .minimumShouldMatch("1"));
    }

    /**
     * 将搜索结果中的source转换为指定的响应对象列表
     * 
     * @param <T> 目标响应对象类型，如DeptCaseDetailResp或PersonalCaseDetailResp
     * @param response 搜索响应对象
     * @param targetClass 目标响应对象的Class类型
     * @return 转换后的响应对象列表
     */
    public <T> List<T> convertSearchResultsToDetailList(AdvancedSearchResponse<?> response, Class<T> targetClass,
                                                        AdvancedSearchRequest request) {
        if (response == null || CollectionUtils.isEmpty(response.getResults())) {
            return new ArrayList<>();
        }

        // 获取查询中的caseTypeId用于过滤
        Long filterCaseTypeId = null;
        if (request instanceof DeptSearchReq) {
            filterCaseTypeId = ((DeptSearchReq) request).getCaseTypeId();
        }

        final Long finalFilterCaseTypeId = filterCaseTypeId;

        return response.getResults().stream()
                .filter(result -> result.getSource() != null)
                .map(result -> convertToDetailResp(result.getSource(), targetClass, finalFilterCaseTypeId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 将单个Document对象转换为指定的响应对象
     * 高性能版本：针对具体类型使用专门的转换方法，避免反射开销
     * 
     * @param <T> 目标响应对象类型
     * @param source Document源对象（如DepartmentCaseDocument或PersonalCaseDocument）
     * @param targetClass 目标响应对象的Class类型
     * @return 转换后的响应对象
     */
    @SuppressWarnings("unchecked")
    private <T> T convertToDetailResp(Object source, Class<T> targetClass, Long filterCaseTypeId) {
        if (source == null || targetClass == null) {
            return null;
        }

        try {
            // 针对具体类型使用转换方法
            String targetClassName = targetClass.getSimpleName();
            String sourceClassName = source.getClass().getSimpleName();
            
            if (Constant.DEPARTMENT_CASE_RESP_NAME.equals(targetClassName)
                    && Constant.DEPARTMENT_CASE_DOC_NAME.equals(sourceClassName)) {
                return (T) convertToDeptCaseDetailResp((DepartmentCaseDocument) source, filterCaseTypeId);
            } else if (Constant.PERSONAL_CASE_RESP_NAME.equals(targetClassName)
                    && Constant.PERSONAL_CASE_DOC_NAME.equals(sourceClassName)) {
                return (T) convertToPersonalCaseDetailResp((PersonalCaseDocument) source);
            } else {
                // 回退到通用方法（使用BeanUtils，但不做嵌套对象转换）
                log.warn("未找到专门的转换方法，使用通用转换: {} -> {}", sourceClassName, targetClassName);
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                return target;
            }
        } catch (Exception e) {
            log.error("转换搜索结果失败，源类型: {}, 目标类型: {}", 
                    source.getClass().getSimpleName(), targetClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 高效转换：DepartmentCaseDocument -> DeptCaseDetailResp
     * 使用BeanUtils作为基础，手动处理需要类型转换的字段
     */
    private DeptCaseDetailResp convertToDeptCaseDetailResp(DepartmentCaseDocument source, Long filterCaseTypeId) {
        if (source == null) {
            return null;
        }
        
        try {
            DeptCaseDetailResp target = new DeptCaseDetailResp();
            
            // 使用BeanUtils进行基础属性拷贝
            BeanUtils.copyProperties(source, target);
            
            // 使用公共方法转换通用嵌套字段
            convertCommonNestedFields(source, target);
            
            // 转换科室病例特有字段（如病例类型列表等）
            convertDepartmentSpecificFields(source, target, filterCaseTypeId);
            
            // 调用setDictLabels设置字典标签
            try {
                target.setDictLabels();
            } catch (Exception e) {
                log.error("设置字典标签失败", e);
                // 继续返回对象，只是没有字典标签
            }
            
            return target;
        } catch (Exception e) {
            log.error("转换DeptCaseDetailResp失败", e);
            return null;
        }
    }
    
    /**
     * 高效转换：PersonalCaseDocument -> PersonalCaseDetailResp
     * 使用BeanUtils作为基础，手动处理需要类型转换的字段
     */
    private PersonalCaseDetailResp convertToPersonalCaseDetailResp(PersonalCaseDocument source) {
        if (source == null) {
            return null;
        }
        
        try {
            PersonalCaseDetailResp target = new PersonalCaseDetailResp();
            
            // 使用BeanUtils进行基础属性拷贝
            BeanUtils.copyProperties(source, target);
            
            // 使用公共方法转换通用嵌套字段
            convertCommonNestedFields(source, target);
            
            // 调用setDictLabels设置字典标签
            try {
                target.setDictLabels();
            } catch (Exception e) {
                log.error("设置字典标签失败", e);
                // 继续返回对象，只是没有字典标签
            }
            
            return target;
        } catch (Exception e) {
            log.error("转换PersonalCaseDetailResp失败", e);
            return null;
        }
    }
    
    /**
     * 高效转换StudyInfo对象
     * 从search.document.StudyInfo转换为sickcase.dto.StudyInfo
     */
    private com.jusha.caselibrary.sickcase.dto.StudyInfo convertStudyInfo(
            com.jusha.caselibrary.search.document.StudyInfo source) {
        
        com.jusha.caselibrary.sickcase.dto.StudyInfo target = 
            new com.jusha.caselibrary.sickcase.dto.StudyInfo();
            
        // 使用BeanUtils进行基础属性拷贝（相同字段名的会自动拷贝）
        BeanUtils.copyProperties(source, target);
        // 优先使用deviceType
        String deviceType = target.getDeviceType();
        if (StringUtils.hasText(deviceType)) {
            Set<String> modalities = new HashSet<>();
            modalities.add(deviceType);
            target.setModalities(modalities);
        }
        return target;
    }

    /**
     * 添加特殊排序逻辑（参照XML中的排序规则）
     * 科室病例库XML原逻辑：报告排序：区间内的按时间正序在前，区间外的按时间正序在后
     * 个人病例库XML原逻辑：同样的排序规则，ORDER BY uc.case_id, CASE WHEN ... THEN 0 ELSE 1 END, s.study_time ASC
     */
    private <T extends AdvancedSearchRequest> void addSpecialSorting(NativeSearchQueryBuilder queryBuilder, T request) {
        // 首先按病例ID排序，确保同一病例的数据聚合在一起
        // 科室病例库使用caseId，个人病例库使用userCaseId或caseId
        if (request instanceof PersonalSearchReq) {
            // 个人病例库优先按用户病例ID排序
            queryBuilder.withSort(SortBuilders.fieldSort("userCaseId").order(SortOrder.ASC));
            // 然后按检查时间升序排序（ES会自然地将在范围内的文档排在前面）
            queryBuilder.withSort(SortBuilders.fieldSort("studyInfoList.studyTime").order(SortOrder.ASC));
        } else {
            // 科室病例库按病例ID排序
            queryBuilder.withSort(SortBuilders.fieldSort("caseId").order(SortOrder.ASC));
            // 然后按检查时间升序排序（ES会自然地将在范围内的文档排在前面）
            queryBuilder.withSort(SortBuilders.fieldSort("studyInfoList.studyTime").order(SortOrder.ASC));
        }
        
        // 用户自定义排序
        if (StringUtils.hasText(request.getSortField())) {
            SortOrder order = "asc".equalsIgnoreCase(request.getSortDirection()) ? SortOrder.ASC : SortOrder.DESC;
            queryBuilder.withSort(SortBuilders.fieldSort(request.getSortField()).order(order));
        }
        
        // 对于search_after分页，必须有唯一的排序字段
        if (request.getUseSearchAfter() || request.needsDeepPaginationOptimization()) {
            queryBuilder.withSort(SortBuilders.fieldSort("_id").order(SortOrder.ASC));
        }
        
        // 最后按相关性排序
        queryBuilder.withSort(SortBuilders.scoreSort().order(SortOrder.DESC));
    }
    
    /**
     * 转换通用嵌套字段：处理科室和个人病例共有的字段
     * 通过JSON序列化/反序列化方式处理类型转换
     * 
     * @param source 源文档对象（DepartmentCaseDocument 或 PersonalCaseDocument）
     * @param target 目标响应对象（DeptCaseDetailResp 或 PersonalCaseDetailResp）
     */
    private <S, T> void convertCommonNestedFields(S source, T target) {
        try {
            // 转换 studyInfoList：search.document.StudyInfo -> sickcase.dto.StudyInfo
            convertStudyInfoList(source, target);
            
            // 转换 tagInfoList：search.document.TagInfo -> sickcase.dto.TagInfo
            convertTagInfoList(source, target);
            
            // 转换 followInfoList：search.document.FollowInfo -> sickcase.dto.FollowInfo
            convertFollowInfoList(source, target);
            
        } catch (Exception e) {
            log.error("转换通用嵌套字段失败", e);
        }
    }
    
    /**
     * 转换科室病例特有字段
     */
    private void convertDepartmentSpecificFields(DepartmentCaseDocument source, DeptCaseDetailResp target,
                                                 Long filterCaseTypeId) {
        try {
            // 转换并过滤病例类型信息列表
            if (source.getCaseTypeInfoList() != null && !source.getCaseTypeInfoList().isEmpty()) {
                List<com.jusha.caselibrary.sickcase.dto.CaseTypeInfo> targetCaseTypeList =
                        JSON.parseArray(JSON.toJSONString(source.getCaseTypeInfoList()),
                                com.jusha.caselibrary.sickcase.dto.CaseTypeInfo.class);
                // 如果指定了caseTypeId，则只保留匹配的类型
                if (filterCaseTypeId != null) {
                    targetCaseTypeList = targetCaseTypeList.stream()
                            .filter(caseType -> filterCaseTypeId.equals(caseType.getCaseTypeId()))
                            .collect(Collectors.toList());
                }

                target.setCaseTypeInfoList(targetCaseTypeList);
            }
        } catch (Exception e) {
            log.error("转换科室病例特有字段失败", e);
        }
    }
    
    /**
     * 转换StudyInfoList的通用方法
     */
    private <S, T> void convertStudyInfoList(S source, T target) {
        try {
            // 使用反射获取studyInfoList
            java.lang.reflect.Method getMethod = source.getClass().getMethod("getStudyInfoList");
            java.lang.reflect.Method setMethod = target.getClass().getMethod("setStudyInfoList", List.class);
            
            @SuppressWarnings("unchecked")
            List<Object> sourceStudyList = (List<Object>) getMethod.invoke(source);
            
            if (sourceStudyList != null && !sourceStudyList.isEmpty()) {
                List<com.jusha.caselibrary.sickcase.dto.StudyInfo> targetStudyList = 
                    JSON.parseArray(JSON.toJSONString(sourceStudyList), 
                        com.jusha.caselibrary.sickcase.dto.StudyInfo.class);
                setMethod.invoke(target, targetStudyList);
            }
        } catch (Exception e) {
            log.error("转换StudyInfoList失败", e);
        }
    }
    
    /**
     * 转换TagInfoList的通用方法
     */
    private <S, T> void convertTagInfoList(S source, T target) {
        try {
            java.lang.reflect.Method getMethod = source.getClass().getMethod("getTagInfoList");
            java.lang.reflect.Method setMethod = target.getClass().getMethod("setTagInfoList", List.class);
            
            @SuppressWarnings("unchecked")
            List<Object> sourceTagList = (List<Object>) getMethod.invoke(source);
            
            if (sourceTagList != null && !sourceTagList.isEmpty()) {
                List<com.jusha.caselibrary.sickcase.dto.TagInfo> targetTagList = 
                    JSON.parseArray(JSON.toJSONString(sourceTagList), 
                        com.jusha.caselibrary.sickcase.dto.TagInfo.class);
                setMethod.invoke(target, targetTagList);
            }
        } catch (Exception e) {
            log.error("转换TagInfoList失败", e);
        }
    }
    
    /**
     * 转换FollowInfoList的通用方法
     */
    private <S, T> void convertFollowInfoList(S source, T target) {
        try {
            java.lang.reflect.Method getMethod = source.getClass().getMethod("getFollowInfoList");
            java.lang.reflect.Method setMethod = target.getClass().getMethod("setFollowInfoList", List.class);
            
            @SuppressWarnings("unchecked")
            List<Object> sourceFollowList = (List<Object>) getMethod.invoke(source);
            
            if (sourceFollowList != null && !sourceFollowList.isEmpty()) {
                List<com.jusha.caselibrary.sickcase.dto.FollowInfo> targetFollowList = 
                    JSON.parseArray(JSON.toJSONString(sourceFollowList), 
                        com.jusha.caselibrary.sickcase.dto.FollowInfo.class);
                setMethod.invoke(target, targetFollowList);
            }
        } catch (Exception e) {
            log.error("转换FollowInfoList失败", e);
        }
    }
}
