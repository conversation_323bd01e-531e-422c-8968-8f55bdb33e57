package com.jusha.caselibrary.search.migration.dto;

import com.jusha.caselibrary.search.migration.enums.CompatibilityLevel;
import com.jusha.caselibrary.search.migration.enums.FieldChangeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 字段变更分析结果
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FieldChangeAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 源索引名称
     */
    private String sourceIndexName;

    /**
     * 目标文档类名
     */
    private String targetDocumentClass;

    /**
     * 字段变更列表
     */
    private List<FieldChange> fieldChanges;

    /**
     * 整体兼容性级别
     */
    private CompatibilityLevel overallCompatibility;

    /**
     * 迁移建议
     */
    private List<String> migrationSuggestions;

    /**
     * 是否需要数据转换
     */
    private boolean needsDataConversion;

    /**
     * 预计迁移时间（分钟）
     */
    private long estimatedMigrationTime;

    /**
     * 分析时间
     */
    private Date analysisTime;

    /**
     * 分析者
     */
    private String analyzedBy;

    /**
     * 字段变更详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldChange implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 变更类型
         */
        private FieldChangeType changeType;

        /**
         * 源字段类型
         */
        private String sourceType;

        /**
         * 目标字段类型
         */
        private String targetType;

        /**
         * 兼容性级别
         */
        private CompatibilityLevel compatibility;

        /**
         * 是否需要数据转换
         */
        private boolean needsDataConversion;

        /**
         * 转换建议
         */
        private String suggestion;

        /**
         * 风险评估
         */
        private String riskAssessment;

        /**
         * 影响范围
         */
        private String impactScope;

        /**
         * 获取变更描述
         */
        public String getChangeDescription() {
            switch (changeType) {
                case ADDED:
                    return String.format("新增字段 '%s'，类型: %s", fieldName, targetType);
                case REMOVED:
                    return String.format("删除字段 '%s'，原类型: %s", fieldName, sourceType);
                case TYPE_CHANGED:
                    return String.format("字段 '%s' 类型变更: %s -> %s", fieldName, sourceType, targetType);
                case PROPERTIES_CHANGED:
                    return String.format("字段 '%s' 属性变更", fieldName);
                case RENAMED:
                    return String.format("字段 '%s' 重命名", fieldName);
                case NESTED_STRUCTURE_CHANGED:
                    return String.format("字段 '%s' 嵌套结构变更", fieldName);
                default:
                    return String.format("字段 '%s' 发生未知变更", fieldName);
            }
        }

        /**
         * 是否为高风险变更
         */
        public boolean isHighRisk() {
            return changeType.getRiskLevel() >= 4 || 
                   compatibility == CompatibilityLevel.INCOMPATIBLE;
        }

        /**
         * 获取处理优先级
         */
        public int getPriority() {
            if (compatibility == CompatibilityLevel.INCOMPATIBLE) {
                return 1; // 最高优先级
            } else if (changeType.isBreakingChange()) {
                return 2; // 高优先级
            } else if (needsDataConversion) {
                return 3; // 中优先级
            } else {
                return 4; // 低优先级
            }
        }
    }

    /**
     * 获取高风险变更数量
     */
    public long getHighRiskChangeCount() {
        if (fieldChanges == null) {
            return 0;
        }
        return fieldChanges.stream()
                .filter(FieldChange::isHighRisk)
                .count();
    }

    /**
     * 获取需要数据转换的字段数量
     */
    public long getDataConversionFieldCount() {
        if (fieldChanges == null) {
            return 0;
        }
        return fieldChanges.stream()
                .filter(FieldChange::isNeedsDataConversion)
                .count();
    }

    /**
     * 是否可以安全迁移
     */
    public boolean canSafelyMigrate() {
        return overallCompatibility != CompatibilityLevel.INCOMPATIBLE &&
               overallCompatibility != CompatibilityLevel.UNKNOWN;
    }

    /**
     * 获取迁移复杂度评分（1-10）
     */
    public int getMigrationComplexityScore() {
        if (fieldChanges == null || fieldChanges.isEmpty()) {
            return 1;
        }

        int score = 1;
        
        // 基于兼容性级别
        switch (overallCompatibility) {
            case COMPATIBLE:
                score += 1;
                break;
            case REQUIRES_CONVERSION:
                score += 3;
                break;
            case WARNING:
                score += 5;
                break;
            case INCOMPATIBLE:
                score += 8;
                break;
            case UNKNOWN:
                score += 6;
                break;
        }

        // 基于变更数量
        int changeCount = fieldChanges.size();
        if (changeCount > 10) {
            score += 3;
        } else if (changeCount > 5) {
            score += 2;
        } else if (changeCount > 2) {
            score += 1;
        }

        // 基于高风险变更
        long highRiskCount = getHighRiskChangeCount();
        score += (int) Math.min(highRiskCount * 2, 4);

        return Math.min(score, 10);
    }

    /**
     * 获取迁移建议摘要
     */
    public String getMigrationSummary() {
        if (!canSafelyMigrate()) {
            return "不建议进行迁移，存在不兼容的字段变更";
        }

        int complexityScore = getMigrationComplexityScore();
        if (complexityScore <= 3) {
            return "可以安全进行迁移，复杂度较低";
        } else if (complexityScore <= 6) {
            return "可以进行迁移，但需要注意数据转换和测试";
        } else {
            return "迁移复杂度较高，建议分阶段进行并充分测试";
        }
    }
}