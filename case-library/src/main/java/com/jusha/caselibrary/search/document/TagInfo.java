package com.jusha.caselibrary.search.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName TagInfo
 * @Description 标签信息
 * <AUTHOR>
 * @Date 2025/7/29 08:43
 **/
@Data
public class TagInfo {

    @ApiModelProperty(value = "标签id")
    @Field(type = FieldType.Keyword)
    private Long tagId;

    @ApiModelProperty(value = "标签名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String tagName;
}
