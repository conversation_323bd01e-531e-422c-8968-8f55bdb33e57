package com.jusha.caselibrary.search.document;

import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName DiseaseOverviewInfo
 * @Description 疾病概述信息
 * <AUTHOR>
 * @Date 2025/7/8 17:26
 **/
@Data
public class DiseaseOverviewInfo {

    @ApiModelProperty(value = "疾病概述")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String overview;

    @ApiModelProperty(value = "病理表现")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String pathology;

    @ApiModelProperty(value = "临床表现")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String clinical;

    @ApiModelProperty(value = "影像学表现")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String imaging;

    @ApiModelProperty(value = "诊断依据")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String diagnosis;

    @ApiModelProperty(value = "鉴别诊断")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String differential;

    @ApiModelProperty(value = "关键帧")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String keyframe;
}
