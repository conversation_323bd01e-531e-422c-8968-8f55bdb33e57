package com.jusha.caselibrary.search.migration;

import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.migration.dto.*;
import com.jusha.caselibrary.search.migration.enums.CompatibilityLevel;
import com.jusha.caselibrary.search.migration.enums.IndexVersionStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 智能索引演进管理器
 * 
 * 核心功能：
 * 1. 支持字段新增、类型变更和全量历史数据处理
 * 2. 协调各个组件完成完整的迁移流程
 * 3. 提供迁移状态管理和进度跟踪
 * 4. 支持迁移中断和恢复
 * 
 * <AUTHOR>
 * @date 2025/08/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndexMigrationManager {

    private final IndexVersionManager indexVersionManager;
    private final FieldCompatibilityChecker fieldCompatibilityChecker;
    private final FieldTypeConverter fieldTypeConverter;
    private final SearchConfig searchConfig;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String MIGRATION_STATUS_PREFIX = "migration_status:";
    private static final String MIGRATION_PROGRESS_PREFIX = "migration_progress:";

    /**
     * 执行完整的索引迁移
     * 
     * @param migrationRequest 迁移请求
     * @return 迁移结果
     */
    public MigrationResult executeMigration(MigrationRequest migrationRequest) {
        String migrationId = generateMigrationId();
        log.info("开始执行索引迁移，ID: {}, 请求: {}", migrationId, migrationRequest);

        try {
            // 1. 验证迁移请求
            validateMigrationRequest(migrationRequest);

            // 2. 检查迁移锁
            if (!indexVersionManager.canMigrate(migrationRequest.getIndexType())) {
                throw new RuntimeException("索引正在迁移中，请稍后重试");
            }

            // 3. 创建迁移状态
            MigrationStatus migrationStatus = createMigrationStatus(migrationId, migrationRequest);
            cacheMigrationStatus(migrationStatus);

            // 4. 分析字段兼容性
            FieldChangeAnalysis analysis = analyzeFieldCompatibility(migrationRequest);
            migrationStatus.setFieldAnalysis(analysis);
            updateMigrationStatus(migrationStatus);

            // 5. 检查兼容性
            if (!analysis.canSafelyMigrate()) {
                migrationStatus.setStatus("FAILED");
                migrationStatus.setErrorMessage("字段兼容性检查失败：" + analysis.getMigrationSummary());
                updateMigrationStatus(migrationStatus);
                return MigrationResult.failure(migrationStatus.getErrorMessage());
            }

            // 6. 创建新版本索引
            IndexVersionInfo newVersion = createNewVersionIndex(migrationRequest);
            migrationStatus.setTargetVersion(newVersion.getVersion());
            updateMigrationStatus(migrationStatus);

            // 7. 执行数据迁移
            if (migrationRequest.getMigrationPlan().getFullMigration()) {
                executeDataMigration(migrationStatus, newVersion);
            }

            // 8. 切换索引别名
            switchIndexAlias(migrationStatus, newVersion);

            // 9. 完成迁移
            completeMigration(migrationStatus);

            log.info("索引迁移完成，ID: {}", migrationId);
            return MigrationResult.success(migrationStatus);

        } catch (Exception e) {
            log.error("索引迁移失败，ID: {}", migrationId, e);
            handleMigrationFailure(migrationId, e);
            return MigrationResult.failure("迁移失败：" + e.getMessage());
        } finally {
            // 释放迁移锁
            indexVersionManager.releaseMigrationLock(migrationRequest.getIndexType());
        }
    }

    /**
     * 异步执行索引迁移
     * 
     * @param migrationRequest 迁移请求
     * @return 迁移ID
     */
    public String executeMigrationAsync(MigrationRequest migrationRequest) {
        String migrationId = generateMigrationId();
        
        CompletableFuture.runAsync(() -> {
            try {
                executeMigration(migrationRequest);
            } catch (Exception e) {
                log.error("异步迁移执行失败，ID: {}", migrationId, e);
            }
        });

        return migrationId;
    }

    /**
     * 获取迁移状态
     * 
     * @param migrationId 迁移ID
     * @return 迁移状态
     */
    public MigrationStatus getMigrationStatus(String migrationId) {
        String cacheKey = MIGRATION_STATUS_PREFIX + migrationId;
        return (MigrationStatus) redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 获取迁移进度
     * 
     * @param migrationId 迁移ID
     * @return 迁移进度
     */
    public MigrationProgress getMigrationProgress(String migrationId) {
        String cacheKey = MIGRATION_PROGRESS_PREFIX + migrationId;
        return (MigrationProgress) redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 取消迁移
     * 
     * @param migrationId 迁移ID
     * @return 是否成功取消
     */
    public boolean cancelMigration(String migrationId) {
        try {
            MigrationStatus status = getMigrationStatus(migrationId);
            if (status == null) {
                return false;
            }

            if ("COMPLETED".equals(status.getStatus()) || "FAILED".equals(status.getStatus())) {
                return false;
            }

            status.setStatus("CANCELLED");
            status.setEndTime(LocalDateTime.now());
            updateMigrationStatus(status);

            log.info("迁移已取消，ID: {}", migrationId);
            return true;

        } catch (Exception e) {
            log.error("取消迁移失败，ID: {}", migrationId, e);
            return false;
        }
    }

    /**
     * 恢复中断的迁移
     * 
     * @param migrationId 迁移ID
     * @return 恢复结果
     */
    public MigrationResult resumeMigration(String migrationId) {
        try {
            MigrationStatus status = getMigrationStatus(migrationId);
            if (status == null) {
                throw new RuntimeException("迁移状态不存在");
            }

            if (!"INTERRUPTED".equals(status.getStatus())) {
                throw new RuntimeException("迁移状态不支持恢复");
            }

            log.info("恢复中断的迁移，ID: {}", migrationId);
            
            // 根据当前阶段恢复迁移
            return resumeFromCurrentPhase(status);

        } catch (Exception e) {
            log.error("恢复迁移失败，ID: {}", migrationId, e);
            return MigrationResult.failure("恢复失败：" + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 生成迁移ID
     */
    private String generateMigrationId() {
        return "migration_" + System.currentTimeMillis() + "_" + 
               Thread.currentThread().getId();
    }

    /**
     * 验证迁移请求
     */
    private void validateMigrationRequest(MigrationRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("迁移请求不能为空");
        }

        if (request.getIndexType() == null) {
            throw new IllegalArgumentException("索引类型不能为空");
        }

        if (request.getMigrationPlan() == null) {
            throw new IllegalArgumentException("迁移计划不能为空");
        }

        if (!request.getMigrationPlan().isValid()) {
            throw new IllegalArgumentException("迁移计划无效");
        }
    }

    /**
     * 创建迁移状态
     */
    private MigrationStatus createMigrationStatus(String migrationId, MigrationRequest request) {
        return MigrationStatus.builder()
                .migrationId(migrationId)
                .indexType(request.getIndexType())
                .status("STARTED")
                .startTime(LocalDateTime.now())
                .migrationPlan(request.getMigrationPlan())
                .currentPhase("INITIALIZING")
                .progress(0)
                .build();
    }

    /**
     * 分析字段兼容性
     */
    private FieldChangeAnalysis analyzeFieldCompatibility(MigrationRequest request) {
        log.info("开始分析字段兼容性，索引类型: {}", request.getIndexType());
        
        String currentIndexName = getCurrentIndexName(request.getIndexType());
        Class<?> documentClass = getDocumentClass(request.getIndexType());
        
        return fieldCompatibilityChecker.analyzeFieldChanges(
            currentIndexName, documentClass, request.getMigrationPlan());
    }

    /**
     * 创建新版本索引
     */
    private IndexVersionInfo createNewVersionIndex(MigrationRequest request) {
        log.info("创建新版本索引，索引类型: {}", request.getIndexType());
        
        Class<?> documentClass = getDocumentClass(request.getIndexType());
        return indexVersionManager.createNewVersion(
            request.getIndexType(), documentClass, request.getMigrationPlan());
    }

    /**
     * 执行数据迁移
     */
    private void executeDataMigration(MigrationStatus status, IndexVersionInfo newVersion) {
        log.info("开始执行数据迁移，目标版本: {}", newVersion.getVersion());
        
        status.setCurrentPhase("DATA_MIGRATION");
        status.setProgress(20);
        updateMigrationStatus(status);

        try {
            // 这里应该调用数据迁移服务
            // dataMigrationService.migrateData(status, newVersion);
            
            // 模拟数据迁移过程
            simulateDataMigration(status);
            
            status.setCurrentPhase("DATA_MIGRATION_COMPLETED");
            status.setProgress(80);
            updateMigrationStatus(status);
            
        } catch (Exception e) {
            status.setStatus("FAILED");
            status.setErrorMessage("数据迁移失败：" + e.getMessage());
            updateMigrationStatus(status);
            throw e;
        }
    }

    /**
     * 切换索引别名
     */
    private void switchIndexAlias(MigrationStatus status, IndexVersionInfo newVersion) {
        log.info("切换索引别名，新版本: {}", newVersion.getVersion());
        
        status.setCurrentPhase("ALIAS_SWITCHING");
        status.setProgress(90);
        updateMigrationStatus(status);

        indexVersionManager.switchAlias(newVersion, true);
        
        status.setCurrentPhase("ALIAS_SWITCHED");
        status.setProgress(95);
        updateMigrationStatus(status);
    }

    /**
     * 完成迁移
     */
    private void completeMigration(MigrationStatus status) {
        status.setStatus("COMPLETED");
        status.setCurrentPhase("COMPLETED");
        status.setProgress(100);
        status.setEndTime(LocalDateTime.now());
        updateMigrationStatus(status);
        
        log.info("迁移完成，ID: {}", status.getMigrationId());
    }

    /**
     * 处理迁移失败
     */
    private void handleMigrationFailure(String migrationId, Exception e) {
        try {
            MigrationStatus status = getMigrationStatus(migrationId);
            if (status != null) {
                status.setStatus("FAILED");
                status.setErrorMessage(e.getMessage());
                status.setEndTime(LocalDateTime.now());
                updateMigrationStatus(status);
            }
        } catch (Exception ex) {
            log.error("处理迁移失败状态时出错", ex);
        }
    }

    /**
     * 从当前阶段恢复迁移
     */
    private MigrationResult resumeFromCurrentPhase(MigrationStatus status) {
        // 根据当前阶段恢复迁移逻辑
        // 这里简化处理，实际应该根据具体阶段进行恢复
        status.setStatus("RESUMED");
        updateMigrationStatus(status);
        
        return MigrationResult.success(status);
    }

    /**
     * 模拟数据迁移过程
     */
    private void simulateDataMigration(MigrationStatus status) {
        try {
            // 模拟分批迁移过程
            for (int i = 1; i <= 10; i++) {
                Thread.sleep(500); // 模拟处理时间
                
                int progress = 20 + (i * 6); // 从20%到80%
                status.setProgress(progress);
                updateMigrationStatus(status);
                
                log.debug("数据迁移进度: {}%", progress);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("数据迁移被中断", e);
        }
    }

    /**
     * 获取当前索引名称
     */
    private String getCurrentIndexName(String indexType) {
        if ("department_cases".equals(indexType)) {
            return searchConfig.getIndexes().getDepartmentCases();
        } else if ("personal_cases".equals(indexType)) {
            return searchConfig.getIndexes().getPersonalCases();
        }
        throw new IllegalArgumentException("不支持的索引类型: " + indexType);
    }

    /**
     * 获取文档类
     */
    private Class<?> getDocumentClass(String indexType) {
        if ("department_cases".equals(indexType)) {
            return DepartmentCaseDocument.class;
        } else if ("personal_cases".equals(indexType)) {
            return PersonalCaseDocument.class;
        }
        throw new IllegalArgumentException("不支持的索引类型: " + indexType);
    }

    /**
     * 缓存迁移状态
     */
    private void cacheMigrationStatus(MigrationStatus status) {
        String cacheKey = MIGRATION_STATUS_PREFIX + status.getMigrationId();
        redisTemplate.opsForValue().set(cacheKey, status, 24, TimeUnit.HOURS);
    }

    /**
     * 更新迁移状态
     */
    private void updateMigrationStatus(MigrationStatus status) {
        cacheMigrationStatus(status);
        log.debug("更新迁移状态: {}", status);
    }
}