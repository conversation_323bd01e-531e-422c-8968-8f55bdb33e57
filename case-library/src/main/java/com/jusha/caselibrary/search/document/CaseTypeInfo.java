package com.jusha.caselibrary.search.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName CaseTypeInfo
 * @Description 病例类型信息
 * <AUTHOR>
 * @Date 2025/7/29 10:13
 **/
@Data
public class CaseTypeInfo {

    @ApiModelProperty(value = "病例类型id")
    @Field(type = FieldType.Long)
    private Long caseTypeId;

    @ApiModelProperty(value = "病例类型名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String caseTypeName;

    @ApiModelProperty(value = "是否需要审核0不需要1需要")
    @Field(type = FieldType.Keyword)
    private String audit;

    @ApiModelProperty(value = "病例库地址")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String address;

    @ApiModelProperty(value = "是否需要疾病树0不需要1需要")
    @Field(type = FieldType.Keyword)
    private String diseaseTree;

}
