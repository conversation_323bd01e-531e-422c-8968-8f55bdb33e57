package com.jusha.caselibrary.search.mapper;

import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @InterfaceName DepCaseES
 * @Description 科室ES文档mapper
 * <AUTHOR>
 * @Date 2025/7/8 17:08
 **/
@Mapper
public interface ESDocumentMapper {
    /**
     * @description 构建科室病例ES文档
     * <AUTHOR>
     * @date 2025/7/8 17:13
     * @param caseId
     * @return DepartmentCaseDocument
     **/
    DepartmentCaseDocument buildDeptCaseDocument(@Param("caseId") Long caseId);

    /**
     * @description 构建个人病例ES文档
     * <AUTHOR>
     * @date 2025/7/11 16:37
     * @param userCaseId
     * @return PersonalCaseDocument
     **/
    PersonalCaseDocument buildPersonalCaseDocument(@Param("userCaseId") Long userCaseId);

    /**
     * @description 构建批量科室病例ES文档
     * <AUTHOR>
     * @date 2025/7/8 17:13
     * @param caseIdList
     * @return List<DepartmentCaseDocument>
     **/
    List<DepartmentCaseDocument> buildSyncDeptCasesByIds(@Param("caseIdList") List<Long> caseIdList);

    /**
     * @description 构建批量个人病例ES文档
     * <AUTHOR>
     * @date 2025/7/8 17:13
     * @param userCaseIdList
     * @return List<PersonalCaseDocument>
     **/
    List<PersonalCaseDocument> buildSyncPersonalCasesByUserCaseIds(@Param("userCaseIdList") List<Long> userCaseIdList);
}
