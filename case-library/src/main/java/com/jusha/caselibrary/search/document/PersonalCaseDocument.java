package com.jusha.caselibrary.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName PersonalCaseDocument
 * @Description 个人病例库ES文档
 * <AUTHOR>
 * @Date 2025/7/7 15:54
 **/
@Data
@Document(indexName = "personal_cases")
public class PersonalCaseDocument {

    @Id
    @ApiModelProperty(value = "个人病例Id", required = true)
    private Long userCaseId;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "病例Id", required = true)
    private Long caseId;

    @ApiModelProperty(value = "疾病id")
    @Field(type = FieldType.Keyword)
    private Long diseaseId;

    @ApiModelProperty(value = "疾病名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String diseaseName;

    // 个人病例库特有字段
    @ApiModelProperty(value = "病例名称")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    @Field(type = FieldType.Keyword)
    private String caseNo;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String diagnosis;

    @ApiModelProperty(value = "患者Id")
    @Field(type = FieldType.Keyword)
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    @Field(type = FieldType.Keyword)
    private String patientSex;

    @ApiModelProperty(value = "患者生日")
    @Field(type = FieldType.Keyword)
    private String patientBirthDate;

    @ApiModelProperty(value = "患者年龄")
    @Field(type = FieldType.Keyword)
    private String patientAge;

    @ApiModelProperty(value = "是否典型")
    @Field(type = FieldType.Keyword)
    private String caseCategory;

    @ApiModelProperty(value = "患者病史")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String medicalHistory;

    @ApiModelProperty(value = "难度等级")
    @Field(type = FieldType.Keyword)
    private String difficulty;

    @ApiModelProperty(value = "征象")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String sign;

    @ApiModelProperty(value = "病例分析")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String caseAnalysis;

    @ApiModelProperty(value = "来源")
    @Field(type = FieldType.Keyword)
    private String sourceType;

    @ApiModelProperty(value = "随访状态")
    @Field(type = FieldType.Keyword)
    private String followStatus;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String remark;

    @ApiModelProperty(value = "创建人")
    @Field(type = FieldType.Long)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DateUtil.DEFAULT_PATTERN)
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "定性匹配")
    @Field(type = FieldType.Keyword)
    private String qualityMatch;

    @ApiModelProperty(value = "定位匹配")
    @Field(type = FieldType.Keyword)
    private String positionMatch;

    @ApiModelProperty(value = "主诉")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String selfComplaints;

    // 关联的目录信息（个人病例库特有）
    @ApiModelProperty(value = "病例目录Id列表")
    @Field(type = FieldType.Keyword)
    private List<Long> catalogIds;

    // 关联的检查信息
    @ApiModelProperty(value = "病例报告")
    @Field(type = FieldType.Nested)
    private List<StudyInfo> studyInfoList;

    // 关联的标签信息
    @ApiModelProperty(value = "病例标签列表")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private List<String> tags;

    @ApiModelProperty(value = "病例标签列表")
    @Field(type = FieldType.Nested)
    private List<TagInfo> tagInfoList;

    // 关联的随访信息
    @Field(type = FieldType.Nested)
    @ApiModelProperty(value = "随访信息")
    private List<FollowInfo> followInfoList;

}