package com.jusha.caselibrary.search.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName AdvancedSearchRequest
 * @Description 高级检索请求DTO
 * <AUTHOR>
 * @Date 2025/7/7 15:56
 **/
@Data
public class AdvancedSearchRequest {

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    /**
     * 疾病名称（科室病例库）
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    /**
     * 影像号
     */
    @ApiModelProperty(value = "影像号")
    private String accessNumber;

    /**
     * 住院号
     */
    @ApiModelProperty(value = "住院号")
    private String inPatientNo;

    /**
     * 门诊号
     */
    @ApiModelProperty(value = "门诊号")
    private String outPatientNo;

    /**
     * 检查项目
     */
    @ApiModelProperty(value = "检查项目")
    private String studyItemName;

    /**
     * 检查部位
     */
    @ApiModelProperty(value = "检查部位")
    private String partName;

    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 标签名称列表
     */
    @ApiModelProperty(value = "标签名称列表")
    private List<String> tagNameList;

    /**
     * 检查日期范围 - 开始日期
     */
    @ApiModelProperty(value = "检查日期范围 - 开始日期")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyStartTime;

    /**
     * 检查日期范围 - 结束日期
     */
    @ApiModelProperty(value = "检查日期范围 - 结束日期")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyEndTime;

    /**
     * 诊断信息
     */
    @ApiModelProperty(value = "诊断信息")
    private String diagnosis;

    /**
     * 临床诊断
     */
    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;

    /**
     * 影像诊断
     */
    @ApiModelProperty(value = "影像诊断")
    private String reportDiagnose;

    /**
     * 影像描述
     */
    @ApiModelProperty(value = "影像描述")
    private String reportDescribe;

    /**
     * 病史信息
     */
    @ApiModelProperty(value = "病史信息")
    private String medicalHistory;

    /**
     * 体征信息
     */
    @ApiModelProperty(value = "体征信息")
    private String physicalSign;

    /**
     * 自述症状
     */
    @ApiModelProperty(value = "患者主诉")
    private String selfReportedSymptom;

    /**
     * 病例分析
     */
    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    /**
     * 病例名称
     */
    @ApiModelProperty(value = "病例名称")
    private String caseName;

    /**
     * 病例编号
     */
    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    /**
     * 难度等级
     */
    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "主诉")
    private String selfComplaints;

    /**
     * 患者性别
     */
    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    /**
     * 患者年龄范围 - 最小年龄
     */
    @ApiModelProperty(value = "患者年龄范围 - 最小年龄")
    private Integer patientAgeMin;

    /**
     * 患者年龄范围 - 最大年龄
     */
    @ApiModelProperty(value = "患者年龄范围 - 最大年龄")
    private Integer patientAgeMax;

    /**
     * 是否阳性
     */
    @ApiModelProperty(value = "是否阳性")
    private String isPositive;

    /**
     * 检查状态
     */
    @ApiModelProperty(value = "检查状态")
    private String studyState;

    /**
     * 报告医生
     */
    @ApiModelProperty(value = "报告医生")
    private String reporter;

    /**
     * 审核医生
     */
    @ApiModelProperty(value = "审核医生")
    private String checker;

    /**
     * 申请科室
     */
    @ApiModelProperty(value = "申请科室")
    private String applyDepartment;

    /**
     * 申请医生
     */
    @ApiModelProperty(value = "申请医生")
    private String applyDoctor;

    @ApiModelProperty(value = "模态/检查类型")
    private String modality;

    /**
     * 关键字搜索
     */
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;

    /**
     * 高亮显示
     */
    @ApiModelProperty(value = "高亮显示，默认为true", hidden = true)
    private Boolean highlight = true;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", hidden = true)
    private String sortField;

    /**
     * 排序方向：asc、desc
     */
    @ApiModelProperty(value = "排序方向：asc、desc，默认为desc", hidden = true)
    private String sortDirection = "desc";

    /**
     * 页码（从1开始）
     */
    @ApiModelProperty(value = "页码（从1开始），不传则查询所有数据")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小,最大值为1000，不传则查询所有数据")
    private Integer pageSize;

    /**
     * 是否包含总数统计
     */
    @ApiModelProperty(value = "是否包含总数统计，默认为true", hidden = true)
    private Boolean includeCount = true;

    /**
     * 聚合统计字段
     */
    @ApiModelProperty(value = "聚合统计字段", hidden = true)
    private List<String> aggregationFields;

    /**
     * 最小匹配度（0-1之间）
     */
    @ApiModelProperty(value = "最小匹配度（0-1之间）", hidden = true)
    private Float minScore;

    /**
     * 搜索超时时间（毫秒）
     */
    @ApiModelProperty(value = "搜索超时时间（毫秒），默认为30000毫秒", hidden = true)
    private Long timeoutMs = 30000L;

    // ==================== 新增随访相关搜索参数 ====================
    
    /**
     * 随访类型
     */
    @ApiModelProperty(value = "随访类型")
    private String followType;
    
    /**
     * 随访结果
     */
    @ApiModelProperty(value = "随访结果")
    private String followupResult;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    // ==================== 新增疾病概述相关搜索参数 ====================
    
    /**
     * 疾病概述
     */
    @ApiModelProperty(value = "疾病概述")
    private String diseaseOverview;
    
    /**
     * 病理表现
     */
    @ApiModelProperty(value = "病理表现")
    private String pathology;
    
    /**
     * 临床表现
     */
    @ApiModelProperty(value = "临床表现")
    private String clinical;
    
    /**
     * 影像学表现
     */
    @ApiModelProperty(value = "影像学表现")
    private String imaging;
    
    /**
     * 诊断依据
     */
    @ApiModelProperty(value = "诊断依据")
    private String diagnosisBasis;
    
    /**
     * 鉴别诊断
     */
    @ApiModelProperty(value = "鉴别诊断")
    private String differential;
    
    /**
     * 关键帧
     */
    @ApiModelProperty(value = "关键帧")
    private String keyframe;

    // ==================== 新增性能优化相关参数 ====================
    
    /**
     * 是否使用search_after分页（避免深度分页问题）
     */
    @ApiModelProperty(value = "是否使用search_after分页（避免深度分页问题），默认为false", hidden = true)
    private Boolean useSearchAfter = false;
    
    /**
     * 是否启用智能查询优化
     */
    private Boolean enableSmartOptimization = true;

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        // 基本验证 - 允许分页参数为null
        if (pageNum != null && pageNum < 1) {
            return false;
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 1000)) {
            return false;
        }

        // 日期范围验证
        if (studyStartTime != null && studyEndTime != null && studyStartTime.isAfter(studyEndTime)) {
            return false;
        }
        // 年龄范围验证
        return patientAgeMin == null || patientAgeMax == null || patientAgeMin <= patientAgeMax;
    }

    /**
     * 是否为空搜索（没有任何搜索条件）
     */
    public boolean isEmpty() {
        return keyword == null && patientName == null && accessNumber == null &&
                inPatientNo == null && outPatientNo == null && studyItemName == null &&
                partName == null && deviceType == null && deviceName == null &&
                (tagNameList == null || tagNameList.isEmpty()) &&
                studyStartTime == null && studyEndTime == null &&
                diagnosis == null && clinicalDiagnosis == null && reportDiagnose == null &&
                reportDescribe == null && medicalHistory == null && physicalSign == null &&
                selfReportedSymptom == null && caseAnalysis == null && caseName == null &&
                caseNo == null && difficulty == null && patientSex == null &&
                patientAgeMin == null && patientAgeMax == null && isPositive == null &&
                studyState == null && reporter == null && checker == null &&
                applyDepartment == null && applyDoctor == null &&
                modality == null &&
                // 新增随访相关字段检查
                followType == null && followupResult == null &&
                // 新增疾病概述相关字段检查
                diseaseOverview == null && pathology == null && clinical == null &&
                imaging == null && diagnosisBasis == null && differential == null &&
                keyframe == null;
    }

    /**
     * 判断是否为复杂查询（需要特殊优化）
     */
    public boolean isComplexQuery() {
        int conditionCount = 0;
        // 统计搜索条件数量
        if (keyword != null) conditionCount++;
        if (patientName != null) conditionCount++;
        if (diagnosis != null) conditionCount++;
        if (clinicalDiagnosis != null) conditionCount++;
        if (reportDiagnose != null) conditionCount++;
        if (reportDescribe != null) conditionCount++;
        if (medicalHistory != null) conditionCount++;
        if (followupResult != null) conditionCount++;
        if (pathology != null) conditionCount++;
        if (clinical != null) conditionCount++;
        if (imaging != null) conditionCount++;
        // 日期范围查询
        if (studyStartTime != null || studyEndTime != null) conditionCount++;
        // 复杂查询的判断标准：条件数量 >= 5 或者包含全文搜索 + 其他条件
        return conditionCount >= 5 || (keyword != null && conditionCount >= 3);
    }

    /**
     * 判断是否需要深度分页优化
     */
    public boolean needsDeepPaginationOptimization() {
        return pageNum != null && pageSize != null && (pageNum - 1) * pageSize > 10000;
    }

    /**
     * 判断是否为分页查询
     */
    public boolean isPaginated() {
        return pageNum != null && pageSize != null;
    }

    public void preprocessRequest(AdvancedSearchRequest request) {
        // 只有在分页参数都不为null时才进行分页设置和验证
        if (request.getPageNum() != null || request.getPageSize() != null) {
            // 设置默认值
            if (request.getPageNum() == null || request.getPageNum() < 1) {
                request.setPageNum(1);
            }
            if (request.getPageSize() == null || request.getPageSize() < 1) {
                request.setPageSize(20);
            }
            if (request.getPageSize() > 1000) {
                request.setPageSize(1000);
            }
        }

        // 关键字预处理
        if (request.getKeyword() != null) {
            request.setKeyword(request.getKeyword().trim());
        }

        // 启用智能优化
        if (request.getEnableSmartOptimization() == null) {
            request.setEnableSmartOptimization(true);
        }
    }
}
