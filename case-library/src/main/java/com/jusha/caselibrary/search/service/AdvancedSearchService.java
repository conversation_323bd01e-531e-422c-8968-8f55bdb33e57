package com.jusha.caselibrary.search.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;

import java.util.List;

/**
 * 高级检索服务接口
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
public interface AdvancedSearchService {

    /**
     * 执行高级检索（支持 AdvancedSearchRequest 及其子类）
     * @param <T> 请求类型，必须继承自 AdvancedSearchRequest
     * @param request 搜索请求
     * @return 搜索响应
     */
    <T extends AdvancedSearchRequest> AdvancedSearchResponse<?> search(T request);

    /**
     * 执行高级检索并返回指定类型的列表或分页结果
     * @param <T> 请求类型，必须继承自 AdvancedSearchRequest
     * @param <R> 响应对象类型
     * @param request 搜索请求
     * @param responseClass 响应对象的Class类型
     * @return 如果是分页查询返回PageInfo<R>，否则返回List<R>
     */
    <T extends AdvancedSearchRequest, R> List<R> searchAndConvertList(T request, Class<R> responseClass);
    <T extends AdvancedSearchRequest, R> PageInfo<R> searchAndConvertPage(T request, Class<R> responseClass);

    /**
     * 执行高级检索并根据分页设置自动返回对应的结果类型
     * @param <T> 请求类型，必须继承自 AdvancedSearchRequest
     * @param <R> 响应对象类型
     * @param request 搜索请求
     * @param responseClass 响应对象的Class类型
     * @return 如果是分页查询返回PageInfo<R>，否则返回List<R>
     */
    <T extends AdvancedSearchRequest, R> Object searchAndConvert(T request, Class<R> responseClass);
}