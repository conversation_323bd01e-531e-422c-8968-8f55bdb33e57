package com.jusha.caselibrary.search.migration.enums;

/**
 * 索引版本状态枚举
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
public enum IndexVersionStatus {

    /**
     * 创建中
     */
    CREATING("创建中"),

    /**
     * 已创建
     */
    CREATED("已创建"),

    /**
     * 迁移中
     */
    MIGRATING("迁移中"),

    /**
     * 活跃状态（当前使用中）
     */
    ACTIVE("活跃"),

    /**
     * 已废弃
     */
    DEPRECATED("已废弃"),

    /**
     * 已删除
     */
    DELETED("已删除"),

    /**
     * 回滚中
     */
    ROLLING_BACK("回滚中"),

    /**
     * 错误状态
     */
    ERROR("错误");

    private final String description;

    IndexVersionStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 是否为终态
     */
    public boolean isFinalState() {
        return this == ACTIVE || this == DEPRECATED || this == DELETED || this == ERROR;
    }

    /**
     * 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == CREATING || this == MIGRATING || this == ROLLING_BACK;
    }

    /**
     * 是否可以切换到目标状态
     */
    public boolean canTransitionTo(IndexVersionStatus targetStatus) {
        switch (this) {
            case CREATING:
                return targetStatus == CREATED || targetStatus == ERROR;
            case CREATED:
                return targetStatus == MIGRATING || targetStatus == ACTIVE || targetStatus == DEPRECATED;
            case MIGRATING:
                return targetStatus == ACTIVE || targetStatus == ERROR;
            case ACTIVE:
                return targetStatus == DEPRECATED || targetStatus == ROLLING_BACK;
            case DEPRECATED:
                return targetStatus == DELETED || targetStatus == ROLLING_BACK;
            case ROLLING_BACK:
                return targetStatus == ACTIVE || targetStatus == ERROR;
            case ERROR:
                return targetStatus == CREATING || targetStatus == DELETED;
            case DELETED:
                return false; // 已删除状态不能转换到其他状态
            default:
                return false;
        }
    }
}