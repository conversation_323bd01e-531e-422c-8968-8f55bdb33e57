package com.jusha.caselibrary.search.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 迁移结果
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 迁移状态
     */
    private MigrationStatus migrationStatus;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 警告消息
     */
    private String warningMessage;

    /**
     * 结果时间
     */
    private LocalDateTime resultTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 迁移的文档数量
     */
    private Long migratedDocumentCount;

    /**
     * 跳过的文档数量
     */
    private Long skippedDocumentCount;

    /**
     * 失败的文档数量
     */
    private Long failedDocumentCount;

    /**
     * 详细信息
     */
    private String details;

    /**
     * 创建成功结果
     */
    public static MigrationResult success(MigrationStatus status) {
        return MigrationResult.builder()
                .success(true)
                .migrationStatus(status)
                .resultTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带详细信息）
     */
    public static MigrationResult success(MigrationStatus status, String details) {
        return MigrationResult.builder()
                .success(true)
                .migrationStatus(status)
                .details(details)
                .resultTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static MigrationResult failure(String errorMessage) {
        return MigrationResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .resultTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带状态）
     */
    public static MigrationResult failure(MigrationStatus status, String errorMessage) {
        return MigrationResult.builder()
                .success(false)
                .migrationStatus(status)
                .errorMessage(errorMessage)
                .resultTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建带警告的成功结果
     */
    public static MigrationResult successWithWarning(MigrationStatus status, String warningMessage) {
        return MigrationResult.builder()
                .success(true)
                .migrationStatus(status)
                .warningMessage(warningMessage)
                .resultTime(LocalDateTime.now())
                .build();
    }

    /**
     * 获取迁移ID
     */
    public String getMigrationId() {
        return migrationStatus != null ? migrationStatus.getMigrationId() : null;
    }

    /**
     * 获取迁移进度
     */
    public Integer getProgress() {
        return migrationStatus != null ? migrationStatus.getProgress() : null;
    }

    /**
     * 是否有警告
     */
    public boolean hasWarning() {
        return warningMessage != null && !warningMessage.trim().isEmpty();
    }

    /**
     * 获取总处理文档数
     */
    public long getTotalProcessedDocuments() {
        long migrated = migratedDocumentCount != null ? migratedDocumentCount : 0;
        long skipped = skippedDocumentCount != null ? skippedDocumentCount : 0;
        long failed = failedDocumentCount != null ? failedDocumentCount : 0;
        return migrated + skipped + failed;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        long total = getTotalProcessedDocuments();
        if (total == 0) {
            return 0.0;
        }
        long migrated = migratedDocumentCount != null ? migratedDocumentCount : 0;
        return (double) migrated / total * 100;
    }

    /**
     * 获取结果摘要
     */
    public String getSummary() {
        if (!success) {
            return "迁移失败：" + (errorMessage != null ? errorMessage : "未知错误");
        }

        StringBuilder summary = new StringBuilder("迁移成功");
        
        if (migrationStatus != null) {
            summary.append("，ID: ").append(migrationStatus.getMigrationId());
        }
        
        if (getTotalProcessedDocuments() > 0) {
            summary.append("，处理文档: ").append(getTotalProcessedDocuments()).append("条");
            summary.append("，成功率: ").append(String.format("%.2f%%", getSuccessRate()));
        }
        
        if (executionTimeMs != null) {
            summary.append("，耗时: ").append(executionTimeMs).append("ms");
        }
        
        if (hasWarning()) {
            summary.append("，警告: ").append(warningMessage);
        }
        
        return summary.toString();
    }
}