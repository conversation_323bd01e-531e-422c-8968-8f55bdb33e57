package com.jusha.caselibrary.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @ClassName AuditInfo
 * @Description 审核信息
 * <AUTHOR>
 * @Date 2025/7/29 09:19
 **/
@Data
public class AuditInfo {

    @ApiModelProperty(value = "审核ID")
    @Field(type = FieldType.Long)
    private Long auditId;

    @ApiModelProperty(value = "病例id")
    @Field(type = FieldType.Long)
    private Long caseId;

    @ApiModelProperty(value = "病例类型id")
    @Field(type = FieldType.Long)
    private Long caseTypeId;

    @ApiModelProperty(value = "审核类型：RIS导入，本地导入，随访转其他，个人转科室")
    @Field(type = FieldType.Keyword)
    private String auditType;

    @ApiModelProperty(value = "发布人")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String pubUserId;

    @ApiModelProperty(value = "发布时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DateUtil.DEFAULT_PATTERN)
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private Date pubTime;

    @ApiModelProperty(value = "审核人")
    @Field(type = FieldType.Long)
    private Long auditedBy;

    @ApiModelProperty(value = "审核时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DateUtil.DEFAULT_PATTERN)
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private Date auditedTime;

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    @Field(type = FieldType.Keyword)
    private String status;

    @ApiModelProperty(value = "审核意见")
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String auditComment;
}
