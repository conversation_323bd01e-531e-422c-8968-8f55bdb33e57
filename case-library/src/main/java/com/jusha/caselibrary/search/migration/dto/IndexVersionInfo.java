package com.jusha.caselibrary.search.migration.dto;

import com.jusha.caselibrary.search.migration.enums.IndexVersionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 索引版本信息
 * 
 * <AUTHOR> Code
 * @date 2025/08/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexVersionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 索引类型（department_cases, personal_cases）
     */
    private String indexType;

    /**
     * 版本号
     */
    private String version;

    /**
     * 索引名称
     */
    private String indexName;

    /**
     * 别名名称
     */
    private String aliasName;

    /**
     * 版本状态
     */
    private IndexVersionStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 切换时间
     */
    private LocalDateTime switchTime;

    /**
     * 回滚时间
     */
    private LocalDateTime rollbackTime;

    /**
     * 迁移计划
     */
    private MigrationPlan migrationPlan;

    /**
     * 文档数量
     */
    private Long documentCount;

    /**
     * 索引大小（字节）
     */
    private Long indexSize;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 是否为当前活跃版本
     */
    public boolean isActive() {
        return IndexVersionStatus.ACTIVE.equals(status);
    }

    /**
     * 是否可以删除
     */
    public boolean canDelete() {
        return !isActive() && 
               !IndexVersionStatus.MIGRATING.equals(status) &&
               !IndexVersionStatus.CREATING.equals(status);
    }
}