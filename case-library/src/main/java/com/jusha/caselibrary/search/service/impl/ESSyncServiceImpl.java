package com.jusha.caselibrary.search.service.impl;

import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.mybatisplus.service.UserCaseService;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.dto.SyncMessage;
import com.jusha.caselibrary.search.service.CaseDataService;
import com.jusha.caselibrary.search.service.ESSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ES数据同步服务实现类
 * 
 * <AUTHOR> Code
 * @date 2025/07/07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ESSyncServiceImpl implements ESSyncService {

    private final ElasticsearchRestTemplate elasticsearchTemplate;

    private final CaseDataService caseDataService;

    private final UserCaseService userCaseService;
    
    private final UserCaseCatalogService userCaseCatalogService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final SearchConfig searchConfig;

    @Override
    public void ensureIndexesExistOrCreate() {
        try {
            log.info("检查ES索引是否存在");
            // 检查科室病例库索引
            IndexCoordinates departmentIndex = IndexCoordinates.of(
                    searchConfig.getIndexes().getDepartmentCases());
            if (!elasticsearchTemplate.indexOps(departmentIndex).exists()) {
                log.info("创建科室病例库索引: {}", departmentIndex.getIndexName());
                createIndexWithSettings(DepartmentCaseDocument.class);
            }

            // 检查个人病例库索引
            IndexCoordinates personalIndex = IndexCoordinates.of(
                    searchConfig.getIndexes().getPersonalCases());
            if (!elasticsearchTemplate.indexOps(personalIndex).exists()) {
                log.info("创建个人病例库索引: {}", personalIndex.getIndexName());
                createIndexWithSettings(PersonalCaseDocument.class);
            }

            log.info("ES索引检查完成");
        } catch (Exception e) {
            log.error("检查ES索引异常", e);
            throw new BusinessException(LocaleUtil.getLocale("check.es.index.exists.failed"));
        }
    }

    /**
     * 创建带有自定义settings的索引
     */
    private void createIndexWithSettings(Class<?> documentClass) {
        try {
            IndexOperations indexOps = elasticsearchTemplate.indexOps(documentClass);

            // 创建设置文档
            Document settings = Document.create()
                    .append("index.number_of_shards", 1)
                    .append("index.number_of_replicas", 0)
                    .append("index.max_result_window", 10000);

            // 创建索引
            indexOps.create(settings);

            // 创建映射
            indexOps.putMapping(elasticsearchTemplate.indexOps(documentClass)
                    .createMapping(documentClass));

            log.info("索引创建成功: {}", documentClass.getSimpleName());
        } catch (Exception e) {
            log.error("创建索引失败: {}", documentClass.getSimpleName(), e);
            throw e;
        }
    }

    @Override
    public void recreateIndexes() {
        try {
            log.info("开始重建ES索引");

            // 删除并重建科室病例库索引
            IndexCoordinates departmentIndex = IndexCoordinates.of(
                    searchConfig.getIndexes().getDepartmentCases());
            if (elasticsearchTemplate.indexOps(departmentIndex).exists()) {
                elasticsearchTemplate.indexOps(departmentIndex).delete();
                log.info("删除科室病例库索引: {}", departmentIndex.getIndexName());
            }
            // 删除并重建个人病例库索引
            IndexCoordinates personalIndex = IndexCoordinates.of(
                    searchConfig.getIndexes().getPersonalCases());
            if (elasticsearchTemplate.indexOps(personalIndex).exists()) {
                elasticsearchTemplate.indexOps(personalIndex).delete();
                log.info("删除个人病例库索引: {}", personalIndex.getIndexName());
            }
            ensureIndexesExistOrCreate();
            log.info("ES索引重建完成");
        } catch (Exception e) {
            log.error("重建ES索引异常", e);
            throw new RuntimeException("重建ES索引失败", e);
        }
    }

    @Override
    public void processSyncMessage(SyncMessage message) {
        try {
            log.info("处理同步消息: {}", message);
            processSingleSyncMessage(message);
        } catch (Exception e) {
            log.error("处理同步消息异常: {}", message, e);
            // 可以考虑将失败的消息放入死信队列或重试队列
            handleSyncFailure(message, e);
        }
    }

    /**
     * 处理单个同步消息
     */
    private void processSingleSyncMessage(SyncMessage message) {
        // 获取有效的ID集合（兼容单个和批量操作）
        List<Long> caseIdList = message.getEffectiveCaseIdList();
        
        if (caseIdList.isEmpty()) {
            log.warn("同步消息中没有有效的病例ID: {}", message);
            return;
        }
        
        if (Constant.DEP_CASE_INDEX_NAME.equals(message.getIndexType())) {
            syncDepartmentCase(caseIdList, message.getOperation());
        } else if (Constant.PERSON_CASE_INDEX_NAME.equals(message.getIndexType())) {
            // 个人病例库操作，使用简化的同步逻辑
            syncPersonalCase(caseIdList, message.getOperation());
        } else {
            log.warn("未知的索引类型: {}", message.getIndexType());
        }
    }

    @Override
    public void fullSyncDepartmentCases() {
        log.info("开始全量同步科室病例数据");

        try {
            // 获取所有科室病例ID
            List<Long> allCaseIds = caseDataService.getAllDepartmentCaseIds();
            log.info("获取到科室病例总数: {}", allCaseIds.size());

            if (!CollectionUtils.isEmpty(allCaseIds)) {
                syncDepartmentCase(allCaseIds, Constant.OPERATION_TYPE_BATCH_UPDATE);
            }

            log.info("全量同步科室病例数据完成");
        } catch (Exception e) {
            log.error("全量同步科室病例数据异常", e);
            throw new RuntimeException("全量同步科室病例数据失败", e);
        }
    }

    @Override
    public void fullSyncPersonalCases(Long userId) {
        log.info("开始全量同步个人病例数据");

        try {
            // 获取所有个人病例ID
            List<Long> allCaseIds = caseDataService.getAllPersonalCaseIds(userId);
            log.info("获取到个人病例总数: {}", allCaseIds.size());

            if (!CollectionUtils.isEmpty(allCaseIds)) {
                syncPersonalCase(allCaseIds, Constant.OPERATION_TYPE_BATCH_UPDATE);
            }

            log.info("全量同步个人病例数据完成");
        } catch (Exception e) {
            log.error("全量同步个人病例数据异常", e);
            throw new RuntimeException("全量同步个人病例数据失败", e);
        }
    }

    @Override
    public void syncDepartmentCase(List<Long> caseIdList, String operation) {
        try {
            log.info("开始同步科室病例数据，caseIds: {}, operation: {}", caseIdList, operation);

            switch (operation.toUpperCase()) {
                case Constant.OPERATION_TYPE_CREATE:
                case Constant.OPERATION_TYPE_UPDATE:
                    if (caseIdList.size() == 1) {
                        syncDepartmentCaseData(caseIdList.get(0));
                    } else {
                        batchSyncDepartmentCaseData(caseIdList);
                    }
                    break;
                case Constant.OPERATION_TYPE_DELETE:
                    if (caseIdList.size() == 1) {
                        deleteDepartmentCaseData(caseIdList.get(0));
                    } else {
                        batchDeleteDepartmentCaseData(caseIdList);
                    }
                    break;
                case Constant.OPERATION_TYPE_BATCH_CREATE:
                case Constant.OPERATION_TYPE_BATCH_UPDATE:
                    batchSyncDepartmentCaseData(caseIdList);
                    break;
                case Constant.OPERATION_TYPE_BATCH_DELETE:
                    batchDeleteDepartmentCaseData(caseIdList);
                    break;
                default:
                    log.warn("不支持的操作类型: {}", operation);
            }

            log.info("科室病例数据同步完成, caseIds: {}, operation: {}", caseIdList, operation);
        } catch (Exception e) {
            log.error("同步科室病例数据异常，caseIds: {}, operation: {}", caseIdList, operation, e);
            throw new RuntimeException("同步科室病例数据失败", e);
        }
    }

    private void batchDeleteDepartmentCaseData(List<Long> caseIdList) {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(
                    searchConfig.getIndexes().getDepartmentCases());
            // 批量删除ES文档
            Query query = new NativeSearchQuery(QueryBuilders.idsQuery()
                    .addIds(Arrays.toString(caseIdList.toArray(new Long[0]))));
            elasticsearchTemplate.delete(query, DepartmentCaseDocument.class, indexCoordinates);

            log.info("批量删除科室病例数据成功，删除数量：{}", caseIdList.size());
        } catch (Exception e) {
            log.error("批量删除科室病例数据失败，病例ID列表：{}", caseIdList, e);
            throw new RuntimeException("批量删除科室病例数据失败", e);
        }
    }

    private void batchSyncDepartmentCaseData(List<Long> caseIdList) {
        List<DepartmentCaseDocument>  documentList =  caseDataService.buildSyncDepartmentCasesByIds(caseIdList);
        if (!CollectionUtils.isEmpty(documentList)) {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(
                    searchConfig.getIndexes().getDepartmentCases());
            try {
                // 批量保存或更新文档到ES
                elasticsearchTemplate.save(documentList, indexCoordinates);
                log.info("批量同步科室病例数据成功，处理数量：{}", documentList.size());
            } catch (Exception e) {
                log.error("批量同步科室病例数据到ES失败，病例ID列表：{}", caseIdList, e);
                throw new RuntimeException("批量同步科室病例数据失败", e);
            }
        } else {
            log.warn("批量同步科室病例数据：没有找到需要同步的数据，病例ID列表：{}", caseIdList);
        }
    }
    /**
     * 个人病例同步方法
     * 使用userCaseId作为主键
     */
    @Override
    public void syncPersonalCase(List<Long> userCaseIdList, String operation) {
        try {
            log.info("开始简化同步个人病例数据，userCaseIds: {}, operation: {}", userCaseIdList, operation);
            
            IndexCoordinates indexCoordinates = IndexCoordinates.of(
                    searchConfig.getIndexes().getPersonalCases());
            
            switch (operation.toUpperCase()) {
                case Constant.OPERATION_TYPE_CREATE:
                case Constant.OPERATION_TYPE_UPDATE:
                    // CREATE/UPDATE操作：通过userCaseId查询ES，有则更新catalogIds，无则新增
                    syncPersonalCase(userCaseIdList.get(0), indexCoordinates);
                    break;
                case Constant.OPERATION_TYPE_DELETE:
                    // DELETE操作：查询数据库获取catalogIds，为空则删除ES文档，不为空则更新
                    syncDeletePersonalCase(userCaseIdList.get(0), indexCoordinates);
                    break;
                case Constant.OPERATION_TYPE_BATCH_CREATE:
                case Constant.OPERATION_TYPE_BATCH_UPDATE:
                    // 批量CREATE/UPDATE操作：批量处理userCaseId列表
                    batchSyncPersonalCase(userCaseIdList, indexCoordinates);
                    break;
                case Constant.OPERATION_TYPE_BATCH_DELETE:
                    // 批量DELETE操作：批量处理userCaseId列表
                    batchDeletePersonalCase(userCaseIdList, indexCoordinates);
                    break;
                default:
                    log.warn("不支持的操作类型: {}", operation);
            }
            
            log.info("简化同步个人病例数据完成，userCaseIds: {}, operation: {}", userCaseIdList, operation);
        } catch (Exception e) {
            log.error("简化同步个人病例数据异常，userCaseIds: {}, operation: {}", userCaseIdList, operation, e);
            throw new RuntimeException("简化同步个人病例数据失败", e);
        }
    }

    /**
     * 处理简化的个人病例CREATE/UPDATE操作
     */
    private void syncPersonalCase(Long userCaseId, IndexCoordinates indexCoordinates) {
        try {
            createOrUpdatePersonalCaseDocument(userCaseId, indexCoordinates);
        } catch (Exception e) {
            log.error("处理个人病例CREATE操作异常，userCaseId: {}", userCaseId, e);
            throw new RuntimeException("处理简化个人病例CREATE操作失败", e);
        }
    }

    /**
     * 处理个人病例DELETE操作
     */
    private void syncDeletePersonalCase(Long userCaseId, IndexCoordinates indexCoordinates) {
        try {
            // 查询数据库获取该userCaseId对应的所有catalogIds
            List<Long> catalogIds = getCatalogIdsByUserCaseId(userCaseId);
            
            if (catalogIds == null || catalogIds.isEmpty()) {
                // catalogIds为空，删除ES文档
                log.info("catalogIds为空，删除个人病例ES文档，userCaseId: {}", userCaseId);
                deletePersonalCaseDocumentByUserCaseId(userCaseId, indexCoordinates);
            } else {
                // catalogIds不为空，更新ES文档的catalogIds
                log.info("catalogIds不为空，更新个人病例ES文档，userCaseId: {}, catalogIds: {}", userCaseId, catalogIds);
                PersonalCaseDocument existingDoc = getPersonalCaseDocumentByUserCaseId(userCaseId, indexCoordinates);
                if (existingDoc != null) {
                    existingDoc.setCatalogIds(catalogIds);
                    elasticsearchTemplate.save(existingDoc, indexCoordinates);
                }
            }
        } catch (Exception e) {
            log.error("处理简化个人病例DELETE操作异常，userCaseId: {}", userCaseId, e);
            throw new RuntimeException("处理简化个人病例DELETE操作失败", e);
        }
    }

    /**
     * 通过userCaseId查询ES文档
     */
    private PersonalCaseDocument getPersonalCaseDocumentByUserCaseId(Long userCaseId, IndexCoordinates indexCoordinates) {
        try {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.must(QueryBuilders.termQuery("userCaseId", userCaseId));

            Query query = new NativeSearchQuery(queryBuilder);
            SearchHits<PersonalCaseDocument> searchHits = elasticsearchTemplate.search(query, PersonalCaseDocument.class, indexCoordinates);

            if (searchHits.hasSearchHits()) {
                SearchHit<PersonalCaseDocument> firstHit = searchHits.getSearchHits().get(0);
                return firstHit.getContent();
            }
            return null;
        } catch (Exception e) {
            log.error("通过userCaseId查询个人病例ES文档异常，userCaseId: {}", userCaseId, e);
            throw e;
        }
    }

    /**
     * 创建简化的个人病例文档
     */
    private void createOrUpdatePersonalCaseDocument(Long userCaseId, IndexCoordinates indexCoordinates) {
        try {
            // 根据userCaseId获取caseId，然后构建文档
            UserCase userCase = userCaseService.getById(userCaseId);
            if (userCase == null) {
                log.warn("未找到userCaseId对应的UserCase记录，userCaseId: {}", userCaseId);
                return;
            }
            
            // 使用caseId构建个人病例文档
            PersonalCaseDocument document = caseDataService.buildPersonalCaseDocument(userCase.getUserCaseId());
            
            if (document != null) {
                // 查询数据库获取catalogIds
                List<Long> catalogIds = getCatalogIdsByUserCaseId(userCaseId);
                document.setCatalogIds(catalogIds != null ? catalogIds : new ArrayList<>());
                
                // 保存到ES
                elasticsearchTemplate.save(document, indexCoordinates);
                log.info("成功创建或更新个人病例ES文档，userCaseId: {}", userCaseId);
            } else {
                log.warn("无法构建或更新个人病例文档，userCaseId: {}", userCaseId);
            }
        } catch (Exception e) {
            log.error("创建或更新个人病例ES文档异常，userCaseId: {}", userCaseId, e);
            throw new RuntimeException("创建或更新个人病例ES文档失败", e);
        }
    }

    /**
     * 根据userCaseId获取catalogIds
     */
    private List<Long> getCatalogIdsByUserCaseId(Long userCaseId) {
        try {
            // 查询UserCaseCatalog表
            return userCaseCatalogService.lambdaQuery()
                    .eq(UserCaseCatalog::getUserCaseId, userCaseId)
                    .list()
                    .stream()
                    .map(UserCaseCatalog::getCatalogId)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("根据userCaseId查询catalogIds异常，userCaseId: {}", userCaseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 通过userCaseId删除ES文档
     */
    private void deletePersonalCaseDocumentByUserCaseId(Long userCaseId, IndexCoordinates indexCoordinates) {
        try {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.must(QueryBuilders.termQuery(Constant.ES_USER_INDEX_ID, userCaseId));
            
            Query query = new NativeSearchQuery(queryBuilder);
            elasticsearchTemplate.delete(query, PersonalCaseDocument.class, indexCoordinates);
            log.info("通过userCaseId删除个人病例ES文档成功，userCaseId: {}", userCaseId);
        } catch (Exception e) {
            log.error("通过userCaseId删除个人病例ES文档异常，userCaseId: {}", userCaseId, e);
            throw e;
        }
    }

    /**
     * 同步科室病例数据到ES
     */
    private void syncDepartmentCaseData(Long caseId) {
        try {
            DepartmentCaseDocument document = caseDataService.buildDepartmentCaseDocument(caseId);
            if (document != null) {
                IndexCoordinates indexCoordinates = IndexCoordinates.of(
                        searchConfig.getIndexes().getDepartmentCases());
                elasticsearchTemplate.save(document, indexCoordinates);
                log.debug("科室病例数据同步到ES成功，caseId: {}", caseId);
            } else {
                log.warn("科室病例数据不存在或构建失败，caseId: {}", caseId);
            }
        } catch (Exception e) {
            log.error("同步科室病例数据到ES异常，caseId: {}", caseId, e);
            throw e;
        }
    }

    /**
     * 删除科室病例数据
     */
    private void deleteDepartmentCaseData(Long caseId) {
        try {
            if (caseId == null) {
                log.warn("删除科室病例数据时，caseId为空");
                return;
            }
            IndexCoordinates indexCoordinates = IndexCoordinates.of(
                    searchConfig.getIndexes().getDepartmentCases());
            elasticsearchTemplate.delete(caseId.toString(), indexCoordinates);
            log.debug("删除科室病例ES数据成功，caseId: {}", caseId);
        } catch (Exception e) {
            log.error("删除科室病例ES数据异常，caseId: {}", caseId, e);
            throw e;
        }
    }

    /**
     * 处理同步失败
     */
    private void handleSyncFailure(SyncMessage message, Exception e) {
        try {
            // 记录失败信息到Redis，用于后续重试或监控
            String failureKey = "sync:failure:" + System.currentTimeMillis();
            String failureInfo = String.format("Message: %s, Error: %s", message.toString(), e.getMessage());
            redisTemplate.opsForValue().set(failureKey, failureInfo, 60 * 60 * 24);

            log.error("同步失败信息已记录到Redis，key: {}", failureKey);
        } catch (Exception ex) {
            log.error("记录同步失败信息异常", ex);
        }
    }

    /**
     * 批量同步个人病例
     */
    private void batchSyncPersonalCase(List<Long> userCaseIdList, IndexCoordinates indexCoordinates) {
        try {
            log.info("开始批量同步个人病例，userCaseIds: {}", userCaseIdList);
            List<PersonalCaseDocument> documentList = caseDataService.buildSyncPersonalCasesByUserCaseIds(userCaseIdList);
            if (!CollectionUtils.isEmpty(documentList)) {
                try {
                    elasticsearchTemplate.save(documentList, indexCoordinates);
                    log.info("批量同步个人病例数据成功，处理数量：{}", documentList.size());
                } catch (Exception e) {
                    log.error("批量同步个人病例数据到ES失败，userCaseIds: {}", userCaseIdList, e);
                    throw new RuntimeException("批量同步个人病例数据失败", e);
                }
            } else {
                log.warn("批量同步个人病例数据：没有找到需要同步的数据，userCaseIds: {}", userCaseIdList);
            }
            log.info("批量同步个人病例完成，处理数量: {}", userCaseIdList.size());
        } catch (Exception e) {
            log.error("批量同步个人病例异常，userCaseIds: {}", userCaseIdList, e);
            throw new RuntimeException("批量同步个人病例失败", e);
        }
    }

    /**
     * 批量删除个人病例
     */
    private void batchDeletePersonalCase(List<Long> userCaseIdList, IndexCoordinates indexCoordinates) {
        try {
            log.info("开始批量删除个人病例，userCaseIds: {}", userCaseIdList);
            if (CollectionUtils.isEmpty(userCaseIdList)) {
                log.warn("批量删除个人病例：没有找到需要删除的数据，userCaseIds: {}", userCaseIdList);
                return;
            }
            for (Long userCaseId : userCaseIdList) {
                try {
                    syncDeletePersonalCase(userCaseId, indexCoordinates);
                } catch (Exception e) {
                    log.error("批量删除个人病例失败，userCaseId: {}", userCaseId, e);
                    // 继续处理其他病例，不因单个失败而中断整个批量操作
                }
            }
            
            log.info("批量删除个人病例完成，处理数量: {}", userCaseIdList.size());
        } catch (Exception e) {
            log.error("批量删除个人病例异常，userCaseIds: {}", userCaseIdList, e);
            throw new RuntimeException("批量删除个人病例失败", e);
        }
    }
}