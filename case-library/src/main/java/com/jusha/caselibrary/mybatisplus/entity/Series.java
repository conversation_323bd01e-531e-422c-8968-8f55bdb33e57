package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例序列表
 * <AUTHOR>
 */
@TableName("t_series")
@Data
public class Series {

    @ApiModelProperty(value = "序列UID")
    @TableId("series_uid")
    private String seriesUid;

    @ApiModelProperty(value = "患者id")
    @TableField("patient_id")
    private String patientId;    

    @ApiModelProperty(value = "患者姓名")
    @TableField("patient_name")
    private String patientName;    

    @ApiModelProperty(value = "性别")
    @TableField("patient_sex")
    private String patientSex;    

    @ApiModelProperty(value = "出生日期")
    @TableField("patient_birth_date")
    private String patientBirthDate;    

    @ApiModelProperty(value = "年龄")
    @TableField("patient_age")
    private String patientAge;    

    @ApiModelProperty(value = "检查UID")
    @TableField("study_uid")
    private String studyUid;    

    @ApiModelProperty(value = "检查流水号")
    @TableField("access_number")
    private String accessNumber;

    @ApiModelProperty(value = "序列描述")
    @TableField("series_description")
    private String seriesDescription;    

    @ApiModelProperty(value = "检查模态")
    @TableField("modality")
    private String modality;    

    @ApiModelProperty(value = "是否关键帧: 0-不是 1-是")
    @TableField("is_keyframe")
    private int isKeyframe;

    @ApiModelProperty(value = "删除标志 0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private Long createBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

}
