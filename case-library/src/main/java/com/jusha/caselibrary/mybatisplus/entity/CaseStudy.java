package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例检查关联表
 * <AUTHOR>
 */
@TableName("t_case_study")
@Data
public class CaseStudy {

    @ApiModelProperty(value = "id")
    @TableId("id")
    private long id;

    @ApiModelProperty(value = "检查UID")
    @TableId("study_uid")
    private String studyUid;

    @ApiModelProperty(value = "病例id")
    @TableId("case_id")
    private long caseId;
}
