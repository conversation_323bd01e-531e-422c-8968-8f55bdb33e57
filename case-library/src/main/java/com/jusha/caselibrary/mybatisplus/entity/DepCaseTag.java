package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 科室病例和标签关联表
 * <AUTHOR>
 */
@TableName("t_dep_case_tag")
@Data
public class DepCaseTag {

    @ApiModelProperty(value = "关联id")
    @TableId("id")
    private Long id;    

    @ApiModelProperty(value = "病例id")
    @TableField("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "标签id")
    @TableField("tag_id")
    private Long tagId;    

}
