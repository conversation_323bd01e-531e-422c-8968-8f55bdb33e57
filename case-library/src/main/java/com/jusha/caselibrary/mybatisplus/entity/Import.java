package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导入任务表
 * <AUTHOR>
 */
@TableName("t_import")
@Data
public class Import {

    @ApiModelProperty(value = "病例导入任务ID")
    @TableId("inport_id")
    private Long inportId;    

    @ApiModelProperty(value = "导入任务名称")
    @TableField("inport_name")
    private String inportName;    

    @ApiModelProperty(value = "对应的-导出任务名称")
    @TableField("export_name")
    private String exportName;    

    @ApiModelProperty(value = "导入资源文件ID")
    @TableField("inport_res_id")
    private Long inportResId;    

    @ApiModelProperty(value = "完成百分比")
    @TableField("percent")
    private Integer percent;    

    @ApiModelProperty(value = "导入人")
    @TableField("create_user_id")
    private Long createUserId;    

    @ApiModelProperty(value = "导入时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "联盟分组ID")
    @TableField("lm_gp_id")
    private Long lmGpId;    

    @ApiModelProperty(value = "导入任务状态：0-未开始，1-进行中，2-成功，3-失败")
    @TableField("inport_status")
    private Integer inportStatus;    

}
