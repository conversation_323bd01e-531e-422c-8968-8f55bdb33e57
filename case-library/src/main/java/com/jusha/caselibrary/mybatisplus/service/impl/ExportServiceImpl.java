package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.Export;
import com.jusha.caselibrary.mybatisplus.mapper.ExportMapper;
import com.jusha.caselibrary.mybatisplus.service.ExportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导出任务表 服务实现类
 * <AUTHOR>
 */
@Service
public class ExportServiceImpl extends ServiceImpl<ExportMapper, Export> implements ExportService {

}
