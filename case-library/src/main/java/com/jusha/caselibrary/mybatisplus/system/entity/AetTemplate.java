package com.jusha.caselibrary.mybatisplus.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 对接PACS服务
 * <AUTHOR>
 */
@TableName("t_aet")
@Data
public class AetTemplate {

    @ApiModelProperty(value = "服务器id")
    @TableId(value = "aet_id")
    private Long aetId;

    @ApiModelProperty(value = "服务器名称")
    @TableField("aet_name")
    private String aetName;

    @ApiModelProperty(value = "服务器域名")
    @TableField("aet_host")
    private String aetHost;

    @ApiModelProperty(value = "服务器路径模板")
    @TableField("aet_template")
    private String aetTemplate;
}
