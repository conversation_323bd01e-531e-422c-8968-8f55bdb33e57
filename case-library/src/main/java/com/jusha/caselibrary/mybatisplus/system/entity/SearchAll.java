package com.jusha.caselibrary.mybatisplus.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对接PACS服务
 * <AUTHOR>
 */
@TableName("t_search_all")
@Data
public class SearchAll {

    @ApiModelProperty(value = "360服务器id")
    @TableId(value = "search_all_id")
    private Long searchAllId;

    @ApiModelProperty(value = "360服务器名称")
    @TableField("search_all_name")
    private String searchAllName;

    @ApiModelProperty(value = "360服务器域名")
    @TableField("search_all_host")
    private String searchAllHost;

    @ApiModelProperty(value = "360服务器路径模板")
    @TableField("search_all_template")
    private String searchAllTemplate;
}
