package com.jusha.caselibrary.mybatisplus.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 疾病相关联文件
 * <AUTHOR>
 */
@TableName("t_disease_file")
@Data
public class DiseaseFile {

    @ApiModelProperty(value = "主键")
    @TableId("file_id")
    private Long fileId;

    @ApiModelProperty(value = "疾病id")
    @TableField("disease_id")
    private Long diseaseId;

    @ApiModelProperty(value = "文件名称")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty(value = "文件类型")
    @TableField("file_type")
    private String fileType;

    @ApiModelProperty(value = "文件URL")
    @TableField("file_url")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private Long createdBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_by")
    private Long updateBy;

}
