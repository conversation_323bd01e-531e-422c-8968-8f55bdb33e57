package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人病例目录关联表
 * <AUTHOR>
 */
@TableName("t_user_case_catalog")
@Data
public class UserCaseCatalog {

    @ApiModelProperty(value = "关联id")
    @TableId("id")
    private Long id;    

    @ApiModelProperty(value = "病例id")
    @TableField("user_case_id")
    private Long userCaseId;    

    @ApiModelProperty(value = "目录id")
    @TableField("catalog_id")
    private Long catalogId;    

}
