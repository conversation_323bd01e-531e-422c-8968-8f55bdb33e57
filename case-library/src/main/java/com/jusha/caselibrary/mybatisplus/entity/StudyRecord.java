package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查记录表
 * <AUTHOR>
 */
@TableName("t_study_record")
@Data
public class StudyRecord {

    @ApiModelProperty(value = "主键")
    @TableField("id")
    private long id;

    @ApiModelProperty(value = "检查UID")
    @TableId("study_uid")
    private String studyUid;

    @ApiModelProperty(value = "检查流水号/影像号")
    @TableField("access_number")
    private String accessNumber;    

    @ApiModelProperty(value = "检查时间")
    @TableField("study_time")
    private Date studyTime;

    @ApiModelProperty(value = "RIS中的检查号")
    @TableField("study_no")
    private String studyNo;    

    @ApiModelProperty(value = "患者id")
    @TableField("patient_id")
    private String patientId;    

    @ApiModelProperty(value = "患者姓名")
    @TableField("patient_name")
    private String patientName;    

    @ApiModelProperty(value = "性别")
    @TableField("patient_sex")
    private String patientSex;    

    @ApiModelProperty(value = "出生日期")
    @TableField("patient_birth_date")
    private String patientBirthDate;    

    @ApiModelProperty(value = "年龄")
    @TableField("patient_age")
    private String patientAge;    

    @ApiModelProperty(value = "就诊类型（门诊、住院、体检）")
    @TableField("patient_type")
    private String patientType;

    @ApiModelProperty(value = "就诊时间")
    @TableField("visit_date")
    private String visitDate;    

    @ApiModelProperty(value = "门诊号")
    @TableField("out_patient_no")
    private String outPatientNo;    

    @ApiModelProperty(value = "住院号")
    @TableField("in_patient_no")
    private String inPatientNo;    

    @ApiModelProperty(value = "症状/体征")
    @TableField("physical_sign")
    private String physicalSign;    

    @ApiModelProperty(value = "临床诊断")
    @TableField("clinical_diagnosis")
    private String clinicalDiagnosis;    

    @ApiModelProperty(value = "检查项目名称")
    @TableField("study_item_name")
    private String studyItemName;    

    @ApiModelProperty(value = "检查部位")
    @TableField("part_name")
    private String partName;    

    @ApiModelProperty(value = "设备/检查类型")
    @TableField("device_type")
    private String deviceType;    

    @ApiModelProperty(value = "检查状态：已登记、已检查、已报告、已审核")
    @TableField("study_state")
    private int studyState;

    @ApiModelProperty(value = "设备名称")
    @TableField("device_name")
    private String deviceName;    

    @ApiModelProperty(value = "病史")
    @TableField("medical_history")
    private String medicalHistory;    

    @ApiModelProperty(value = "患者主诉")
    @TableField("self_reported_symptom")
    private String selfReportedSymptom;    

    @ApiModelProperty(value = "影像学表现")
    @TableField("report_describe")
    private String reportDescribe;    

    @ApiModelProperty(value = "影像学诊断")
    @TableField("report_diagnose")
    private String reportDiagnose;    

    @ApiModelProperty(value = "阳性/阴性")
    @TableField("is_postive")
    private int isPostive;

    @ApiModelProperty(value = "登记时间")
    @TableField("register_time")
    private String registerTime;    

    @ApiModelProperty(value = "报告时间")
    @TableField("report_time")
    private String reportTime;    

    @ApiModelProperty(value = "报告医生")
    @TableField("reporter")
    private String reporter;    

    @ApiModelProperty(value = "审核医生")
    @TableField("checker")
    private String checker;    

    @ApiModelProperty(value = "审核时间")
    @TableField("check_time")
    private String checkTime;    

    @ApiModelProperty(value = "申请号")
    @TableField("apply_number")
    private String applyNumber;    

    @ApiModelProperty(value = "申请科室")
    @TableField("apply_department")
    private String applyDepartment;    

    @ApiModelProperty(value = "申请医生")
    @TableField("apply_doctor")
    private String applyDoctor;    

    @ApiModelProperty(value = "技师")
    @TableField("artificer")
    private String artificer;    

    @ApiModelProperty(value = "是否公有")
    @TableField("is_public")
    private String isPublic;    

    @ApiModelProperty(value = "是否在线导入")
    @TableField("isonline")
    private int isonline;

    @ApiModelProperty(value = "导入状态，0无需导入，1正在导入，2导入完成，3导入失败")
    @TableField("is_export")
    private int isExport;

    @ApiModelProperty(value = "关联状态，0未关联，1已关联")
    @TableField("association")
    private int association;

    @ApiModelProperty(value = "删除标志 0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private Long createBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private String modalities;

}
