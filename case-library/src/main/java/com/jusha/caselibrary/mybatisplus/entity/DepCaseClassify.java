package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 科室病例库分类关联表
 * <AUTHOR>
 */
@TableName("t_dep_case_classify")
@Data
public class DepCaseClassify {

    @ApiModelProperty(value = "关联id")
    @TableId("id")
    private Long id;    

    @ApiModelProperty(value = "病例id")
    @TableField("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "病例类型id")
    @TableField("case_type_id")
    private Long caseTypeId;    

}
