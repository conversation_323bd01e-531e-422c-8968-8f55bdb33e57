package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseMapper;
import com.jusha.caselibrary.mybatisplus.service.UserCaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 个人病例表 服务实现类
 * <AUTHOR>
 */
@Service
public class UserCaseServiceImpl extends ServiceImpl<UserCaseMapper, UserCase> implements UserCaseService {

}
