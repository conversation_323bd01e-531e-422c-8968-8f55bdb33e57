package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户表头设置表
 * <AUTHOR>
 */
@TableName("t_user_table")
@Data
public class UserTable {

    @ApiModelProperty(value = "id")
    @TableId("id")
    private Long id;    

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private Long userId;    

    @ApiModelProperty(value = "表格类型")
    @TableField("type")
    private Integer type;    

    @ApiModelProperty(value = "表格设置json")
    @TableField("set_data")
    private String setData;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

}
