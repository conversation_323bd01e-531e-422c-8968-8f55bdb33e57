package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导入文件上传
 * <AUTHOR>
 */
@TableName("t_import_upload")
@Data
public class ImportUpload {

    @ApiModelProperty(value = "导入id")
    @TableField("inport_id")
    private Long inportId;    

    @ApiModelProperty(value = "文件md5")
    @TableField("md5")
    private String md5;    

    @ApiModelProperty(value = "分块数量")
    @TableField("chunks")
    private Integer chunks;    

    @ApiModelProperty(value = "当前块")
    @TableField("chunk")
    private Integer chunk;    

    @ApiModelProperty(value = "0-未完成，1-完成")
    @TableField("finished")
    private Integer finished;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

}
