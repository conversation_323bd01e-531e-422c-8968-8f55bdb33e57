package com.jusha.caselibrary.mybatisplus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jusha.caselibrary.mybatisplus.entity.StudyRecord;
import com.jusha.caselibrary.mybatisplus.mapper.StudyRecordMapper;
import com.jusha.caselibrary.mybatisplus.service.StudyRecordService;
import org.springframework.stereotype.Service;

/**
 * 检查记录表 服务实现类
 * <AUTHOR>
 */
@Service
public class StudyRecordServiceImpl extends ServiceImpl<StudyRecordMapper, StudyRecord> implements StudyRecordService {

}
