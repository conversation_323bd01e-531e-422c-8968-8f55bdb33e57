package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例疾病关联表
 * <AUTHOR>
 */
@TableName("t_dep_case_disease")
@Data
public class DepCaseDisease {

    @ApiModelProperty(value = "id")
    @TableId("id")
    private long id;

    @ApiModelProperty(value = "病例id")
    @TableField("case_id")
    private long caseId;

    @ApiModelProperty(value = "疾病id")
    @TableField(value = "disease_id")
    private long diseaseId;

}
