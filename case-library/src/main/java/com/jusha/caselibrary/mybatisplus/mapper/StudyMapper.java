package com.jusha.caselibrary.mybatisplus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jusha.caselibrary.mybatisplus.entity.Study;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 病例检查表 Mapper 接口
 * <AUTHOR>
 */
public interface StudyMapper extends BaseMapper<Study> {
    List<Study> queryNotRelateDicom(@Param("userId") long userId, @Param("keyword")String keyword);
}
