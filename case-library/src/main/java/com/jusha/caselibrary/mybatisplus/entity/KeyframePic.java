package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 关键帧序列的关联图片
 * <AUTHOR>
 */
@TableName("t_keyframe_pic")
@Data
public class KeyframePic {

    @ApiModelProperty(value = "关键帧图片ID")
    @TableId("kf_pic_id")
    private Long kfPicId;    

    @ApiModelProperty(value = "(0020,000E)#序列UID")
    @TableField("series_uid")
    private String seriesUid;    

    @ApiModelProperty(value = "(0008,0018)#实例UID")
    @TableField("instance_uid")
    private String instanceUid;    

    @ApiModelProperty(value = "(0020,000D)#检查UID")
    @TableField("study_uid")
    private String studyUid;    

    @ApiModelProperty(value = "图片格式")
    @TableField("mime_type")
    private String mimeType;    

    @ApiModelProperty(value = "关联资源ID--")
    @TableField("resource_id")
    private Long resourceId;    

    @ApiModelProperty(value = "文件访问相对路径")
    @TableField("file_url")
    private String fileUrl;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

}
