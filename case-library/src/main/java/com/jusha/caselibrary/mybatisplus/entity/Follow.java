package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 随访表
 * <AUTHOR>
 */
@TableName("t_follow")
@Data
public class Follow {

    @ApiModelProperty(value = "随访id")
    @TableId("follow_id")
    private Long followId;    

    @ApiModelProperty(value = "病例id")
    @TableField("case_id")
    private Long caseId;

    @ApiModelProperty(value = "检查UID")
    @TableField("study_uid")
    private String studyUid;

    @ApiModelProperty(value = "随访结果，富文本")
    @TableField("followup_result")
    private String followupResult;    

    @ApiModelProperty(value = "随访类型：手术，超声，临床，病理 字典")
    @TableField("follow_type")
    private String followType;

    @ApiModelProperty(value = "删除标志0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private Long createdBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

}
