package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.UserCaseTag;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseTagMapper;
import com.jusha.caselibrary.mybatisplus.service.UserCaseTagService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 个人病例和标签关联表 服务实现类
 * <AUTHOR>
 */
@Service
public class UserCaseTagServiceImpl extends ServiceImpl<UserCaseTagMapper, UserCaseTag> implements UserCaseTagService {

}
