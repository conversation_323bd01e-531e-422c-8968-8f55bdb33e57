package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.ExportDetail;
import com.jusha.caselibrary.mybatisplus.mapper.ExportDetailMapper;
import com.jusha.caselibrary.mybatisplus.service.ExportDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导出详情表 服务实现类
 * <AUTHOR>
 */
@Service
public class ExportDetailServiceImpl extends ServiceImpl<ExportDetailMapper, ExportDetail> implements ExportDetailService {

}
