package com.jusha.caselibrary.sickcase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName FollowInfo
 * @Description 随访信息
 * <AUTHOR>
 * @Date 2025/7/8 17:25
 **/
@Data
public class FollowInfo {

    @ApiModelProperty(value = "随访ID")
    private Long followId;

    @ApiModelProperty(value = "随访类型")
    private String followType;

    @ApiModelProperty(value = "随访类型标签")
    private String followTypeLabel;

    @ApiModelProperty(value = "随访结果")
    private String followupResult;

    @ApiModelProperty(value = "随访时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime createTime;

}
