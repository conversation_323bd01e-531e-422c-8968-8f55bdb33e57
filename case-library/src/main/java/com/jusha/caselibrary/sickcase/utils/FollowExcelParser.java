package com.jusha.caselibrary.sickcase.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.jusha.caselibrary.sickcase.dto.ExcelFollowRow;
import com.jusha.caselibrary.sickcase.dto.resp.FollowExcelImportResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName FollowExcelParser
 * @Description 随访Excel解析工具类（使用EasyExcel高性能解析）
 * <AUTHOR>
 * @Date 2025/7/18
 **/
@Slf4j
public class FollowExcelParser {

    /**
     * 解析Excel文件
     */
    public FollowExcelImportResp parseExcel(MultipartFile file) {
        FollowExcelImportResp response = new FollowExcelImportResp();
        long startTime = System.currentTimeMillis();
        
        try {
            if (file == null || file.isEmpty()) {
                response.addErrorMessage("文件为空");
                return response;
            }
            
            String fileName = file.getOriginalFilename();
            if (StringUtils.isBlank(fileName) || 
                (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                response.addErrorMessage("文件格式不正确，请上传Excel文件");
                return response;
            }
            
            // 使用EasyExcel解析
            FollowExcelListener listener = new FollowExcelListener(response);
            EasyExcel.read(file.getInputStream(), ExcelFollowRow.class, listener)
                    .sheet()
                    // 跳过表头
                    .headRowNumber(1)
                    .doRead();
            
            long endTime = System.currentTimeMillis();
            response.setProcessingTime(endTime - startTime);
            response.calculateSuccess();
            
            log.info("Excel解析完成: {}", response.getSummary());
            
            return response;
            
        } catch (Exception e) {
            log.error("解析Excel文件时发生异常", e);
            response.addErrorMessage("解析Excel文件失败");
            response.setProcessingTime(System.currentTimeMillis() - startTime);
            return response;
        }
    }
    
    /**
     * 获取有效的行数据（用于异步处理）
     */
    public List<ExcelFollowRow> getValidRows(MultipartFile file) {
        List<ExcelFollowRow> validRows = new ArrayList<>();
        
        try {
            if (file == null || file.isEmpty()) {
                return validRows;
            }
            
            String fileName = file.getOriginalFilename();
            if (StringUtils.isBlank(fileName) || 
                (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                return validRows;
            }
            
            // 使用EasyExcel解析，只获取有效行
            ValidRowsListener listener = new ValidRowsListener(validRows);
            EasyExcel.read(file.getInputStream(), ExcelFollowRow.class, listener)
                    .sheet()
                    .headRowNumber(1)
                    .doRead();
            
        } catch (Exception e) {
            log.error("获取有效行数据失败", e);
        }
        
        return validRows;
    }
    
    /**
     * Excel读取监听器
     */
    private static class FollowExcelListener implements ReadListener<ExcelFollowRow> {
        
        private final FollowExcelImportResp response;
        private int currentRow = 1; // 从第1行开始（跳过表头）
        
        public FollowExcelListener(FollowExcelImportResp response) {
            this.response = response;
        }
        
        @Override
        public void invoke(ExcelFollowRow data, AnalysisContext context) {
            currentRow++;
            response.incrementTotalRows();
            
            try {
                data.setRowIndex(currentRow);
                String validationError = validateRowData(data);
                
                if (validationError != null) {
                    response.addErrorMessage(String.format("第%d行: %s", currentRow, validationError));
                    response.incrementErrorRows();
                } else {
                    response.incrementSuccessRows();
                }
            } catch (Exception e) {
                log.error("处理第{}行数据时发生异常", currentRow, e);
                response.addErrorMessage(String.format("第%d行: 处理异常 - %s", currentRow, e.getMessage()));
                response.incrementErrorRows();
            }
        }
        
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("Excel解析完成，共处理{}行数据", currentRow - 1);
        }
    }
    
    /**
     * 有效行数据监听器
     */
    private static class ValidRowsListener implements ReadListener<ExcelFollowRow> {
        
        private final List<ExcelFollowRow> validRows;
        private int currentRow = 1;
        
        public ValidRowsListener(List<ExcelFollowRow> validRows) {
            this.validRows = validRows;
        }
        
        @Override
        public void invoke(ExcelFollowRow data, AnalysisContext context) {
            currentRow++;
            
            try {
                data.setRowIndex(currentRow);
                String validationError = validateRowData(data);
                
                if (validationError == null) {
                    validRows.add(data);
                }
            } catch (Exception e) {
                log.error("处理第{}行数据时发生异常", currentRow, e);
            }
        }
        
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("获取有效行数据完成，共{}行有效数据", validRows.size());
        }
    }
    
    /**
     * 验证行数据
     */
    private static String validateRowData(ExcelFollowRow data) {
        if (data == null) {
            return "数据为空";
        }
        
        // 检查影像号
        if (data.getAccessNumber() == null) {
            return "影像号不能为空";
        }

        // 检查随访类型
        if (StringUtils.isBlank(data.getFollowType())) {
            return "随访类型不能为空";
        }

        // 检查随访结果
        if (StringUtils.isBlank(data.getFollowupResult())) {
            return "随访结果不能为空";
        }

        // 验证通过
        return null;
    }
}