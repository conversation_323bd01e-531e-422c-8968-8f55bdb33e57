package com.jusha.caselibrary.sickcase.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.aop.NoDuplicate;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.RedisUtil;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.CaseExportProcessResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.FollowExcelImportResp;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.task.CaseDetailExportTask;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName DeptCaseController
 * @Description 科室病例管理
 * <AUTHOR>
 * @Date 2025/7/10 09:27
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dept/case")
@Api(tags = "病例管理-科室病例库管理")
public class DeptCaseController {

    private final DeptCaseService deptCaseService;

    private final AdvancedSearchService advancedSearchService;

    @Value("${TMP-LOCATIONS}")
    private String tmpPath;

    @ApiOperation("科室病例库高级检索")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResultBean<PageInfo<DeptCaseDetailResp>> advanced (@Validated @RequestBody DeptSearchReq req) {
        PageInfo<DeptCaseDetailResp> response = advancedSearchService.searchAndConvertPage(req, DeptCaseDetailResp.class);
        return ResultBean.success(response);
    }

    @EscapeWildcard
    @ApiOperation("科室病例库详细列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultBean<List<DeptCaseDetailResp>> list (@Validated @RequestBody DeptCaseSearchReq req) {
        List<DeptCaseDetailResp> resp = deptCaseService.getDeptCaseDetailList(req);
        return ResultBean.success(resp);
    }

    @EscapeWildcard
    @ApiOperation("科室病例库详细列表分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResultBean<PageInfo<DeptCaseDetailResp>> page (@Validated @RequestBody DeptCaseSearchReq req) {
        PageInfo<DeptCaseDetailResp> resp = deptCaseService.getDeptCaseDetailPage(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("科室病例库病例详情查询")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<DeptCaseDetailResp> detail (@RequestParam("caseId") Long caseId) {
        DeptCaseDetailResp resp = deptCaseService.getDeptCaseDetail(caseId);
        return ResultBean.success(resp);
    }

    @ApiOperation("科室病例库病例删除")
    @NoDuplicate(keys = {"#deptCaseDeleteReq.caseId"})
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete (@Validated @RequestBody DeptCaseDeleteReq deptCaseDeleteReq) {
        deptCaseService.deleteDeptCaseById(deptCaseDeleteReq.getCaseId(), deptCaseDeleteReq.getCaseTypeId());
        return ResultBean.success();
    }

    @ApiOperation("科室病例库病例导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    public ResultBean<String> export (@Validated @RequestBody DeptCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<CaseExportDataDto, DeptCaseSearchReq>(taskId, req, tmpPath));
        return ResultBean.success(taskId);
    }

    @ApiOperation("科室病例库随访病例导出")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/follow/export", method = RequestMethod.POST)
    public ResultBean<String> followExport (@Validated @RequestBody DeptCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<FollowCaseExportDataDto, DeptCaseSearchReq>(taskId, req, tmpPath));
        return ResultBean.success(taskId);
    }

    @ApiOperation("获取科室病例库病例导出任务进度")
    @RequestMapping(value = "/export/progress", method = RequestMethod.GET)
    public ResultBean<CaseExportProcessResp> exportProgress (@RequestParam("taskId") String taskId) {
        CaseExportProcessResp resp = null;
        String taskJson = ContextHolder.stringRedisTemplate().opsForValue().get(RedisUtil.caseExportTaskKey(taskId));
        if (StringUtils.isNotBlank(taskJson)) {
            resp = JSON.parseObject(taskJson, CaseExportProcessResp.class);
        }
        return ResultBean.success(resp);
    }

    @ApiOperation("新增科室病例库病例（基本信息）")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Long> create (@Validated @RequestBody DeptCaseCreateReq req) {
        long caseId = deptCaseService.createDeptCase(req);
        return ResultBean.success(caseId);
    }

    @ApiOperation("修改科室病例库病例（基本信息）")
    @NoDuplicate(keys = {"#req.caseId"})
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public ResultBean<Void> update (@Validated @RequestBody DeptCaseUpdateReq req) {
        deptCaseService.updateDeptCase(req);
        return ResultBean.success();
    }

    @ApiOperation("发送个人病例到科室病例库")
    @NoDuplicate(keys = {"#req.userCaseId"})
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    @RequestMapping(value = "/send", method = RequestMethod.POST)
    public ResultBean<Void> send (@Validated @RequestBody DeptCaseSendReq req) {
        deptCaseService.sendCaseToDept(req);
        return ResultBean.success();
    }

    @ApiOperation("查询待审核病例列表")
    @RequestMapping(value = "/verify/list", method = RequestMethod.POST)
    public ResultBean<PageInfo<AuditCaseDetailResp>> verifyCaseList (@Validated @RequestBody AuditCaseSearchReq req) {
        PageInfo<AuditCaseDetailResp> resp = deptCaseService.getAuditCaseList(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("查询我创建的病例列表")
    @RequestMapping(value = "/verify/my/list", method = RequestMethod.POST)
    public ResultBean<PageInfo<AuditCaseDetailResp>> myVerifyCaseList (@Validated @RequestBody AuditCaseSearchReq req) {
        PageInfo<AuditCaseDetailResp> resp = deptCaseService.getMyAuditCaseList(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("审核病例")
    @NoDuplicate(keys = {"#req.auditId"})
    @RequestMapping(value = "/verify", method = RequestMethod.POST)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public ResultBean<Void> verify (@Validated @RequestBody CaseAuditReq req) {
        deptCaseService.verifyCase(req);
        return ResultBean.success();
    }

    @ApiOperation("获取病例标签列表")
    @RequestMapping(value = "/tag/list", method = RequestMethod.GET)
    public ResultBean<List<TagInfo>> tagList () {
        List<TagInfo> resp = deptCaseService.getCaseTagList();
        return ResultBean.success(resp);
    }

    @ApiOperation("病例打标签")
    @NoDuplicate(keys = {"#req.id"})
    @RequestMapping(value = "/tag", method = RequestMethod.POST)
    public ResultBean<Void> tag (@Validated @RequestBody CaseTagReq req) {
        req.setCaseId(req.getId());
        deptCaseService.tagCase(req);
        return ResultBean.success();
    }

    @ApiOperation("病例随访")
    @NoDuplicate(keys = {"#req.caseId"})
    @RequestMapping(value = "/follow", method = RequestMethod.POST)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public ResultBean<Void> follow (@Validated @RequestBody CaseFollowReq req) {
        deptCaseService.followCase(req);
        return ResultBean.success();
    }

    @ApiOperation("修改病例随访")
    @NoDuplicate(keys = {"#req.followId"})
    @RequestMapping(value = "/follow/update", method = RequestMethod.POST)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public ResultBean<Void> updateFollow (@Validated @RequestBody CaseFollowUpdateReq req) {
        deptCaseService.updateFollow(req);
        return ResultBean.success();
    }

    @ApiOperation("Excel批量导入随访记录")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/importFollowExcel", method = RequestMethod.POST)
    public ResultBean<FollowExcelImportResp> importFollowExcel (@RequestParam("file") MultipartFile file) {
        FollowExcelImportResp resp = deptCaseService.importFollowExcel(file);
        return ResultBean.success(resp);
    }

}
