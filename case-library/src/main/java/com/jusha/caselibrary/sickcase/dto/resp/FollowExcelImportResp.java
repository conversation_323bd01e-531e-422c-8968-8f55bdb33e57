package com.jusha.caselibrary.sickcase.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName FollowExcelImportResp
 * @Description 随访Excel导入响应类（简化版）
 * <AUTHOR>
 * @Date 2025/7/18
 **/
@Data
public class FollowExcelImportResp {

    @ApiModelProperty(value = "导入是否成功")
    private Boolean success;

    @ApiModelProperty(value = "总行数")
    private Integer totalRows;

    @ApiModelProperty(value = "成功导入行数")
    private Integer successRows;

    @ApiModelProperty(value = "失败行数")
    private Integer errorRows;

    @ApiModelProperty(value = "错误信息汇总")
    private List<String> errorMessages;

    @ApiModelProperty(value = "处理耗时(毫秒)")
    private Long processingTime;

    @ApiModelProperty(value = "被导入的科室病例Id")
    private List<Long> caseIdList;

    @ApiModelProperty(value = "被导入的个人病例库的病例Id")
    private List<Long> userCaseIdList;

    public FollowExcelImportResp() {
        this.errorMessages = new ArrayList<>();
        this.totalRows = 0;
        this.successRows = 0;
        this.errorRows = 0;
        this.success = false;
    }

    /**
     * 添加错误信息
     */
    public void addErrorMessage(String errorMessage) {
        this.errorMessages.add(errorMessage);
    }

    /**
     * 增加总行数
     */
    public void incrementTotalRows() {
        this.totalRows++;
    }

    /**
     * 增加成功行数
     */
    public void incrementSuccessRows() {
        this.successRows++;
    }

    /**
     * 增加错误行数
     */
    public void incrementErrorRows() {
        this.errorRows++;
    }

    /**
     * 计算总体成功状态
     */
    public void calculateSuccess() {
        this.success = this.errorRows == 0 && this.successRows > 0;
    }

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (totalRows == 0) {
            return 0.0;
        }
        return (double) successRows / totalRows * 100;
    }

    /**
     * 是否有错误
     */
    public Boolean hasErrors() {
        return this.errorRows > 0 || !this.errorMessages.isEmpty();
    }

    /**
     * 获取导入摘要信息
     */
    public String getSummary() {
        return String.format("总计%d行，成功%d行，失败%d行，成功率%.1f%%", 
                totalRows, successRows, errorRows, getSuccessRate());
    }
}