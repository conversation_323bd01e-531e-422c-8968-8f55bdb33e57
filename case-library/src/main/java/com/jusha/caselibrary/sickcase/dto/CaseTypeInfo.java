package com.jusha.caselibrary.sickcase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName CaseTypeInfo
 * @Description 病例类型信息
 * <AUTHOR>
 * @Date 2025/7/29 10:13
 **/
@Data
public class CaseTypeInfo {

    @ApiModelProperty(value = "病例类型id")
    private Long caseTypeId;

    @ApiModelProperty(value = "病例类型名称")
    private String caseTypeName;

    @ApiModelProperty(value = "是否需要审核0不需要1需要")
    private String audit;

    @ApiModelProperty(value = "病例库地址")
    private String address;

}
