package com.jusha.caselibrary.sickcase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName AuditInfo
 * @Description 审核信息
 * <AUTHOR>
 * @Date 2025/7/29 09:19
 **/
@Data
public class AuditInfo {

    @ApiModelProperty(value = "审核ID")
    private Long auditId;

    @ApiModelProperty(value = "病例id")
    private Long caseId;

    @ApiModelProperty(value = "病例类型id")
    private Long caseTypeId;

    @ApiModelProperty(value = "审核类型：RIS导入，本地导入，随访转其他，个人转科室")
    private String auditType;

    @ApiModelProperty(value = "审核类型：RIS导入，本地导入，随访转其他，个人转科室")
    private String auditTypeLabel;

    @ApiModelProperty(value = "发布人")
    private String pubUserId;

    @ApiModelProperty(value = "发布人姓名，以逗号分割")
    private String pubUserName;

    @ApiModelProperty(value = "发布时间")
    private Date pubTime;

    @ApiModelProperty(value = "审核人")
    private Long auditedBy;

    @ApiModelProperty(value = "审核人姓名")
    private String auditedUserName;

    @ApiModelProperty(value = "审核时间")
    private Date auditedTime;

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    private String status;

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    private String statusLabel;

    @ApiModelProperty(value = "审核意见")
    private String auditComment;
}
