package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName DeptCaseSendReq
 * @Description 发送个人病例到科室病例库请求实体
 * <AUTHOR>
 * @Date 2025/7/28 10:19
 **/
@Data
public class DeptCaseSendReq {

    @ApiModelProperty(value = "个人病例Id")
    @NotNull(message = "个人病例Id不能为空")
    private Long userCaseId;

    @ApiModelProperty(value = "疾病id列表")
    private List<Long> diseaseIdList;

    @ApiModelProperty(value = "病例Id")
    private Long caseId;

    @ApiModelProperty(value = "病例库类型Id")
    @NotNull(message = "病例库类型Id不能为空")
    private Long caseTypeId;

    @ApiModelProperty(value = "标签ID列表")
    private List<Long> tagIdList;
}
