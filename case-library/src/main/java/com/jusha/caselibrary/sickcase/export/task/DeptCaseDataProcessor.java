package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DeptCaseDataProcessor
 * @Description 科室病例数据处理器实现
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Slf4j
public class DeptCaseDataProcessor implements CaseDataProcessor<DeptCaseDetailResp> {
    
    @Override
    public List<DeptCaseDetailResp> processData(List<DeptCaseDetailResp> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            log.info("科室病例数据列表为空，无需处理");
            return new ArrayList<>();
        }
        
        List<DeptCaseDetailResp> processedList = new ArrayList<>();
        
        for (DeptCaseDetailResp data : rawDataList) {
            try {
                if (validateData(data)) {
                    DeptCaseDetailResp processedData = cleanAndFormatData(data);
                    if (processedData != null) {
                        processedList.add(processedData);
                    }
                } else {
                    log.warn("科室病例数据验证失败，跳过处理，病例ID: {}", 
                            data != null ? data.getCaseId() : "null");
                }
            } catch (Exception e) {
                log.error("处理科室病例数据失败，病例ID: {}, 错误: {}", 
                        data != null ? data.getCaseId() : "null", e.getMessage(), e);
                // 继续处理其他数据，不中断整个流程
            }
        }
        
        log.info("科室病例数据处理完成，原始数量: {}, 处理后数量: {}", 
                rawDataList.size(), processedList.size());
        
        return processedList;
    }
    
    @Override
    public DeptCaseDetailResp cleanAndFormatData(DeptCaseDetailResp data) {
        if (data == null) {
            return null;
        }
        
        try {
            // 清理空值和格式化文本
            if (isEmpty(data.getPatientName())) {
                data.setPatientName("");
            }
            
            if (isEmpty(data.getPatientSex())) {
                data.setPatientSex("");
            }
            
            // 格式化长文本，移除多余的空白字符
            if (isNotEmpty(data.getMedicalHistory())) {
                data.setMedicalHistory(cleanText(data.getMedicalHistory()));
            }
            
            if (isNotEmpty(data.getCaseAnalysis())) {
                data.setCaseAnalysis(cleanText(data.getCaseAnalysis()));
            }
            
            if (isNotEmpty(data.getDiagnosis())) {
                data.setDiagnosis(cleanText(data.getDiagnosis()));
            }
            
            // 使用字典标签值替换原始字典值
            if (isNotBlank(data.getDifficultyLabel())) {
                data.setDifficulty(data.getDifficultyLabel());
            }
            
            if (isNotBlank(data.getCaseCategoryLabel())) {
                data.setCaseCategory(data.getCaseCategoryLabel());
            }
            
            if (isNotBlank(data.getSourceTypeLabel())) {
                data.setSourceType(data.getSourceTypeLabel());
            }
            
            if (isNotBlank(data.getFollowStatusLabel())) {
                data.setFollowStatus(data.getFollowStatusLabel());
            }
            
            // 处理StudyInfoDto中的字典标签值
            if (data.getStudyInfoList() != null && !data.getStudyInfoList().isEmpty()) {
                data.getStudyInfoList().forEach(study -> {
                    if (isNotBlank(study.getPatientTypeLabel())) {
                        study.setPatientType(study.getPatientTypeLabel());
                    }
                    if (isNotBlank(study.getStudyStateLabel())) {
                        study.setStudyState(study.getStudyStateLabel());
                    }
                    if (isNotBlank(study.getIsPostiveLabel())) {
                        study.setIsPostive(study.getIsPostiveLabel());
                    }
                    if (isNotBlank(study.getIsPublicLabel())) {
                        study.setIsPublic(study.getIsPublicLabel());
                    }
                    if (isNotBlank(study.getQualityMatchLabel())) {
                        study.setQualityMatch(study.getQualityMatchLabel());
                    }
                    if (isNotBlank(study.getPositionMatchLabel())) {
                        study.setPositionMatch(study.getPositionMatchLabel());
                    }
                    // 处理FollowInfoDto中的字典标签值
                    if (study.getFollowInfoList() != null && !study.getFollowInfoList().isEmpty()) {
                        study.getFollowInfoList().forEach(follow -> {
                            if (isNotBlank(follow.getFollowTypeLabel())) {
                                follow.setFollowType(follow.getFollowTypeLabel());
                            }
                        });
                    }
                });
            }
            
            return data;
            
        } catch (Exception e) {
            log.error("清洗和格式化科室病例数据失败，病例ID: {}, 错误: {}", 
                    data.getCaseId(), e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }
    
    @Override
    public boolean validateData(DeptCaseDetailResp data) {
        if (data == null) {
            return false;
        }
        
        // 科室病例特定的验证逻辑
        if (data.getCaseId() == null) {
            log.warn("科室病例ID为空，数据无效");
            return false;
        }
        
        return true;
    }
    
    @Override
    public Class<DeptCaseDetailResp> getSupportedDataType() {
        return DeptCaseDetailResp.class;
    }
    
    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (isEmpty(text)) {
            return "";
        }
        // 移除多余的空白字符，保留换行符
        return text.trim().replaceAll("[ \\t]+", " ");
    }
    
    /**
     * 判断字符串是否为空或空白
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空且不为空白
     */
    private boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 判断字符串是否不为空白
     */
    private boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }
}