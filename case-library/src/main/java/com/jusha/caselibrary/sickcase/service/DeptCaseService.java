package com.jusha.caselibrary.sickcase.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.sickcase.dto.AuditInfo;
import com.jusha.caselibrary.sickcase.dto.CaseFollowImport;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.FollowExcelImportResp;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @ClassName DeptCaseService
 * @Description 科室病例管理服务接口
 * <AUTHOR>
 * @Date 2025/7/10 09:31
 **/
public interface DeptCaseService {

    /**
     * @description 科室病例库详细列表
     * <AUTHOR>
     * @date 2025/7/10 10:59
     * @param req
     * @return List<DeptCaseDetailResp>
     **/
    List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req);

    /**
     * @description 科室病例库详细列表分页
     * <AUTHOR>
     * @date 2025/7/10 11:39
     * @param req
     * @return PageInfo<DeptCaseDetailResp>
     **/
    PageInfo<DeptCaseDetailResp> getDeptCaseDetailPage(DeptCaseSearchReq req);

    /**
     * @description 科室病例库病例详情查询
     * <AUTHOR>
     * @date 2025/7/10 11:39
     * @param caseId
     * @return DeptCaseDetailResp
     **/
    DeptCaseDetailResp getDeptCaseDetail(Long caseId, Long caseTypeId);

    /**
     * @param caseId
     * @param caseTypeId
     * @return void
     * @description 科室病例库病例删除
     * <AUTHOR>
     * @date 2025/7/10 11:47
     **/
    void deleteDeptCaseById(Long caseId, Long caseTypeId);

    /**
     * @description 新增科室病例库病例（基本信息）
     * <AUTHOR>
     * @date 2025/7/14 11:50
     * @param req
     * @return long
     **/
    long createDeptCase(DeptCaseCreateReq req);

    /**
     * @description 修改科室病例库病例（基本信息）
     * <AUTHOR>
     * @date 2025/7/14 11:53
     * @param req
     * @return void
     **/
    void updateDeptCase(DeptCaseUpdateReq req);

    /**
     * @description 审核病例列表
     * <AUTHOR>
     * @date 2025/7/15 11:03
     * @param req
     * @return PageInfo<AuditCaseDetailResp>
     **/
    PageInfo<AuditCaseDetailResp> getAuditCaseList(AuditCaseSearchReq req);

    /**
     * @description 我的审核病例列表
     * <AUTHOR>
     * @date 2025/7/15 11:35
     * @param req
     * @return PageInfo<AuditCaseDetailResp>
     **/
    PageInfo<AuditCaseDetailResp> getMyAuditCaseList(AuditCaseSearchReq req);

    /**
     * @description 审核病例
     * <AUTHOR>
     * @date 2025/7/15 14:00
     * @param req
     * @return void
     **/
    void verifyCase(CaseAuditReq req);

    /**
     * @description 获取病例标签列表
     * <AUTHOR>
     * @date 2025/7/16 08:57
     * @return List<TagInfo>
     **/
    List<TagInfo> getCaseTagList();

    /**
     * @description 病例打标签
     * <AUTHOR>
     * @date 2025/7/16 08:55
     * @param req
     * @return void
     **/
    void tagCase(DeptCaseTagReq req);

    /**
     * @description 修改病例随访
     * <AUTHOR>
     * @date 2025/7/17 10:27
     * @param req
     * @return void
     **/
    void updateFollow(CaseFollowUpdateReq req);


    /**
     * @param req
     * @param caseIdList
     * @param userCaseIdList
     * @return void
     * @description 导入随访记录
     * <AUTHOR>
     * @date 2025/7/18 10:36
     **/
    void importFollow(List<CaseFollowImport> req, Map<Integer, String> resultMap,
                      List<Long> caseIdList, List<Long> userCaseIdList);

    /**
     * @description Excel批量导入随访记录
     * <AUTHOR>
     * @date 2025/7/18 11:38
     * @param file Excel文件
     * @return FollowExcelImportResp
     **/
    FollowExcelImportResp importFollowExcel(MultipartFile file);

    /**
     * @description 发送个人病例到科室病例库
     * <AUTHOR>
     * @date 2025/7/28 10:21
     * @param req
     * @return void
     **/
    long sendCaseToDept(DeptCaseSendReq req);

    /**
     * @description 再次提交审核
     * <AUTHOR>
     * @date 2025/8/6 14:27
     * @param caseId
     * @param caseTypeId
     * @return void
     **/
    void reSubmitVerify(Long caseId, Long caseTypeId);

    /**
     * @description 获取病例审核历史记录
     * <AUTHOR>
     * @date 2025/8/6 15:29
     * @param caseId
     * @param caseTypeId
     * @return List<AuditInfo>
     **/
    List<AuditInfo> getAuditRecordList(Long caseId, Long caseTypeId);
}
