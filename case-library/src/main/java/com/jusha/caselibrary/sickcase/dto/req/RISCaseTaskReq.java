package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 从RIS导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class RISCaseTaskReq {

    @ApiModelProperty(value = "从RIS导入病例的影像导入任务集合")
    private List<String> taskIds;
}
