package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 从RIS导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class QueryCaseExistReq {

    @ApiModelProperty("从RIS查询出来的参数")
    private List<StudyInfo> StudyInfoList;

    @ApiModelProperty("导入目的地，个人病例库1，科室病例库2")
    private int targetLibrary;

    @Data
    public static class StudyInfo {

        private String patientId;

        private String studyUid;
    }
}
