package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询是否已存在该病人下的病例
 * @date 2025/7/15
 */

@ApiModel
@Data
public class QueryCaseExistReq {

    @ApiModelProperty("从RIS查询出来的参数或者上传的dicom影像读取的数据")
    @NotNull(message = "病人ID集合不能为空")
    private List<String> patientIds;

    @ApiModelProperty("病例类型id")
    private Long caseTypeId;
}
