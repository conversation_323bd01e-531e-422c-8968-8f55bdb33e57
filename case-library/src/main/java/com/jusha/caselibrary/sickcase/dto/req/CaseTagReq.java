package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName CaseTagReq
 * @Description 病例打标签请求实体
 * <AUTHOR>
 * @Date 2025/7/16 08:50
 **/
@Data
public class CaseTagReq {

    @ApiModelProperty(value = "病例Id")
    @NotNull(message = "病例Id或个人病例Id不能为空")
    private Long id;

    @ApiModelProperty(value = "个人病例Id", hidden = true)
    private Long userCaseId;

    @ApiModelProperty(value = "科室病例Id", hidden = true)
    private Long caseId;

    @ApiModelProperty(value = "标签列表")
    @NotNull(message = "标签列表不能为空")
    private List<String> tagList;
}
