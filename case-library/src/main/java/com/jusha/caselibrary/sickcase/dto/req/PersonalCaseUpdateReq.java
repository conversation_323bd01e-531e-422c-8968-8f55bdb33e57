package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName PersonalCaseUpdateReq
 * @Description 个人病例库修改请求实体
 * <AUTHOR>
 * @Date 2025/7/11
 **/
@Data
@Validated
public class PersonalCaseUpdateReq {

    @ApiModelProperty(value = "目录ID")
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;

    @ApiModelProperty(value = "个人病例Id")
    @NotNull(message = "个人病例Id不能为空")
    private Long userCaseId;

    @ApiModelProperty(value = "疾病id列表")
    private List<Long> diseaseIdList;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    private String diagnosis;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "1典型2非典型")
    private String caseCategory;

    @ApiModelProperty(value = "病史")
    private String medicalHistory;

    @ApiModelProperty(value = "难度等级：字典")
    private String difficulty;

    @ApiModelProperty(value = "征象")
    private String sign;

    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "主诉")
    private String selfComplaints;

    @ApiModelProperty(value = "是否已导出: 0否 1是")
    private String isExport;

    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "标签名称列表")
    private List<String> tagNameList;


}