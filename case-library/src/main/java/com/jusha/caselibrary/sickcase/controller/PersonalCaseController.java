package com.jusha.caselibrary.sickcase.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.aop.NoDuplicate;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.common.util.RedisUtil;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.CaseExportProcessResp;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.task.CaseDetailExportTask;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName PersonalCaseController
 * @Description  个人病例库控制层
 * <AUTHOR>
 * @Date 2025/7/10 09:29
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/personal/case")
@Api(tags = "病例管理-个人病例库管理")
public class PersonalCaseController {

    private final PersonalCaseService personalCaseService;

    private final AdvancedSearchService advancedSearchService;

    @Value("${TMP-LOCATIONS}")
    private String tmpPath;

    @ApiOperation("个人病例库高级检索")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResultBean<PageInfo<PersonalCaseDetailResp>> advanced (@Validated @RequestBody PersonalSearchReq req) {
        req.setUserId(LoginUtil.getLoginUserId());
        PageInfo<PersonalCaseDetailResp> response = advancedSearchService.searchAndConvertPage(req, PersonalCaseDetailResp.class);
        return ResultBean.success(response);
    }

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultBean<List<PersonalCaseDetailResp>> list(@Validated @RequestBody PersonalCaseSearchReq req) {
        List<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailList(req);
        return ResultBean.success(resp);
    }

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResultBean<PageInfo<PersonalCaseDetailResp>> page(@Validated @RequestBody PersonalCaseSearchReq req) {
        PageInfo<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailPage(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("个人病例库病例详情查询")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<PersonalCaseDetailResp> detail(@Validated @RequestParam("userCaseId") Long userCaseId) {
        PersonalCaseDetailResp resp = personalCaseService.getPersonalCaseDetail(userCaseId);
        return ResultBean.success(resp);
    }

    @ApiOperation("在指定目录下创建个人病例（基本信息）")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Long> createPersonalCaseInCatalog(@Validated @RequestBody PersonalCaseCreateReq req) {
        long userCaseId = personalCaseService.createPersonalCaseInCatalog(req);
        return ResultBean.success(userCaseId);
    }

    @ApiOperation("从科室收藏到个人病例库")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/collect", method = RequestMethod.POST)
    public ResultBean<Long> copyCaseFromDept(@Validated @RequestBody CollectCaseReq req) {
        long userCaseId = personalCaseService.copyCaseFromDept(req);
        return ResultBean.success(userCaseId);
    }

    @ApiOperation("在指定目录下修改个人病例（基本信息）")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    public ResultBean<Void> update(@Validated @RequestBody PersonalCaseUpdateReq req) {
        personalCaseService.updatePersonalCase(req);
        return ResultBean.success();
    }

    @ApiOperation("个人病例库病例删除")
    @NoDuplicate(keys = {"#req.userCaseId"})
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete(@Validated @RequestBody PersonalCaseDeleteReq req) {
        Long userCaseId = req.getUserCaseId();
        Long catalogId = req.getCatalogId();
        personalCaseService.deletePersonalCaseById(userCaseId, catalogId);
        return ResultBean.success();
    }

    @ApiOperation("个人病例库病例导出")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public ResultBean<String> export (@Validated @RequestBody PersonalCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<CaseExportDataDto, PersonalCaseSearchReq>(taskId, req, tmpPath, false));
        return ResultBean.success(taskId);
    }

    @ApiOperation("个人随访病例库病例导出")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/follow/export", method = RequestMethod.POST)
    public ResultBean<String> followExport (@Validated @RequestBody PersonalCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<FollowCaseExportDataDto, PersonalCaseSearchReq>(taskId, req, tmpPath, true));
        return ResultBean.success(taskId);
    }

    @ApiOperation("获取个人例库病例导出任务进度")
    @RequestMapping(value = "/export/progress", method = RequestMethod.GET)
    public ResultBean<CaseExportProcessResp> exportProgress (@RequestParam("taskId") String taskId) {
        CaseExportProcessResp resp = null;
        String taskJson = ContextHolder.stringRedisTemplate().opsForValue().get(RedisUtil.caseExportTaskKey(taskId));
        if (StringUtils.isNotBlank(taskJson)) {
            resp = JSON.parseObject(taskJson, CaseExportProcessResp.class);
        }
        return ResultBean.success(resp);
    }

    @ApiOperation("获取个人已经打过的病例标签列表")
    @RequestMapping(value = "/tag/list", method = RequestMethod.GET)
    public ResultBean<List<TagInfo>> tagList () {
        List<TagInfo> resp = personalCaseService.getCaseTagList();
        return ResultBean.success(resp);
    }

    @ApiOperation("科室病例库病例打标签")
    @NoDuplicate(keys = {"#req.id"})
    @RequestMapping(value = "/tag", method = RequestMethod.POST)
    public ResultBean<Void> tag (@Validated @RequestBody UserCaseTagReq req) {
        personalCaseService.tagCase(req);
        return ResultBean.success();
    }
}
