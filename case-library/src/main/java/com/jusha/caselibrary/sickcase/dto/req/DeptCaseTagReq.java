package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName DeptCaseTagReq
 * @Description 病例打标签请求实体
 * <AUTHOR>
 * @Date 2025/7/16 08:50
 **/
@Data
public class DeptCaseTagReq {

    @ApiModelProperty(value = "科室病例Id")
    @NotNull(message = "科室病例Id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "标签列表")
    @NotNull(message = "标签列表不能为空")
    private List<String> tagNameList;
}
