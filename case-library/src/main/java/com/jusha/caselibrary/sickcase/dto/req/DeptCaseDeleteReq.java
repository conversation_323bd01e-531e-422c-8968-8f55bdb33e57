package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName DeptCaseDeleteReq
 * @Description 科室病例库删除请求实体
 * <AUTHOR>
 * @Date 2025/7/21 15:26
 **/
@Data
public class DeptCaseDeleteReq {

    @ApiModelProperty(value = "病例id")
    @NotNull(message = "病例id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "病例库类型Id")
    @NotNull(message = "病例库类型Id不能为空")
    private Long caseTypeId;
}
