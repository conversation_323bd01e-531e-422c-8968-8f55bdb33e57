package com.jusha.caselibrary.sickcase.export.utils;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName ExportFieldMergeHandler
 * @Description 导出字段合并处理器
 * <AUTHOR>
 * @Date 2025/7/18 16:39
 **/
@Slf4j
public class ExportFieldMergeHandler implements CellWriteHandler {
    private final List<CaseExportDataDto> dataList;
    private final List<ExportFieldInfo> mergeableFields;
    private final Map<Integer, Integer> indexToColumnMap;

    public ExportFieldMergeHandler(List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        this.dataList = dataList;
        this.mergeableFields = fieldInfoList.stream()
                .filter(fieldInfo -> fieldInfo.isMergeable() && "CASE".equals(fieldInfo.getMergeType()))
                .collect(Collectors.toList());

        // 创建索引到列的映射
        this.indexToColumnMap = new HashMap<>();
        for (int i = 0; i < fieldInfoList.size(); i++) {
            ExportFieldInfo fieldInfo = fieldInfoList.get(i);
            indexToColumnMap.put(fieldInfo.getIndex(), i);
        }
    }

    @Override
    public void afterCellCreate(CellWriteHandlerContext context) {
        // 在所有数据写入完成后执行合并
        if (!context.getHead() && context.getRelativeRowIndex() != null) {
            int currentRowIndex = context.getRelativeRowIndex();

            // 只在第一行执行合并逻辑
            if (currentRowIndex < dataList.size()) {
                CaseExportDataDto currentData = dataList.get(currentRowIndex);

                if (currentData.isFirstRowOfMerge() && currentData.getMergeRowCount() > 1) {
                    // 执行单元格合并
                    for (ExportFieldInfo fieldInfo : mergeableFields) {
                        Integer columnIndex = indexToColumnMap.get(fieldInfo.getIndex());
                        if (columnIndex != null) {
                            // EasyExcel 行号从1开始（0是表头）
                            int startRow = currentRowIndex + 1;
                            int endRow = startRow + currentData.getMergeRowCount() - 1;

                            try {
                                CellRangeAddress mergeRegion = new CellRangeAddress(startRow, endRow, columnIndex, columnIndex);
                                context.getWriteSheetHolder().getSheet().addMergedRegion(mergeRegion);
                            } catch (Exception e) {
                                log.warn("合并单元格失败: 行{}-{}, 列{}", startRow, endRow, columnIndex, e);
                            }
                        }
                    }
                }
            }
        }
    }
}
