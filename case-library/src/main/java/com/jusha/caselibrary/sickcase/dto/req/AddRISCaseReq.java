package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.Scope;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description 从RIS导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class AddRISCaseReq {

    @ApiModelProperty("从RIS查询出来的参数")
    private List<RISStudy> risStudyList;

    @ApiModelProperty("导入目的地，个人病例库1，科室病例库2")
    private int targetLibrary;

    @ApiModelProperty("病例库id，个人病例库收藏夹id，科室病例库id")
    private Long targetId;

    @ApiModelProperty("如果是教学病例库，还需要有疾病id")
    private Long diseaseId;
}
