package com.jusha.caselibrary.sickcase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName SeriesInfo
 * @Description 序列信息
 * <AUTHOR>
 * @Date 2025/7/29 08:45
 **/
@Data
public class SeriesInfo {

    @ApiModelProperty(value = "序列UID")
    private String seriesUid;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ApiModelProperty(value = "检查流水号")
    private String accessNumber;

    @ApiModelProperty(value = "序列描述")
    private String seriesDescription;

    @ApiModelProperty(value = "检查模态")
    private String modality;

    @ApiModelProperty(value = "是否关键帧: 0-不是 1-是")
    private int isKeyframe;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private Date createTime;
}
