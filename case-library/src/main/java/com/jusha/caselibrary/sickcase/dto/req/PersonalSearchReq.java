package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;

/**
 * @ClassName PersonalSearchReq
 * @Description 个人病例库高级检索请求实体
 * <AUTHOR>
 * @Date 2025/7/25 09:52
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PersonalSearchReq extends AdvancedSearchRequest {

    /**
     * 检索类型：department（科室病例库）、personal（个人病例库）
     */
    @ApiModelProperty(value = "检索类型：department（科室病例库）、personal（个人病例库）", hidden = true)
    private String searchType = Constant.PERSON_CASE_INDEX_NAME;

    /**
     * 用户ID（个人病例库检索时必填）
     */
    @ApiModelProperty(value = "用户ID（个人病例库检索时必填）", hidden = true)
    private Long userId;

    /**
     * 目录ID（个人病例库）
     */
    @ApiModelProperty(value = "目录ID")
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
}
