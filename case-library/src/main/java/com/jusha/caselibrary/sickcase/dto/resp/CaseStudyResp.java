package com.jusha.caselibrary.sickcase.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashSet;

/**
 * 病例检查表
 * <AUTHOR>
 */
@Data
public class CaseStudyResp {

    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ApiModelProperty(value = "病例id")
    private Long caseId;

    @ApiModelProperty(value = "检查流水号/影像号")
    private String accessNumber;

    @ApiModelProperty(value = "检查时间")
    private String studyTime;

    @ApiModelProperty(value = "RIS中的检查号")
    private String studyNo;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "就诊类型（门诊、住院、体检）")
    private String patientType;

    @ApiModelProperty(value = "就诊时间")
    private String visitDate;

    @ApiModelProperty(value = "门诊号")
    private String outPatientNo;

    @ApiModelProperty(value = "住院号")
    private String inPatientNo;

    @ApiModelProperty(value = "症状/体征")
    private String physicalSign;

    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty(value = "检查项目名称")
    private String studyItemName;

    @ApiModelProperty(value = "检查部位")
    private String partName;

    @ApiModelProperty(value = "设备/检查类型")
    private String deviceType;

    @ApiModelProperty(value = "检查模态")
    private HashSet<String> modalityList;

    @ApiModelProperty(value = "病史")
    private String medicalHistory;

    @ApiModelProperty(value = "患者主诉")
    private String selfReportedSymptom;

    @ApiModelProperty(value = "影像学表现")
    private String reportDescribe;

}
