package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 已存在的病例添加检查
 * @date 2025/7/30
 */

@ApiModel
@Data
@Valid
public class AddStudyInfoUploadReq {

    @ApiModelProperty
    private Long caseTypeId;

    @ApiModelProperty("病例id")
    private Long caseId;

    @ApiModelProperty("检查信息")
    private List<StudyInfo> studyInfoList;

    @Data
    public static class StudyInfo {

        private String patientId;

        private String studyUid;
    }
}