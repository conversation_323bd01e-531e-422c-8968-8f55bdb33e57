package com.jusha.caselibrary.sickcase.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @ClassName CaseFollowReq
 * @Description 科室病例库随访请求实体
 * <AUTHOR>
 * @Date 2025/7/17 09:58
 **/
@Data
public class CaseFollowReq {

    @ApiModelProperty(value = "病例Id")
    @NotNull(message = "病例Id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "检查Uid")
    @NotNull(message = "检查Uid不能为空")
    private String studyUid;

    @ApiModelProperty(value = "随访类型")
    @NotNull(message = "随访类型不能为空")
    private String followType;

    @ApiModelProperty(value = "随访结果")
    @NotNull(message = "随访结果不能为空")
    private String followupResult;

    @ApiModelProperty(value = "定位")
    private String positionMatch;

    @ApiModelProperty(value = "定性")
    private String qualityMatch;

}
