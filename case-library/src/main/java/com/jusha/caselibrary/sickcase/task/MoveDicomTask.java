package com.jusha.caselibrary.sickcase.task;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.api.DimseApi;
import com.jusha.caselibrary.mybatisplus.entity.Study;
import com.jusha.caselibrary.mybatisplus.service.StudyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 腾讯云大文件流式上传任务
 */
@Slf4j
@RequiredArgsConstructor
public class MoveDicomTask implements Runnable {

    /**
     * 任务ID
     */
    private final String taskId;

    private final String studyUid;

    private final DimseApi dimseApi;

    private final StudyService studyService;

    @Override
    public void run(){

        int i = 0;
        while (true){
            try {
                Thread.sleep(2000);
                ResultBean<Boolean> resultBean = dimseApi.scuMoveState(taskId);
                if(resultBean.getState()){
                    if(resultBean.getData()){
                        log.info("拉取影像"+studyUid+"成功了================");
                        studyService.lambdaUpdate().eq(Study::getStudyUid,studyUid).set(Study::getIsExport,2).update();
                        break;
                    }else {
                        log.info("拉取影像"+studyUid+"还未成功================"+i);
                        i++;
                    }
                }else {
                    log.info("拉取影像"+studyUid+"失败了================");
                    studyService.lambdaUpdate().eq(Study::getStudyUid,studyUid).set(Study::getIsExport,3).update();
                    break;
                }
                if(i>100){
                    log.info("拉取影像"+studyUid+"超过100次认为失败了================");
                    studyService.lambdaUpdate().eq(Study::getStudyUid,studyUid).set(Study::getIsExport,3).update();
                    break;
                }
            }catch (Exception e){
                log.error("异步任务报错"+e);
            }
        }
    }

}