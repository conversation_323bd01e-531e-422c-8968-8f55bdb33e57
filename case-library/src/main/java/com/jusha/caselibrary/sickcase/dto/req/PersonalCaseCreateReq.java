package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName PersonalCaseCreateReq
 * @Description 个人病例库请求实体
 * <AUTHOR>
 * @Date 2025/7/11
 **/
@Data
@Validated
@Accessors(chain = true)
public class PersonalCaseCreateReq {

    @ApiModelProperty(value = "目录ID列表")
    @NotNull(message = "目录ID列表不能为空")
    private List<Long> catalogIdList;

    @ApiModelProperty(value = "病例Id")
    private Long caseId;

    @ApiModelProperty(value = "病例Id列表")
    private List<Long> diseaseIdList;

    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    private String diagnosis;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "病史")
    private String medicalHistory;

    @ApiModelProperty(value = "征象")
    private String sign;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "主诉")
    private String selfComplaints;

    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ApiModelProperty(value = "随访状态,0未随访，1已随访")
    private String followStatus = "0";

    @ApiModelProperty(value = "来源，1RIS导入，2本地上传")
    private String sourceType;

    @ApiModelProperty(value = "难度等级，字典")
    private String difficulty;

    @ApiModelProperty(value = "病例类型（典型、非典型等），字典")
    private String caseCategory;

    @ApiModelProperty(value = "定性匹配，字典")
    private String qualityMatch;

    @ApiModelProperty(value = "定性匹配，字典")
    private String positionMatch;

}