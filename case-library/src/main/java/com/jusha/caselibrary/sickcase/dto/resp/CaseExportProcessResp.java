package com.jusha.caselibrary.sickcase.dto.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName CaseExportProcessResp
 * @Description 病例数据导出结果对象
 * <AUTHOR>
 * @Date 2024/2/21 9:27
 **/
@Data
public class CaseExportProcessResp {
    /**
     * TaskID
     */
    @ApiModelProperty("TaskID")
    private String taskId;

    @ApiModelProperty("完成百分比")
    private int percent;
    
    @ApiModelProperty("进度")
    private int progress;
    
    @ApiModelProperty("进度消息")
    private String message;

    @ApiModelProperty("数据为空标识")
    private short emptyFlag = 0;

    @ApiModelProperty("文件访问全路径")
    private String fullUrl;
    
    @ApiModelProperty("文件路径")
    private String filePath;
    
    @ApiModelProperty("文件名")
    private String fileName;
    
    @ApiModelProperty("导出数量")
    private int exportCount;
    
    @ApiModelProperty("创建时间")
    private java.time.LocalDateTime createTime;

    @ApiModelProperty(value = "原文件名称")
    private String originName;

    @ApiModelProperty(value = "实际存储文件名称（加入时间）")
    private String actualName;

    @ApiModelProperty(value = "文件访问相对路径")
    private String fileUrl;
}
