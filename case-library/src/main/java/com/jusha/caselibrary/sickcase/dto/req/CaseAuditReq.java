package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * @ClassName CaseAuditReq
 * @Description 审核病例请求实体
 * <AUTHOR>
 * @Date 2025/7/15 13:52
 **/
@Data
@Validated
public class CaseAuditReq {

    @ApiModelProperty(value = "审核id")
    @NotNull(message = "审核id不能为空")
    private Long auditId;

    @ApiModelProperty(value = "病例Id")
    @NotNull(message = "病例Id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "审核意见")
    private String auditComment;

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    @NotNull(message = "审核状态不能为空")
    private String status;
}
