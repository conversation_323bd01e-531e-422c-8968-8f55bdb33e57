package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;

/**
 * @ClassName DeptSearchReq
 * @Description 科室病例库高级检索请求实体
 * <AUTHOR>
 * @Date 2025/7/25 09:52
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class DeptSearchReq extends AdvancedSearchRequest {

    /**
     * 检索类型：department（科室病例库）、personal（个人病例库）
     */
    @ApiModelProperty(value = "检索类型：department（科室病例库）、personal（个人病例库）", hidden = true)
    private String searchType = Constant.DEP_CASE_INDEX_NAME;

    /**
     * 分类ID（科室病例库）
     */
    @ApiModelProperty(value = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private Long caseTypeId;

}
