package com.jusha.caselibrary.sickcase.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @ClassName ExcelFollowRow
 * @Description Excel随访数据行映射类
 * <AUTHOR>
 * @Date 2025/7/18
 **/
@Data
public class ExcelFollowRow {

    @ExcelProperty(index = 0, value = "影像号")
    @ApiModelProperty(value = "影像号")
    @NotNull(message = "影像号不能为空")
    private String accessNumber;

    @ExcelProperty(index = 1, value = "随访类型")
    @ApiModelProperty(value = "随访类型(1-4)")
    @Pattern(regexp = "^[1-4]$", message = "随访类型必须是1-4之间的数字")
    private String followType;

    @ExcelProperty(index = 2, value = "随访结果")
    @ApiModelProperty(value = "随访结果")
    @NotBlank(message = "随访结果不能为空")
    private String followupResult;

    @ApiModelProperty(value = "行号(用于错误定位)")
    private Integer rowIndex;

    /**
     * 获取默认随访类型
     */
    public String getFollowType() {
        return followType == null || followType.trim().isEmpty() ? "1" : followType.trim();
    }

    /**
     * 获取清理后的随访结果
     */
    public String getFollowupResult() {
        return followupResult == null ? null : followupResult.trim();
    }
}