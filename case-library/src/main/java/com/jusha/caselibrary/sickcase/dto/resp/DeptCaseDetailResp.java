package com.jusha.caselibrary.sickcase.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.DictConvertUtil;
import com.jusha.caselibrary.common.util.UserUtil;
import com.jusha.caselibrary.search.document.DiseaseInfo;
import com.jusha.caselibrary.sickcase.dto.CaseTypeInfo;
import com.jusha.caselibrary.sickcase.dto.StudyInfo;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName DeptCaseDetailResp
 * @Description 科室病例详情返回实体类
 * <AUTHOR>
 * @Date 2025/7/10 10:36
 **/
@Data
public class DeptCaseDetailResp {

    @ApiModelProperty(value = "病例Id")
    private Long caseId;

    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    private String diagnosis;

    @ApiModelProperty(value = "患者Id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ApiModelProperty(value = "患者生日")
    private String patientBirthDate;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "患者病史")
    private String medicalHistory;

    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ApiModelProperty(value = "难度等级标签")
    private String difficultyLabel;

    @ApiModelProperty(value = "是否典型")
    private String caseCategory;

    @ApiModelProperty(value = "是否典型标签")
    private String caseCategoryLabel;

    @ApiModelProperty(value = "征象")
    private String sign;

    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ApiModelProperty(value = "来源标签")
    private String sourceTypeLabel;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "随访状态标签")
    private String followStatusLabel;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "引用晨会列表")
    private String meetings;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "主诉")
    private String selfComplaints;

    // 关联的分类信息
    @ApiModelProperty(value = "病例库类型列表")
    private List<CaseTypeInfo> caseTypeInfoList;

    // 关联的检查信息
    @ApiModelProperty(value = "病例报告")
    private List<StudyInfo> studyInfoList;

    // 关联的标签信息
    @ApiModelProperty(value = "病例标签列表")
    private List<TagInfo> tagInfoList;

    // 关联的疾病信息
    @ApiModelProperty(value = "疾病信息")
    private List<DiseaseInfo> diseaseInfoList;

    @ApiModelProperty(value = "被收藏目录Id列表")
    private List<Long> favoriteCatalogIdList;

    /**
     * 设置字典标签值
     */
    public void handleData() {
        this.difficultyLabel = DictConvertUtil.convertDifficulty(this.difficulty);
        this.caseCategoryLabel = DictConvertUtil.convertCaseCategory(this.caseCategory);
        this.sourceTypeLabel = DictConvertUtil.convertSourceType(this.sourceType);
        this.followStatusLabel = DictConvertUtil.convertFollowStatus(this.followStatus);
        this.createUserName = UserUtil.getUserNameById(this.createBy);
        // 处理StudyInfoDto中的字典字段
        if (CollectionUtils.isNotEmpty(this.studyInfoList)) {
            this.studyInfoList.forEach(StudyInfo::handleData);
        }
    }
}
