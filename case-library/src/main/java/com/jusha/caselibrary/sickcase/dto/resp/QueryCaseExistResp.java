package com.jusha.caselibrary.sickcase.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 病例检查表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QueryCaseExistResp {

    @ApiModelProperty
    private List<PersonalCaseDetailResp> personalCaseDetailRespList;

    @ApiModelProperty
    private List<DeptCaseDetailResp> deptCaseDetailRespList;

}
