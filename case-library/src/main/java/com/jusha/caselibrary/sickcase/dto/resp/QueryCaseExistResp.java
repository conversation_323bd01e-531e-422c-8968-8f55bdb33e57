package com.jusha.caselibrary.sickcase.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 病例检查表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QueryCaseExistResp {

    @ApiModelProperty
    private List<Parent> parents;

    @ApiModelProperty(value = "病例id")
    private Long caseId;

    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

//    @ApiModelProperty(value = "疾病id")
//    private Long diseaseId;
//
//    @ApiModelProperty(value = "疾病名称")
//    private String diseaseName;

    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ApiModelProperty(value = "检查流水号/影像号")
    private String accessNumber;

    @ApiModelProperty(value = "检查时间")
    private Date studyTime;

    @ApiModelProperty(value = "RIS中的检查号")
    private String studyNo;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @Data
    public static class Parent {
        @ApiModelProperty(value = "所属个人收藏夹或者科室病例库名称(可能属于多个)")
        private String parentName;

        @ApiModelProperty(value = "所属个人收藏夹或者科室病例库id(可能属于多个)")
        private Long parentId;
    }
}
