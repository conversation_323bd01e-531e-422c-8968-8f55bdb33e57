package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.sickcase.dto.FollowInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName CaseFollowUpdateReq
 * @Description 病例随访修改请求实体
 * <AUTHOR>
 * @Date 2025/7/17 10:27
 **/
@Data
public class CaseFollowUpdateReq {

    @ApiModelProperty(value = "检查随访信息列表")
    @NotNull(message = "检查随访信息列表不能为空")
    private List<StudyFollowUpdate> studyFollowUpdateList;

    @ApiModelProperty(value = "病例Id")
    @NotNull(message = "病例Id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @Data
    public static class StudyFollowUpdate {

        @ApiModelProperty(value = "检查Uid")
        @NotNull(message = "检查Uid不能为空")
        private String studyUid;

        @ApiModelProperty(value = "定性匹配，字典")
        private String qualityMatch;

        @ApiModelProperty(value = "定位匹配，字典")
        private String positionMatch;

        @ApiModelProperty(value = "随访结果")
        private List<FollowInfo> followInfoList;

    }
}
