package com.jusha.caselibrary.sickcase.mapper;

import com.jusha.caselibrary.sickcase.dto.req.AuditCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName DeptCaseMapper
 * @Description 科室病例Mapper
 * <AUTHOR>
 * @Date 2025/7/10 09:34
 **/
@Mapper
public interface DeptCaseMapper {

    /**
     * @description 科室病例库详细列表
     * <AUTHOR>
     * @date 2025/7/10 11:01
     * @param req
     * @return List<DeptCaseDetailResp>
     **/
    List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req);

    /**
     * @description 审核病例库列表
     * <AUTHOR>
     * @date 2025/7/30 20:11
     * @param req
     * @return PageInfo<AuditCaseDetailResp>
     **/
    List<AuditCaseDetailResp> getAuditCaseList(AuditCaseSearchReq req);

    /**
     * @description 我的审核病例列表
     * <AUTHOR>
     * @date 2025/7/30 20:11
     * @param req
     * @return PageInfo<AuditCaseDetailResp>
     **/
    List<AuditCaseDetailResp> getMyAuditCaseList(AuditCaseSearchReq req);
}
