package com.jusha.caselibrary.sickcase.mapper;

import com.jusha.caselibrary.sickcase.dto.CaseBaseInfo;
import com.jusha.caselibrary.sickcase.dto.req.AuditCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName DeptCaseMapper
 * @Description 科室病例Mapper
 * <AUTHOR>
 * @Date 2025/7/10 09:34
 **/
@Mapper
public interface DeptCaseMapper {

    /**
     * @description 科室病例库详细列表
     * <AUTHOR>
     * @date 2025/7/10 11:01
     * @param req
     * @return List<Long>
     **/
    List<Long> getDeptCaseIdList(DeptCaseSearchReq req);

    /**
     * @description 审核病例库列表
     * <AUTHOR>
     * @date 2025/7/30 20:11
     * @param req
     * @return List<CaseBaseInfo> 返回包含case_id和case_type_id的列表
     **/
    List<CaseBaseInfo> getCaseAuditIdList(AuditCaseSearchReq req);

    /**
     * @description 审核病例库列表
     * <AUTHOR>
     * @date 2025/8/5 19:59
     * @param req
     * @return List<AuditCaseDetailResp>
     **/
    List<AuditCaseDetailResp> getAuditCaseList(AuditCaseSearchReq req);

    /**
     * @param req
     * @return List<DeptCaseDetailResp>
     * @description 查询完整病例信息
     * <AUTHOR>
     * @date 2025/8/5 17:28
     **/
    List<DeptCaseDetailResp> getCaseDetailAttributes(DeptCaseSearchReq req);
}
