package com.jusha.caselibrary.sickcase.export.task;

import com.alibaba.fastjson.JSON;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.acHolder.PropertiesBean;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.FileUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.RedisUtil;
import com.jusha.caselibrary.file.service.impl.FileServiceImpl;
import com.jusha.caselibrary.sickcase.dto.resp.CaseExportProcessResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.utils.CaseExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName CaseDetailExportTask
 * @Description 病例详情导出任务类 - 支持复杂单元格合并
 * <AUTHOR>
 * @Date 2025/7/10 14:41
 **/
@Slf4j
public class CaseDetailExportTask<D, T> implements Runnable {

    private final String taskId;
    private final T searchRequest;
    private final String tmpPath;
    private final Boolean followExport;

    /**
     * 构造函数
     *
     * @param taskId 任务ID
     * @param searchRequest 查询请求参数
     * @param tmpPath 临时文件路径
     */
    public CaseDetailExportTask(String taskId, T searchRequest, String tmpPath, Boolean followExport) {
        this.taskId = taskId;
        this.searchRequest = searchRequest;
        this.tmpPath = tmpPath;
        this.followExport = followExport;
    }

    @Override
    public void run() {
        try {
            log.info("开始执行病例导出任务，任务ID: {}", taskId);
            
            // 初始化进度
            updateProgress(0, LocaleUtil.getLocale("export.task.start"));
            
            // 查询病例数据
            updateProgress(10, LocaleUtil.getLocale("export.task.querying.data"));
            List<D> exportDataList = queryExportData();
            
            if (exportDataList.isEmpty()) {
                updateProgress(-1, LocaleUtil.getLocale("export.task.complete.no.data"));
                return;
            }
            
            // 数据转换和处理
            updateProgress(30, LocaleUtil.getLocale("export.task.processing.data"));
            processExportData(exportDataList);
            
            // 生成导出文件
            updateProgress(50, LocaleUtil.getLocale("export.task.generating.file"));
            String exportFilePath = generateExportFile(exportDataList);
            
            // 上传导出文件
            updateProgress(90, LocaleUtil.getLocale("export.task.uploading.file"));
            // 完成导出
            uploadExportedFile(new File(exportFilePath), taskId, exportDataList.size());
            
            log.info("病例导出任务完成，任务ID: {}, 文件路径: {}, 导出数量: {}", 
                    taskId, exportFilePath, exportDataList.size());
            
        } catch (Exception e) {
            log.error("病例导出任务执行失败，任务ID: {}, 错误信息: {}", taskId, e.getMessage(), e);
            updateProgress(-1, getMessage("export.task.failed", e.getMessage()));
        }
    }

    /**
     * 查询导出数据 - 重构后使用策略模式和数据处理器
     */
    private List<D> queryExportData() {
        List<D> exportDataList = new ArrayList<>();

        try {
            // 使用策略工厂获取对应的导出策略
            CaseExportStrategy<T, ?> strategy = ExportStrategyFactory.getStrategy(searchRequest);

            // 通过策略查询病例详情列表，现在返回原生类型
            List<?> caseDetailList = strategy.queryCaseDetailList(searchRequest);
            log.info("查询病例详情列表完成，数量: {}", caseDetailList != null ? caseDetailList.size() : 0);

            if (caseDetailList != null && !caseDetailList.isEmpty()) {
                if (this.followExport) {
                    // FollowCaseExportDataDto类型：创建复合数据结构
                    FollowCaseExportDataDto followExportData = createFollowCaseExportData(caseDetailList);
                    exportDataList.add((D) followExportData);
                } else {
                    // 直接使用原生数据类型，无需转换
                    for (Object caseDetail : caseDetailList) {
                        try {
                            exportDataList.add(convertToExportData(caseDetail));
                        } catch (Exception e) {
                            log.warn("添加病例数据失败，错误: {}", e.getMessage());
                            // 继续处理其他病例
                        }
                    }
                }

                // 更新进度
                updateProgress(25, getMessage("export.task.query.data.found", caseDetailList.size()));
            }

        } catch (Exception e) {
            log.error("查询导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询导出数据失败: " + e.getMessage(), e);
        }

        return exportDataList;
    }

    /**
     * 创建FollowCaseExportDataDto复合数据结构
     */
    private FollowCaseExportDataDto createFollowCaseExportData(List<?> caseDetailList) {
        FollowCaseExportDataDto followExportData = new FollowCaseExportDataDto();
        
        // 转换病例数据列表
        List<CaseExportDataDto> caseExportDataList = new ArrayList<>();
        for (Object caseDetail : caseDetailList) {
            try {
                CaseExportDataDto caseExportData = convertToCaseExportData(caseDetail);
                if (caseExportData != null) {
                    caseExportDataList.add(caseExportData);
                }
            } catch (Exception e) {
                log.warn("转换病例数据失败，错误: {}", e.getMessage());
            }
        }
        
        // 设置病例数据列表
        followExportData.setCaseExportDataDtoList(caseExportDataList);
        
        // 计算统计信息
        followExportData.calculateStatistics();
        
        log.info("创建FollowCaseExportDataDto完成，病例数量: {}, 统计字段数量: {}",
                caseExportDataList.size(), followExportData.getDynamicExportFieldNames().size());
        
        return followExportData;
    }

    /**
     * 转换为导出数据DTO
     * 支持复杂的数据结构转换，使用字典标签而不是原始值
     */
    @SuppressWarnings("unchecked")
    private D convertToExportData(Object caseDetail) {
        // 根据泛型类型判断转换方式
        if (this.followExport) {
            // 这里暂时返回null，实际转换逻辑在processExportData中处理
            return null;
        } else {
            // 原有的CaseExportDataDto转换逻辑
            CaseExportDataDto exportData = convertToCaseExportData(caseDetail);
            return (D) exportData;
        }
    }

    /**
     * 转换为CaseExportDataDto - 支持不同类型的病例详情
     */
    private CaseExportDataDto convertToCaseExportData(Object caseDetail) {
        if (caseDetail == null) {
            return null;
        }
        
        CaseExportDataDto exportData = new CaseExportDataDto();
        
        try {
            // 复制基本属性
            BeanUtils.copyProperties(caseDetail, exportData);
            
            // 根据不同类型处理字典标签值
            if (caseDetail instanceof DeptCaseDetailResp) {
                DeptCaseDetailResp deptCase =
                    (DeptCaseDetailResp) caseDetail;
                // 复制关联的数据列表
                exportData.setStudyInfoList(deptCase.getStudyInfoList());
                exportData.setTagInfoList(deptCase.getTagInfoList());
            } else if (caseDetail instanceof PersonalCaseDetailResp) {
                PersonalCaseDetailResp personalCase =
                    (PersonalCaseDetailResp) caseDetail;
                
                // 复制关联的数据列表
                exportData.setStudyInfoList(personalCase.getStudyInfoList());
                exportData.setTagInfoList(personalCase.getTagInfoList());
            }
            // 设置导出相关信息
            exportData.setExportTime(LocalDateTime.now());
        } catch (Exception e) {
            log.error("转换病例数据失败: {}", e.getMessage(), e);
            return null;
        }
        
        return exportData;
    }

    /**
     * 从请求对象中获取导出字段列表
     */
    private List<String> getExportFieldsFromRequest() {
        try {
            if (searchRequest == null) {
                log.debug("请求对象为空，返回空字段列表");
                return null;
            }
            
            // 通过反射获取exportFields字段
            Class<?> requestClass = searchRequest.getClass();
            try {
                java.lang.reflect.Field exportFieldsField = requestClass.getDeclaredField("exportFields");
                exportFieldsField.setAccessible(true);
                
                @SuppressWarnings("unchecked")
                List<String> exportFields = (List<String>) exportFieldsField.get(searchRequest);
                
                if (exportFields != null && !exportFields.isEmpty()) {
                    log.info("从请求中获取到导出字段列表: {}", exportFields);
                    return exportFields;
                } else {
                    log.debug("请求中的导出字段列表为空，将导出所有字段");
                    return null;
                }
                
            } catch (NoSuchFieldException e) {
                log.debug("请求类 {} 中没有exportFields字段，将导出所有字段", requestClass.getSimpleName());
                return null;
            } catch (IllegalAccessException e) {
                log.warn("无法访问请求类 {} 中的exportFields字段: {}", requestClass.getSimpleName(), e.getMessage());
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取导出字段列表时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理导出数据
     * 主要进行数据清洗和格式化
     */
    private void processExportData(List<D> exportDataList) {
        try {
            if (this.followExport) {
                // FollowCaseExportDataDto类型：处理复合数据结构
                for (D data : exportDataList) {
                    FollowCaseExportDataDto followData = (FollowCaseExportDataDto) data;
                    // 对病例数据列表进行清洗和格式化
                    if (followData.getCaseExportDataDtoList() != null && !followData.getCaseExportDataDtoList().isEmpty()) {
                        followData.getCaseExportDataDtoList().forEach(this::cleanAndFormatCaseData);
                    }
                }
            } else {
                // 使用数据处理器进行数据处理
                try {
                    CaseDataProcessor<D> processor = ExportStrategyFactory.getDataProcessor(searchRequest);
                    // 使用数据处理器处理整个数据列表
                    List<D> processedDataList = processor.processData(exportDataList);
                    exportDataList.clear();
                    exportDataList.addAll(processedDataList);
                } catch (RuntimeException e) {
                    log.warn("获取数据处理器失败，使用默认处理方式: {}", e.getMessage());
                    exportDataList.forEach(this::cleanAndFormatData);
                }
            }
            
            log.info("数据处理完成，共处理 {} 条记录", exportDataList.size());
            
        } catch (Exception e) {
            log.error("处理导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理导出数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清洗和格式化数据
     */
    private void cleanAndFormatData(D exportData) {
        CaseExportDataDto data = (CaseExportDataDto) exportData;
        cleanAndFormatCaseData(data);
    }

    /**
     * 清洗和格式化病例数据
     */
    private void cleanAndFormatCaseData(CaseExportDataDto data) {
        // 清理空值和格式化文本
        if (isEmpty(data.getPatientName())) {
            data.setPatientName("");
        }
        
        if (isEmpty(data.getPatientSex())) {
            data.setPatientSex("");
        }
        
        // 格式化长文本，移除多余的空白字符
        if (isNotEmpty(data.getMedicalHistory())) {
            data.setMedicalHistory(cleanText(data.getMedicalHistory()));
        }
        
        if (isNotEmpty(data.getCaseAnalysis())) {
            data.setCaseAnalysis(cleanText(data.getCaseAnalysis()));
        }

        if (isNotEmpty(data.getDiagnosis())) {
            data.setDiagnosis(cleanText(data.getDiagnosis()));
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (isEmpty(text)) {
            return "";
        }
        // 移除多余的空白字符，保留换行符
        return text.trim().replaceAll("[ \\t]+", " ");
    }

    /**
     * 生成导出文件
     */
    private String generateExportFile(List<D> exportDataList) {
        try {
            // 确保导出目录存在
            File exportDir = new File(tmpPath);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            
            // 获取CaseExportUtil实例
            CaseExportUtil caseExportUtil = ContextHolder.getBean(CaseExportUtil.class);
            
            // 生成文件名
            String fileName = "病例导出_" + formatDateTime(LocalDateTime.now()) + taskId + ".xlsx";
            String filePath = tmpPath + File.separator + taskId + "_" + fileName;
            
            // 获取导出字段列表
            List<String> exportFields = getExportFieldsFromRequest();
            
            String resultPath;
            
            if (this.followExport && !exportDataList.isEmpty()) {
                // FollowCaseExportDataDto类型：使用复合数据导出
                FollowCaseExportDataDto followData = (FollowCaseExportDataDto) exportDataList.get(0);
                resultPath = caseExportUtil.exportFollowCaseToExcel(followData, filePath, exportFields);
            } else {
                // CaseExportDataDto类型：使用标准导出
                List<CaseExportDataDto> dataList = new ArrayList<>();
                for (D data : exportDataList) {
                    dataList.add((CaseExportDataDto) data);
                }
                resultPath = caseExportUtil.exportToExcel(dataList, filePath, exportFields);
            }
            
            log.info("Excel文件生成成功: {}", resultPath);
            return resultPath;
            
        } catch (Exception e) {
            log.error("生成导出文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成导出文件失败: " + e.getMessage(), e);
        }
    }

    // 上传导出的文件
    private void uploadExportedFile(File file, String taskId, int exportCount) {
        try {

            String endpoint = ContextHolder.getContext().getBean(PropertiesBean.class).getEndpoint();
            FileServiceImpl fileService = ContextHolder.getBean(FileServiceImpl.class);
            // 确保文件写入完成
            try (FileOutputStream fos = new FileOutputStream(file, true)) {
                fos.getFD().sync();
            }
            String objectKey = Constant.FILE_SEPARATOR + DateUtil.convertDateToStr(new Date(),DateUtil.DAY_OF_YEAR_PATTERN) + Constant.FILE_SEPARATOR + file.getName();
            boolean flag = fileService.uploadFile(objectKey, file.getAbsolutePath());
            if(flag){
                // 更新任务状态
                // 更新任务进度信息，包含文件路径
                CaseExportProcessResp taskDto = new CaseExportProcessResp();
                taskDto.setTaskId(taskId);
                taskDto.setProgress(100);
                taskDto.setMessage(LocaleUtil.getLocale("export.task.complete.success"));
                taskDto.setFilePath(endpoint + Constant.FILE_SEPARATOR + Constant.UPLOAD_BUCKET + objectKey);
                taskDto.setFullUrl(endpoint + Constant.FILE_SEPARATOR + Constant.UPLOAD_BUCKET + objectKey);
                taskDto.setFileName(file.getName());
                taskDto.setExportCount(exportCount);
                taskDto.setCreateTime(LocalDateTime.now());

                String taskDtoJson = JSON.toJSONString(taskDto);
                ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                        Constant.TASK_OVER_TIME, TimeUnit.MINUTES);

                log.info("导出结果保存成功，任务ID: {}", taskId);
            }
        }catch (Exception e) {
            log.error("上传文件失败：{}", e.getMessage(), e);
        } finally {
            // 删除临时文件
            if (file.exists()) {
                FileUtil.delFile(file);
            }
        }
    }

    /**
     * 更新导出进度
     */
    private void updateProgress(int progress, String message) {
        try {
            CaseExportProcessResp taskDto = new CaseExportProcessResp();
            taskDto.setTaskId(taskId);
            taskDto.setProgress(progress);
            taskDto.setMessage(message);
            
            String taskDtoJson = JSON.toJSONString(taskDto);
            ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                    Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
            
            log.info("更新导出进度，任务ID: {}, 进度: {}%, 消息: {}", taskId, progress, message);
            
        } catch (Exception e) {
            log.error("更新导出进度失败: {}", e.getMessage(), e);
            // 这里不抛异常，避免影响主流程
        }
    }

    /**
     * 获取国际化消息
     */
    private String getMessage(String code, Object... args) {
        try {
            return ContextHolder.getBean(MessageSource.class).getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.warn("获取国际化消息失败, code: {}", code);
            // 返回code作为备用
            return code;
        }
    }
    
    /**
     * 判断字符串是否为空或空白
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空且不为空白
     */
    private boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        try {
            return dateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        } catch (Exception e) {
            log.warn("格式化日期时间失败: {}", e.getMessage());
            return String.valueOf(System.currentTimeMillis());
        }
    }
}
