package com.jusha.caselibrary.sickcase.export.utils;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName ExportFieldStyleHandler
 * @Description 导出字段样式处理器
 * <AUTHOR>
 * @Date 2025/7/18 16:39
 **/
public class ExportFieldStyleHandler implements CellWriteHandler {
    private final Map<Integer, Integer> columnWidthMap;

    public ExportFieldStyleHandler(List<ExportFieldInfo> fieldInfoList) {
        this.columnWidthMap = fieldInfoList.stream()
                .collect(Collectors.toMap(
                        ExportFieldInfo::getIndex,
                        ExportFieldInfo::getWidth,
                        (existing, replacement) -> existing
                ));
    }

    @Override
    public void afterCellCreate(CellWriteHandlerContext context) {
        // 设置列宽
        Integer columnIndex = context.getColumnIndex();
        if (columnWidthMap.containsKey(columnIndex)) {
            int width = columnWidthMap.get(columnIndex);
            context.getWriteSheetHolder().getSheet().setColumnWidth(columnIndex, width * 256);
        }
    }

    @Override
    public void afterCellDataConverted(CellWriteHandlerContext context) {
        // 可以在这里根据 @ExportField 注解设置更多样式
        Cell cell = context.getCell();
        if (cell != null) {
            // 设置基础样式
            CellStyle cellStyle = context.getWriteWorkbookHolder().getWorkbook().createCellStyle();

            if (context.getHead()) {
                // 表头样式
                cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                Font font = context.getWriteWorkbookHolder().getWorkbook().createFont();
                font.setFontName("宋体");
                font.setFontHeightInPoints((short) 12);
                font.setBold(true);
                cellStyle.setFont(font);
            } else {
                // 内容样式 - 强制设置为文本格式
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setWrapText(true);
                
                // 设置单元格格式为文本，防止数值显示为科学记数法
                DataFormat dataFormat = context.getWriteWorkbookHolder().getWorkbook().createDataFormat();
                cellStyle.setDataFormat(dataFormat.getFormat("@"));

                Font font = context.getWriteWorkbookHolder().getWorkbook().createFont();
                font.setFontName("宋体");
                font.setFontHeightInPoints((short) 11);
                cellStyle.setFont(font);
            }

            // 设置边框
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);

            cell.setCellStyle(cellStyle);
        }
    }
}
