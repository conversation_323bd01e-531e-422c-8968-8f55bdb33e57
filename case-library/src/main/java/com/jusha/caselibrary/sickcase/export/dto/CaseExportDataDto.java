package com.jusha.caselibrary.sickcase.export.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.sickcase.dto.DiseaseOverviewInfo;
import com.jusha.caselibrary.sickcase.dto.FollowInfo;
import com.jusha.caselibrary.sickcase.dto.StudyInfo;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import com.jusha.caselibrary.common.aop.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CaseExportDataDto
 * @Description 病例导出数据DTO - 根据用户反馈重新设计，严格按照图片字段
 * <AUTHOR>
 * @Date 2025/7/10 17:25
 **/
@Data
public class CaseExportDataDto {

    // ==================== 基本病例信息（支持合并） ====================
    
    @ExportField(value = "病例ID", index = 0, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病例ID")
    private Long caseId;

    @ExportField(value = "病例名称", index = 1, width = 25, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ExportField(value = "病例编号", index = 2, width = 20, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ExportField(value = "疾病名称", index = 3, width = 20, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ExportField(value = "最终诊断", index = 4, width = 25, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "最终诊断")
    private String diagnosis;

    @ExportField(value = "患者ID", index = 5, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ExportField(value = "患者姓名", index = 6, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ExportField(value = "患者性别", index = 7, width = 10, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ExportField(value = "患者生日", index = 8, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者生日")
    private String patientBirthDate;

    @ExportField(value = "患者年龄", index = 9, width = 10, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ExportField(value = "患者病史", index = 10, width = 40, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "患者病史")
    private String medicalHistory;

    @ExportField(value = "难度等级", index = 11, width = 12, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ExportField(value = "是否典型", index = 12, width = 12, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "是否典型")
    private String caseCategory;

    @ExportField(value = "征象", index = 13, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "征象")
    private String sign;

    @ExportField(value = "病例分析", index = 14, width = 40, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ExportField(value = "备注", index = 15, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ExportField(value = "创建人", index = 15, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ExportField(value = "定性匹配", index = 16, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "定性匹配")
    private String qualityMatch;

    @ExportField(value = "定位匹配", index = 17, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "定位匹配")
    private String positionMatch;

    @ExportField(value = "来源", index = 18, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ExportField(value = "随访状态", index = 19, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ExportField(value = "病例库类型", index = 20, width = 15, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病例库类型")
    private String caseType;

    @ExportField(value = "创建时间", index = 21, width = 20, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "创建时间")
    private String createTimeText;

    @ExportField(value = "标签信息", index = 22, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "标签信息")
    private String tagInfoText;

    // ==================== 随访结果分类字段（四个分类） ====================
    
    @ExportField(value = "手术", index = 23, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "手术随访结果")
    private String surgeryResult;

    @ExportField(value = "超声", index = 24, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "超声随访结果")
    private String ultrasoundResult;

    @ExportField(value = "临床", index = 25, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "临床随访结果")
    private String clinicalResult;

    @ExportField(value = "病理", index = 26, width = 30, mergeable = true, mergeType = "CASE")
    @ApiModelProperty(value = "病理随访结果")
    private String pathologyResult;

    // ==================== 检查报告信息（不合并） ====================
    
    @ExportField(value = "检查ID", index = 27, width = 15)
    @ApiModelProperty(value = "检查ID")
    private String studyId;

    @ExportField(value = "检查来源/检查时间", index = 28, width = 20)
    @ApiModelProperty(value = "检查来源/检查时间")
    private String studySourceTime;

    @ExportField(value = "检查时间的检查部位", index = 29, width = 20)
    @ApiModelProperty(value = "检查时间的检查部位")
    private String studyTimePart;

    @ExportField(value = "检查日期", index = 30, width = 15)
    @ApiModelProperty(value = "检查日期")
    private String studyDate;

    @ExportField(value = "门诊号", index = 31, width = 15)
    @ApiModelProperty(value = "门诊号")
    private String outpatientNo;

    @ExportField(value = "住院号", index = 32, width = 15)
    @ApiModelProperty(value = "住院号")
    private String inpatientNo;

    @ExportField(value = "检查号", index = 33, width = 15)
    @ApiModelProperty(value = "检查号")
    private String studyNo;

    @ExportField(value = "检查项目", index = 34, width = 20)
    @ApiModelProperty(value = "检查项目")
    private String studyItem;

    @ExportField(value = "检查项目名称", index = 35, width = 20)
    @ApiModelProperty(value = "检查项目名称")
    private String studyItemName;

    @ExportField(value = "检查部位", index = 36, width = 15)
    @ApiModelProperty(value = "检查部位")
    private String partName;

    @ExportField(value = "设备名称", index = 37, width = 15)
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ExportField(value = "常规", index = 38, width = 20)
    @ApiModelProperty(value = "常规")
    private String routine;

    @ExportField(value = "影像学表现", index = 39, width = 50)
    @ApiModelProperty(value = "影像学表现")
    private String reportDescribe;

    @ExportField(value = "影像学诊断", index = 40, width = 40)
    @ApiModelProperty(value = "影像学诊断")
    private String reportDiagnose;

    @ExportField(value = "报告时间", index = 41, width = 20)
    @ApiModelProperty(value = "报告时间")
    private String reportTime;

    @ExportField(value = "报告人", index = 42, width = 15)
    @ApiModelProperty(value = "报告人")
    private String reporter;

    @ExportField(value = "审核人", index = 43, width = 15)
    @ApiModelProperty(value = "审核人")
    private String checker;

    @ExportField(value = "审核时间", index = 44, width = 20)
    @ApiModelProperty(value = "审核时间")
    private String checkTime;

    @ExportField(value = "申请号", index = 45, width = 15)
    @ApiModelProperty(value = "申请号")
    private String requestNo;

    @ExportField(value = "申请科室", index = 46, width = 15)
    @ApiModelProperty(value = "申请科室")
    private String requestDept;

    @ExportField(value = "申请医生", index = 47, width = 15)
    @ApiModelProperty(value = "申请医生")
    private String requestDoctor;

    @ExportField(value = "技师", index = 48, width = 15)
    @ApiModelProperty(value = "技师")
    private String technician;

    @ExportField(value = "意见公告", index = 49, width = 30)
    @ApiModelProperty(value = "意见公告")
    private String opinion;

    @ExportField(value = "检查描述", index = 50, width = 40)
    @ApiModelProperty(value = "检查描述")
    private String studyDescription;

    // ==================== 不导出的内部字段（用于数据处理，exportable=false） ====================
    
    @ExportField(value = "疾病ID", index = 999, exportable = false)
    @ApiModelProperty(value = "疾病ID")
    private Long diseaseId;

    @ExportField(value = "创建人ID", index = 999, exportable = false)
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ExportField(value = "创建时间原始", index = 999, exportable = false)
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime createTime;

    @ExportField(value = "导出时间", index = 999, exportable = false)
    @ApiModelProperty(value = "导出时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime exportTime;

    // ==================== 原始数据列表（不导出） ====================
    @ApiModelProperty(value = "病例报告")
    private List<StudyInfo> studyInfoList;

    @ApiModelProperty(value = "病例标签列表")
    private List<TagInfo> tagInfoList;

    @ApiModelProperty(value = "随访信息")
    private List<FollowInfo> followInfoList;

    @ApiModelProperty(value = "疾病概述信息")
    private DiseaseOverviewInfo diseaseOverviewInfo;

    // ==================== 导出相关字段（不导出） ====================
    @ApiModelProperty(value = "导出文件名")
    private String exportFileName;

    @ApiModelProperty(value = "导出用户ID")
    private Long exportUserId;

    @ApiModelProperty(value = "导出用户名")
    private String exportUserName;

    // ==================== 合并控制字段（不导出） ====================
    @ApiModelProperty(value = "是否为合并行的第一行")
    private boolean isFirstRowOfMerge = false;

    @ApiModelProperty(value = "合并行数")
    private int mergeRowCount = 1;

    @ApiModelProperty(value = "数据行类型：CASE-病例行，STUDY-检查报告行")
    private String rowType = "CASE";

    /**
     * 处理随访结果分类
     * 将随访信息按类型分类到对应的字段中
     */
    public void processFollowupResults() {
        if (CollUtil.isEmpty(followInfoList)) {
            return;
        }

        // 按随访类型分类处理
        for (FollowInfo followInfo : followInfoList) {
            String followType = followInfo.getFollowType();
            String followResult = followInfo.getFollowupResult();
            
            if (StrUtil.isEmpty(followType) || StrUtil.isEmpty(followResult)) {
                continue;
            }

            // 根据随访类型分类存储
            switch (followType.trim()) {
                case "手术":
                    this.surgeryResult = appendResult(this.surgeryResult, followResult);
                    break;
                case "超声":
                    this.ultrasoundResult = appendResult(this.ultrasoundResult, followResult);
                    break;
                case "临床":
                    this.clinicalResult = appendResult(this.clinicalResult, followResult);
                    break;
                case "病理":
                    this.pathologyResult = appendResult(this.pathologyResult, followResult);
                    break;
                default:
                    // 其他类型的随访结果归类到临床
                    this.clinicalResult = appendResult(this.clinicalResult, followResult);
                    break;
            }
        }
    }

    /**
     * 处理检查报告信息
     * 设置第一个检查报告的信息到对应字段
     */
    public void processStudyInfo() {
        if (CollUtil.isNotEmpty(studyInfoList)) {
            StudyInfo firstStudy = studyInfoList.get(0);
            
            // 设置检查报告相关字段
            this.studySourceTime = formatDateTime(firstStudy.getStudyTime());
            this.studyTimePart = firstStudy.getPartName();
            this.studyDate = formatDate(firstStudy.getStudyTime());
            this.outpatientNo = firstStudy.getOutPatientNo();
            this.inpatientNo = firstStudy.getInPatientNo();
            this.studyNo = firstStudy.getStudyNo();
            this.studyItem = firstStudy.getAccessNumber(); // 使用检查流水号作为检查项目
            this.studyItemName = firstStudy.getStudyItemName();
            this.partName = firstStudy.getPartName();
            this.deviceName = firstStudy.getDeviceName();
            this.routine = ""; // StudyInfoDto中没有routine字段，设为空
            this.reportDescribe = truncateText(firstStudy.getReportDescribe(), 200);
            this.reportDiagnose = truncateText(firstStudy.getReportDiagnose(), 200);
            this.reportTime = firstStudy.getReportTime(); // 已经是String类型
            this.reporter = firstStudy.getReporter();
            this.checker = firstStudy.getChecker();
            this.checkTime = firstStudy.getCheckTime(); // 已经是String类型
            this.requestNo = firstStudy.getApplyNumber();
            this.requestDept = firstStudy.getApplyDepartment();
            this.requestDoctor = firstStudy.getApplyDoctor();
            this.technician = firstStudy.getArtificer();
            this.opinion = ""; // StudyInfoDto中没有opinion字段，设为空
            this.studyDescription = truncateText(firstStudy.getMedicalHistory(), 200); // 使用病史作为检查描述
        }
    }

    /**
     * 处理其他信息字段
     */
    public void processOtherFields() {
        // 处理标签信息
        if (CollUtil.isNotEmpty(tagInfoList)) {
            this.tagInfoText = tagInfoList.stream()
                    .map(TagInfo::getTagName)
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.joining("；"));
        }

        // 处理创建时间文本
        if (createTime != null) {
            this.createTimeText = formatDateTime(createTime);
        }

        // 处理创建人名称（如果没有设置的话）
        if (StrUtil.isEmpty(createByName) && createBy != null) {
            this.createByName = createBy.toString();
        }
    }

    /**
     * 追加随访结果
     */
    private String appendResult(String existingResult, String newResult) {
        if (StrUtil.isEmpty(existingResult)) {
            return truncateText(newResult, 200);
        }
        return existingResult + "；" + truncateText(newResult, 200);
    }

    /**
     * 截断文本，防止Excel单元格内容过长
     */
    private String truncateText(String text, int maxLength) {
        if (StrUtil.isEmpty(text)) {
            return "";
        }
        if (text.length() > maxLength) {
            return text.substring(0, maxLength - 3) + "...";
        }
        return text;
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        try {
            return dateTime.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN));
        } catch (Exception e) {
            return dateTime.toString();
        }
    }

    /**
     * 格式化日期
     */
    private String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        try {
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return dateTime.toString();
        }
    }

    /**
     * 创建检查报告行数据
     * 用于展开多个检查报告
     */
    public static CaseExportDataDto createStudyRow(CaseExportDataDto caseData, StudyInfo studyInfo) {
        CaseExportDataDto studyRow = new CaseExportDataDto();
        
        // 复制基础病例信息（用于合并）
        studyRow.setCaseId(caseData.getCaseId());
        studyRow.setCaseName(caseData.getCaseName());
        studyRow.setCaseNo(caseData.getCaseNo());
        studyRow.setDiseaseName(caseData.getDiseaseName());
        studyRow.setDiagnosis(caseData.getDiagnosis());
        studyRow.setPatientId(caseData.getPatientId());
        studyRow.setPatientName(caseData.getPatientName());
        studyRow.setPatientSex(caseData.getPatientSex());
        studyRow.setPatientBirthDate(caseData.getPatientBirthDate());
        studyRow.setPatientAge(caseData.getPatientAge());
        studyRow.setMedicalHistory(caseData.getMedicalHistory());
        studyRow.setDifficulty(caseData.getDifficulty());
        studyRow.setCaseCategory(caseData.getCaseCategory());
        studyRow.setSign(caseData.getSign());
        studyRow.setCaseAnalysis(caseData.getCaseAnalysis());
        studyRow.setCreateByName(caseData.getCreateByName());
        studyRow.setQualityMatch(caseData.getQualityMatch());
        studyRow.setPositionMatch(caseData.getPositionMatch());
        studyRow.setSourceType(caseData.getSourceType());
        studyRow.setFollowStatus(caseData.getFollowStatus());
        studyRow.setCaseType(caseData.getCaseType());
        studyRow.setCreateTimeText(caseData.getCreateTimeText());
        studyRow.setTagInfoText(caseData.getTagInfoText());
        studyRow.setSurgeryResult(caseData.getSurgeryResult());
        studyRow.setUltrasoundResult(caseData.getUltrasoundResult());
        studyRow.setClinicalResult(caseData.getClinicalResult());
        studyRow.setPathologyResult(caseData.getPathologyResult());
        
        // 设置检查报告信息
        studyRow.setStudySourceTime(studyRow.formatDateTime(studyInfo.getStudyTime()));
        studyRow.setStudyTimePart(studyInfo.getPartName());
        studyRow.setStudyDate(studyRow.formatDate(studyInfo.getStudyTime()));
        studyRow.setOutpatientNo(studyInfo.getOutPatientNo());
        studyRow.setInpatientNo(studyInfo.getInPatientNo());
        studyRow.setStudyNo(studyInfo.getStudyNo());
        studyRow.setStudyItem(studyInfo.getAccessNumber()); // 使用检查流水号作为检查项目
        studyRow.setStudyItemName(studyInfo.getStudyItemName());
        studyRow.setPartName(studyInfo.getPartName());
        studyRow.setDeviceName(studyInfo.getDeviceName());
        studyRow.setRoutine(""); // StudyInfoDto中没有routine字段，设为空
        studyRow.setReportDescribe(studyRow.truncateText(studyInfo.getReportDescribe(), 200));
        studyRow.setReportDiagnose(studyRow.truncateText(studyInfo.getReportDiagnose(), 200));
        studyRow.setReportTime(studyInfo.getReportTime()); // 已经是String类型
        studyRow.setReporter(studyInfo.getReporter());
        studyRow.setChecker(studyInfo.getChecker());
        studyRow.setCheckTime(studyInfo.getCheckTime()); // 已经是String类型
        studyRow.setRequestNo(studyInfo.getApplyNumber());
        studyRow.setRequestDept(studyInfo.getApplyDepartment());
        studyRow.setRequestDoctor(studyInfo.getApplyDoctor());
        studyRow.setTechnician(studyInfo.getArtificer());
        studyRow.setOpinion(""); // StudyInfoDto中没有opinion字段，设为空
        studyRow.setStudyDescription(studyRow.truncateText(studyInfo.getMedicalHistory(), 200)); // 使用病史作为检查描述
        
        // 设置行类型
        studyRow.setRowType("STUDY");
        studyRow.setFirstRowOfMerge(false);
        
        return studyRow;
    }
}