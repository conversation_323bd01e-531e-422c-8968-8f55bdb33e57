package com.jusha.caselibrary.sickcase.export.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.aop.ExportField;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.UserUtil;
import com.jusha.caselibrary.sickcase.dto.DiseaseInfo;
import com.jusha.caselibrary.sickcase.dto.FollowInfo;
import com.jusha.caselibrary.sickcase.dto.StudyInfo;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName CaseExportDataDto
 * @Description 病例导出数据DTO - 根据用户反馈重新设计，严格按照图片字段
 * <AUTHOR>
 * @Date 2025/7/10 17:25
 **/
@Data
@Slf4j
public class CaseExportDataDto {

    // ==================== 基本病例信息（支持合并） ====================
    
    @ExportField(value = "病例ID", index = 0, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例ID")
    private Long caseId;

    @ExportField(value = "病例名称", index = 1, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ExportField(value = "病例编号", index = 2, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ExportField(value = "疾病名称", index = 3, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "疾病名称")
    private String diseaseNames;

    @ExportField(value = "最终诊断", index = 4, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "最终诊断")
    private String diagnosis;

    @ExportField(value = "患者ID", index = 5, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ExportField(value = "患者姓名", index = 6, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ExportField(value = "患者性别", index = 7, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ExportField(value = "患者生日", index = 8, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "患者生日")
    private String patientBirthDate;

    @ExportField(value = "患者年龄", index = 9, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ExportField(value = "难度等级", index = 10, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ExportField(value = "病例类型", index = 11, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例类型")
    private String caseCategory;

    @ExportField(value = "征象", index = 12, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "征象")
    private String sign;

    @ExportField(value = "病例分析", index = 13, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ExportField(value = "备注", index = 14, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "备注")
    private String remark;

    @ExportField(value = "创建人", index = 15, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ExportField(value = "来源", index = 16, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ExportField(value = "病例库类型", index = 17, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "病例库类型")
    private String caseType;

    @ExportField(value = "创建时间", index = 18, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ExportField(value = "标签信息", index = 19, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "标签信息")
    private String tagInfoText;

    @ExportField(value = "随访状态", index = 20, mergeable = true, mergeType = Constant.MERGE_TYPE_CASE)
    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    // ==================== 随访结果分类字段（四个分类） ====================
    
    @ApiModelProperty(value = "动态随访结果映射")
    private Map<String, String> followupResultMap = new HashMap<>();

    // ==================== 检查报告信息（不合并） ====================
    
    @ExportField(value = "检查UID", index = 21, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ExportField(value = "定性匹配", index = 22, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "定性匹配")
    private String qualityMatch;

    @ExportField(value = "定位匹配", index = 23, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "定位匹配")
    private String positionMatch;

    @ExportField(value = "检查流水号/影像号", index = 24, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "检查流水号/影像号")
    private String accessNumber;

    @ExportField(value = "检查时间", index = 25, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "检查时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyTime;

    @ExportField(value = "检查号", index = 26, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "检查号")
    private String studyNo;

    @ExportField(value = "就诊类型", index = 27, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    @ApiModelProperty(value = "就诊类型（门诊、住院、体检）")
    private String patientType;

    @ApiModelProperty(value = "就诊日期")
    @ExportField(value = "就诊类型", index = 28, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String visitDate;

    @ApiModelProperty(value = "门诊号")
    @ExportField(value = "就诊类型", index = 29, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String outPatientNo;

    @ApiModelProperty(value = "住院号")
    @ExportField(value = "就诊类型", index = 30, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String inPatientNo;

    @ApiModelProperty(value = "症状/体征")
    @ExportField(value = "就诊类型", index = 31, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String physicalSign;

    @ApiModelProperty(value = "临床诊断")
    @ExportField(value = "就诊类型", index = 32, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String clinicalDiagnosis;

    @ApiModelProperty(value = "检查项目名称")
    @ExportField(value = "就诊类型", index = 33, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String studyItemName;

    @ApiModelProperty(value = "检查部位名称")
    @ExportField(value = "就诊类型", index = 34, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String partName;

    @ApiModelProperty(value = "设备类型")
    @ExportField(value = "就诊类型", index = 35, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String deviceType;

    @ApiModelProperty(value = "检查状态")
    @ExportField(value = "就诊类型", index = 36, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String studyState;

    @ApiModelProperty(value = "设备名称")
    @ExportField(value = "就诊类型", index = 37, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String deviceName;

    @ApiModelProperty(value = "病史")
    @ExportField(value = "就诊类型", index = 38, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String medicalHistory;

    @ApiModelProperty(value = "患者主诉")
    @ExportField(value = "就诊类型", index = 39, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String selfReportedSymptom;

    @ApiModelProperty(value = "影像学表现")
    @ExportField(value = "就诊类型", index = 40, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String reportDescribe;

    @ApiModelProperty(value = "影像学诊断")
    @ExportField(value = "就诊类型", index = 41, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String reportDiagnose;

    @ApiModelProperty(value = "阳性/阴性")
    @ExportField(value = "就诊类型", index = 42, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String isPostive;

    @ApiModelProperty(value = "登记时间")
    @ExportField(value = "就诊类型", index = 43, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String registerTime;

    @ApiModelProperty(value = "报告时间")
    @ExportField(value = "就诊类型", index = 44, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String reportTime;

    @ApiModelProperty(value = "报告人")
    @ExportField(value = "就诊类型", index = 45, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String reporter;

    @ApiModelProperty(value = "审核人")
    @ExportField(value = "就诊类型", index = 46, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String checker;

    @ApiModelProperty(value = "审核时间")
    @ExportField(value = "就诊类型", index = 47, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String checkTime;

    @ApiModelProperty(value = "申请号")
    @ExportField(value = "就诊类型", index = 48, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String applyNumber;

    @ApiModelProperty(value = "申请科室")
    @ExportField(value = "就诊类型", index = 49, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String applyDepartment;

    @ApiModelProperty(value = "申请医生")
    @ExportField(value = "就诊类型", index = 50, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String applyDoctor;

    @ApiModelProperty(value = "技师")
    @ExportField(value = "就诊类型", index = 51, mergeable = true, mergeType = Constant.MERGE_TYPE_STUDY)
    private String artificer;

    // ==================== 不导出的内部字段（用于数据处理，exportable=false） ====================

    @ExportField(value = "创建人ID", index = 999, exportable = false)
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ExportField(value = "导出时间", index = 999, exportable = false)
    @ApiModelProperty(value = "导出时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime exportTime;

    // ==================== 原始数据列表（不导出） ====================
    @ApiModelProperty(value = "病例报告")
    private List<StudyInfo> studyInfoList;

    @ApiModelProperty(value = "病例标签列表")
    private List<TagInfo> tagInfoList;

    @ApiModelProperty(value = "随访信息")
    private List<FollowInfo> followInfoList;

    @ApiModelProperty(value = "疾病信息")
    private List<DiseaseInfo> diseaseInfoList;

    // ==================== 导出相关字段（不导出） ====================
    @ApiModelProperty(value = "导出文件名")
    private String exportFileName;

    @ApiModelProperty(value = "导出用户ID")
    private Long exportUserId;

    @ApiModelProperty(value = "导出用户名")
    private String exportUserName;

    // ==================== 合并控制字段（不导出） ====================
    @ApiModelProperty(value = "是否为合并行的第一行")
    private boolean isFirstRowOfMerge = false;

    @ApiModelProperty(value = "合并行数")
    private int mergeRowCount = 1;

    @ApiModelProperty(value = "数据行类型：CASE-病例行，STUDY-检查报告行")
    private String rowType = Constant.MERGE_TYPE_CASE;

    @ApiModelProperty(value = "随访结果合并列名")
    private String followResultMergeColName = Constant.FOLLOW_RESULT;
    
    @ApiModelProperty(value = "检查报告合并列名")
    private String studyInfoMergeColName = Constant.STUDY_INFO;

    /**
     * 处理随访结果分类
     * 将随访信息按类型分类到动态字段中，同类型的结果进行拼接
     */
    public void processFollowupResults() {
        if (CollUtil.isEmpty(followInfoList)) {
            return;
        }

        followupResultMap.clear();

        // 按随访类型分组处理
        Map<String, List<FollowInfo>> groupedFollowInfo = followInfoList.stream()
                .filter(followInfo -> StrUtil.isNotEmpty(followInfo.getFollowTypeLabel()) &&
                        StrUtil.isNotEmpty(followInfo.getFollowupResult()))
                .collect(Collectors.groupingBy(FollowInfo::getFollowTypeLabel));

        // 对每个类型的随访结果进行拼接
        for (Map.Entry<String, List<FollowInfo>> entry : groupedFollowInfo.entrySet()) {
            String followTypeLabel = entry.getKey();
            List<FollowInfo> followInfos = entry.getValue();

            // 按时间排序（如果有随访时间字段）
            followInfos.sort((f1, f2) -> {
                // 如果有随访时间字段，按时间排序
                if (f1.getCreateTime() != null && f2.getCreateTime() != null) {
                    return f1.getCreateTime().compareTo(f2.getCreateTime());
                }
                // 如果没有时间字段，按ID或创建时间排序
                if (f1.getFollowId() != null && f2.getFollowId() != null) {
                    return f1.getFollowId().compareTo(f2.getFollowId());
                }
                return 0;
            });

            // 拼接同类型的随访结果
            String combinedResult = combineFollowupResults(followInfos);
            followupResultMap.put(followTypeLabel, combinedResult);
        }
    }

    /**
     * 拼接同类型的随访结果
     */
    private String combineFollowupResults(List<FollowInfo> followInfos) {
        if (CollUtil.isEmpty(followInfos)) {
            return "";
        }

        if (followInfos.size() == 1) {
            return cleanHtmlContent(followInfos.get(0).getFollowupResult());
        }

        StringBuilder combinedResult = new StringBuilder();

        for (int i = 0; i < followInfos.size(); i++) {
            FollowInfo followInfo = followInfos.get(i);
            String result = cleanHtmlContent(followInfo.getFollowupResult());

            if (StrUtil.isEmpty(result)) {
                continue;
            }

            // 添加序号和时间信息（如果有的话）
            combinedResult.append("【").append(i + 1).append("】");

            // 如果有随访时间，添加时间信息
            if (followInfo.getCreateTime() != null) {
                String timeStr = cn.hutool.core.date.DateUtil.format(followInfo.getCreateTime(), DateUtil.SHORT_PATTERN);
                combinedResult.append("(").append(timeStr).append(") ");
            } else {
                combinedResult.append(" ");
            }

            // 添加随访结果内容
            combinedResult.append(result);

            // 添加分隔符（除了最后一个）
            if (i < followInfos.size() - 1) {
                combinedResult.append("\n━━━━━━━━━━━━━━━━━━━━\n");
            }
        }
        // 限制总长度
        return truncateText(combinedResult.toString(), 1000);
    }

    /**
     * 清理HTML内容，保留基本格式
     */
    private String cleanHtmlContent(String htmlContent) {
        if (StrUtil.isEmpty(htmlContent)) {
            return "";
        }

        // 如果不是HTML格式，直接返回
        if (!htmlContent.contains("<") || !htmlContent.contains(">")) {
            return htmlContent.trim();
        }

        try {
            // 替换常见的HTML标签为纯文本格式
            String cleaned = htmlContent
                    // 段落标签替换为换行
                    .replaceAll("(?i)<p[^>]*>", "")
                    .replaceAll("(?i)</p>", "\n")
                    // 换行标签
                    .replaceAll("(?i)<br[^>]*>", "\n")
                    // 加粗标签
                    .replaceAll("(?i)<b[^>]*>", "【")
                    .replaceAll("(?i)</b>", "】")
                    .replaceAll("(?i)<strong[^>]*>", "【")
                    .replaceAll("(?i)</strong>", "】")
                    // 斜体标签
                    .replaceAll("(?i)<i[^>]*>", "")
                    .replaceAll("(?i)</i>", "")
                    .replaceAll("(?i)<em[^>]*>", "")
                    .replaceAll("(?i)</em>", "")
                    // 列表标签
                    .replaceAll("(?i)<ul[^>]*>", "")
                    .replaceAll("(?i)</ul>", "")
                    .replaceAll("(?i)<ol[^>]*>", "")
                    .replaceAll("(?i)</ol>", "")
                    .replaceAll("(?i)<li[^>]*>", "• ")
                    .replaceAll("(?i)</li>", "\n")
                    // 移除其他HTML标签
                    .replaceAll("<[^>]+>", "")
                    // 解码HTML实体
                    .replace("&nbsp;", " ")
                    .replace("&lt;", "<")
                    .replace("&gt;", ">")
                    .replace("&amp;", "&")
                    .replace("&quot;", "\"")
                    .replace("&#39;", "'");

            // 清理多余的空行和空格
            // 多个换行合并为一个
            cleaned = cleaned.replaceAll("\n\\s*\n", "\n")
                    // 多个空格合并为一个
                    .replaceAll("[ \t]+", " ")
                    .trim();

            return cleaned;

        } catch (Exception e) {
            log.warn("清理HTML内容失败，返回原内容: {}", e.getMessage());
            return htmlContent.trim();
        }
    }

    /**
     * 获取指定类型的随访结果（格式化后的）
     */
    public String getFollowupResultByType(String followTypeLabel) {
        if (followupResultMap == null) {
            return "";
        }
        return followupResultMap.getOrDefault(followTypeLabel, "");
    }

    /**
     * 获取所有随访类型标签
     */
    public Set<String> getFollowTypeLabels() {
        if (followupResultMap == null) {
            return new HashSet<>();
        }
        return followupResultMap.keySet();
    }

    /**
     * 处理检查报告信息
     * 设置第一个检查报告的信息到对应字段
     */
    public void processStudyInfo() {
        if (CollUtil.isNotEmpty(studyInfoList)) {
            StudyInfo firstStudy = studyInfoList.get(0);
            // 设置检查报告相关字段
            this.studyUid = firstStudy.getStudyUid();
            this.accessNumber = firstStudy.getAccessNumber();
            this.studyTime = firstStudy.getStudyTime();
            this.studyNo = firstStudy.getStudyNo();
            this.qualityMatch = firstStudy.getQualityMatch();
            this.positionMatch = firstStudy.getPositionMatch();
            this.patientType = firstStudy.getPatientType();
            this.visitDate = firstStudy.getVisitDate();
            this.outPatientNo = firstStudy.getOutPatientNo();
            this.inPatientNo = firstStudy.getInPatientNo();
            this.physicalSign = firstStudy.getPhysicalSign();
            this.clinicalDiagnosis = firstStudy.getClinicalDiagnosis();
            this.studyItemName = firstStudy.getStudyItemName();
            this.partName = firstStudy.getPartName();
            this.deviceType = firstStudy.getDeviceType();
            this.studyState = firstStudy.getStudyState();
            this.deviceName = firstStudy.getDeviceName();
            this.medicalHistory = firstStudy.getMedicalHistory();
            this.selfReportedSymptom = firstStudy.getSelfReportedSymptom();
            this.reportDescribe = truncateText(firstStudy.getReportDescribe(), 200);
            this.reportDiagnose = truncateText(firstStudy.getReportDiagnose(), 200);
            this.isPostive = firstStudy.getIsPostive();
            this.registerTime = firstStudy.getRegisterTime();
            this.reportTime = firstStudy.getReportTime();
            this.reporter = firstStudy.getReporter();
            this.checker = firstStudy.getChecker();
            this.checkTime = firstStudy.getCheckTime();
            this.applyNumber = firstStudy.getApplyNumber();
            this.applyDepartment = firstStudy.getApplyDepartment();
            this.applyDoctor = firstStudy.getApplyDoctor();
            this.artificer = firstStudy.getArtificer();
        }
    }

    /**
     * 处理其他信息字段
     */
    public void processOtherFields() {
        // 处理标签信息
        if (CollUtil.isNotEmpty(tagInfoList)) {
            this.tagInfoText = tagInfoList.stream()
                    .map(TagInfo::getTagName)
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.joining("；"));
        }
        // 处理创建人名称（如果没有设置的话）
        if (StrUtil.isEmpty(createByName) && createBy != null) {
            this.createByName = UserUtil.getUserNameById(createBy);
        }
        // 处理疾病名称
        if (CollUtil.isNotEmpty(diseaseInfoList)) {
            this.diseaseNames = diseaseInfoList.stream()
                    .map(DiseaseInfo::getDiseaseName)
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.joining("；"));
        }
    }

    /**
     * 截断文本，防止Excel单元格内容过长
     */
    private String truncateText(String text, int maxLength) {
        if (StrUtil.isEmpty(text)) {
            return "";
        }
        if (text.length() > maxLength) {
            return text.substring(0, maxLength - 3) + "...";
        }
        return text;
    }

    /**
     * 创建检查报告行数据
     * 用于展开多个检查报告
     */
    public static CaseExportDataDto createStudyRow(CaseExportDataDto caseData, StudyInfo studyInfo) {
        CaseExportDataDto studyRow = new CaseExportDataDto();

        // 复制基础病例信息（用于合并）
        studyRow.setCaseId(caseData.getCaseId());
        studyRow.setCaseName(caseData.getCaseName());
        studyRow.setCaseNo(caseData.getCaseNo());
        studyRow.setDiseaseNames(caseData.getDiseaseNames());
        studyRow.setDiagnosis(caseData.getDiagnosis());
        studyRow.setPatientId(caseData.getPatientId());
        studyRow.setPatientName(caseData.getPatientName());
        studyRow.setPatientSex(caseData.getPatientSex());
        studyRow.setPatientBirthDate(caseData.getPatientBirthDate());
        studyRow.setPatientAge(caseData.getPatientAge());
        studyRow.setDifficulty(caseData.getDifficulty());
        studyRow.setCaseCategory(caseData.getCaseCategory());
        studyRow.setSign(caseData.getSign());
        studyRow.setCaseAnalysis(caseData.getCaseAnalysis());
        studyRow.setCreateByName(caseData.getCreateByName());
        studyRow.setSourceType(caseData.getSourceType());
        studyRow.setCaseType(caseData.getCaseType());
        studyRow.setCreateTime(caseData.getCreateTime());
        studyRow.setTagInfoText(caseData.getTagInfoText());
        studyRow.setFollowStatus(caseData.getFollowStatus());

        // 设置检查报告信息
        studyRow.studyUid = studyInfo.getStudyUid();
        studyRow.accessNumber = studyInfo.getAccessNumber();
        studyRow.studyTime = studyInfo.getStudyTime();
        studyRow.studyNo = studyInfo.getStudyNo();
        studyRow.qualityMatch = studyInfo.getQualityMatch();
        studyRow.positionMatch = studyInfo.getPositionMatch();
        studyRow.patientType = studyInfo.getPatientType();
        studyRow.visitDate = studyInfo.getVisitDate();
        studyRow.outPatientNo = studyInfo.getOutPatientNo();
        studyRow.inPatientNo = studyInfo.getInPatientNo();
        studyRow.physicalSign = studyInfo.getPhysicalSign();
        studyRow.clinicalDiagnosis = studyInfo.getClinicalDiagnosis();
        studyRow.studyItemName = studyInfo.getStudyItemName();
        studyRow.partName = studyInfo.getPartName();
        studyRow.deviceType = studyInfo.getDeviceType();
        studyRow.studyState = studyInfo.getStudyState();
        studyRow.deviceName = studyInfo.getDeviceName();
        studyRow.medicalHistory = studyInfo.getMedicalHistory();
        studyRow.selfReportedSymptom = studyInfo.getSelfReportedSymptom();
        studyRow.reportDescribe = studyRow.truncateText(studyInfo.getReportDescribe(), 200);
        studyRow.reportDiagnose = studyRow.truncateText(studyInfo.getReportDiagnose(), 200);
        studyRow.isPostive = studyInfo.getIsPostive();
        studyRow.registerTime = studyInfo.getRegisterTime();
        studyRow.reportTime = studyInfo.getReportTime();
        studyRow.reporter = studyInfo.getReporter();
        studyRow.checker = studyInfo.getChecker();
        studyRow.checkTime = studyInfo.getCheckTime();
        studyRow.applyNumber = studyInfo.getApplyNumber();
        studyRow.applyDepartment = studyInfo.getApplyDepartment();
        studyRow.applyDoctor = studyInfo.getApplyDoctor();
        studyRow.artificer = studyInfo.getArtificer();

        // 设置行类型
        studyRow.setRowType(Constant.MERGE_TYPE_STUDY);
        studyRow.setFirstRowOfMerge(false);
        
        return studyRow;
    }
}