package com.jusha.caselibrary.sickcase.export.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jusha.caselibrary.sickcase.dto.StudyInfo;
import com.jusha.caselibrary.common.aop.ExportField;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @ClassName CaseExportUtil
 * @Description 病例导出工具类
 * <AUTHOR>
 * @Date 2025/7/18 15:05
 **/
@Slf4j
@Component
public class CaseExportUtil {

    // 批处理大小，EasyExcel 推荐的批量写入大小
    private static final int BATCH_SIZE = 2000;

    // 缓存导出字段信息，避免重复解析（线程安全）
    private static final Map<Class<?>, List<ExportFieldInfo>> FIELD_INFO_CACHE = new ConcurrentHashMap<>();

    // 字段值缓存，提升反射性能
    private static final Map<String, Field> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 导出病例数据到Excel文件 - EasyExcel
     * 利用 EasyExcel 的流式写入、内存优化和注解驱动特性
     *
     * @param exportDataList 导出数据列表
     * @param outputPath     输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath) throws Exception {
        return exportToExcel(exportDataList, outputPath, null);
    }

    /**
     * 导出病例数据到Excel文件 - 支持字段过滤
     * 利用 EasyExcel 的流式写入、内存优化和注解驱动特性
     *
     * @param exportDataList 导出数据列表
     * @param outputPath     输出文件路径
     * @param exportFields   要导出的字段列表，为null或空时导出所有字段
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath, List<String> exportFields) throws Exception {
        log.info("开始导出Excel，数据量: {}, 输出路径: {}, 导出字段: {}",
                exportDataList != null ? exportDataList.size() : 0, outputPath, 
                exportFields != null ? exportFields : "所有字段");

        long startTime = System.currentTimeMillis();

        try {
            // 参数验证
            validateExportParameters(exportDataList, outputPath);

            // 确保输出目录存在
            ensureOutputDirectory(outputPath);
            // 预处理数据（并行处理）
            List<CaseExportDataDto> processedDataList = processExportDataOptimized(exportDataList);

            // 获取字段信息（支持字段过滤）
            List<ExportFieldInfo> fieldInfoList = getExportFieldInfoWithFilter(CaseExportDataDto.class, exportFields);

            // 使用 EasyExcel 流式写入，完全基于 @ExportField 注解
            EasyExcel.write(outputPath)
                    // 基于注解动态构建表头
                    .head(buildDynamicHead(fieldInfoList))
                    // 自定义样式处理器
                    .registerWriteHandler(new ExportFieldStyleHandler(fieldInfoList))
                    // 自定义合并处理器
                    .registerWriteHandler(new ExportFieldMergeHandler(processedDataList, fieldInfoList))
                    .sheet("病例导出数据")
                    .doWrite(() -> {
                        // 转换数据为 EasyExcel 需要的格式
                        return convertToExcelData(processedDataList, fieldInfoList);
                    });

            long endTime = System.currentTimeMillis();
            log.info("病例数据导出完成，文件路径: {}, 导出数量: {}, 导出字段数: {}, 耗时: {}ms",
                    outputPath, exportDataList != null ? exportDataList.size() : 0, fieldInfoList.size(), endTime - startTime);
            return outputPath;

        } catch (IllegalArgumentException e) {
            log.error("导出参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("病例数据导出失败，输出路径: {}, 错误信息: {}", outputPath, e.getMessage(), e);
            throw new Exception("病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出随访病例数据到Excel文件
     * 支持多 Sheet 导出和复合数据结构
     *
     * @param followExportData 随访导出数据
     * @param outputPath       输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath) throws Exception {
        return exportFollowCaseToExcel(followExportData, outputPath, null);
    }

    /**
     * 导出随访病例数据到Excel文件 - 支持字段过滤
     * 支持多 Sheet 导出和复合数据结构
     *
     * @param followExportData 随访导出数据
     * @param outputPath       输出文件路径
     * @param exportFields     要导出的字段列表，为null或空时导出所有字段
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath, List<String> exportFields) throws Exception {
        log.info("开始导出随访病例Excel，输出路径: {}, 导出字段: {}", outputPath, 
                exportFields != null ? exportFields : "所有字段");

        long startTime = System.currentTimeMillis();

        // 参数验证
        validateFollowExportParameters(followExportData, outputPath);

        // 确保输出目录存在
        ensureOutputDirectory(outputPath);

        try (ExcelWriter excelWriter = EasyExcel.write(outputPath).build()) {

            // 第一个 Sheet：病例数据
            if (CollUtil.isNotEmpty(followExportData.getCaseExportDataDtoList())) {
                List<CaseExportDataDto> processedDataList = processExportDataOptimized(
                        followExportData.getCaseExportDataDtoList());

                // 获取字段信息（支持字段过滤）
                List<ExportFieldInfo> fieldInfoList = getExportFieldInfoWithFilter(CaseExportDataDto.class, exportFields);

                WriteSheet caseSheet = EasyExcel.writerSheet(0, "病例数据")
                        .head(buildDynamicHead(fieldInfoList))
                        .registerWriteHandler(new ExportFieldStyleHandler(fieldInfoList))
                        .registerWriteHandler(new ExportFieldMergeHandler(processedDataList, fieldInfoList))
                        .build();

                // 批量写入病例数据
                writeBatchData(excelWriter, caseSheet, processedDataList, fieldInfoList);
            }

            // 第二个 Sheet：统计数据
            WriteSheet statisticsSheet = EasyExcel.writerSheet(1, "统计数据")
                    .head(buildStatisticsHead())
                    .registerWriteHandler(new ExportFieldStyleHandler(Collections.emptyList()))
                    .build();

            // 写入统计数据
            List<List<Object>> statisticsData = buildStatisticsData(followExportData);
            excelWriter.write(statisticsData, statisticsSheet);

            long endTime = System.currentTimeMillis();
            log.info("随访病例数据导出完成，文件路径: {}, 病例数量: {}, 统计字段数量: {}, 耗时: {}ms",
                    outputPath,
                    followExportData.getCaseExportDataDtoList() != null ? followExportData.getCaseExportDataDtoList().size() : 0,
                    followExportData.getDynamicExportFieldNames().size(),
                    endTime - startTime);
            return outputPath;

        } catch (IllegalArgumentException e) {
            log.error("导出参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("随访病例数据导出失败，输出路径: {}, 错误信息: {}", outputPath, e.getMessage(), e);
            throw new Exception("随访病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量写入数据，支持大数据量流式处理
     */
    private void writeBatchData(ExcelWriter excelWriter, WriteSheet writeSheet,
                                List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        int totalSize = dataList.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<CaseExportDataDto> batch = dataList.subList(i, endIndex);

            // 转换批次数据
            List<List<Object>> excelData = convertToExcelData(batch, fieldInfoList);
            excelWriter.write(excelData, writeSheet);

            log.debug("已写入批次 {}-{}/{}", i + 1, endIndex, totalSize);
        }
    }

    /**
     * 优化的数据预处理（使用并行流和缓存）
     */
    private List<CaseExportDataDto> processExportDataOptimized(List<CaseExportDataDto> exportDataList) {

        // 对于大数据集使用并行流，小数据集使用普通流
        if (exportDataList.size() > 100) {
            return exportDataList.parallelStream()
                    .map(this::processSingleCaseData)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } else {
            return exportDataList.stream()
                    .map(this::processSingleCaseData)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 处理单个病例数据
     */
    private List<CaseExportDataDto> processSingleCaseData(CaseExportDataDto caseData) {
        List<CaseExportDataDto> result = new ArrayList<>();

        // 处理随访结果分类
        caseData.processFollowupResults();
        caseData.processOtherFields();

        // 检查是否有多个检查报告
        List<StudyInfo> studyList = caseData.getStudyInfoList();

        if (CollUtil.isEmpty(studyList)) {
            // 没有检查报告，创建一行基础数据
            caseData.setRowType("CASE");
            caseData.setFirstRowOfMerge(true);
            caseData.setMergeRowCount(1);
            result.add(caseData);
        } else {
            // 有检查报告，第一行包含第一个检查报告信息
            caseData.processStudyInfo();
            caseData.setRowType("CASE");
            caseData.setFirstRowOfMerge(true);
            caseData.setMergeRowCount(studyList.size());
            result.add(caseData);

            // 为其余检查报告创建额外的行
            for (int i = 1; i < studyList.size(); i++) {
                CaseExportDataDto studyRow = CaseExportDataDto.createStudyRow(caseData, studyList.get(i));
                result.add(studyRow);
            }
        }

        return result;
    }

    /**
     * 动态构建表头（基于注解）
     */
    private List<List<String>> buildDynamicHead(List<ExportFieldInfo> fieldInfoList) {
        return fieldInfoList.stream()
                .sorted(Comparator.comparingInt(ExportFieldInfo::getIndex))
                .map(fieldInfo -> Collections.singletonList(fieldInfo.getHeaderName()))
                .collect(Collectors.toList());
    }

    /**
     * 构建统计数据表头
     */
    private List<List<String>> buildStatisticsHead() {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("统计项目"));
        head.add(Collections.singletonList("统计值"));
        return head;
    }

    /**
     * 构建统计数据
     */
    private List<List<Object>> buildStatisticsData(FollowCaseExportDataDto followExportData) {
        List<List<Object>> data = new ArrayList<>();

        Set<String> fieldNames = followExportData.getDynamicExportFieldNames();
        for (String fieldName : fieldNames) {
            List<Object> row = new ArrayList<>();
            row.add(fieldName);
            Object value = followExportData.getDynamicExportFieldValue(fieldName);
            row.add(value != null ? value.toString() : "");
            data.add(row);
        }

        return data;
    }

    /**
     * 转换数据为 EasyExcel 需要的格式
     */
    private List<List<Object>> convertToExcelData(List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        return dataList.stream()
                .map(data -> convertSingleRowData(data, fieldInfoList))
                .collect(Collectors.toList());
    }

    /**
     * 转换单行数据
     */
    private List<Object> convertSingleRowData(CaseExportDataDto data, List<ExportFieldInfo> fieldInfoList) {
        return fieldInfoList.stream()
                .sorted(Comparator.comparingInt(ExportFieldInfo::getIndex))
                .map(fieldInfo -> getFieldValue(data, fieldInfo.getField()))
                .collect(Collectors.toList());
    }

    /**
     * 获取字段值（使用缓存提升性能）
     */
    private Object getFieldValue(Object obj, Field field) {
        try {
            String fieldKey = obj.getClass().getName() + "." + field.getName();
            Field cachedField = FIELD_CACHE.computeIfAbsent(fieldKey, k -> {
                field.setAccessible(true);
                return field;
            });

            Object value = cachedField.get(obj);
            return value != null ? value : "";
        } catch (Exception e) {
            log.warn("获取字段值失败: {}", field.getName(), e);
            return "";
        }
    }

    /**
     * 获取导出字段信息（使用缓存）
     */
    private List<ExportFieldInfo> getExportFieldInfoCached(Class<?> clazz) {
        return FIELD_INFO_CACHE.computeIfAbsent(clazz, this::parseExportFieldsOptimized);
    }

    /**
     * 获取导出字段信息（支持字段过滤）
     */
    private List<ExportFieldInfo> getExportFieldInfoWithFilter(Class<?> clazz, List<String> exportFields) {
        // 先获取所有字段
        List<ExportFieldInfo> allFieldInfoList = getExportFieldInfoCached(clazz);
        
        // 如果没有指定字段过滤，返回所有字段
        if (exportFields == null || exportFields.isEmpty()) {
            return allFieldInfoList;
        }
        
        // 创建字段名到字段信息的映射
        Map<String, ExportFieldInfo> fieldMap = allFieldInfoList.stream()
                .collect(Collectors.toMap(
                    fieldInfo -> fieldInfo.getField().getName(), 
                    fieldInfo -> fieldInfo
                ));
        
        // 根据指定的字段列表过滤，并保持原有的顺序
        List<ExportFieldInfo> filteredFieldInfoList = new ArrayList<>();
        int index = 0;
        for (String fieldName : exportFields) {
            ExportFieldInfo fieldInfo = fieldMap.get(fieldName);
            if (fieldInfo != null) {
                // 创建新的字段信息，更新index以保证连续性
                ExportFieldInfo newFieldInfo = new ExportFieldInfo(
                    fieldInfo.getHeaderName(),
                    // 重新分配索引
                    index++,
                    fieldInfo.getWidth(),
                    fieldInfo.isMergeable(),
                    fieldInfo.getMergeType(),
                    fieldInfo.isExportable(),
                    fieldInfo.getField()
                );
                filteredFieldInfoList.add(newFieldInfo);
            } else {
                log.warn("指定的导出字段不存在: {}", fieldName);
            }
        }
        
        log.info("字段过滤完成，原字段数: {}, 过滤后字段数: {}", 
                allFieldInfoList.size(), filteredFieldInfoList.size());
        
        return filteredFieldInfoList;
    }

    /**
     * 解析导出字段信息
     */
    private List<ExportFieldInfo> parseExportFieldsOptimized(Class<?> clazz) {
        List<ExportFieldInfo> fieldInfoList = new ArrayList<>();

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExportField annotation = field.getAnnotation(ExportField.class);
            if (annotation != null && annotation.exportable()) {
                field.setAccessible(true);

                ExportFieldInfo fieldInfo = new ExportFieldInfo(
                        annotation.value(),
                        annotation.index(),
                        annotation.width(),
                        annotation.mergeable(),
                        annotation.mergeType(),
                        annotation.exportable(),
                        field
                );
                fieldInfoList.add(fieldInfo);
            }
        }

        // 按索引排序
        fieldInfoList.sort(Comparator.comparingInt(ExportFieldInfo::getIndex));

        log.info("解析到 {} 个可导出字段", fieldInfoList.size());
        return fieldInfoList;
    }

    /**
     * 验证导出参数
     */
    private void validateExportParameters(List<CaseExportDataDto> exportDataList, String outputPath) {
        if (CollUtil.isEmpty(exportDataList)) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        if (!outputPath.toLowerCase().endsWith(".xlsx") && !outputPath.toLowerCase().endsWith(".xls")) {
            throw new IllegalArgumentException("输出文件必须是Excel格式(.xlsx或.xls)");
        }

        log.debug("导出参数验证通过，数据量: {}, 输出路径: {}", exportDataList.size(), outputPath);
    }

    /**
     * 验证随访导出参数
     */
    private void validateFollowExportParameters(FollowCaseExportDataDto followExportData, String outputPath) {
        if (followExportData == null) {
            throw new IllegalArgumentException("随访导出数据不能为空");
        }

        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        if (!outputPath.toLowerCase().endsWith(".xlsx") && !outputPath.toLowerCase().endsWith(".xls")) {
            throw new IllegalArgumentException("输出文件必须是Excel格式(.xlsx或.xls)");
        }

        // 验证病例数据
        if (CollUtil.isEmpty(followExportData.getCaseExportDataDtoList())) {
            log.warn("随访导出数据中没有病例数据");
        }

        log.debug("随访导出参数验证通过，输出路径: {}", outputPath);
    }

    /**
     * 确保输出目录存在
     */
    private void ensureOutputDirectory(String outputPath) {
        try {
            File outputFile = new File(outputPath);
            File outputDir = outputFile.getParentFile();

            if (outputDir != null && !outputDir.exists()) {
                boolean created = outputDir.mkdirs();
                if (created) {
                    log.info("创建输出目录: {}", outputDir.getAbsolutePath());
                } else {
                    log.warn("输出目录创建可能失败: {}", outputDir.getAbsolutePath());
                }
            }

            // 检查目录是否可写
            if (outputDir != null && !outputDir.canWrite()) {
                throw new IllegalArgumentException("输出目录不可写: " + outputDir.getAbsolutePath());
            }

        } catch (Exception e) {
            log.error("确保输出目录存在时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("无法创建输出目录: " + e.getMessage(), e);
        }
    }
}
