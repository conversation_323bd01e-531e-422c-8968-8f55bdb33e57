package com.jusha.caselibrary.sickcase.export.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jusha.caselibrary.common.aop.ExportField;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.sickcase.dto.StudyInfo;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @ClassName CaseExportUtil
 * @Description 病例导出工具类
 * <AUTHOR>
 * @Date 2025/7/18 15:05
 **/
@Slf4j
@Component
public class CaseExportUtil {

    // 批处理大小，EasyExcel 推荐的批量写入大小
    private static final int BATCH_SIZE = 2000;
    
    // 最大导出条数限制
    private static final int MAX_EXPORT_ROWS = 10_000;
    
    // 导出文件大小上限（10MB）
    private static final long MAX_FILE_SIZE_BYTES = 10L * 1024 * 1024;

    // 缓存导出字段信息，避免重复解析（线程安全）
    private static final Map<Class<?>, List<ExportFieldInfo>> FIELD_INFO_CACHE = new ConcurrentHashMap<>();

    // 字段值缓存，提升反射性能
    private static final Map<String, Field> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 导出病例数据到Excel文件 - EasyExcel
     * 利用 EasyExcel 的流式写入、内存优化和注解驱动特性
     *
     * @param exportDataList 导出数据列表
     * @param outputPath     输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath) throws Exception {
        return exportToExcel(exportDataList, outputPath, null);
    }

    /**
     * 导出病例数据到Excel文件 - 支持字段过滤
     * 利用 EasyExcel 的流式写入、内存优化和注解驱动特性
     *
     * @param exportDataList 导出数据列表
     * @param outputPath     输出文件路径
     * @param exportFields   要导出的字段列表，为null或空时导出所有字段
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath, List<String> exportFields) throws Exception {
        log.info("开始导出Excel，数据量: {}, 输出路径: {}, 导出字段: {}",
                exportDataList != null ? exportDataList.size() : 0, outputPath, 
                exportFields != null ? exportFields : "所有字段");

        if (exportDataList == null || exportDataList.isEmpty()) {
            log.warn("导出数据为空，跳过导出");
            return outputPath;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 参数验证
            validateExportParameters(exportDataList, outputPath);

            // 确保输出目录存在
            ensureOutputDirectory(outputPath);

            // 预处理数据（并行处理）
            List<CaseExportDataDto> processedDataList = processExportDataOptimized(exportDataList);

            // 收集所有随访类型标签
            Set<String> allFollowTypes = collectAllFollowTypes(processedDataList);
            log.info("检测到的随访类型: {}", allFollowTypes);

            // 获取字段信息（支持字段过滤）
            List<ExportFieldInfo> baseFieldInfoList = getExportFieldInfoWithFilter(CaseExportDataDto.class, exportFields);

            List<ExportFieldInfo> dynamicFieldInfoList = buildDynamicFieldInfoList(baseFieldInfoList, allFollowTypes);

            // 构建动态表头（包含随访结果合并）
            List<List<String>> dynamicHead = buildDynamicHeadWithFollowTypes(dynamicFieldInfoList, allFollowTypes);


            // 使用 EasyExcel 流式写入，完全基于 @ExportField 注解
            EasyExcel.write(outputPath)
                    .head(dynamicHead)
                    // 自定义样式处理器
                    .registerWriteHandler(new ExportFieldStyleHandler(dynamicFieldInfoList))
                    // 自定义合并处理器（传递 fieldInfoList）
                    .registerWriteHandler(new CustomMergeStrategy(processedDataList, allFollowTypes, dynamicFieldInfoList))
                    // 自定义单元格宽度配置
                    .registerWriteHandler(new CustomCellWriteWidthConfig())
                    // 自定义单元格高度配置
                    .registerWriteHandler(new CustomCellWriteHeightConfig())
                    .sheet("病例导出数据")
                    .doWrite(() -> {
                        // 转换数据为 EasyExcel 需要的格式（包含动态随访结果）
                        return convertToExcelDataWithFollowTypes(processedDataList, dynamicFieldInfoList, allFollowTypes);
                    });

            // 写入完成后检查文件大小
            ensureFileSizeWithinLimit(outputPath, MAX_FILE_SIZE_BYTES);

            long endTime = System.currentTimeMillis();
            log.info("病例数据导出完成，文件路径: {}, 导出数量: {}, 导出字段数: {}, 耗时: {}ms",
                    outputPath, exportDataList.size(), baseFieldInfoList.size(), endTime - startTime);
            return outputPath;

        } catch (IllegalArgumentException e) {
            log.error("导出参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("病例数据导出失败，输出路径: {}, 错误信息: {}", outputPath, e.getMessage(), e);
            throw new Exception("病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建动态字段信息列表（包含随访结果字段）
     */
    private List<ExportFieldInfo> buildDynamicFieldInfoList(List<ExportFieldInfo> baseFieldInfoList, Set<String> followTypes) {
        List<ExportFieldInfo> dynamicFieldInfoList = new ArrayList<>();

        // 按索引排序基础字段
        List<ExportFieldInfo> sortedBaseFields = baseFieldInfoList.stream()
                // 只处理可导出字段
                .filter(fieldInfo -> fieldInfo.isExportable()
                        && !fieldInfo.getHeaderName().equals(Constant.FOLLOW_RESULT))
                .sorted(Comparator.comparingInt(ExportFieldInfo::getIndex))
                .collect(Collectors.toList());
        // 获取是否有随访结果字段
        boolean hasFollowResult = baseFieldInfoList.get(baseFieldInfoList.size() - 1)
                .getHeaderName().equals(Constant.FOLLOW_RESULT);

        int currentIndex = 0;

        // 先添加所有 CASE 类型的字段（基础字段）
        for (ExportFieldInfo fieldInfo : sortedBaseFields) {
            if (Constant.MERGE_TYPE_CASE.equals(fieldInfo.getMergeType())) {
                ExportFieldInfo newFieldInfo = fieldInfo.copy();
                newFieldInfo.setIndex(currentIndex++);
                dynamicFieldInfoList.add(newFieldInfo);
            }
        }

        // 然后添加动态随访结果字段
        if (hasFollowResult) {
            for (String followType : followTypes) {
                ExportFieldInfo followFieldInfo = new ExportFieldInfo(
                        // 表头名称就是随访类型标签
                        followType,
                        // 动态分配索引
                        currentIndex++,
                        // 默认宽度
                        15,
                        // 可合并
                        true,
                        // 随访结果字段和病例信息一样合并
                        Constant.MERGE_TYPE_CASE,
                        // 可导出
                        true,
                        // 这是动态字段，没有对应的Field
                        null
                );
                dynamicFieldInfoList.add(followFieldInfo);
            }
        }

        // 最后添加所有 STUDY 类型的字段（检查报告字段）
        for (ExportFieldInfo fieldInfo : sortedBaseFields) {
            if (Constant.MERGE_TYPE_STUDY.equals(fieldInfo.getMergeType())) {
                ExportFieldInfo newFieldInfo = fieldInfo.copy();
                newFieldInfo.setIndex(currentIndex++);
                dynamicFieldInfoList.add(newFieldInfo);
            }
        }

        // 添加其他类型的字段（如果有的话）
        for (ExportFieldInfo fieldInfo : sortedBaseFields) {
            if (!Constant.MERGE_TYPE_CASE.equals(fieldInfo.getMergeType()) &&
                    !Constant.MERGE_TYPE_STUDY.equals(fieldInfo.getMergeType())) {
                ExportFieldInfo newFieldInfo = fieldInfo.copy();
                newFieldInfo.setIndex(currentIndex++);
                dynamicFieldInfoList.add(newFieldInfo);
            }
        }

        return dynamicFieldInfoList;
    }

    /**
     * 转换数据为 EasyExcel 需要的格式（包含动态随访结果）
     */
    private List<List<Object>> convertToExcelDataWithFollowTypes(List<CaseExportDataDto> dataList,
                                                                 List<ExportFieldInfo> fieldInfoList,
                                                                 Set<String> followTypes) {
        return dataList.stream()
                .map(data -> convertSingleRowDataWithFollowTypes(data, fieldInfoList, followTypes))
                .collect(Collectors.toList());
    }

    /**
     * 将字段值转换为字符串格式，避免科学记数法
     */
    private String convertToStringValue(Object value) {
        if (value == null) {
            return "";
        }

        // 如果是数字类型，转换为字符串以避免科学记数法
        if (value instanceof Number) {
            if (value instanceof Long || value instanceof Integer) {
                return String.valueOf(value);
            } else if (value instanceof Double || value instanceof Float) {
                // 对于浮点数，保留适当的小数位数
                return String.format("%.2f", ((Number) value).doubleValue());
            }
        }

        return value.toString();
    }

    /**
     * 导出随访病例数据到Excel文件
     * 支持多 Sheet 导出和复合数据结构
     *
     * @param followExportData 随访导出数据
     * @param outputPath       输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath) throws Exception {
        return exportFollowCaseToExcel(followExportData, outputPath, null);
    }

    /**
     * 导出随访病例数据到Excel文件 - 支持字段过滤
     * 支持多 Sheet 导出和复合数据结构
     *
     * @param followExportData 随访导出数据
     * @param outputPath       输出文件路径
     * @param exportFields     要导出的字段列表，为null或空时导出所有字段
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath, List<String> exportFields) throws Exception {
        log.info("开始导出随访病例Excel，输出路径: {}, 导出字段: {}", outputPath, 
                exportFields != null ? exportFields : "所有字段");

        long startTime = System.currentTimeMillis();

        // 参数验证
        validateFollowExportParameters(followExportData, outputPath);

        // 确保输出目录存在
        ensureOutputDirectory(outputPath);

        // 预处理数据
        List<CaseExportDataDto> processedDataList = processExportDataOptimized(
                followExportData.getCaseExportDataDtoList());

        // 收集所有随访类型标签
        Set<String> allFollowTypes = collectAllFollowTypes(followExportData.getCaseExportDataDtoList());
        log.info("检测到的随访类型: {}", allFollowTypes);

        try {
            // 获取基础字段信息
            List<ExportFieldInfo> baseFieldInfoList = getExportFieldInfoWithFilter(CaseExportDataDto.class, exportFields);

            // 构建动态字段信息（包含随访结果字段）
            List<ExportFieldInfo> dynamicFieldInfoList = buildDynamicFieldInfoList(baseFieldInfoList, allFollowTypes);

            // 构建完整的导出数据（包含病例数据和统计数据）
            List<List<Object>> completeExcelData = buildCompleteExcelData(processedDataList, followExportData, dynamicFieldInfoList, allFollowTypes);
            
            // 验证导出数据
            if (completeExcelData == null || completeExcelData.isEmpty()) {
                log.warn("完整的Excel数据为空，创建默认数据");
                completeExcelData = new ArrayList<>();
                // 添加一个空行，避免Excel文件为空
                List<Object> emptyRow = new ArrayList<>();
                for (int i = 0; i < dynamicFieldInfoList.size(); i++) {
                    emptyRow.add("");
                }
                completeExcelData.add(emptyRow);
            }

            // 构建动态表头
            List<List<String>> dynamicHead = buildDynamicHeadWithFollowTypes(dynamicFieldInfoList, allFollowTypes);
            
            // 验证表头数据
            if (dynamicHead == null || dynamicHead.isEmpty()) {
                log.error("动态表头为空，无法生成Excel文件");
                throw new Exception("动态表头构建失败");
            }

            // 使用 EasyExcel 写入单个 Sheet
            EasyExcel.write(outputPath)
                    .head(dynamicHead)
                    .registerWriteHandler(new ExportFieldStyleHandler(dynamicFieldInfoList))
                    // 自定义合并处理器（传递 fieldInfoList）
                    .registerWriteHandler(new CustomMergeStrategy(processedDataList, allFollowTypes, dynamicFieldInfoList))
                    .registerWriteHandler(new CustomCellWriteWidthConfig())
                    .registerWriteHandler(new CustomCellWriteHeightConfig())
                    .sheet("病例数据")
                    .doWrite(completeExcelData);

            // 写入完成后检查文件大小
            ensureFileSizeWithinLimit(outputPath, MAX_FILE_SIZE_BYTES);

            long endTime = System.currentTimeMillis();
            log.info("随访病例数据导出完成，文件路径: {}, 病例数量: {}, 随访类型数量: {}, 耗时: {}ms",
                    outputPath, processedDataList.size(), allFollowTypes.size(), endTime - startTime);
            return outputPath;

        } catch (Exception e) {
            log.error("随访病例数据导出失败: {}", e.getMessage(), e);
            throw new Exception("随访病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 收集所有随访类型标签
     */
    private Set<String> collectAllFollowTypes(List<CaseExportDataDto> dataList) {
        Set<String> followTypes = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(dataList)) {
            for (CaseExportDataDto data : dataList) {
//                data.processFollowupResults(); // 确保处理了随访结果
                followTypes.addAll(data.getFollowTypeLabels());
            }
        }
        return followTypes;
    }

    /**
     * 找到插入随访类型字段的位置
     */
    private int findInsertIndexForFollowTypes(List<ExportFieldInfo> fieldInfoList) {
        // 在标签信息字段之后插入随访结果字段
        for (int i = 0; i < fieldInfoList.size(); i++) {
            if ("标签信息".equals(fieldInfoList.get(i).getHeaderName())) {
                return i + 1;
            }
        }
        // 如果没找到标签信息字段，就插入到最后
        return fieldInfoList.size();
    }

    /**
     * 获取字段信息列表中的最大索引
     */
    private int getMaxIndex(List<ExportFieldInfo> fieldInfoList) {
        return fieldInfoList.stream()
                .mapToInt(ExportFieldInfo::getIndex)
                .max()
                .orElse(0);
    }

    /**
     * 构建动态表头（包含随访结果主表头和子表头）
     */
    private List<List<String>> buildDynamicHeadWithFollowTypes(List<ExportFieldInfo> fieldInfoList, Set<String> followTypes) {
        List<List<String>> head = new ArrayList<>();

        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            String headerName = fieldInfo.getHeaderName();
            String mergeType = fieldInfo.getMergeType();

            // 判断是否是动态随访结果字段
            if (followTypes.contains(headerName)) {
                // 随访结果字段：第一行是"随访结果"，第二行是具体的随访类型
                head.add(Arrays.asList(Constant.FOLLOW_RESULT, headerName));
            }
            // 根据 mergeType 判断字段类型
            else if (Constant.MERGE_TYPE_STUDY.equals(mergeType)) {
                // 检查报告字段：第一行是"检查报告"，第二行是具体的检查字段
                head.add(Arrays.asList(Constant.STUDY_INFO, headerName));
            }
            // CASE 类型或其他类型的基础字段
            else {
                // 基础字段：两行都是相同内容
                head.add(Arrays.asList(headerName, headerName));
            }
        }

        return head;
    }

    /**
     * 构建完整的Excel数据（病例数据 + 统计数据）
     */
    private List<List<Object>> buildCompleteExcelData(List<CaseExportDataDto> processedDataList,
                                                      FollowCaseExportDataDto followExportData,
                                                      List<ExportFieldInfo> fieldInfoList,
                                                      Set<String> followTypes) {
        List<List<Object>> completeData = new ArrayList<>();

        // 添加病例数据
        for (CaseExportDataDto data : processedDataList) {
            List<Object> row = convertSingleRowDataWithFollowTypes(data, fieldInfoList, followTypes);
            completeData.add(row);
        }

        // 添加空行分隔
        List<Object> emptyRow = new ArrayList<>();
        for (int i = 0; i < fieldInfoList.size(); i++) {
            emptyRow.add("");
        }
        completeData.add(emptyRow);

        // 添加统计数据标题行
        List<Object> statisticsTitleRow = new ArrayList<>();
        statisticsTitleRow.add("统计信息");
        for (int i = 1; i < fieldInfoList.size(); i++) {
            statisticsTitleRow.add("");
        }
        completeData.add(statisticsTitleRow);

        // 添加统计数据
        Set<String> statisticsFieldNames = followExportData.getDynamicExportFieldNames();
        if (statisticsFieldNames != null && !statisticsFieldNames.isEmpty()) {
            for (String fieldName : statisticsFieldNames) {
                List<Object> statisticsRow = new ArrayList<>();
                statisticsRow.add(fieldName != null ? fieldName : "");
                Object value = followExportData.getDynamicExportFieldValue(fieldName);
                statisticsRow.add(value != null ? value.toString() : "");
                // 填充剩余列
                for (int i = 2; i < fieldInfoList.size(); i++) {
                    statisticsRow.add("");
                }
                completeData.add(statisticsRow);
            }
        }

        return completeData;
    }

    /**
     * 转换单行数据（包含动态随访结果）
     */
    private List<Object> convertSingleRowDataWithFollowTypes(CaseExportDataDto data,
                                                             List<ExportFieldInfo> fieldInfoList,
                                                             Set<String> followTypes) {
        List<Object> row = new ArrayList<>();

        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            String headerName = fieldInfo.getHeaderName();

            if (followTypes.contains(headerName)) {
                // 随访结果字段：从动态映射中获取值
                row.add(data.getFollowupResultByType(headerName));
            } else {
                // 普通字段：通过反射获取值
                if (fieldInfo.getField() != null) {
                    row.add(getFieldValue(data, fieldInfo.getField()));
                } else {
                    row.add("");
                }
            }
        }

        return row;
    }

    /**
     * 批量写入数据，支持大数据量流式处理
     */
    private void writeBatchData(ExcelWriter excelWriter, WriteSheet writeSheet,
                                List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        int totalSize = dataList.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<CaseExportDataDto> batch = dataList.subList(i, endIndex);

            // 转换批次数据
            List<List<Object>> excelData = convertToExcelData(batch, fieldInfoList);
            excelWriter.write(excelData, writeSheet);

            log.debug("已写入批次 {}-{}/{}", i + 1, endIndex, totalSize);
        }
    }

    /**
     * 优化的数据预处理（使用并行流和缓存）
     */
    private List<CaseExportDataDto> processExportDataOptimized(List<CaseExportDataDto> exportDataList) {

        // 对于大数据集使用并行流，小数据集使用普通流
        if (exportDataList.size() > 100) {
            return exportDataList.parallelStream()
                    .map(this::processSingleCaseData)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } else {
            return exportDataList.stream()
                    .map(this::processSingleCaseData)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 处理单个病例数据
     */
    private List<CaseExportDataDto> processSingleCaseData(CaseExportDataDto caseData) {
        List<CaseExportDataDto> result = new ArrayList<>();

        // 处理随访结果分类
        caseData.processFollowupResults();
        caseData.processOtherFields();

        // 检查是否有多个检查报告
        List<StudyInfo> studyList = caseData.getStudyInfoList();

        if (CollUtil.isEmpty(studyList)) {
            // 没有检查报告，创建一行基础数据
            caseData.setRowType(Constant.MERGE_TYPE_CASE);
            caseData.setFirstRowOfMerge(true);
            caseData.setMergeRowCount(1);
            result.add(caseData);
        } else {
            // 有检查报告，第一行包含第一个检查报告信息
            caseData.processStudyInfo();
            caseData.setRowType(Constant.MERGE_TYPE_CASE);
            caseData.setFirstRowOfMerge(true);
            caseData.setMergeRowCount(studyList.size());
            result.add(caseData);

            // 为其余检查报告创建额外的行
            for (int i = 1; i < studyList.size(); i++) {
                CaseExportDataDto studyRow = CaseExportDataDto.createStudyRow(caseData, studyList.get(i));
                result.add(studyRow);
            }
        }

        return result;
    }

    /**
     * 转换数据为 EasyExcel 需要的格式
     */
    private List<List<Object>> convertToExcelData(List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        return dataList.stream()
                .map(data -> convertSingleRowData(data, fieldInfoList))
                .collect(Collectors.toList());
    }

    /**
     * 转换单行数据
     */
    private List<Object> convertSingleRowData(CaseExportDataDto data, List<ExportFieldInfo> fieldInfoList) {
        return fieldInfoList.stream()
                .sorted(Comparator.comparingInt(ExportFieldInfo::getIndex))
                .map(fieldInfo -> getFieldValue(data, fieldInfo.getField()))
                .collect(Collectors.toList());
    }

    /**
     * 获取字段值（使用缓存提升性能）
     */
    private Object getFieldValue(Object obj, Field field) {
        try {
            String fieldKey = obj.getClass().getName() + "." + field.getName();
            Field cachedField = FIELD_CACHE.computeIfAbsent(fieldKey, k -> {
                field.setAccessible(true);
                return field;
            });

            Object value = cachedField.get(obj);
            return value != null ? value : "";
        } catch (Exception e) {
            log.warn("获取字段值失败: {}", field.getName(), e);
            return "";
        }
    }

    /**
     * 获取导出字段信息（使用缓存）
     */
    private List<ExportFieldInfo> getExportFieldInfoCached(Class<?> clazz) {
        return FIELD_INFO_CACHE.computeIfAbsent(clazz, this::parseExportFieldsOptimized);
    }

    /**
     * 获取导出字段信息（支持字段过滤）
     */
    private List<ExportFieldInfo> getExportFieldInfoWithFilter(Class<?> clazz, List<String> exportFields) {
        // 先获取所有字段
        List<ExportFieldInfo> allFieldInfoList = getExportFieldInfoCached(clazz);
        
        // 如果没有指定字段过滤，返回所有字段
        if (exportFields == null || exportFields.isEmpty()) {
            return allFieldInfoList;
        }
        
        // 创建字段名到字段信息的映射
        Map<String, ExportFieldInfo> fieldMap = allFieldInfoList.stream()
                .collect(Collectors.toMap(
                    fieldInfo -> fieldInfo.getField().getName(), 
                    fieldInfo -> fieldInfo
                ));
        
        // 根据指定的字段列表过滤，并保持原有的顺序
        List<ExportFieldInfo> filteredFieldInfoList = new ArrayList<>();
        int index = 0;
        ExportFieldInfo followFieldInfo = null;
        for (String fieldName : exportFields) {
            ExportFieldInfo fieldInfo = fieldMap.get(fieldName);
            if (Constant.FOLLOW_FIELD.equals(fieldName)) {
                // 随访结果放最后
                followFieldInfo = fieldInfo;
                continue;
            }
            if (fieldInfo != null) {
                // 创建新的字段信息，更新index以保证连续性
                ExportFieldInfo newFieldInfo = new ExportFieldInfo(
                    fieldInfo.getHeaderName(),
                    // 重新分配索引
                    index++,
                    fieldInfo.getWidth(),
                    fieldInfo.isMergeable(),
                    fieldInfo.getMergeType(),
                    fieldInfo.isExportable(),
                    fieldInfo.getField()
                );
                filteredFieldInfoList.add(newFieldInfo);
            } else {
                log.warn("指定的导出字段不存在: {}", fieldName);
            }
        }
        if (followFieldInfo != null) {
            filteredFieldInfoList.add(followFieldInfo);
        }
        
        log.info("字段过滤完成，原字段数: {}, 过滤后字段数: {}", 
                allFieldInfoList.size(), filteredFieldInfoList.size());
        
        return filteredFieldInfoList;
    }

    /**
     * 解析导出字段信息
     */
    private List<ExportFieldInfo> parseExportFieldsOptimized(Class<?> clazz) {
        List<ExportFieldInfo> fieldInfoList = new ArrayList<>();

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExportField annotation = field.getAnnotation(ExportField.class);
            if (annotation != null && annotation.exportable()) {
                field.setAccessible(true);

                ExportFieldInfo fieldInfo = new ExportFieldInfo(
                        annotation.value(),
                        annotation.index(),
                        annotation.width(),
                        annotation.mergeable(),
                        annotation.mergeType(),
                        annotation.exportable(),
                        field
                );
                fieldInfoList.add(fieldInfo);
            }
        }

        // 按索引排序
        fieldInfoList.sort(Comparator.comparingInt(ExportFieldInfo::getIndex));

        log.info("解析到 {} 个可导出字段", fieldInfoList.size());
        return fieldInfoList;
    }

    /**
     * 验证导出参数
     */
    private void validateExportParameters(List<CaseExportDataDto> exportDataList, String outputPath) {
        if (CollUtil.isEmpty(exportDataList)) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        // 条数限制检查
        if (exportDataList.size() > MAX_EXPORT_ROWS) {
            throw new IllegalArgumentException("导出数据条数超过上限(10000)，请缩小筛选范围后重试");
        }

        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        if (!outputPath.toLowerCase().endsWith(".xlsx") && !outputPath.toLowerCase().endsWith(".xls")) {
            throw new IllegalArgumentException("输出文件必须是Excel格式(.xlsx或.xls)");
        }

        log.debug("导出参数验证通过，数据量: {}, 输出路径: {}", exportDataList.size(), outputPath);
    }

    /**
     * 验证随访导出参数
     */
    private void validateFollowExportParameters(FollowCaseExportDataDto followExportData, String outputPath) {
        if (followExportData == null) {
            throw new IllegalArgumentException("随访导出数据不能为空");
        }

        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        if (!outputPath.toLowerCase().endsWith(".xlsx") && !outputPath.toLowerCase().endsWith(".xls")) {
            throw new IllegalArgumentException("输出文件必须是Excel格式(.xlsx或.xls)");
        }

        // 验证病例数据
        if (CollUtil.isEmpty(followExportData.getCaseExportDataDtoList())) {
            log.warn("随访导出数据中没有病例数据");
        } else {
            // 条数限制检查
            if (followExportData.getCaseExportDataDtoList().size() > MAX_EXPORT_ROWS) {
                throw new IllegalArgumentException("随访导出数据条数超过上限(10000)，请缩小筛选范围后重试");
            }
        }

        log.debug("随访导出参数验证通过，输出路径: {}", outputPath);
    }

    /**
     * 确保输出目录存在
     */
    private void ensureOutputDirectory(String outputPath) {
        try {
            File outputFile = new File(outputPath);
            File outputDir = outputFile.getParentFile();

            if (outputDir != null && !outputDir.exists()) {
                boolean created = outputDir.mkdirs();
                if (created) {
                    log.info("创建输出目录: {}", outputDir.getAbsolutePath());
                } else {
                    log.warn("输出目录创建可能失败: {}", outputDir.getAbsolutePath());
                }
            }

            // 检查目录是否可写
            if (outputDir != null && !outputDir.canWrite()) {
                throw new IllegalArgumentException("输出目录不可写: " + outputDir.getAbsolutePath());
            }

        } catch (Exception e) {
            log.error("确保输出目录存在时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("无法创建输出目录: " + e.getMessage(), e);
        }
    }

    /**
     * 确保导出的文件大小不超过限制
     */
    private void ensureFileSizeWithinLimit(String outputPath, long maxBytes) throws Exception {
        try {
            File f = new File(outputPath);
            if (f.exists()) {
                long size = f.length();
                if (size > maxBytes) {
                    // 删除过大的文件，避免下游拿到损坏或超标文件
                    boolean deleted = f.delete();
                    log.error("导出文件大小超限: {} bytes > {} bytes, 文件已{}", size, maxBytes, deleted ? "删除" : "无法删除");
                    throw new Exception("导出文件大小超过10MB，请缩小筛选范围或分批导出");
                }
                log.debug("文件大小检查通过: {} bytes (限制: {} bytes)", size, maxBytes);
            } else {
                log.warn("导出文件不存在，无法检查大小: {}", outputPath);
            }
        } catch (Exception ex) {
            // 直接抛出，让上层处理
            throw ex;
        }
    }
}
