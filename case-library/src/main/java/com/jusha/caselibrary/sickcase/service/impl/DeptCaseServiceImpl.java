package com.jusha.caselibrary.sickcase.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.*;
import com.jusha.caselibrary.feign.api.DockingApi;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.search.service.ESSyncService;
import com.jusha.caselibrary.sickcase.dto.*;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.FollowExcelImportResp;
import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper;
import com.jusha.caselibrary.sickcase.service.CaseAddService;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import com.jusha.caselibrary.sickcase.utils.FollowExcelParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @ClassName DeptCaseServiceImpl
 * @Description 科室病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptCaseServiceImpl implements DeptCaseService {

    private final DeptCaseMapper deptCaseMapper;

    private final DepCaseService depCaseService;

    private final UserCaseService userCaseService;

    private final AuditRecordService auditRecordService;

    private final TagService tagService;

    private final DepCaseTagService depCaseTagService;

    private final DepCaseClassifyService depCaseClassifyService;

    private final FollowService followService;

    private final CaseStudyService caseStudyService;

    private final StudyService studyService;

    private final DepCaseTypeService depCaseTypeService;

    private final AdvancedSearchService advancedSearchService;

    private final ESSyncService esSyncService;

    private final DepCaseDiseaseService depcaseDiseaseService;

    private final UserCaseDiseaseService userCaseDiseaseService;

    private final DockingApi caselibRISApi;

    //懒加载，避免循环引用报错
    @Autowired
    @Lazy
    private CaseAddService caseAddService;

    @Override
    public List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req) {
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        DeptSearchReq deptSearchReq = new DeptSearchReq();
        BeanUtils.copyProperties(req, deptSearchReq);
        return advancedSearchService.searchAndConvertList(deptSearchReq, DeptCaseDetailResp.class);

    }

    @Override
    public PageInfo<DeptCaseDetailResp> getDeptCaseDetailPage(DeptCaseSearchReq req) {
        String keyword = req.getKeyword();
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        if (StringUtils.hasText(keyword) && !"*".equals(keyword)) {
            //有关键字使用ES查询
            DeptSearchReq deptSearchReq = new DeptSearchReq();
            BeanUtils.copyProperties(req, deptSearchReq);
            return advancedSearchService.searchAndConvertPage(deptSearchReq, DeptCaseDetailResp.class);
        } else {
            //没有关键字使用数据库查询，先分页查询病例Id，再填充复杂属性
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
            List<Long> deptCaseIdList = deptCaseMapper.getDeptCaseIdList(req);
            PageInfo<Long> pageInfo = new PageInfo<>(deptCaseIdList);

            if (CollectionUtils.isNotEmpty(deptCaseIdList)) {
                // 补充：填充复杂的关联属性
                req.setCaseIdList(deptCaseIdList);
                List<DeptCaseDetailResp> filledDeptCaseDetailList = deptCaseMapper
                        .getCaseDetailAttributes(req);
                // 转换字典值为标签
                filledDeptCaseDetailList.forEach(DeptCaseDetailResp::handleData);
                PageInfo<DeptCaseDetailResp> respPageInfo = new PageInfo<>(filledDeptCaseDetailList);
                BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
                return respPageInfo;
            }
            return new PageInfo<>();
        }
    }

    @Override
    public DeptCaseDetailResp getDeptCaseDetail(Long caseId, Long caseTypeId) {
        DeptCaseSearchReq deptCaseSearchReq = new DeptCaseSearchReq();
        deptCaseSearchReq.setCaseIdList(Collections.singletonList(caseId));
        deptCaseSearchReq.setCaseTypeId(caseTypeId);
        if (deptCaseSearchReq.getUserId() == null) {
            deptCaseSearchReq.setUserId(LoginUtil.getLoginUserId());
        }
        List<DeptCaseDetailResp> deptCaseDetailList = deptCaseMapper.getCaseDetailAttributes(deptCaseSearchReq);
        if (CollectionUtils.isNotEmpty(deptCaseDetailList)) {
            DeptCaseDetailResp result = deptCaseDetailList.get(0);
            // 转换字典值为标签
            result.handleData();
            return result;
        }
        return new DeptCaseDetailResp();
    }

    @Override
    public void deleteDeptCaseById(Long caseId, Long caseTypeId) {
        // 先删除类型关联关系和审核记录，随访没有不用管
        depCaseClassifyService.lambdaUpdate().eq(DepCaseClassify::getCaseId, caseId)
                .eq(DepCaseClassify::getCaseTypeId, caseTypeId).remove();
        auditRecordService.lambdaUpdate()
                .eq(AuditRecord::getCaseTypeId, caseTypeId)
                .eq(AuditRecord::getCaseId, caseId)
                .remove();
        // 再删除该病例（只有随访才会删除病例）
        if (caseTypeId.equals(Constant.DEP_FOLLOW_CASE_TYPE_ID)) {
            //随访病例
            //查询是否有其他关联
            List<DepCaseClassify> classifyList = depCaseClassifyService.lambdaQuery()
                    .eq(DepCaseClassify::getCaseId, caseId).list();
            if (CollectionUtils.isNotEmpty(classifyList)) {
                List<Long> caseTypeIdList = classifyList.stream().map(DepCaseClassify::getCaseTypeId)
                        .distinct().collect(Collectors.toList());
                //存在其他关联则抛出异常
                StringBuffer sb = new StringBuffer("：");
                depCaseTypeService.lambdaQuery().in(DepCaseType::getCaseTypeId, caseTypeIdList).list()
                        .forEach(depCaseType -> sb.append(depCaseType.getCaseTypeName()));
                throw new BusinessException(LocaleUtil.getLocale("case.exist.in.other.library") + sb);
            }
            // 若存在疾病则先删除疾病和病例关联关系
            depcaseDiseaseService.lambdaUpdate().eq(DepCaseDisease::getCaseId, caseId).remove();
            depCaseService.lambdaUpdate().eq(DepCase::getCaseId, caseId).remove();
        }
        esSyncService.syncDepartmentCase(Collections.singletonList(caseId), Constant.OPERATION_TYPE_DELETE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long createDeptCase(DeptCaseCreateReq req) {
        //保存病例
        DepCase depCase = new DepCase();
        long caseId;
        if(req.getCaseId() == null){
            caseId = YitIdHelper.nextId();
        }else {
            caseId = req.getCaseId();
        }

        BeanUtils.copyProperties(req, depCase);
        depCase.setCaseId(caseId);
        depCase.setCreateBy(LoginUtil.getLoginUserId());
        depCaseService.save(depCase);

        //增加疾病和病例关联关系
        List<Long> diseaseIdList = req.getDiseaseIdList();
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
//        if (depCaseType.getDiseaseTree().equals(Constant.NEED_DISEASE_TREE)
//                && CollectionUtils.isEmpty(diseaseIdList)) {
//            throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
//        }
        if (CollectionUtils.isNotEmpty(diseaseIdList)) {
            ArrayList<DepCaseDisease> depDiseaseList = new ArrayList<>();
            diseaseIdList.forEach(diseaseId -> {
                if (depcaseDiseaseService.lambdaQuery().eq(DepCaseDisease::getCaseId, caseId)
                        .eq(DepCaseDisease::getDiseaseId, diseaseId).count() == 0) {
                    DepCaseDisease depcaseDisease = new DepCaseDisease();
                    depcaseDisease.setId(YitIdHelper.nextId());
                    depcaseDisease.setCaseId(caseId);
                    depcaseDisease.setDiseaseId(diseaseId);
                    depDiseaseList.add(depcaseDisease);
                }
            });
            depcaseDiseaseService.saveBatch(depDiseaseList);
        }
        //保存病例与类型关联
        //检查是否已经关联
        Integer count = depCaseClassifyService.lambdaQuery()
                .eq(DepCaseClassify::getCaseId, caseId)
                .eq(DepCaseClassify::getCaseTypeId, req.getCaseTypeId())
                .count();
        if (count == 0 && !req.getCaseTypeId().equals(Constant.DEP_FOLLOW_CASE_TYPE_ID)) {
            DepCaseClassify depCaseClassify = new DepCaseClassify();
            depCaseClassify.setId(YitIdHelper.nextId());
            depCaseClassify.setCaseId(caseId);
            depCaseClassify.setCaseTypeId(req.getCaseTypeId());
            depCaseClassifyService.save(depCaseClassify);
        }
        //查询是否需要审核
        if (!depCaseType.getAudit().equals(Constant.CASE_AUDIT_STATUS_1)) {
            return caseId;
        }
        //保存病例与审核表关联
        AuditRecord auditRecord = auditRecordService.lambdaQuery().eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, req.getCaseTypeId()).one();
        if (auditRecord != null) {
            auditRecord.setPubUserId(auditRecord.getPubUserId() + "," + LoginUtil.getLoginUserId());
        } else {
            auditRecord = new AuditRecord();
            auditRecord.setAuditId(YitIdHelper.nextId());
            auditRecord.setCaseId(caseId);
            auditRecord.setCaseTypeId(req.getCaseTypeId());
            auditRecord.setAuditType(req.getAuditType());
            auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
            auditRecord.setPubTime(new Date());
        }
        auditRecordService.saveOrUpdate(auditRecord);
        return caseId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDeptCase(DeptCaseUpdateReq req) {
        DepCase depCase = depCaseService.getById(req.getCaseId());
        if (depCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        BeanUtils.copyProperties(req, depCase);
        depCase.setUpdateTime(new Date());
        depCase.setUpdateBy(LoginUtil.getLoginUserId());
        depCase.setModifyUsers(depCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
        depCaseService.updateById(depCase);
        // 修改疾病关联关系
        List<Long> diseaseIdList = req.getDiseaseIdList();
        if (CollectionUtils.isNotEmpty(diseaseIdList)) {
            depcaseDiseaseService.lambdaUpdate().eq(DepCaseDisease::getCaseId, req.getCaseId()).remove();
            ArrayList<DepCaseDisease> depDiseaseList = new ArrayList<>();
            diseaseIdList.forEach(diseaseId -> {
                DepCaseDisease depcaseDisease = new DepCaseDisease();
                depcaseDisease.setId(YitIdHelper.nextId());
                depcaseDisease.setCaseId(req.getCaseId());
                depcaseDisease.setDiseaseId(diseaseId);
                depDiseaseList.add(depcaseDisease);
            });
            depcaseDiseaseService.saveBatch(depDiseaseList);
        }
        // 修改标签
        List<String> tagNameList = req.getTagNameList();
        DeptCaseTagReq deptCaseTagReq = new DeptCaseTagReq();
        deptCaseTagReq.setCaseId(req.getCaseId());
        deptCaseTagReq.setTagNameList(tagNameList);
        tagCase(deptCaseTagReq);
    }

    @Override
    public PageInfo<AuditCaseDetailResp> getAuditCaseList(AuditCaseSearchReq req) {
        // 先分页查询审核Id，再填充复杂属性，避免复杂JOIN导致的分页问题
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<CaseBaseInfo> caseBaseInfoList = deptCaseMapper.getCaseAuditIdList(req);
        PageInfo<CaseBaseInfo> pageInfo = new PageInfo<>(caseBaseInfoList);
        if (CollectionUtils.isNotEmpty(caseBaseInfoList)) {
            // 获取带复杂的关联属性实体
            req.setCaseBaseInfoList(caseBaseInfoList);
            List<AuditCaseDetailResp> filledAuditCaseList = deptCaseMapper.getAuditCaseList(req);
            // 转换字典值为标签
            filledAuditCaseList.forEach(AuditCaseDetailResp::handleData);
            PageInfo<AuditCaseDetailResp> respPageInfo = new PageInfo<>(filledAuditCaseList);
            BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
            return respPageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    public PageInfo<AuditCaseDetailResp> getMyAuditCaseList(AuditCaseSearchReq req) {
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        // 先分页查询基本信息，再填充复杂属性，避免复杂JOIN导致的分页问题
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<CaseBaseInfo> myCaseBaseInfoList = deptCaseMapper.getCaseAuditIdList(req);
        PageInfo<CaseBaseInfo> pageInfo = new PageInfo<>(myCaseBaseInfoList);
        if (CollectionUtils.isNotEmpty(myCaseBaseInfoList)) {
            // 获取带复杂的关联属性实体
            req.setCaseBaseInfoList(myCaseBaseInfoList);
            List<AuditCaseDetailResp> filledAuditCaseList = deptCaseMapper.getAuditCaseList(req);
            // 转换字典值为标签
            filledAuditCaseList.forEach(AuditCaseDetailResp::handleData);
            PageInfo<AuditCaseDetailResp> respPageInfo = new PageInfo<>(filledAuditCaseList);
            BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
            return respPageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    public List<AuditInfo> getAuditRecordList(Long caseId, Long caseTypeId) {
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, caseTypeId)
                .orderByDesc(AuditRecord::getPubTime)
                .list();
        if (CollectionUtils.isEmpty(auditRecords)) {
            return new ArrayList<>();
        }
        return auditRecords.stream().map(auditRecord -> {
            String pubUserIds = auditRecord.getPubUserId();
            AuditInfo auditInfo = new AuditInfo();
            auditInfo.setAuditId(auditRecord.getAuditId());
            auditInfo.setAuditType(auditRecord.getAuditType());
            auditInfo.setAuditTypeLabel(DictConvertUtil.convertAuditType(auditRecord.getAuditType()));
            auditInfo.setStatus(auditRecord.getStatus());
            auditInfo.setStatusLabel(DictConvertUtil.convertStatus(auditRecord.getStatus()));
            auditInfo.setCaseId(auditRecord.getCaseId());
            auditInfo.setCaseTypeId(auditRecord.getCaseTypeId());
            auditInfo.setAuditedTime(auditRecord.getAuditedTime());
            auditInfo.setAuditedBy(auditRecord.getAuditedBy());
            auditInfo.setAuditedUserName(UserUtil.getUserNameById(auditRecord.getAuditedBy()));
            auditInfo.setPubUserId(pubUserIds);
            auditInfo.setAuditComment(auditRecord.getAuditComment());
            if (!pubUserIds.isEmpty()) {
                String[] userIds = pubUserIds.split(",");
                StringBuilder userNames = new StringBuilder();
                for (String userId : userIds) {
                    userNames.append(UserUtil.getUserNameById(Long.parseLong(userId))).append(",");
                }
                if (userNames.toString().endsWith(",")) {
                    userNames = new StringBuilder(userNames.substring(0, userNames.length() - 1));
                }
                auditInfo.setPubUserName(userNames.toString());
            }
            auditInfo.setPubTime(auditRecord.getPubTime());
            return auditInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public void verifyCase(CaseAuditReq req) {
        AuditRecord auditRecord = auditRecordService.getById(req.getAuditId());
        if (auditRecord == null) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        if (!auditRecord.getStatus().equals(Constant.CASE_AUDIT_STATUS_0)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.has.been.verified"));
        }
        auditRecord.setStatus(req.getStatus());
        auditRecord.setAuditComment(req.getAuditComment());
        auditRecord.setAuditedBy(LoginUtil.getLoginUserId());
        auditRecord.setAuditedTime(new Date());
        auditRecordService.updateById(auditRecord);
    }

    @Override
    public void reSubmitVerify(Long caseId, Long caseTypeId) {
        // 1、查询该病例以往提交的审核记录，并获取最新的
        List<AuditRecord> auditRecordList = auditRecordService.lambdaQuery().eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, caseTypeId).list();
        if (CollectionUtils.isEmpty(auditRecordList)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        AuditRecord latestAuditRecord = auditRecordList.stream().max(Comparator.comparing(AuditRecord::getPubTime))
                .orElse(null);
        if (latestAuditRecord == null) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        if (!latestAuditRecord.getStatus().equals(Constant.CASE_AUDIT_STATUS_2)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.reject"));
        }
        // 2、新建审核记录
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setAuditId(YitIdHelper.nextId());
        auditRecord.setCaseId(caseId);
        auditRecord.setCaseTypeId(caseTypeId);
        auditRecord.setAuditType(latestAuditRecord.getAuditType());
        auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
        auditRecord.setPubTime(new Date());
        auditRecord.setStatus(Constant.CASE_AUDIT_STATUS_0);
        auditRecordService.saveOrUpdate(auditRecord);
    }

    @Override
    public List<TagInfo> getCaseTagList() {
        // 查询已经打过的科室病例标签
        List<DepCaseTag> depCaseTagList = depCaseTagService.lambdaQuery().list();
        if (CollectionUtils.isNotEmpty(depCaseTagList)) {
            List<Long> tagIdList = depCaseTagList.stream()
                    .map(DepCaseTag::getTagId).collect(Collectors.toList());
            List<Tag> tagList = tagService.lambdaQuery().in(Tag::getTagId, tagIdList).list();
            if (CollectionUtils.isEmpty(tagList)) {
                return new ArrayList<>();
            }
            return tagList.stream().map(tag -> {
                TagInfo tagInfo = new TagInfo();
                BeanUtils.copyProperties(tag, tagInfo);
                return tagInfo;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public void tagCase(DeptCaseTagReq req) {
        List<String> tagNameList = req.getTagNameList();
        if (tagNameList.size() > 10) {
            throw new BusinessException(LocaleUtil.getLocale("tag.list.too.much"));
        }
        List<Tag> tags = tagService.list();
        // 先删除旧标签关联关系
        depCaseTagService.lambdaUpdate().eq(DepCaseTag::getCaseId, req.getCaseId()).remove();
        // 将标签列表转换为map结构
        Map<String, Long> tagMap = tags.stream().collect(Collectors.toMap(Tag::getTagName, Tag::getTagId));
        if (CollectionUtils.isNotEmpty(tagNameList)) {
            tagNameList.forEach(tag -> {
                if (!tagMap.containsKey(tag)) {
                    Tag newTag = new Tag();
                    newTag.setTagId(YitIdHelper.nextId());
                    newTag.setTagName(tag);
                    tagService.save(newTag);
                    tagMap.put(tag, newTag.getTagId());
                }
                DepCaseTag depCaseTag = new DepCaseTag();
                depCaseTag.setId(YitIdHelper.nextId());
                depCaseTag.setCaseId(req.getCaseId());
                depCaseTag.setTagId(tagMap.get(tag));
                depCaseTagService.save(depCaseTag);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFollow(CaseFollowUpdateReq req) {
        // 验证病例是否存在
        DepCase depCase = depCaseService.getById(req.getCaseId());
        List<UserCase> userCaseList = userCaseService.lambdaQuery().eq(UserCase::getCaseId, req.getCaseId()).list();
        if (depCase == null && CollectionUtils.isEmpty(userCaseList)) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        // 新增或修改、删除病例随访
        List<CaseFollowUpdateReq.StudyFollowUpdate> studyFollowUpdateList = req.getStudyFollowUpdateList();
        if (CollectionUtils.isNotEmpty(studyFollowUpdateList)) {
            studyFollowUpdateList.forEach(studyFollowUpdate -> {
                List<FollowInfo> followInfoList = studyFollowUpdate.getFollowInfoList();
                if (CollectionUtils.isEmpty(followInfoList)) {
                    // 如果传入的为空，则删除该检查所有随访记录
                    followService.lambdaUpdate().eq(Follow::getStudyUid, studyFollowUpdate.getStudyUid()).remove();
                    return;
                }
                // 获取所有随访记录
                List<Follow> follows = followService.lambdaQuery()
                        .eq(Follow::getCaseId, req.getCaseId())
                        .eq(Follow::getStudyUid, studyFollowUpdate.getStudyUid()).list();
                List<Long> orignFollowIdList = follows.stream().map(Follow::getFollowId).collect(Collectors.toList());
                // 处理随访
                handleFollow(followInfoList, studyFollowUpdate.getStudyUid(), req.getCaseId(), orignFollowIdList);
                // 处理定位、定性
                String qualityMatch = studyFollowUpdate.getQualityMatch();
                String positionMatch = studyFollowUpdate.getPositionMatch();
                if (positionMatch != null) {
                    studyService.lambdaUpdate().eq(Study::getStudyUid, studyFollowUpdate.getStudyUid())
                            .set(Study::getPositionMatch, positionMatch).update();
                }
                if (qualityMatch != null) {
                    studyService.lambdaUpdate().eq(Study::getStudyUid, studyFollowUpdate.getStudyUid())
                            .set(Study::getQualityMatch, qualityMatch).update();
                }
            });
        }
        updateCaseFollowStatus(req, depCase, userCaseList);
    }

    /**
     * 更新病例随访状态
     */
    private void updateCaseFollowStatus(CaseFollowUpdateReq req, DepCase depCase, List<UserCase> userCaseList) {
        //更新科室病例库
        if (depCase != null) {
            depCase.setFollowStatus(req.getFollowStatus());
            depCase.setModifyUsers(depCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
            depCase.setUpdateBy(LoginUtil.getLoginUserId());
            depCase.setUpdateTime(new Date());
            depCaseService.updateById(depCase);
            //同步到es
            esSyncService.syncDepartmentCase(Collections.singletonList(depCase.getCaseId()), Constant.OPERATION_TYPE_UPDATE);
        }
        //更新个人病例库
        if (CollectionUtils.isNotEmpty(userCaseList)) {
            userCaseList.forEach(userCase -> {
                userCase.setFollowStatus(req.getFollowStatus());
                userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
                userCase.setUpdateBy(LoginUtil.getLoginUserId());
                userCase.setUpdateTime(new Date());
                userCaseService.updateById(userCase);
            });
            esSyncService.syncPersonalCase(userCaseList.stream().map(UserCase::getUserCaseId)
                    .collect(Collectors.toList()), Constant.OPERATION_TYPE_UPDATE);
        }
    }

    /**
     * 处理随访
     */
    private void handleFollow(List<FollowInfo> followInfoList, String studyUid,
                              Long caseId, List<Long> orignFollowIdList) {
        followInfoList.forEach(followInfo -> {
            // 验证参数
            if (StringUtils.isEmpty(followInfo.getFollowType())
                    || StringUtils.isEmpty(followInfo.getFollowupResult())) {
                throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
            }
            // 如果存在则从列表中移除
            orignFollowIdList.remove(followInfo.getFollowId());
            if (followInfo.getFollowId() == null) {
                // 新增
                Follow follow = new Follow();
                follow.setFollowId(YitIdHelper.nextId());
                follow.setCaseId(caseId);
                follow.setStudyUid(studyUid);
                follow.setFollowType(followInfo.getFollowType());
                follow.setFollowupResult(followInfo.getFollowupResult());
                follow.setCreatedBy(LoginUtil.getLoginUserId());
                follow.setCreateTime(new Date());
                followService.save(follow);
            } else {
                // 修改
                Follow follow = followService.getById(followInfo.getFollowId());
                if (follow == null) {
                    throw new BusinessException(LocaleUtil.getLocale("follow.not.exist"));
                }
                follow.setFollowType(followInfo.getFollowType());
                follow.setFollowupResult(followInfo.getFollowupResult());
                follow.setUpdateBy(LoginUtil.getLoginUserId());
                follow.setUpdateTime(new Date());
                followService.updateById(follow);
            }
        });
        // 删除剩余的随访记录
        if (CollectionUtils.isNotEmpty(orignFollowIdList)) {
            followService.lambdaUpdate().in(Follow::getFollowId, orignFollowIdList).remove();
        }
    }

    @Override
    public FollowExcelImportResp importFollowExcel(MultipartFile file) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 使用FollowExcelParser解析Excel文件
            log.info("【诊断日志】开始解析Excel文件，文件名: {}, 文件大小: {} bytes",
                    file.getOriginalFilename(), file.getSize());
            FollowExcelParser parser = new FollowExcelParser();
            FollowExcelImportResp response = parser.parseExcel(file);

            // 添加诊断日志
            log.info("【诊断日志】Excel解析完成 - 总行数: {}, 成功行数: {}, 错误行数: {}, 是否成功: {}",
                    response.getTotalRows(), response.getSuccessRows(),
                    response.getErrorRows(), response.getSuccess());

            if (response.hasErrors()) {
                log.warn("【诊断日志】Excel解析发现错误: {}", response.getErrorMessages());
            }

            // 2. 如果解析失败，直接返回
            if (!response.getSuccess()) {
                log.warn("Excel解析失败或无有效数据");
                return response;
            }

            // 3. 异步处理导入逻辑
            if (response.getSuccessRows() > 0) {
                processImportAsync(file, response);
            }

            long endTime = System.currentTimeMillis();
            response.setProcessingTime(endTime - startTime);

            log.info("Excel导入完成，总计{}条，成功{}条，失败{}条",
                    response.getTotalRows(), response.getSuccessRows(), response.getErrorRows());

            return response;

        } catch (Exception e) {
            log.error("Excel导入过程中发生异常", e);
            FollowExcelImportResp errorResponse = new FollowExcelImportResp();
            errorResponse.setSuccess(false);
            errorResponse.addErrorMessage("导入失败: " + e.getMessage());
            errorResponse.setProcessingTime(System.currentTimeMillis() - startTime);
            return errorResponse;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public long sendCaseToDept(DeptCaseSendReq req) {
        // 1. 验证病例库类型及是否需要疾病Id
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        if (!Constant.DEP_FOLLOW_CASE_TYPE_ID.equals(req.getCaseTypeId()) && depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        // 如果是教学库，疾病类型不可以是空的
        List<Long> diseaseIdList = req.getDiseaseIdList();
        if (depCaseType.getCaseTypeId().equals(Constant.DEP_TEACH_CASE_TYPE_ID)) {
            if (CollectionUtils.isEmpty(diseaseIdList)) {
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }
        }

        // 2. 查询个人病例库病例
        UserCase userCase = userCaseService.getById(req.getUserCaseId());
        if (userCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }

        long caseId = userCase.getCaseId();
        DepCase depCase = depCaseService.getById(caseId);
        if(depCase != null){
            //科室库已存在该病例的情况下，如果该病例已经存在于目标病例库，弹出异常，不存在则加上关联关系
            List<DepCaseClassify> depCaseClassifyList = depCaseClassifyService.lambdaQuery().eq(DepCaseClassify::getCaseId,depCase.getCaseId()).list();
            if(!depCaseClassifyList.isEmpty()){
                List<Long> depCaseTypeList = depCaseClassifyList.stream().map(DepCaseClassify::getCaseTypeId).collect(Collectors.toList());
                //注意-1不需要加关联关系
                if(!depCaseTypeList.contains(req.getCaseTypeId()) && req.getCaseTypeId()!=-1L){
                    DepCaseClassify depCaseClassify = new DepCaseClassify();
                    depCaseClassify.setId(YitIdHelper.nextId());
                    depCaseClassify.setCaseId(caseId);
                    depCaseClassify.setCaseTypeId(req.getCaseTypeId());
                    depCaseClassifyService.save(depCaseClassify);
                }else {
                    throw new BusinessException(LocaleUtil.getLocale("case.already.exist"));
                }
            }
        }else {
            //该病例不存在，那么考虑新建。新建时需要先判断影像如果已存在，不让新建，抛出异常。影像不存在则创建
            //存在一种情况，科室病例库新建一个病例加了这个影像，个人病例库后新建一个病例也添加了这个影像。那么，判断一下，这个个人病例库关联关联的影像是不是关联了科室病例库

            //拿到病例关联的影像Id集合
            List<CaseStudy> caseStudyListUsers = caseStudyService.lambdaQuery().eq(CaseStudy::getCaseId,caseId).list();
            List<String> studyUids = caseStudyListUsers.stream().map(CaseStudy::getStudyUid).collect(Collectors.toList());
            //再挨个判断这个影像有没有关联科室病例库
            for(String studyUid : studyUids){
                List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,studyUid).list();
                if(!caseStudyList.isEmpty()){
                    List<Long> caseIds = caseStudyList.stream().map(CaseStudy::getCaseId).collect(Collectors.toList());
                    for(Long deptCaseId : caseIds) {
                        if (depCaseService.getById(deptCaseId) != null) {
                            Study study = studyService.getById(studyUid);
                            throw new BusinessException("【" + DateUtil.convertDateToStr(study.getStudyTime(),DateUtil.DEFAULT_PATTERN) + study.getDeviceType() + "】" + LocaleUtil.getLocale("study.of.case.already.exist"));
                        }
                    }
                }
            }

            // 3. 复制病例到科室病例库
            DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
            BeanUtils.copyProperties(userCase, deptCaseCreateReq);
            deptCaseCreateReq.setCaseTypeId(req.getCaseTypeId());
            deptCaseCreateReq.setDiseaseIdList(diseaseIdList);
            deptCaseCreateReq.setCaseId(caseId);
            createDeptCase(deptCaseCreateReq);
            // 4. 保存标签
            List<Long> tagIdList = req.getTagIdList();
            if (CollectionUtils.isNotEmpty(tagIdList)) {
                tagIdList.forEach(tagId -> {
                    if (depCaseTagService.lambdaQuery()
                            .eq(DepCaseTag::getCaseId, req.getCaseId())
                            .eq(DepCaseTag::getTagId, tagId).count() == 0) {
                        DepCaseTag depCaseTag = new DepCaseTag();
                        depCaseTag.setId(YitIdHelper.nextId());
                        depCaseTag.setCaseId(req.getCaseId());
                        depCaseTag.setTagId(tagId);
                        depCaseTagService.save(depCaseTag);
                    }
                });
            }
        }
        // 除了同步科室病例库，个人的这个被发送的病例也需要同步，因为需要查询他被发送到哪了
        esSyncService.syncPersonalCase(Collections.singletonList(req.getUserCaseId()), Constant.OPERATION_TYPE_UPDATE);
        return caseId;
    }

    /**
     * 异步处理导入逻辑
     */
    private void processImportAsync(MultipartFile file, FollowExcelImportResp response) {
        ExecutorService followImportExecutorService = ContextHolder.getBean("followImport", ExecutorService.class);
        try {
            // 重新解析Excel获取有效数据
            FollowExcelParser parser = new FollowExcelParser();
            List<ExcelFollowRow> validRows = parser.getValidRows(file);

            if (CollectionUtils.isEmpty(validRows)) {
                return;
            }
            // 转换为导入请求
            List<CaseFollowImport> followRequests = validRows.stream()
                    .map(this::convertToFollowImportReq)
                    .collect(Collectors.toList());

            // 线程安全的结果收集器及病例Id集合
            Map<Integer, String> globalImportResults = new ConcurrentHashMap<>();
            List<Long> caseIdList = new CopyOnWriteArrayList<>();
            List<Long> userCaseIdList = new CopyOnWriteArrayList<>();

            // 分批处理，每批100条记录以提高性能
            int batchSize = 100;
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < followRequests.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, followRequests.size());
                List<CaseFollowImport> batch = followRequests.subList(i, endIndex);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 每个批次使用独立的结果Map和科室病例Id及个人病例Id
                    Map<Integer, String> batchResults = new HashMap<>();
                    importFollow(batch, batchResults, caseIdList, userCaseIdList);
                    // 将批次结果合并到全局结果中
                    globalImportResults.putAll(batchResults);
                }, followImportExecutorService);
                futures.add(future);
            }

            // 等待所有批次处理完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统一更新响应结果
            updateResponseAfterImport(response, followRequests.size(), globalImportResults);
            response.setCaseIdList(caseIdList);
            response.setUserCaseIdList(userCaseIdList);

            // 同步ES
            esSyncService.syncDepartmentCase(caseIdList, Constant.OPERATION_TYPE_BATCH_UPDATE);
            esSyncService.syncPersonalCase(userCaseIdList, Constant.OPERATION_TYPE_BATCH_UPDATE);

            log.info("异步导入处理完成，实际成功{}条，失败{}条",
                    followRequests.size() - globalImportResults.size(), globalImportResults.size());

        } catch (Exception e) {
            log.error("异步导入处理失败", e);
        }

    }

    @Override
    public void importFollow(List<CaseFollowImport> req, Map<Integer, String> resultMap,
                             List<Long> caseIdList, List<Long> userCaseIdList) {
        long startTime = System.currentTimeMillis();
        log.info("开始批量导入随访数据，总数量: {}", req.size());

        if (CollectionUtils.isEmpty(req)) {
            // 为每个请求添加错误信息
            for (int i = 0; i < req.size(); i++) {
                resultMap.put(i, LocaleUtil.getLocale("import.data.is.null"));
            }
            log.error(LocaleUtil.getLocale("import.data.is.null"));
            return;
        }

        try {
            // 1. 批量查询获取病例Id
            List<String> accessNumberList = req.stream().map(CaseFollowImport::getAccessNumber)
                    .collect(Collectors.toList());

            //1.5看下如果该影像号对应的检查不存在，那么尝试从RIS中拉取
            for(String accessNumber : accessNumberList){
                List<Study> studyList = studyService.lambdaQuery().eq(Study::getAccessNumber, accessNumberList).list();
                if(studyList.isEmpty()){
                    ResultBean<List<RISStudy>> resultBean = caselibRISApi.getStudyInfo(accessNumber);
                    if(resultBean.getState()){
                        List<RISStudy> risStudyList = resultBean.getData();
                        if(!risStudyList.isEmpty()){
                            AddRISCaseReq addRISCaseReq = new AddRISCaseReq();
                            addRISCaseReq.setTargetLibrary(2);
                            addRISCaseReq.setTargetId(-1L);
                            addRISCaseReq.setRisStudyList(risStudyList);
                            caseAddService.addCaseFromRis(addRISCaseReq);
                        }
                    }
                }
            }

            List<Study> studyList = studyService.lambdaQuery()
                    .in(Study::getAccessNumber, accessNumberList)
                    .list();

            if (CollectionUtils.isEmpty(studyList)) {
                // 为每个请求添加错误信息
                for (CaseFollowImport item : req) {
                    resultMap.put(item.getNo(), LocaleUtil.getLocale("follow.case.not.exist"));
                }
                log.error(LocaleUtil.getLocale("follow.case.not.exist"));
                return;
            }
            // 2.获取病例id信息
            List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().in(CaseStudy::getStudyUid, studyList.stream()
                    .map(Study::getStudyUid).collect(Collectors.toList())).list();

            // 3.遍历studyList，返回key为accessNumber，value为caseId集合的map
            Map<String, List<Long>> accessNumToCaseIdMap = new HashMap<>();
            Map<Long, String> studyUidToCaseIdMap = new HashMap<>();
            studyList.forEach(study -> {
                String accessNumber = study.getAccessNumber();
                List<Long> caseIds = caseStudyList.stream()
                        .filter(caseStudy -> caseStudy.getStudyUid().equals(study.getStudyUid()))
                        .map(CaseStudy::getCaseId)
                        .collect(Collectors.toList());
                accessNumToCaseIdMap.put(accessNumber, caseIds);
                caseIds.forEach(caseId -> studyUidToCaseIdMap.put(caseId, study.getStudyUid()));
            });
            // 4.遍历accessNumToCaseIdMap，查询病例是否已随访，如果已随访，跳过，否则创建随访记录
            List<Follow> followsToSave = new ArrayList<>();
            Long currentUserId = LoginUtil.getLoginUserId();
            Date currentTime = new Date();

            req.forEach(caseFollowImport -> {
                String accessNumber = caseFollowImport.getAccessNumber();
                List<Long> caseIds = accessNumToCaseIdMap.get(accessNumber);
                if (CollectionUtils.isEmpty(caseIds)) {
                    resultMap.put(caseFollowImport.getNo(), LocaleUtil.getLocale("follow.case.not.exist"));
                    return;
                }
                caseIds.forEach(caseId -> {
                    try {
                        // 创建随访记录
                        Follow follow = new Follow();
                        follow.setFollowId(YitIdHelper.nextId());
                        follow.setCaseId(caseId);
                        follow.setStudyUid(studyUidToCaseIdMap.get(caseId));
                        follow.setFollowType(caseFollowImport.getFollowType());
                        follow.setFollowupResult(caseFollowImport.getFollowupResult());
                        follow.setCreatedBy(currentUserId);
                        follow.setCreateTime(currentTime);
                        followsToSave.add(follow);
                        // 查询caseId是个人库还是科室库
                        DepCase depCase = depCaseService.getById(caseId);
                        if (depCase != null) {
                            caseIdList.add(caseId);
                        }
                        List<UserCase> userCaseList = userCaseService.lambdaQuery().eq(UserCase::getCaseId, caseId).list();
                        if (CollectionUtils.isNotEmpty(userCaseList)) {
                            userCaseIdList.addAll(userCaseList.stream().map(UserCase::getUserCaseId).collect(Collectors.toList()));
                        }
                    } catch (Exception e) {
                        resultMap.put(caseFollowImport.getNo(), "创建随访记录失败: " + e.getMessage());
                    }
                });
            });

            if (CollectionUtils.isNotEmpty(followsToSave)) {
                followService.saveBatch(followsToSave);
            }

            long endTime = System.currentTimeMillis();
            log.info("批量导入随访数据完成，总数量: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    req.size(), req.size() - resultMap.size(), resultMap.size(), (endTime - startTime));

        } catch (Exception e) {
            // 批次处理失败时，为所有记录添加错误信息
            for (CaseFollowImport item : req) {
                resultMap.put(item.getNo(), "批次处理失败: " + e.getMessage());
            }
            log.error("批次处理失败", e);
        }
    }

    /**
     * 导入完成后统一更新响应结果
     */
    private void updateResponseAfterImport(FollowExcelImportResp response, int totalProcessed, Map<Integer, String> errorResults) {
        synchronized (response) {
            int actualErrorCount = errorResults.size();
            int actualSuccessCount = totalProcessed - actualErrorCount;

            // 重新计算成功和失败数量
            response.setSuccessRows(actualSuccessCount);
            response.setErrorRows(response.getErrorRows() + actualErrorCount);

            // 添加导入过程中的错误信息
            for (Map.Entry<Integer, String> entry : errorResults.entrySet()) {
                response.addErrorMessage("第" + entry.getKey() + "行: " + entry.getValue());
            }

            response.calculateSuccess();
        }
    }

    /**
     * 转换ExcelFollowRowDto为CaseFollowImportReq
     */
    private CaseFollowImport convertToFollowImportReq(ExcelFollowRow rowDto) {
        CaseFollowImport req = new CaseFollowImport();
        req.setNo(rowDto.getRowIndex());
        req.setAccessNumber(rowDto.getAccessNumber());
        req.setFollowType(rowDto.getFollowType());
        req.setFollowupResult(rowDto.getFollowupResult());
        return req;
    }
}
