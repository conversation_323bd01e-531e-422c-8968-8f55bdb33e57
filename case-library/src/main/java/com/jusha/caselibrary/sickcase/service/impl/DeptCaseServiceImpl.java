package com.jusha.caselibrary.sickcase.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.DictConvertUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.common.util.UserUtil;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.search.service.ESSyncService;
import com.jusha.caselibrary.sickcase.dto.*;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.dto.resp.FollowExcelImportResp;
import com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import com.jusha.caselibrary.sickcase.utils.FollowExcelParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName DeptCaseServiceImpl
 * @Description 科室病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptCaseServiceImpl implements DeptCaseService {

    private final DeptCaseMapper deptCaseMapper;

    private final DepCaseService depCaseService;

    private final UserCaseService userCaseService;

    private final AuditRecordService auditRecordService;

    private final TagService tagService;

    private final DepCaseTagService depCaseTagService;

    private final DepCaseClassifyService depCaseClassifyService;

    private final FollowService followService;

    private final CaseStudyService caseStudyService;

    private final StudyService studyService;

    private final DepCaseTypeService depCaseTypeService;

    private final AdvancedSearchService advancedSearchService;

    private final ESSyncService esSyncService;

    private final DepCaseDiseaseService depcaseDiseaseService;

    private final UserCaseDiseaseService userCaseDiseaseService;

    @Override
    public List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req) {
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        DeptSearchReq deptSearchReq = new DeptSearchReq();
        BeanUtils.copyProperties(req, deptSearchReq);
        return advancedSearchService.searchAndConvertList(deptSearchReq, DeptCaseDetailResp.class);

    }

    @Override
    public PageInfo<DeptCaseDetailResp> getDeptCaseDetailPage(DeptCaseSearchReq req) {
        String keyword = req.getKeyword();
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        if (StringUtils.hasText(keyword)) {
            //有关键字使用ES查询
            DeptSearchReq deptSearchReq = new DeptSearchReq();
            BeanUtils.copyProperties(req, deptSearchReq);
            return advancedSearchService.searchAndConvertPage(deptSearchReq, DeptCaseDetailResp.class);
        } else {
            //没有关键字使用数据库查询，先分页查询病例Id，再填充复杂属性
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
            List<Long> deptCaseIdList = deptCaseMapper.getDeptCaseIdList(req);
            PageInfo<Long> pageInfo = new PageInfo<>(deptCaseIdList);

            if (CollectionUtils.isNotEmpty(deptCaseIdList)) {
                // 补充：填充复杂的关联属性
                req.setCaseIdList(deptCaseIdList);
                List<DeptCaseDetailResp> filledDeptCaseDetailList = deptCaseMapper
                        .getCaseDetailAttributes(req);
                // 转换字典值为标签
                filledDeptCaseDetailList.forEach(DeptCaseDetailResp::handleData);
                PageInfo<DeptCaseDetailResp> respPageInfo = new PageInfo<>(filledDeptCaseDetailList);
                BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
                return respPageInfo;
            }
            return new PageInfo<>();
        }
    }

    @Override
    public DeptCaseDetailResp getDeptCaseDetail(Long caseId, Long caseTypeId) {
        DeptCaseSearchReq deptCaseSearchReq = new DeptCaseSearchReq();
        deptCaseSearchReq.setCaseIdList(Collections.singletonList(caseId));
        deptCaseSearchReq.setCaseTypeId(caseTypeId);
        if (deptCaseSearchReq.getUserId() == null) {
            deptCaseSearchReq.setUserId(LoginUtil.getLoginUserId());
        }
        List<DeptCaseDetailResp> deptCaseDetailList = deptCaseMapper.getCaseDetailAttributes(deptCaseSearchReq);
        if (CollectionUtils.isNotEmpty(deptCaseDetailList)) {
            DeptCaseDetailResp result = deptCaseDetailList.get(0);
            // 转换字典值为标签
            result.handleData();
            return result;
        }
        return new DeptCaseDetailResp();
    }

    @Override
    public void deleteDeptCaseById(Long caseId, Long caseTypeId) {
        // 若存在关联关系则先删除与类型的关联关系
        if (caseTypeId.equals(Constant.DEP_FOLLOW_CASE_TYPE_ID)) {
            //查询是否有其他关联
            List<DepCaseClassify> classifyList = depCaseClassifyService.lambdaQuery()
                    .eq(DepCaseClassify::getCaseId, caseId).list();
            if (CollectionUtils.isNotEmpty(classifyList)) {
                throw new BusinessException(LocaleUtil.getLocale("case.exist.in.other.library"));
            }
        }
        depCaseClassifyService.lambdaUpdate().eq(DepCaseClassify::getCaseId, caseId)
                .eq(DepCaseClassify::getCaseTypeId, caseTypeId).remove();
        //如果存在审核记录则先把审核记录删除了
        auditRecordService.lambdaUpdate()
                .eq(AuditRecord::getCaseTypeId, caseTypeId)
                .eq(AuditRecord::getCaseId, caseId)
                .remove();
        // 若关联关系都没有则删除病例
        if (depCaseClassifyService.lambdaQuery().eq(DepCaseClassify::getCaseId, caseId).count() == 0) {
            // 若存在疾病则先删除疾病和病例关联关系
            depcaseDiseaseService.lambdaUpdate().eq(DepCaseDisease::getCaseId, caseId).remove();
            boolean removed = depCaseService.lambdaUpdate().eq(DepCase::getCaseId, caseId).remove();
            if (removed) {
                esSyncService.syncDepartmentCase(Collections.singletonList(caseId), Constant.OPERATION_TYPE_DELETE);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long createDeptCase(DeptCaseCreateReq req) {
        //保存病例
        DepCase depCase = new DepCase();
        long caseId = YitIdHelper.nextId();
        BeanUtils.copyProperties(req, depCase);
        depCase.setCaseId(caseId);
        depCase.setCreateBy(LoginUtil.getLoginUserId());
        depCaseService.save(depCase);

        //增加疾病和病例关联关系
        List<Long> diseaseIdList = req.getDiseaseIdList();
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        if (depCaseType.getDiseaseTree().equals(Constant.NEED_DISEASE_TREE)
                && CollectionUtils.isEmpty(diseaseIdList)) {
            throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
        }
        if (CollectionUtils.isNotEmpty(diseaseIdList)) {
            ArrayList<DepCaseDisease> depDiseaseList = new ArrayList<>();
            diseaseIdList.forEach(diseaseId -> {
                if (depcaseDiseaseService.lambdaQuery().eq(DepCaseDisease::getCaseId, caseId)
                        .eq(DepCaseDisease::getDiseaseId, diseaseId).count() == 0) {
                    DepCaseDisease depcaseDisease = new DepCaseDisease();
                    depcaseDisease.setId(YitIdHelper.nextId());
                    depcaseDisease.setCaseId(caseId);
                    depcaseDisease.setDiseaseId(diseaseId);
                    depDiseaseList.add(depcaseDisease);
                }
            });
            depcaseDiseaseService.saveBatch(depDiseaseList);
        }
        //保存病例与类型关联
        //检查是否已经关联
        Integer count = depCaseClassifyService.lambdaQuery()
                .eq(DepCaseClassify::getCaseId, caseId)
                .eq(DepCaseClassify::getCaseTypeId, req.getCaseTypeId())
                .count();
        if (count == 0 && !req.getCaseTypeId().equals(Constant.DEP_FOLLOW_CASE_TYPE_ID)) {
            DepCaseClassify depCaseClassify = new DepCaseClassify();
            depCaseClassify.setId(YitIdHelper.nextId());
            depCaseClassify.setCaseId(caseId);
            depCaseClassify.setCaseTypeId(req.getCaseTypeId());
            depCaseClassifyService.save(depCaseClassify);
        }
        //查询是否需要审核
        if (!depCaseType.getAudit().equals(Constant.CASE_AUDIT_STATUS_1)) {
            return caseId;
        }
        //保存病例与审核表关联
        AuditRecord auditRecord = auditRecordService.lambdaQuery().eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, req.getCaseTypeId()).one();
        if (auditRecord != null) {
            auditRecord.setPubUserId(auditRecord.getPubUserId() + "," + LoginUtil.getLoginUserId());
        } else {
            auditRecord = new AuditRecord();
            auditRecord.setAuditId(YitIdHelper.nextId());
            auditRecord.setCaseId(caseId);
            auditRecord.setCaseTypeId(req.getCaseTypeId());
            auditRecord.setAuditType(req.getAuditType());
            auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
            auditRecord.setPubTime(new Date());
        }
        auditRecordService.saveOrUpdate(auditRecord);
        return caseId;
    }

    @Override
    public void updateDeptCase(DeptCaseUpdateReq req) {
        DepCase depCase = depCaseService.getById(req.getCaseId());
        if (depCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        BeanUtils.copyProperties(req, depCase);
        depCase.setUpdateTime(new Date());
        depCase.setUpdateBy(LoginUtil.getLoginUserId());
        depCase.setModifyUsers(depCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
        depCaseService.updateById(depCase);
    }

    @Override
    public PageInfo<AuditCaseDetailResp> getAuditCaseList(AuditCaseSearchReq req) {
        // 先分页查询审核Id，再填充复杂属性，避免复杂JOIN导致的分页问题
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<CaseBaseInfo> caseBaseInfoList = deptCaseMapper.getCaseAuditIdList(req);
        PageInfo<CaseBaseInfo> pageInfo = new PageInfo<>(caseBaseInfoList);
        if (CollectionUtils.isNotEmpty(caseBaseInfoList)) {
            // 获取带复杂的关联属性实体
            req.setCaseBaseInfoList(caseBaseInfoList);
            List<AuditCaseDetailResp> filledAuditCaseList = deptCaseMapper.getAuditCaseList(req);
            // 转换字典值为标签
            filledAuditCaseList.forEach(AuditCaseDetailResp::handleData);
            PageInfo<AuditCaseDetailResp> respPageInfo = new PageInfo<>(filledAuditCaseList);
            BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
            return respPageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    public PageInfo<AuditCaseDetailResp> getMyAuditCaseList(AuditCaseSearchReq req) {
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        // 先分页查询基本信息，再填充复杂属性，避免复杂JOIN导致的分页问题
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<CaseBaseInfo> myCaseBaseInfoList = deptCaseMapper.getCaseAuditIdList(req);
        PageInfo<CaseBaseInfo> pageInfo = new PageInfo<>(myCaseBaseInfoList);
        if (CollectionUtils.isNotEmpty(myCaseBaseInfoList)) {
            // 获取带复杂的关联属性实体
            req.setCaseBaseInfoList(myCaseBaseInfoList);
            List<AuditCaseDetailResp> filledAuditCaseList = deptCaseMapper.getAuditCaseList(req);
            // 转换字典值为标签
            filledAuditCaseList.forEach(AuditCaseDetailResp::handleData);
            PageInfo<AuditCaseDetailResp> respPageInfo = new PageInfo<>(filledAuditCaseList);
            BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
            return respPageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    public List<AuditInfo> getAuditRecordList(Long caseId, Long caseTypeId) {
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, caseTypeId)
                .orderByDesc(AuditRecord::getPubTime)
                .list();
        if (CollectionUtils.isEmpty(auditRecords)) {
            return new ArrayList<>();
        }
        return auditRecords.stream().map(auditRecord -> {
            String pubUserIds = auditRecord.getPubUserId();
            AuditInfo auditInfo = new AuditInfo();
            auditInfo.setAuditId(auditRecord.getAuditId());
            auditInfo.setAuditType(auditRecord.getAuditType());
            auditInfo.setAuditTypeLabel(DictConvertUtil.convertAuditType(auditRecord.getAuditType()));
            auditInfo.setStatus(auditRecord.getStatus());
            auditInfo.setStatusLabel(DictConvertUtil.convertStatus(auditRecord.getStatus()));
            auditInfo.setCaseId(auditRecord.getCaseId());
            auditInfo.setCaseTypeId(auditRecord.getCaseTypeId());
            auditInfo.setAuditedTime(auditRecord.getAuditedTime());
            auditInfo.setAuditedBy(auditRecord.getAuditedBy());
            auditInfo.setAuditedUserName(UserUtil.getUserNameById(auditRecord.getAuditedBy()));
            auditInfo.setPubUserId(pubUserIds);
            auditInfo.setAuditComment(auditRecord.getAuditComment());
            if (!pubUserIds.isEmpty()) {
                String[] userIds = pubUserIds.split(",");
                StringBuilder userNames = new StringBuilder();
                for (String userId : userIds) {
                    userNames.append(UserUtil.getUserNameById(Long.parseLong(userId))).append(",");
                }
                if (userNames.toString().endsWith(",")) {
                    userNames = new StringBuilder(userNames.substring(0, userNames.length() - 1));
                }
                auditInfo.setPubUserName(userNames.toString());
            }
            auditInfo.setPubTime(auditRecord.getPubTime());
            return auditInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public void verifyCase(CaseAuditReq req) {
        AuditRecord auditRecord = auditRecordService.getById(req.getAuditId());
        if (auditRecord == null) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        if (!auditRecord.getStatus().equals(Constant.CASE_AUDIT_STATUS_0)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.has.been.verified"));
        }
        auditRecord.setStatus(req.getStatus());
        auditRecord.setAuditedBy(LoginUtil.getLoginUserId());
        auditRecord.setAuditedTime(new Date());
        auditRecordService.updateById(auditRecord);
    }

    @Override
    public void reSubmitVerify(Long caseId, Long caseTypeId) {
        // 1、查询该病例以往提交的审核记录，并获取最新的
        List<AuditRecord> auditRecordList = auditRecordService.lambdaQuery().eq(AuditRecord::getCaseId, caseId)
                .eq(AuditRecord::getCaseTypeId, caseTypeId).list();
        if (CollectionUtils.isEmpty(auditRecordList)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        AuditRecord latestAuditRecord = auditRecordList.stream().max(Comparator.comparing(AuditRecord::getPubTime))
                .orElse(null);
        if (latestAuditRecord == null) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.exist"));
        }
        if (!latestAuditRecord.getStatus().equals(Constant.CASE_AUDIT_STATUS_2)) {
            throw new BusinessException(LocaleUtil.getLocale("audit.record.not.reject"));
        }
        // 2、新建审核记录
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setAuditId(YitIdHelper.nextId());
        auditRecord.setCaseId(caseId);
        auditRecord.setCaseTypeId(caseTypeId);
        auditRecord.setAuditType(latestAuditRecord.getAuditType());
        auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
        auditRecord.setPubTime(new Date());
        auditRecord.setStatus(Constant.CASE_AUDIT_STATUS_0);
        auditRecordService.saveOrUpdate(auditRecord);
    }

    @Override
    public List<TagInfo> getCaseTagList() {
        return tagService.list().stream().map(tag -> {
            TagInfo tagInfo = new TagInfo();
            BeanUtils.copyProperties(tag, tagInfo);
            return tagInfo;
        }).collect(Collectors.toList());
    }

    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    public void tagCase(CaseTagReq req) {
        List<String> tagList = req.getTagList();
        if (tagList.size() > 10) {
            throw new BusinessException(LocaleUtil.getLocale("tag.list.too.much"));
        }
        List<Tag> tags = tagService.list();
        // 先删除旧标签关联关系
        depCaseTagService.lambdaUpdate().eq(DepCaseTag::getCaseId, req.getId()).remove();
        // 将标签列表转换为map结构
        Map<String, Long> tagMap = tags.stream().collect(Collectors.toMap(Tag::getTagName, Tag::getTagId));
        if (CollectionUtils.isNotEmpty(tagList)) {
            tagList.forEach(tag -> {
                if (!tagMap.containsKey(tag)) {
                    Tag newTag = new Tag();
                    newTag.setTagId(YitIdHelper.nextId());
                    newTag.setTagName(tag);
                    tagService.save(newTag);
                    tagMap.put(tag, newTag.getTagId());
                }
                DepCaseTag depCaseTag = new DepCaseTag();
                depCaseTag.setId(YitIdHelper.nextId());
                depCaseTag.setCaseId(req.getId());
                depCaseTag.setTagId(tagMap.get(tag));
                depCaseTagService.save(depCaseTag);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followCase(CaseFollowReq req) {
        Follow follow = followService.lambdaQuery().eq(Follow::getCaseId, req.getCaseId())
                .eq(Follow::getFollowType, req.getFollowType()).one();
        if (follow == null) {
            follow = new Follow();
            follow.setFollowId(YitIdHelper.nextId());
            follow.setCaseId(req.getCaseId());
            follow.setStudyUid(req.getStudyUid());
            follow.setFollowType(req.getFollowType());
            follow.setFollowupResult(req.getFollowupResult());
            follow.setCreatedBy(LoginUtil.getLoginUserId());
            follow.setCreateTime(new Date());
            followService.save(follow);
            //更新科室病例库
            DepCase depCase = depCaseService.getById(req.getCaseId());
            depCase.setFollowStatus(Constant.FOLLOW_STATUS_1);
            depCase.setModifyUsers(depCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
            depCase.setUpdateBy(LoginUtil.getLoginUserId());
            depCase.setUpdateTime(new Date());
            depCaseService.updateById(depCase);
            //更新个人病例库
            List<UserCase> userCaseList = userCaseService
                    .lambdaQuery().eq(UserCase::getCaseId, req.getCaseId()).list();
            if (CollectionUtils.isNotEmpty(userCaseList)) {
                userCaseList.forEach(userCase -> {
                    userCase.setFollowStatus(Constant.FOLLOW_STATUS_1);
                    userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
                    userCase.setUpdateBy(LoginUtil.getLoginUserId());
                    userCase.setUpdateTime(new Date());
                    userCaseService.updateById(userCase);
                });
            }
        }
        String positionMatch = req.getPositionMatch();
        String qualityMatch = req.getQualityMatch();
        if (positionMatch != null) {
            studyService.lambdaUpdate().eq(Study::getStudyUid, req.getStudyUid()).set(Study::getPositionMatch, positionMatch).update();
        }
        if (qualityMatch != null) {
            studyService.lambdaUpdate().eq(Study::getStudyUid, req.getStudyUid()).set(Study::getQualityMatch, qualityMatch).update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFollow(CaseFollowUpdateReq req) {
        // 验证病例是否存在
        DepCase depCase = depCaseService.getById(req.getCaseId());
        List<UserCase> userCaseList = userCaseService.lambdaQuery().eq(UserCase::getCaseId, req.getCaseId()).list();
        if (depCase == null && CollectionUtils.isEmpty(userCaseList)) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        // 新增或修改病例随访
        List<FollowInfo> followInfoList = req.getFollowInfoList();
        if (CollectionUtils.isNotEmpty(followInfoList)) {
            followInfoList.forEach(followInfo -> {
                // 验证参数
                if (StringUtils.isEmpty(followInfo.getFollowType())
                        || StringUtils.isEmpty(followInfo.getFollowupResult())) {
                    throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
                }
                if (followInfo.getFollowId() == null) {
                    // 新增
                    Follow follow = new Follow();
                    follow.setFollowId(YitIdHelper.nextId());
                    follow.setCaseId(req.getCaseId());
                    follow.setStudyUid(req.getStudyUid());
                    follow.setFollowType(followInfo.getFollowType());
                    follow.setFollowupResult(followInfo.getFollowupResult());
                    follow.setCreatedBy(LoginUtil.getLoginUserId());
                    follow.setCreateTime(new Date());
                    followService.save(follow);
                } else {
                    // 修改
                    Follow follow = followService.getById(followInfo.getFollowId());
                    if (follow == null) {
                        throw new BusinessException(LocaleUtil.getLocale("follow.not.exist"));
                    }
                    follow.setFollowType(followInfo.getFollowType());
                    follow.setStudyUid(req.getStudyUid());
                    follow.setFollowupResult(followInfo.getFollowupResult());
                    follow.setUpdateBy(LoginUtil.getLoginUserId());
                    follow.setUpdateTime(new Date());
                    followService.updateById(follow);
                }
            });
        }
        //更新科室病例库
        if (depCase != null) {
            depCase.setFollowStatus(req.getFollowStatus());
            depCase.setModifyUsers(depCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
            depCase.setUpdateBy(LoginUtil.getLoginUserId());
            depCase.setUpdateTime(new Date());
            depCaseService.updateById(depCase);
            //同步到es
            esSyncService.syncDepartmentCase(Collections.singletonList(depCase.getCaseId()), Constant.OPERATION_TYPE_UPDATE);
        }
        //更新个人病例库
        if (CollectionUtils.isNotEmpty(userCaseList)) {
            userCaseList.forEach(userCase -> {
                userCase.setFollowStatus(req.getFollowStatus());
                userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
                userCase.setUpdateBy(LoginUtil.getLoginUserId());
                userCase.setUpdateTime(new Date());
                userCaseService.updateById(userCase);
            });
            esSyncService.syncPersonalCase(userCaseList.stream().map(UserCase::getUserCaseId)
                    .collect(Collectors.toList()), Constant.OPERATION_TYPE_UPDATE);
        }
        String positionMatch = req.getPositionMatch();
        String qualityMatch = req.getQualityMatch();
        if (positionMatch != null) {
            studyService.lambdaUpdate().eq(Study::getStudyUid, req.getStudyUid()).set(Study::getPositionMatch, positionMatch).update();
        }
        if (qualityMatch != null) {
            studyService.lambdaUpdate().eq(Study::getStudyUid, req.getStudyUid()).set(Study::getQualityMatch, qualityMatch).update();
        }
    }

    @Override
    public FollowExcelImportResp importFollowExcel(MultipartFile file) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 使用FollowExcelParser解析Excel文件
            log.info("【诊断日志】开始解析Excel文件，文件名: {}, 文件大小: {} bytes",
                    file.getOriginalFilename(), file.getSize());
            FollowExcelParser parser = new FollowExcelParser();
            FollowExcelImportResp response = parser.parseExcel(file);

            // 添加诊断日志
            log.info("【诊断日志】Excel解析完成 - 总行数: {}, 成功行数: {}, 错误行数: {}, 是否成功: {}",
                    response.getTotalRows(), response.getSuccessRows(),
                    response.getErrorRows(), response.getSuccess());

            if (response.hasErrors()) {
                log.warn("【诊断日志】Excel解析发现错误: {}", response.getErrorMessages());
            }

            // 2. 如果解析失败，直接返回
            if (!response.getSuccess()) {
                log.warn("Excel解析失败或无有效数据");
                return response;
            }

            // 3. 异步处理导入逻辑
            if (response.getSuccessRows() > 0) {
                processImportAsync(file, response);
            }

            long endTime = System.currentTimeMillis();
            response.setProcessingTime(endTime - startTime);

            log.info("Excel导入完成，总计{}条，成功{}条，失败{}条",
                    response.getTotalRows(), response.getSuccessRows(), response.getErrorRows());

            return response;

        } catch (Exception e) {
            log.error("Excel导入过程中发生异常", e);
            FollowExcelImportResp errorResponse = new FollowExcelImportResp();
            errorResponse.setSuccess(false);
            errorResponse.addErrorMessage("导入失败: " + e.getMessage());
            errorResponse.setProcessingTime(System.currentTimeMillis() - startTime);
            return errorResponse;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendCaseToDept(DeptCaseSendReq req) {
        // 1. 验证病例库类型及是否需要疾病Id
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        List<Long> diseaseIdList = Collections.emptyList();
        if (depCaseType.getDiseaseTree().equals(Constant.NEED_DISEASE_TREE)) {
            List<UserCaseDisease> userCaseDiseaseList = userCaseDiseaseService.lambdaQuery()
                    .eq(UserCaseDisease::getUserCaseId, req.getUserCaseId()).list();
            if (CollectionUtils.isEmpty(userCaseDiseaseList)) {
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }
            diseaseIdList = userCaseDiseaseList.stream().map(UserCaseDisease::getDiseaseId).collect(Collectors.toList());
        }
        // 2. 查询个人病例库病例
        UserCase userCase = userCaseService.getById(req.getUserCaseId());
        if (userCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        // 3. 复制病例到科室病例库
        DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
        BeanUtils.copyProperties(userCase, deptCaseCreateReq);
        deptCaseCreateReq.setDiseaseIdList(diseaseIdList);
        createDeptCase(deptCaseCreateReq);
        // 4. 保存标签
        List<Long> tagIdList = req.getTagIdList();
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            tagIdList.forEach(tagId -> {
                if (depCaseTagService.lambdaQuery()
                        .eq(DepCaseTag::getCaseId, req.getCaseId())
                        .eq(DepCaseTag::getTagId, tagId).count() == 0) {
                    DepCaseTag depCaseTag = new DepCaseTag();
                    depCaseTag.setId(YitIdHelper.nextId());
                    depCaseTag.setCaseId(req.getCaseId());
                    depCaseTag.setTagId(tagId);
                    depCaseTagService.save(depCaseTag);
                }
            });
        }
    }

    /**
     * 异步处理导入逻辑
     */
    private void processImportAsync(MultipartFile file, FollowExcelImportResp response) {
        ExecutorService followImportExecutorService = ContextHolder.getBean("followImport", ExecutorService.class);
        CompletableFuture.runAsync(() -> {
            try {
                // 重新解析Excel获取有效数据
                FollowExcelParser parser = new FollowExcelParser();
                List<ExcelFollowRow> validRows = parser.getValidRows(file);

                if (CollectionUtils.isEmpty(validRows)) {
                    return;
                }

                // 转换为导入请求
                List<CaseFollowImport> followRequests = validRows.stream()
                        .map(this::convertToFollowImportReq)
                        .collect(Collectors.toList());

                // 使用现有的importFollow方法进行数据入库
                Map<Integer, String> importResults = new ConcurrentHashMap<>();

                // 分批处理，每批100条记录以提高性能
                int batchSize = 100;
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (int i = 0; i < followRequests.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, followRequests.size());
                    List<CaseFollowImport> batch = followRequests.subList(i, endIndex);

                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        importFollow(batch, importResults);
                    }, followImportExecutorService);

                    futures.add(future);
                }

                // 等待所有批次处理完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                // 更新响应中的错误信息
                int actualErrorCount = importResults.size();
                int actualSuccessCount = response.getSuccessRows() - actualErrorCount;

                response.setSuccessRows(actualSuccessCount);
                response.setErrorRows(response.getErrorRows() + actualErrorCount);

                // 添加导入过程中的错误信息
                for (String errorMsg : importResults.values()) {
                    response.addErrorMessage(errorMsg);
                }

                response.calculateSuccess();

                log.info("异步导入处理完成，实际成功{}条，失败{}条", actualSuccessCount, actualErrorCount);

            } catch (Exception e) {
                log.error("异步导入处理失败", e);
                response.addErrorMessage("异步导入处理失败: " + e.getMessage());
            }
        }, followImportExecutorService);
    }

    @Override
    public void importFollow(List<CaseFollowImport> req, Map<Integer, String> resultMap) {
        long startTime = System.currentTimeMillis();
        log.info("开始批量导入随访数据，总数量: {}", req.size());

        if (CollectionUtils.isEmpty(req)) {
            resultMap.put(0, "导入失败，数据为空");
            return;
        }
        try {
            // 1. 批量查询获取病例Id
            List<String> accessNumberList = req.stream().map(CaseFollowImport::getAccessNumber)
                    .collect(Collectors.toList());
            List<Study> studyList = studyService.lambdaQuery()
                    .in(Study::getAccessNumber, accessNumberList)
                    .list();
            if (CollectionUtils.isEmpty(studyList)) {
                resultMap.put(0, "导入失败，未找到对应的病例报告");
                return;
            }
            // 2.获取病例id信息
            List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().in(CaseStudy::getStudyUid, studyList.stream()
                    .map(Study::getStudyUid).collect(Collectors.toList())).list();
            // 3.遍历studyList，返回key为accessNumber，value为caseId集合的map
            Map<String, List<Long>> accessNumToCaseIdMap = new HashMap<>();
            studyList.forEach(study -> {
                String accessNumber = study.getAccessNumber();
                List<Long> caseIdList = caseStudyList.stream()
                        .filter(caseStudy -> caseStudy.getStudyUid().equals(study.getStudyUid()))
                        .map(CaseStudy::getCaseId)
                        .collect(Collectors.toList());
                accessNumToCaseIdMap.put(accessNumber, caseIdList);
            });
            Map<String, CaseFollowImport> followImportDtoMap = req.stream()
                    .collect(Collectors.toMap(CaseFollowImport::getAccessNumber, r -> r));
            // 4.遍历accessNumToCaseIdMap，查询病例是否已随访，如果已随访，跳过，否则创建随访记录
            List<Follow> followsToSave = new ArrayList<>();
            Long currentUserId = LoginUtil.getLoginUserId();
            Date currentTime = new Date();
            accessNumToCaseIdMap.forEach((k, v) -> {
                CaseFollowImport caseFollowImport = followImportDtoMap.get(k);
                AtomicInteger hasFollowed = new AtomicInteger();
                v.forEach(caseId -> {
                    Integer count = followService.lambdaQuery().eq(Follow::getCaseId, caseId)
                            .eq(Follow::getFollowType, caseFollowImport.getFollowType()).count();
                    if (count > 0) {
                        hasFollowed.addAndGet(1);
                    } else {
                        // 创建随访记录
                        Follow follow = new Follow();
                        follow.setFollowId(YitIdHelper.nextId());
                        follow.setCaseId(caseId);
                        follow.setFollowType(caseFollowImport.getFollowType());
                        follow.setFollowupResult(caseFollowImport.getFollowupResult());
                        follow.setCreatedBy(currentUserId);
                        follow.setCreateTime(currentTime);
                        followsToSave.add(follow);
                    }
                });
                if (hasFollowed.get() == v.size()) {
                    resultMap.put(caseFollowImport.getNo(), "导入失败，该病理对应的所有类型病例都已随访");
                }
            });
            if (CollectionUtils.isNotEmpty(followsToSave)) {
                followService.saveBatch(followsToSave);
            }
            long endTime = System.currentTimeMillis();
            log.info("批量导入随访数据完成，总数量: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    req.size(), req.size() - resultMap.size(), resultMap.size(), (endTime - startTime));
        } catch (Exception e) {
            log.error("批量导入随访数据发生异常", e);
            resultMap.put(0, "导入失败: " + e.getMessage());
        }
    }

    /**
     * 转换ExcelFollowRowDto为CaseFollowImportReq
     */
    private CaseFollowImport convertToFollowImportReq(ExcelFollowRow rowDto) {
        CaseFollowImport req = new CaseFollowImport();
        req.setNo(rowDto.getRowIndex());
        req.setAccessNumber(rowDto.getAccessNumber());
        req.setFollowType(rowDto.getFollowType());
        req.setFollowupResult(rowDto.getFollowupResult());
        return req;
    }
}
