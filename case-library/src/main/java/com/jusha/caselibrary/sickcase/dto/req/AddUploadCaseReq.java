package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 从RIS导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class AddUploadCaseReq {

    @ApiModelProperty("检查信息")
    private List<StudyInfo> studyInfoList;

    @ApiModelProperty("导入目的地，个人病例库1，科室病例库2")
    private int targetLibrary;

    @ApiModelProperty("病例库id，个人病例库收藏夹id，科室病例库id")
    private Long targetId;

    @ApiModelProperty("如果是教学病例库，还需要有疾病id")
    private Long diseaseId;

    @ApiModelProperty("补充信息")
    private Supplement supplement;

    @Data
    public static class StudyInfo {

        private String patientId;

        private String studyUid;
    }

    @Data
    public static class Follow {

        @ApiModelProperty("随访类型")
        private String followType;

        @ApiModelProperty("随访结果")
        private String followupResult;
    }

    @Data
    public static class Supplement {

        @ApiModelProperty("病史")
        private String medicalHistory;

        @ApiModelProperty("病例类型")
        private int caseCategory;

        @ApiModelProperty("病例难度")
        private int difficulty;

        @ApiModelProperty("病例标签")
        private String[] tags;

        @ApiModelProperty("病例标签")
        private List<Follow> followInfoList;

        @ApiModelProperty("最终诊断")
        private String diagnosis;

        @ApiModelProperty("病例分析")
        private String caseAnalysis;

        @ApiModelProperty("备注")
        private String remark;

        @ApiModelProperty("患者主诉")
        private String selfComplaints;

        @ApiModelProperty(value = "征象")
        private String sign;

        @ApiModelProperty("病例名称")
        private String caseName;

        @ApiModelProperty("病例编号")
        private String caseNo;
    }
}
