package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;

import java.util.List;

/**
 * @ClassName CaseExportStrategy
 * @Description 病例导出策略接口 - 支持泛型，不同策略返回不同类型
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
public interface CaseExportStrategy<T, R> {
    
    /**
     * 查询病例详情列表
     *
     * @param searchRequest 查询请求参数
     * @return 病例详情列表，类型由具体策略决定
     */
    List<R> queryCaseDetailList(T searchRequest);
    
    /**
     * 获取支持的导出类型
     *
     * @return 导出类型
     */
    ExportType getSupportedExportType();
    
    /**
     * 验证请求参数
     *
     * @param searchRequest 查询请求参数
     */
    default void validateRequest(T searchRequest) {
        if (searchRequest == null) {
            throw new BusinessException(LocaleUtil.getLocale("common.request.not.null"));
        }
    }
    
    /**
     * 获取返回数据类型
     *
     * @return 返回数据类型的Class对象
     */
    Class<R> getReturnDataType();
}