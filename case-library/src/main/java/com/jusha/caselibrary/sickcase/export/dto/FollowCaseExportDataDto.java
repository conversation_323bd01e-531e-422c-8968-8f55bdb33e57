package com.jusha.caselibrary.sickcase.export.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.jusha.caselibrary.common.aop.ExportField;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.RedisUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName FollowCaseExportDataDto
 * @Description 随访导出数据DTO - 包含动态字典统计信息
 * <AUTHOR>
 * @Date 2025/7/11 08:45
 **/
@Data
public class FollowCaseExportDataDto {

    @ApiModelProperty(value = "病例导出数据列表")
    private List<CaseExportDataDto> caseExportDataDtoList;

    // ==================== 基础统计 ====================

    @ExportField(value = "总病例数", index = 0,mergeable = true, mergeType = Constant.MERGE_TYPE_SUMMARY)
    @ApiModelProperty(value = "总病例数")
    private Integer totalCaseCount;

    @ExportField(value = "统计时间", index = 1,mergeable = true, mergeType = Constant.MERGE_TYPE_SUMMARY)
    @ApiModelProperty(value = "统计时间")
    private String statisticsTime;

    // ==================== 定性匹配统计 ====================
    // 动态生成的定性匹配统计字段
    private Map<String, Integer> qualityMatchCounts = new HashMap<>();
    private Map<String, String> qualityMatchRates = new HashMap<>();

    // ==================== 定位匹配统计 ====================
    // 动态生成的定位匹配统计字段
    private Map<String, Integer> positionMatchCounts = new HashMap<>();
    private Map<String, String> positionMatchRates = new HashMap<>();

    // ==================== 导出字段映射 ====================
    // 用于存储动态生成的导出字段值
    private Map<String, Object> dynamicExportFields = new HashMap<>();

    /**
     * 计算统计信息
     */
    public void calculateStatistics() {
        if (CollUtil.isEmpty(caseExportDataDtoList)) {
            initializeZeroStatistics();
            return;
        }

        // 总数
        this.totalCaseCount = caseExportDataDtoList.size();

        // 获取字典配置
        Map<String, String> qualityMatchDict = RedisUtil.getDictConfig("qualityMatch");
        Map<String, String> positionMatchDict = RedisUtil.getDictConfig("positionMatch");

        // 计算定性匹配统计
        calculateQualityMatchStatistics(qualityMatchDict);

        // 计算定位匹配统计
        calculatePositionMatchStatistics(positionMatchDict);

        // 生成动态导出字段
        generateDynamicExportFields();

        // 设置统计时间
        this.statisticsTime = java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN));
    }

    /**
     * 计算定性匹配统计
     */
    private void calculateQualityMatchStatistics(Map<String, String> qualityMatchDict) {
        // 统计实际数据中的值
        Map<String, Long> actualCounts = caseExportDataDtoList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getQualityMatch()))
                .collect(Collectors.groupingBy(
                        CaseExportDataDto::getQualityMatch,
                        Collectors.counting()
                ));

        // 统计空值数量
        long emptyCount = caseExportDataDtoList.stream()
                .filter(item -> StrUtil.isBlank(item.getQualityMatch()))
                .count();

        // 清空之前的统计
        qualityMatchCounts.clear();
        qualityMatchRates.clear();

        // 为每个字典值计算统计
        for (Map.Entry<String, String> entry : qualityMatchDict.entrySet()) {
            String dictLabel = entry.getValue();
            Integer count = actualCounts.getOrDefault(dictLabel, 0L).intValue();
            String rate = calculateRate(count, totalCaseCount);

            qualityMatchCounts.put(dictLabel, count);
            qualityMatchRates.put(dictLabel, rate);
        }

        // 添加空值统计
        if (emptyCount > 0) {
            qualityMatchCounts.put("未填写", (int) emptyCount);
            qualityMatchRates.put("未填写", calculateRate((int) emptyCount, totalCaseCount));
        }
    }

    /**
     * 计算定位匹配统计
     */
    private void calculatePositionMatchStatistics(Map<String, String> positionMatchDict) {
        // 统计实际数据中的值
        Map<String, Long> actualCounts = caseExportDataDtoList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getPositionMatch()))
                .collect(Collectors.groupingBy(
                        CaseExportDataDto::getPositionMatch,
                        Collectors.counting()
                ));

        // 统计空值数量
        long emptyCount = caseExportDataDtoList.stream()
                .filter(item -> StrUtil.isBlank(item.getPositionMatch()))
                .count();

        // 清空之前的统计
        positionMatchCounts.clear();
        positionMatchRates.clear();

        // 为每个字典值计算统计
        for (Map.Entry<String, String> entry : positionMatchDict.entrySet()) {
            String dictLabel = entry.getValue();
            Integer count = actualCounts.getOrDefault(dictLabel, 0L).intValue();
            String rate = calculateRate(count, totalCaseCount);

            positionMatchCounts.put(dictLabel, count);
            positionMatchRates.put(dictLabel, rate);
        }

        // 添加空值统计
        if (emptyCount > 0) {
            positionMatchCounts.put("未填写", (int) emptyCount);
            positionMatchRates.put("未填写", calculateRate((int) emptyCount, totalCaseCount));
        }
    }

    /**
     * 生成动态导出字段
     */
    private void generateDynamicExportFields() {
        dynamicExportFields.clear();

        // 添加基础字段
        dynamicExportFields.put("总病例数", totalCaseCount);
        dynamicExportFields.put("统计时间", statisticsTime);

        // 添加定性匹配统计字段
        for (Map.Entry<String, Integer> entry : qualityMatchCounts.entrySet()) {
            String label = entry.getKey();
            Integer count = entry.getValue();
            String rate = qualityMatchRates.get(label);

            dynamicExportFields.put("定性" + label + "数量", count);
            dynamicExportFields.put("定性" + label + "率", rate);
        }

        // 添加定位匹配统计字段
        for (Map.Entry<String, Integer> entry : positionMatchCounts.entrySet()) {
            String label = entry.getKey();
            Integer count = entry.getValue();
            String rate = positionMatchRates.get(label);

            dynamicExportFields.put("定位" + label + "数量", count);
            dynamicExportFields.put("定位" + label + "率", rate);
        }
    }

    /**
     * 计算比率
     */
    private String calculateRate(Integer count, Integer total) {
        if (total == null || total == 0) {
            return "0.00%";
        }
        double rate = (count != null ? count : 0) * 100.0 / total;
        return NumberUtil.decimalFormat("#.##", rate) + "%";
    }

    /**
     * 初始化零统计数据
     */
    private void initializeZeroStatistics() {
        this.totalCaseCount = 0;
        this.qualityMatchCounts.clear();
        this.qualityMatchRates.clear();
        this.positionMatchCounts.clear();
        this.positionMatchRates.clear();
        this.dynamicExportFields.clear();
        this.statisticsTime = java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN));
    }

    /**
     * 获取动态导出字段值
     */
    public Object getDynamicExportFieldValue(String fieldName) {
        return dynamicExportFields.get(fieldName);
    }

    /**
     * 获取所有动态导出字段名
     */
    public Set<String> getDynamicExportFieldNames() {
        return dynamicExportFields.keySet();
    }
}
