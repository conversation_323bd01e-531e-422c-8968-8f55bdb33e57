package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DeptCaseExportStrategy
 * @Description 科室病例导出策略实现 - 返回DeptCaseDetailResp类型
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Slf4j
public class DeptCaseExportStrategy implements CaseExportStrategy<DeptCaseSearchReq, DeptCaseDetailResp> {
    
    private final DeptCaseDataProcessor dataProcessor;
    
    public DeptCaseExportStrategy() {
        this.dataProcessor = new DeptCaseDataProcessor();
    }
    
    @Override
    public List<DeptCaseDetailResp> queryCaseDetailList(DeptCaseSearchReq searchRequest) {
        try {
            // 验证请求参数
            validateRequest(searchRequest);
            
            // 获取科室病例服务实例
            DeptCaseService deptCaseService = ContextHolder.getBean(DeptCaseService.class);
            
            // 查询科室病例详情列表
            List<DeptCaseDetailResp> caseDetailList = deptCaseService.getDeptCaseDetailList(searchRequest);
            
            if (caseDetailList == null || caseDetailList.isEmpty()) {
                log.info("科室病例查询结果为空，查询条件: {}", searchRequest);
                return new ArrayList<>();
            }
            
            // 使用数据处理器处理数据
            List<DeptCaseDetailResp> processedList = dataProcessor.processData(caseDetailList);
            
            log.info("科室病例查询成功，共查询到 {} 条记录", processedList.size());
            return processedList;
            
        } catch (Exception e) {
            log.error("查询科室病例详情失败，查询条件: {}, 错误信息: {}", searchRequest, e.getMessage(), e);
            throw new BusinessException(LocaleUtil.getLocale("case.query.failed"));
        }
    }
    
    @Override
    public ExportType getSupportedExportType() {
        return ExportType.DEPT_CASE;
    }
    
    @Override
    public Class<DeptCaseDetailResp> getReturnDataType() {
        return DeptCaseDetailResp.class;
    }
    
    @Override
    public void validateRequest(DeptCaseSearchReq searchRequest) {
        // 调用父接口的默认验证
        CaseExportStrategy.super.validateRequest(searchRequest);
        
        // 科室病例特定的验证逻辑
        // 这里可以根据实际业务需求添加更多验证规则
        if (searchRequest.getPageNum() != null && searchRequest.getPageNum() < 1) {
            throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
        }
        
        if (searchRequest.getPageSize() != null && searchRequest.getPageSize() < 1) {
            throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
        }
        
        log.debug("科室病例导出请求参数验证通过: {}", searchRequest);
    }
}