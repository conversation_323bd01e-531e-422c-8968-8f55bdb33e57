package com.jusha.caselibrary.sickcase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.search.service.ESSyncService;
import com.jusha.caselibrary.sickcase.dto.TagInfo;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.mapper.PersonalCaseMapper;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName PersonalCaseServiceImpl
 * @Description 个人病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalCaseServiceImpl implements PersonalCaseService {

    private final PersonalCaseMapper personalCaseMapper;

    private final UserCaseService usrCaseService;

    private final DepCaseService depCaseService;

    private final UserCaseCatalogService userCaseCatalogService;

    private final UserCatalogService userCatalogService;

    private final TagService tagService;

    private final UserCaseTagService userCaseTagService;

    private final AdvancedSearchService advancedSearchService;

    private final ESSyncService esSyncService;

    private final UserCaseDiseaseService usercaseDiseaseService;

    private final DepCaseDiseaseService depCaseDiseaseService;
    private final UserCaseService userCaseService;

    @Override
    public List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req) {
        PersonalSearchReq personalSearchReq = new PersonalSearchReq();
        BeanUtil.copyProperties(req, personalSearchReq);
        personalSearchReq.setUserId(LoginUtil.getLoginUserId());
        return advancedSearchService.searchAndConvertList(personalSearchReq, PersonalCaseDetailResp.class);
    }

    @Override
    public PageInfo<PersonalCaseDetailResp> getPersonalCaseDetailPage(PersonalCaseSearchReq req) {
        String keyword = req.getKeyword();
        if (req.getUserId() == null) {
            req.setUserId(LoginUtil.getLoginUserId());
        }
        if (StringUtils.hasText(keyword) && !"*".equals(keyword)) {
            // 如果关键字包含值，则直接使用高级检索
            PersonalSearchReq personalSearchReq = new PersonalSearchReq();
            BeanUtil.copyProperties(req, personalSearchReq);
            return advancedSearchService.searchAndConvertPage(personalSearchReq, PersonalCaseDetailResp.class);
        } else {
            // 如果没有值，则直接查询数据库
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
            List<Long> personalCaseIdList = personalCaseMapper
                    .getPersonalCaseIdList(req);
            PageInfo<Long> pageInfo = new PageInfo<>(personalCaseIdList);
            if (CollectionUtil.isNotEmpty(personalCaseIdList)) {
                // 获取带复杂的关联属性实体
                req.setUserCaseIdList(personalCaseIdList);
                List<PersonalCaseDetailResp> filledPersonalCaseDetailList = personalCaseMapper
                        .getPersonalCaseDetailList(req);
                // 转换字典值为标签
                filledPersonalCaseDetailList.forEach(PersonalCaseDetailResp::handleData);
                PageInfo<PersonalCaseDetailResp> respPageInfo = new PageInfo<>(filledPersonalCaseDetailList);
                BeanUtils.copyProperties(pageInfo, respPageInfo, "list");
                return respPageInfo;
            }
        }
        return new PageInfo<>();
    }

    @Override
    public PersonalCaseDetailResp getPersonalCaseDetail(Long userCaseId) {
        PersonalCaseSearchReq req = new PersonalCaseSearchReq();
        req.setUserCaseIdList(Collections.singletonList(userCaseId));
        List<PersonalCaseDetailResp> list = personalCaseMapper.getPersonalCaseDetailList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            list.get(0).handleData();
            return list.get(0);
        }
        return new PersonalCaseDetailResp();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePersonalCaseById(Long userCaseId, Long catalogId) {
        //1、删除个人病例与目录的关联关系
        userCaseCatalogService.lambdaUpdate()
                .eq(UserCaseCatalog::getCatalogId, catalogId)
                .eq(UserCaseCatalog::getUserCaseId, userCaseId).remove();
        //2、如果该病例不存在于其他目录，则删除病例
        if (userCaseCatalogService.lambdaQuery().eq(UserCaseCatalog::getUserCaseId, userCaseId).count() == 0) {
            //删除疾病关联关系
            usercaseDiseaseService.lambdaUpdate().eq(UserCaseDisease::getUserCaseId, userCaseId).remove();
            boolean removed = usrCaseService.lambdaUpdate().eq(UserCase::getUserCaseId, userCaseId).remove();
            if (removed) {
                esSyncService.syncPersonalCase(Collections.singletonList(userCaseId), Constant.OPERATION_TYPE_DELETE);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long createPersonalCaseInCatalog(PersonalCaseCreateReq req) {
        Long loginUserId = LoginUtil.getLoginUserId();
        Long caseId = req.getCaseId();
        //保存病例
        UserCase userCase = null;
        if (caseId != null) {
            userCase = usrCaseService.lambdaQuery().eq(UserCase::getCaseId, caseId)
                    .eq(UserCase::getCreateBy, loginUserId).one();
        }
        if (userCase == null) {
            userCase = new UserCase();
            long userCaseId = YitIdHelper.nextId();
            BeanUtils.copyProperties(req, userCase);
            userCase.setCaseId(caseId == null ? YitIdHelper.nextId() : caseId);
            userCase.setUserCaseId(userCaseId);
            userCase.setCreateBy(LoginUtil.getLoginUserId());
            usrCaseService.save(userCase);
        }

        //增加疾病和病例关联关系
        if (CollectionUtils.isNotEmpty(req.getDiseaseIdList())) {
            List<UserCaseDisease> userCaseDiseaseList = new ArrayList<>();
            UserCase finalUserCase = userCase;
            req.getDiseaseIdList().forEach(diseaseId -> {
                if (usercaseDiseaseService.lambdaQuery().eq(UserCaseDisease::getUserCaseId, finalUserCase.getUserCaseId())
                        .eq(UserCaseDisease::getDiseaseId, diseaseId).count() == 0){
                    UserCaseDisease usercaseDisease = new UserCaseDisease();
                    usercaseDisease.setId(YitIdHelper.nextId());
                    usercaseDisease.setUserCaseId(finalUserCase.getUserCaseId());
                    usercaseDisease.setDiseaseId(diseaseId);
                    userCaseDiseaseList.add(usercaseDisease);
                }
            });
            usercaseDiseaseService.saveBatch(userCaseDiseaseList);
        }

        // 保存病例与目录的关联关系
        // 1.先删除所有已经关联的目录的关联关系
        userCaseCatalogService.lambdaUpdate().eq(UserCaseCatalog::getUserCaseId, userCase.getUserCaseId()).remove();
        // 2.保存新的关联关系
        List<Long> catalogIdList = req.getCatalogIdList();
        if (CollectionUtils.isNotEmpty(catalogIdList)) {
            List<UserCaseCatalog> userCaseCatalogList = new ArrayList<>();
            for (Long catalogId : catalogIdList) {
                UserCaseCatalog userCaseCatalog = new UserCaseCatalog();
                userCaseCatalog.setId(YitIdHelper.nextId());
                userCaseCatalog.setUserCaseId(userCase.getUserCaseId());
                userCaseCatalog.setCatalogId(catalogId);
                userCaseCatalogList.add(userCaseCatalog);
            }
            userCaseCatalogService.saveBatch(userCaseCatalogList);
        }
        return userCase.getUserCaseId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePersonalCase(PersonalCaseUpdateReq req) {
        UserCase userCase = usrCaseService.getById(req.getUserCaseId());
        if (userCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        BeanUtils.copyProperties(req, userCase);
        userCase.setUpdateTime(new Date());
        userCase.setUpdateBy(LoginUtil.getLoginUserId());
        userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
        usrCaseService.updateById(userCase);
        // 修改疾病关联关系
        List<Long> diseaseIdList = req.getDiseaseIdList();
        if (CollectionUtils.isNotEmpty(diseaseIdList)) {
            usercaseDiseaseService.lambdaUpdate().eq(UserCaseDisease::getUserCaseId, req.getUserCaseId()).remove();
            ArrayList<UserCaseDisease> userCaseDiseaseList = new ArrayList<>();
            diseaseIdList.forEach(diseaseId -> {
                UserCaseDisease userCaseDisease = new UserCaseDisease();
                userCaseDisease.setId(YitIdHelper.nextId());
                userCaseDisease.setUserCaseId(req.getUserCaseId());
                userCaseDisease.setDiseaseId(diseaseId);
                userCaseDiseaseList.add(userCaseDisease);
            });
            usercaseDiseaseService.saveBatch(userCaseDiseaseList);
        }
        // 修改标签信息
        UserCaseTagReq userCaseTagReq = new UserCaseTagReq();
        userCaseTagReq.setUserCaseId(req.getUserCaseId());
        userCaseTagReq.setTagNameList(req.getTagNameList());
        tagCase(userCaseTagReq);
    }

    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    public void tagCase(UserCaseTagReq req) {
        List<String> tagNameList = req.getTagNameList();
        List<Tag> tags = tagService.list();
        // 先删除旧标签关联关系
        userCaseTagService.lambdaUpdate().eq(UserCaseTag::getUserCaseId, req.getUserCaseId()).remove();
        // 将标签列表转换为map结构
        Map<String, Long> tagMap = tags.stream().collect(Collectors.toMap(Tag::getTagName, Tag::getTagId));
        if (CollectionUtils.isNotEmpty(tagNameList)) {
            tagNameList.forEach(tag -> {
                if (!tagMap.containsKey(tag)) {
                    Tag newTag = new Tag();
                    newTag.setTagId(YitIdHelper.nextId());
                    newTag.setTagName(tag);
                    tagService.save(newTag);
                    tagMap.put(tag, newTag.getTagId());
                }
                UserCaseTag userCaseTag = new UserCaseTag();
                userCaseTag.setId(YitIdHelper.nextId());
                userCaseTag.setUserCaseId(req.getUserCaseId());
                userCaseTag.setTagId(tagMap.get(tag));
                userCaseTagService.save(userCaseTag);
            });
        }
    }

    @Override
    public List<TagInfo> getCaseTagList() {
        // 查询已经打过的个人病例标签
        Long loginUserId = LoginUtil.getLoginUserId();
        List<UserCase> userCaseList = usrCaseService.lambdaQuery().eq(UserCase::getCreateBy, loginUserId).list();
        if (CollectionUtils.isEmpty(userCaseList)) {
            return new ArrayList<>();
        }
        List<Long> userCaseIdList = userCaseList.stream().map(UserCase::getUserCaseId).collect(Collectors.toList());
        List<UserCaseTag> userCaseTagList = userCaseTagService.lambdaQuery()
                .in(UserCaseTag::getUserCaseId, userCaseIdList).list();
        if (CollectionUtils.isNotEmpty(userCaseTagList)) {
            List<Long> tagIdList = userCaseTagList.stream()
                    .map(UserCaseTag::getTagId).collect(Collectors.toList());
            List<Tag> tagList = tagService.lambdaQuery().in(Tag::getTagId, tagIdList).list();
            if (CollectionUtils.isNotEmpty(tagList)) {
                return tagList.stream().map(tag -> {
                    TagInfo tagInfo = new TagInfo();
                    BeanUtils.copyProperties(tag, tagInfo);
                    return tagInfo;
                }).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    public long copyCaseFromDept(CollectCaseReq req) {
        // 1. 查询科室病例库病例
        DepCase depCase = depCaseService.getById(req.getCaseId());
        if (depCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        // 1.1 获取科室病例库病例的疾病
        List<DepCaseDisease> diseaseList = depCaseDiseaseService.lambdaQuery()
                .eq(DepCaseDisease::getCaseId, req.getCaseId()).list();
        List<Long> diseaseIdList = diseaseList.stream().map(DepCaseDisease::getDiseaseId).collect(Collectors.toList());
        // 2. 复制到个人病例库
        PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
        BeanUtils.copyProperties(depCase, personalCaseCreateReq);
        personalCaseCreateReq.setDiseaseIdList(diseaseIdList);
        personalCaseCreateReq.setCatalogIdList(req.getCatalogIdList());
        long userCaseId = createPersonalCaseInCatalog(personalCaseCreateReq);
        // 3. 保存标签
        List<Long> tagIdList = req.getTagIdList();
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            tagIdList.forEach(tagId -> {
                if (userCaseTagService.lambdaQuery()
                        .eq(UserCaseTag::getUserCaseId, userCaseId)
                        .eq(UserCaseTag::getTagId, tagId).count() == 0) {
                    UserCaseTag userCaseTag = new UserCaseTag();
                    userCaseTag.setId(YitIdHelper.nextId());
                    userCaseTag.setUserCaseId(userCaseId);
                    userCaseTag.setTagId(tagId);
                    userCaseTagService.save(userCaseTag);
                }
            });
        }
        // 除了同步个人病例库，科室的这个被收藏的病例也需要同步，因为需要查询他被哪些收藏了
        esSyncService.syncDepartmentCase(Collections.singletonList(req.getCaseId()), Constant.OPERATION_TYPE_UPDATE);
        return userCaseId;
    }
}
