package com.jusha.caselibrary.sickcase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.search.service.ESSyncService;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.mapper.PersonalCaseMapper;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName PersonalCaseServiceImpl
 * @Description 个人病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalCaseServiceImpl implements PersonalCaseService {

    private final PersonalCaseMapper personalCaseMapper;

    private final UserCaseService usrCaseService;

    private final DepCaseService depCaseService;

    private final UserCaseCatalogService userCaseCatalogService;

    private final UserCatalogService userCatalogService;

    private final TagService tagService;

    private final UserCaseTagService userCaseTagService;

    private final AdvancedSearchService advancedSearchService;

    private final ESSyncService esSyncService;

    @Override
    public List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req) {
        PersonalSearchReq personalSearchReq = new PersonalSearchReq();
        BeanUtil.copyProperties(req, personalSearchReq);
        personalSearchReq.setUserId(LoginUtil.getLoginUserId());
        return advancedSearchService.searchAndConvertList(personalSearchReq, PersonalCaseDetailResp.class);
    }

    @Override
    public PageInfo<PersonalCaseDetailResp> getPersonalCaseDetailPage(PersonalCaseSearchReq req) {
        PageInfo<PersonalCaseDetailResp> pageInfo = new PageInfo<>();
        String keyword = req.getKeyword();
        if (StringUtils.hasText(keyword)) {
            // 如果关键字包含值，则直接使用高级检索
            PersonalSearchReq personalSearchReq = new PersonalSearchReq();
            BeanUtil.copyProperties(req, personalSearchReq);
            pageInfo = advancedSearchService.searchAndConvertPage(personalSearchReq, PersonalCaseDetailResp.class);
        } else {
            // 如果没有值，则直接查询数据库
            req.setUserId(LoginUtil.getLoginUserId());
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
            List<PersonalCaseDetailResp> personalCaseDetailList = personalCaseMapper
                    .getPersonalCaseDetailList(req);
            if (CollectionUtil.isNotEmpty(personalCaseDetailList)) {
                // 转换字典值为标签
                personalCaseDetailList.forEach(PersonalCaseDetailResp::setDictLabels);
                pageInfo = new PageInfo<>(personalCaseDetailList);
            }
        }
        return pageInfo;
    }

    @Override
    public PersonalCaseDetailResp getPersonalCaseDetail(Long userCaseId) {
        PersonalCaseSearchReq req = new PersonalCaseSearchReq();
        req.setUserCaseId(userCaseId);
        List<PersonalCaseDetailResp> list = personalCaseMapper.getPersonalCaseDetailList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            list.get(0).setDictLabels();
            return list.get(0);
        }
        return new PersonalCaseDetailResp();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePersonalCaseById(Long userCaseId, Long catalogId) {
        //1、删除个人病例与目录的关联关系
        userCaseCatalogService.lambdaUpdate()
                .eq(UserCaseCatalog::getCatalogId, catalogId)
                .eq(UserCaseCatalog::getUserCaseId, userCaseId).remove();
        //2、如果该病例不存在于其他目录，则删除病例
        if (userCaseCatalogService.lambdaQuery().eq(UserCaseCatalog::getUserCaseId, userCaseId).count() == 0) {
            boolean removed = usrCaseService.lambdaUpdate().eq(UserCase::getUserCaseId, userCaseId).remove();
            if (removed) {
                esSyncService.syncPersonalCase(Collections.singletonList(userCaseId), Constant.OPERATION_TYPE_DELETE);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long createPersonalCaseInCatalog(PersonalCaseCreateReq req) {
        Long loginUserId = LoginUtil.getLoginUserId();
        Long caseId = req.getCaseId();
        //检查目录是否存在
        Long catalogId = req.getCatalogId();
        UserCatalog userCatalog = userCatalogService.getById(catalogId);
        if (userCatalog == null) {
            throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
        }
        //保存病例
        UserCase userCase = null;
        if (caseId != null) {
            userCase = usrCaseService.lambdaQuery().eq(UserCase::getCaseId, caseId)
                    .eq(UserCase::getCreateBy, loginUserId).one();
        }
        if (userCase == null) {
            userCase = new UserCase();
            long userCaseId = YitIdHelper.nextId();
            BeanUtils.copyProperties(req, userCase);
            userCase.setCaseId(caseId == null ? YitIdHelper.nextId() : caseId);
            userCase.setUserCaseId(userCaseId);
            userCase.setCreateBy(LoginUtil.getLoginUserId());
            usrCaseService.save(userCase);
        }
        // 保存病例与目录的关联关系
        // 如果目录下已经存在该病例，则不再重复添加
        UserCaseCatalog userCaseCatalog = userCaseCatalogService.lambdaQuery().
                eq(UserCaseCatalog::getUserCaseId, userCase.getUserCaseId())
                .eq(UserCaseCatalog::getCatalogId, catalogId).one();
        if (userCaseCatalog == null) {
            userCaseCatalog = new UserCaseCatalog();
            userCaseCatalog.setId(YitIdHelper.nextId());
            userCaseCatalog.setUserCaseId(userCase.getUserCaseId());
            userCaseCatalog.setCatalogId(catalogId);
            userCaseCatalogService.save(userCaseCatalog);
        }
        return userCase.getUserCaseId();
    }

    @Override
    public void updatePersonalCase(PersonalCaseUpdateReq req) {
        UserCase userCase = usrCaseService.getById(req.getUserCaseId());
        if (userCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        BeanUtils.copyProperties(req, userCase);
        userCase.setUpdateTime(new Date());
        userCase.setUpdateBy(LoginUtil.getLoginUserId());
        userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
        usrCaseService.updateById(userCase);
    }

    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    public void tagCase(CaseTagReq req) {
        List<String> tagList = req.getTagList();
        List<Tag> tags = tagService.list();
        // 将标签列表转换为map结构
        Map<String, Long> tagMap = tags.stream().collect(Collectors.toMap(Tag::getTagName, Tag::getTagId));
        if (CollectionUtils.isNotEmpty(tagList)) {
            tagList.forEach(tag -> {
                if (!tagMap.containsKey(tag)) {
                    Tag newTag = new Tag();
                    newTag.setTagId(YitIdHelper.nextId());
                    newTag.setTagName(tag);
                    tagService.save(newTag);
                    tagMap.put(tag, newTag.getTagId());
                }
                UserCaseTag userCaseTag = new UserCaseTag();
                userCaseTag.setId(YitIdHelper.nextId());
                userCaseTag.setUserCaseId(req.getId());
                userCaseTag.setTagId(tagMap.get(tag));
                userCaseTagService.save(userCaseTag);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    public long copyCaseFromDept(CollectCaseReq req) {
        // 1. 查询科室病例库病例
        DepCase depCase = depCaseService.getById(req.getCaseId());
        if (depCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        // 2. 复制到个人病例库
        PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
        BeanUtils.copyProperties(depCase, personalCaseCreateReq);
        long userCaseId = createPersonalCaseInCatalog(personalCaseCreateReq);
        // 3. 保存标签
        List<Long> tagIdList = req.getTagIdList();
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            tagIdList.forEach(tagId -> {
                if (userCaseTagService.lambdaQuery()
                        .eq(UserCaseTag::getUserCaseId, userCaseId)
                        .eq(UserCaseTag::getTagId, tagId).count() == 0) {
                    UserCaseTag userCaseTag = new UserCaseTag();
                    userCaseTag.setId(YitIdHelper.nextId());
                    userCaseTag.setUserCaseId(userCaseId);
                    userCaseTag.setTagId(tagId);
                    userCaseTagService.save(userCaseTag);
                }
            });
        }
        return userCaseId;
    }
}
