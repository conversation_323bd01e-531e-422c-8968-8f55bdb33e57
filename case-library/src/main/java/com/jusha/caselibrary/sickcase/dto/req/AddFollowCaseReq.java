package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 从随访库导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class AddFollowCaseReq {

    @ApiModelProperty("病例id")
    private Long caseId;

    @ApiModelProperty("病例库id")
    private Long caseTypeId;

    @ApiModelProperty("如果是教学病例库，还需要有疾病id")
    private Long diseaseId;
}