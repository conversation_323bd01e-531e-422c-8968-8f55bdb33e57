package com.jusha.caselibrary.sickcase.export.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @ClassName CustomMergeStrategy
 * @Description 自定义合并策略
 * <AUTHOR>
 * @Date 2025/8/1 11:56
 **/
@Slf4j
public class CustomMergeStrategy extends AbstractMergeStrategy {
    private List<CaseExportDataDto> dataList;
    private Set<String> followTypes;
    private List<ExportFieldInfo> fieldInfoList;
    private boolean headerMerged = false;
    private boolean dataRowMerged = false;

    public CustomMergeStrategy(List<CaseExportDataDto> dataList, Set<String> followTypes, List<ExportFieldInfo> fieldInfoList) {
        this.dataList = dataList;
        this.followTypes = followTypes != null ? followTypes : new HashSet<>();
        this.fieldInfoList = fieldInfoList;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        // 表头合并（只在第一次执行，且在写入表头后）
        if (!headerMerged && relativeRowIndex == 0) {
            mergeHeaders(sheet);
            headerMerged = true;
        }

        // 数据行合并（在所有数据写入完成后执行一次）
        if (!dataRowMerged && relativeRowIndex != null && relativeRowIndex == dataList.size() - 1) {
            mergeDataRows(sheet);
            dataRowMerged = true;
        }
    }

    /**
     * 合并表头 - 基于字段分组
     */
    private void mergeHeaders(Sheet sheet) {
        try {
            // 如果没有随访类型，跳过随访结果表头合并
            if (!followTypes.isEmpty()) {
                mergeFollowResultHeaders(sheet);
            } else {
                log.debug("没有随访类型，跳过随访结果表头合并");
            }

            // 检查是否有检查报告字段，有的话才进行合并
            if (hasStudyInfoFields()) {
                mergeStudyInfoHeaders(sheet);
            } else {
                log.debug("没有检查报告字段，跳过检查报告表头合并");
            }

        } catch (Exception e) {
            log.error("表头合并失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查是否有检查报告字段
     */
    private boolean hasStudyInfoFields() {
        if (fieldInfoList == null || fieldInfoList.isEmpty()) {
            return false;
        }

        return fieldInfoList.stream()
                .anyMatch(field -> Constant.MERGE_TYPE_STUDY.equals(field.getMergeType()));
    }

    /**
     * 合并随访结果表头
     */
    private void mergeFollowResultHeaders(Sheet sheet) {
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            log.warn("第一行为空，无法合并随访结果表头");
            return;
        }

        // 找到随访结果列的范围
        List<Integer> followResultCols = new ArrayList<>();

        for (int col = 0; col <= firstRow.getLastCellNum(); col++) {
            Cell cell = firstRow.getCell(col);
            if (cell != null && Constant.FOLLOW_RESULT.equals(cell.getStringCellValue())) {
                followResultCols.add(col);
            }
        }

        // 只有超过1列时才需要合并
        if (followResultCols.size() > 1) {
            int startCol = followResultCols.get(0);
            int endCol = followResultCols.get(followResultCols.size() - 1);

            // 检查是否已经存在合并区域
            if (isMergedRegionExists(sheet, 0, 0, startCol, endCol)) {
                CellRangeAddress mergeRange = new CellRangeAddress(0, 0, startCol, endCol);
                sheet.addMergedRegion(mergeRange);
                log.debug("合并随访结果表头: 列{}-{}, 共{}列", startCol, endCol, followResultCols.size());
            } else {
                log.debug("随访结果表头合并区域已存在，跳过合并");
            }
        } else {
            log.debug("随访结果只有{}列，无需合并", followResultCols.size());
        }
    }

    /**
     * 合并检查报告表头
     */
    private void mergeStudyInfoHeaders(Sheet sheet) {
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            log.warn("第一行为空，无法合并检查报告表头");
            return;
        }

        // 找到检查报告列的范围
        List<Integer> studyInfoCols = new ArrayList<>();

        for (int col = 0; col <= firstRow.getLastCellNum(); col++) {
            Cell cell = firstRow.getCell(col);
            if (cell != null && Constant.STUDY_INFO.equals(cell.getStringCellValue())) {
                studyInfoCols.add(col);
            }
        }

        // 只有超过1列时才需要合并
        if (studyInfoCols.size() > 1) {
            int startCol = studyInfoCols.get(0);
            int endCol = studyInfoCols.get(studyInfoCols.size() - 1);

            // 检查是否已经存在合并区域
            if (isMergedRegionExists(sheet, 0, 0, startCol, endCol)) {
                CellRangeAddress mergeRange = new CellRangeAddress(0, 0, startCol, endCol);
                sheet.addMergedRegion(mergeRange);
                log.debug("合并检查报告表头: 列{}-{}, 共{}列", startCol, endCol, studyInfoCols.size());
            } else {
                log.debug("检查报告表头合并区域已存在，跳过合并");
            }
        } else {
            log.debug("检查报告只有{}列，无需合并", studyInfoCols.size());
        }
    }

    /**
     * 检查指定区域是否已经存在合并区域
     */
    private boolean isMergedRegionExists(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        for (CellRangeAddress existingRange : mergedRegions) {
            if (existingRange.getFirstRow() == firstRow &&
                    existingRange.getLastRow() == lastRow &&
                    existingRange.getFirstColumn() == firstCol &&
                    existingRange.getLastColumn() == lastCol) {
                return false;
            }

            // 检查是否有重叠区域
            if (isOverlapping(existingRange, firstRow, lastRow, firstCol, lastCol)) {
                log.warn("发现重叠的合并区域: 现有区域({},{},{},{}), 新区域({},{},{},{})",
                        existingRange.getFirstRow(), existingRange.getLastRow(),
                        existingRange.getFirstColumn(), existingRange.getLastColumn(),
                        firstRow, lastRow, firstCol, lastCol);
                return false;
            }
        }

        return true;
    }

    /**
     * 检查两个区域是否重叠
     */
    private boolean isOverlapping(CellRangeAddress existingRange, int firstRow, int lastRow, int firstCol, int lastCol) {
        return !(existingRange.getLastRow() < firstRow ||
                existingRange.getFirstRow() > lastRow ||
                existingRange.getLastColumn() < firstCol ||
                existingRange.getFirstColumn() > lastCol);
    }

    /**
     * 合并数据行 - 只合并 CASE 类型字段和随访结果字段
     */
    private void mergeDataRows(Sheet sheet) {
        if (dataList == null || dataList.isEmpty()) {
            log.debug("数据列表为空，跳过数据行合并");
            return;
        }

        try {
            // 从数据行开始（跳过两行表头）
            int rowIndex = 2;

            for (CaseExportDataDto data : dataList) {
                if (data.isFirstRowOfMerge() && data.getMergeRowCount() > 1) {
                    int studyCount = data.getMergeRowCount();

                    // 计算需要合并的列（CASE 类型字段 + 随访结果字段）
                    List<Integer> mergeColumns = calculateMergeColumns();

                    // 对每个需要合并的列进行合并
                    for (int col : mergeColumns) {
                        // 检查是否已经存在合并区域
                        if (isMergedRegionExists(sheet, rowIndex, rowIndex + studyCount - 1, col, col)) {
                            CellRangeAddress mergeRange = new CellRangeAddress(
                                    rowIndex, rowIndex + studyCount - 1, col, col);
                            sheet.addMergedRegion(mergeRange);
                        }
                    }

                    rowIndex += studyCount;
                    log.debug("合并病例数据行: 行{}-{}, 合并列数: {}",
                            rowIndex - studyCount, rowIndex - 1, mergeColumns.size());
                } else if (!data.isFirstRowOfMerge()) {
                    // 跳过非首行的合并行
                    continue;
                } else {
                    rowIndex++;
                }
            }
        } catch (Exception e) {
            log.error("数据行合并失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算需要合并的列（CASE 类型字段 + 随访结果字段）
     */
    private List<Integer> calculateMergeColumns() {
        List<Integer> mergeColumns = new ArrayList<>();

        if (fieldInfoList == null || fieldInfoList.isEmpty()) {
            log.warn("字段信息列表为空，无法计算合并列");
            return mergeColumns;
        }

        int colIndex = 0;
        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            // CASE 类型字段需要合并
            if (Constant.MERGE_TYPE_CASE.equals(fieldInfo.getMergeType())) {
                mergeColumns.add(colIndex);
            }
            // 动态随访结果字段也需要合并（它们的 mergeType 设置为 CASE）
            else if (followTypes.contains(fieldInfo.getHeaderName())) {
                mergeColumns.add(colIndex);
            }
            // STUDY 类型字段不合并

            colIndex++;
        }

        log.debug("计算得出需要合并的列: {}", mergeColumns);
        return mergeColumns;
    }
}
