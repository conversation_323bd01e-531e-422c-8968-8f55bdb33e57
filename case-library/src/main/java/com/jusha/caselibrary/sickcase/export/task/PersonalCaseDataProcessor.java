package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PersonalCaseDataProcessor
 * @Description 个人病例数据处理器实现
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Slf4j
public class PersonalCaseDataProcessor implements CaseDataProcessor<PersonalCaseDetailResp> {
    
    @Override
    public List<PersonalCaseDetailResp> processData(List<PersonalCaseDetailResp> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            log.info("个人病例数据列表为空，无需处理");
            return new ArrayList<>();
        }
        
        List<PersonalCaseDetailResp> processedList = new ArrayList<>();
        
        for (PersonalCaseDetailResp data : rawDataList) {
            try {
                if (validateData(data)) {
                    PersonalCaseDetailResp processedData = cleanAndFormatData(data);
                    if (processedData != null) {
                        processedList.add(processedData);
                    }
                } else {
                    log.warn("个人病例数据验证失败，跳过处理，病例ID: {}", 
                            data != null ? data.getUserCaseId() : "null");
                }
            } catch (Exception e) {
                log.error("处理个人病例数据失败，病例ID: {}, 错误: {}", 
                        data != null ? data.getUserCaseId() : "null", e.getMessage(), e);
                // 继续处理其他数据，不中断整个流程
            }
        }
        
        log.info("个人病例数据处理完成，原始数量: {}, 处理后数量: {}", 
                rawDataList.size(), processedList.size());
        
        return processedList;
    }
    
    @Override
    public PersonalCaseDetailResp cleanAndFormatData(PersonalCaseDetailResp data) {
        if (data == null) {
            return null;
        }
        
        try {
            // 清理空值和格式化文本
            if (isEmpty(data.getPatientName())) {
                data.setPatientName("");
            }
            
            if (isEmpty(data.getPatientSex())) {
                data.setPatientSex("");
            }
            
            // 格式化长文本，移除多余的空白字符
            if (isNotEmpty(data.getMedicalHistory())) {
                data.setMedicalHistory(cleanText(data.getMedicalHistory()));
            }
            
            if (isNotEmpty(data.getCaseAnalysis())) {
                data.setCaseAnalysis(cleanText(data.getCaseAnalysis()));
            }
            
            if (isNotEmpty(data.getDiagnosis())) {
                data.setDiagnosis(cleanText(data.getDiagnosis()));
            }
            
            // 处理个人病例特有字段
            if (isNotEmpty(data.getCatalogName())) {
                data.setCatalogName(cleanText(data.getCatalogName()));
            }
            
            // 使用字典标签值替换原始字典值
            if (isNotBlank(data.getDifficultyLabel())) {
                data.setDifficulty(data.getDifficultyLabel());
            }
            
            if (isNotBlank(data.getCaseCategoryLabel())) {
                data.setCaseCategory(data.getCaseCategoryLabel());
            }
            
            if (isNotBlank(data.getSourceTypeLabel())) {
                data.setSourceType(data.getSourceTypeLabel());
            }
            
            if (isNotBlank(data.getFollowStatusLabel())) {
                data.setFollowStatus(data.getFollowStatusLabel());
            }
            
            // 处理StudyInfoDto中的字典标签值
            if (data.getStudyInfoList() != null && !data.getStudyInfoList().isEmpty()) {
                data.getStudyInfoList().forEach(study -> {
                    if (isNotBlank(study.getPatientTypeLabel())) {
                        study.setPatientType(study.getPatientTypeLabel());
                    }
                    if (isNotBlank(study.getStudyStateLabel())) {
                        study.setStudyState(study.getStudyStateLabel());
                    }
                    if (isNotBlank(study.getIsPostiveLabel())) {
                        study.setIsPostive(study.getIsPostiveLabel());
                    }
                    if (isNotBlank(study.getIsPublicLabel())) {
                        study.setIsPublic(study.getIsPublicLabel());
                    }
                    if (isNotBlank(study.getQualityMatchLabel())) {
                        study.setQualityMatch(study.getQualityMatchLabel());
                    }

                    if (isNotBlank(study.getPositionMatchLabel())) {
                        study.setPositionMatch(study.getPositionMatchLabel());
                    }
                    // 处理FollowInfoDto中的字典标签值
                    if (study.getFollowInfoList() != null && !study.getFollowInfoList().isEmpty()) {
                        study.getFollowInfoList().forEach(follow -> {
                            if (isNotBlank(follow.getFollowTypeLabel())) {
                                follow.setFollowType(follow.getFollowTypeLabel());
                            }
                        });
                    }
                });
            }
            
            return data;
            
        } catch (Exception e) {
            log.error("清洗和格式化个人病例数据失败，病例ID: {}, 错误: {}", 
                    data.getUserCaseId(), e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }
    
    @Override
    public boolean validateData(PersonalCaseDetailResp data) {
        if (data == null) {
            return false;
        }
        
        // 个人病例特定的验证逻辑
        if (data.getUserCaseId() == null) {
            log.warn("个人病例ID为空，数据无效");
            return false;
        }
        
        return true;
    }
    
    @Override
    public Class<PersonalCaseDetailResp> getSupportedDataType() {
        return PersonalCaseDetailResp.class;
    }
    
    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (isEmpty(text)) {
            return "";
        }
        // 移除多余的空白字符，保留换行符
        return text.trim().replaceAll("[ \\t]+", " ");
    }
    
    /**
     * 判断字符串是否为空或空白
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空且不为空白
     */
    private boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 判断字符串是否不为空白
     */
    private boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }
}