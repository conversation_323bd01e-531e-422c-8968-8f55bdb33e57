package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ExportStrategyFactory
 * @Description 导出策略工厂类 - 支持处理器创建和新的泛型架构
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Slf4j
public class ExportStrategyFactory {
    
    /**
     * 策略缓存
     */
    private static final Map<ExportType, CaseExportStrategy<?, ?>> STRATEGY_CACHE = new HashMap<>();
    
    /**
     * 数据处理器缓存
     */
    private static final Map<ExportType, CaseDataProcessor<?>> PROCESSOR_CACHE = new HashMap<>();
    
    static {
        // 初始化策略实例
        STRATEGY_CACHE.put(ExportType.DEPT_CASE, new DeptCaseExportStrategy());
        STRATEGY_CACHE.put(ExportType.PERSONAL_CASE, new PersonalCaseExportStrategy());
        
        // 初始化数据处理器实例
        PROCESSOR_CACHE.put(ExportType.DEPT_CASE, new DeptCaseDataProcessor());
        PROCESSOR_CACHE.put(ExportType.PERSONAL_CASE, new PersonalCaseDataProcessor());
    }
    
    /**
     * 根据请求类型获取对应的导出策略
     *
     * @param searchRequest 查询请求参数
     * @return 导出策略实例
     * @throws
     */
    @SuppressWarnings("unchecked")
    public static <T, R> CaseExportStrategy<T, R> getStrategy(T searchRequest){
        if (searchRequest == null) {
            throw new BusinessException(LocaleUtil.getLocale("common.request.not.null"));
        }
        
        try {
            // 根据请求类型确定导出类型
            ExportType exportType = ExportType.fromRequestType(searchRequest);
            
            // 从缓存中获取策略实例
            CaseExportStrategy<?, ?> strategy = STRATEGY_CACHE.get(exportType);

            if (strategy == null) {
                throw new BusinessException(LocaleUtil.getLocale("case.export.strategy.not.found"));
            }
            return (CaseExportStrategy<T, R>) strategy;

        } catch (IllegalArgumentException e) {
            log.error("不支持的请求类型: {}", searchRequest.getClass().getName());
            throw new BusinessException(LocaleUtil.getLocale("case.export.strategy.not.found"));
        }
    }

    /**
     * 根据导出类型获取对应的数据处理器
     *
     * @param exportType 导出类型
     * @return 数据处理器实例
     */
    @SuppressWarnings("unchecked")
    public static <R> CaseDataProcessor<R> getProcessor(ExportType exportType) {
        if (exportType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.export.type.not.null"));
        }
        
        CaseDataProcessor<?> processor = PROCESSOR_CACHE.get(exportType);
        
        if (processor == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.export.processor.not.found"));
        }
        
        log.debug("获取数据处理器成功，类型: {}, 处理器类: {}",
                exportType.getDescription(), processor.getClass().getSimpleName());
        
        return (CaseDataProcessor<R>) processor;
    }
    
    /**
     * 根据请求类型获取对应的数据处理器
     *
     * @param searchRequest 查询请求参数
     * @return 数据处理器实例
     */
    @SuppressWarnings("unchecked")
    public static <T, R> CaseDataProcessor<R> getDataProcessor(T searchRequest) {
        if (searchRequest == null) {
            throw new BusinessException(LocaleUtil.getLocale("common.request.not.null"));
        }
        
        try {
            // 根据请求类型确定导出类型
            ExportType exportType = ExportType.fromRequestType(searchRequest);
            
            // 使用现有的 getProcessor 方法
            return getProcessor(exportType);
            
        } catch (IllegalArgumentException e) {
            log.error("不支持的请求类型: {}", searchRequest.getClass().getName());
            throw new BusinessException(LocaleUtil.getLocale("case.export.processor.not.found"));
        }
    }
}