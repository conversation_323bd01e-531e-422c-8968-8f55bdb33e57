package com.jusha.caselibrary.sickcase.dto.resp;

import com.jusha.caselibrary.common.util.DictConvertUtil;
import com.jusha.caselibrary.common.util.UserUtil;
import com.jusha.caselibrary.sickcase.dto.AuditInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName AuditCaseDetailResp
 * @Description 审核病例详情返回实体类
 * <AUTHOR>
 * @Date 2025/7/10 10:36
 **/
@Data
public class AuditCaseDetailResp extends DeptCaseDetailResp {

    @ApiModelProperty(value = "最新审核信息")
    private AuditInfo auditInfo;


    /**
     * 设置字典标签值
     */
    @Override
    public void handleData() {
        super.handleData();
        if (this.auditInfo != null) {
            this.auditInfo.setAuditTypeLabel(DictConvertUtil.convertAuditType(this.auditInfo.getAuditType()));
            this.auditInfo.setStatusLabel(DictConvertUtil.convertStatus(this.auditInfo.getStatus()));
            this.auditInfo.setAuditedUserName(UserUtil.getUserNameById(this.auditInfo.getAuditedBy()));
            String pubUserIds = this.auditInfo.getPubUserId();
            if (pubUserIds != null && !pubUserIds.isEmpty()) {
                String[] userIds = this.auditInfo.getPubUserId().split(",");
                StringBuilder userNames = new StringBuilder();
                for (String userId : userIds) {
                    userNames.append(UserUtil.getUserNameById(Long.parseLong(userId))).append(",");
                }
                if (userNames.toString().endsWith(",")) {
                    userNames = new StringBuilder(userNames.substring(0, userNames.length() - 1));
                }
                this.auditInfo.setPubUserName(userNames.toString());
            }
        }
    }
}
