package com.jusha.caselibrary.sickcase.dto.resp;

import com.jusha.caselibrary.common.util.DictConvertUtil;
import com.jusha.caselibrary.sickcase.dto.AuditInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName AuditCaseDetailResp
 * @Description 审核病例详情返回实体类
 * <AUTHOR>
 * @Date 2025/7/10 10:36
 **/
@Data
public class AuditCaseDetailResp extends DeptCaseDetailResp {

    @ApiModelProperty(value = "审核ID")
    private AuditInfo auditInfo;


    @Override
    /**
     * 设置字典标签值
     */
    public void setDictLabels() {
        super.setDictLabels();
        this.auditInfo.setAuditTypeLabel(DictConvertUtil.convertAuditType(this.auditInfo.getAuditType()));
        this.auditInfo.setStatusLabel(DictConvertUtil.convertStatus(this.auditInfo.getStatus()));
    }
}
