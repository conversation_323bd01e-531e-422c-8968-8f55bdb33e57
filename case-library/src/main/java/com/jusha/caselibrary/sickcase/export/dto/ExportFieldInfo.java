package com.jusha.caselibrary.sickcase.export.dto;

import lombok.Data;

import java.lang.reflect.Field;

/**
 * @ClassName ExportFieldInfo
 * @Description 导出字段信息
 * <AUTHOR>
 * @Date 2025/7/11 10:17
 **/
@Data
public class ExportFieldInfo {

    private String headerName;
    private int index;
    private int width;
    private boolean mergeable;
    private String mergeType;
    private boolean exportable;
    private Field field;

    public ExportFieldInfo(String headerName, int index, int width, boolean mergeable, String mergeType, boolean exportable, Field field) {
        this.headerName = headerName;
        this.index = index;
        this.width = width;
        this.mergeable = mergeable;
        this.mergeType = mergeType;
        this.exportable = exportable;
        this.field = field;
    }

    /**
     * 判断是否为动态字段
     */
    public boolean isDynamicField() {
        return field == null;
    }

    /**
     * 复制字段信息
     */
    public ExportFieldInfo copy() {
        return new ExportFieldInfo(
                this.headerName,
                this.index,
                this.width,
                this.mergeable,
                this.mergeType,
                this.exportable,
                this.field
        );
    }
}
