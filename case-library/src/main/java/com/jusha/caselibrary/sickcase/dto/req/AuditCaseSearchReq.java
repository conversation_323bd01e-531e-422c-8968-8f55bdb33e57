package com.jusha.caselibrary.sickcase.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName AuditCaseSearchReq
 * @Description 审核病例库列表请求实体
 * <AUTHOR>
 * @Date 2025/7/10 10:09
 **/
@Data
public class AuditCaseSearchReq {

    @ApiModelProperty(value = "病例Id", hidden = true)
    private Long caseId;

    @ApiModelProperty(value = "用户Id", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "审核ID", hidden = true)
    private Long auditId;

    @ApiModelProperty(value = "病例库类型Id")
    private Long caseTypeId;

    @ApiModelProperty(value = "检查开始时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyStartTime;

    @ApiModelProperty(value = "检查结束时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyEndTime;

    @ApiModelProperty(value = "检查类型")
    private String modality;

    @ApiModelProperty(value = "患者性别：男、女")
    private String patientSex;

    @ApiModelProperty(value = "随访类型：手术，超声，临床，病理")
    private String followType;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    private String status;

/*    @ApiModelProperty(value = "关键字: 患者姓名、影像号、住院号、门诊号、" +
            "影像学表现、影像学诊断、随访结果、病例分析等")
    private String keyword;*/

    @ApiModelProperty(value = "审核类型：RIS导入，本地导入，随访转其他，个人转科室")
    private String auditType;

    @ApiModelProperty(value = "发布人")
    private Long pubUserId;

    @ApiModelProperty(value = "发布搜索开始时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime pubStartTime;

    @ApiModelProperty(value = "发布搜索结束时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime pubEndTime;

    @ApiModelProperty(value = "审核人")
    private Long auditedBy;

    @ApiModelProperty(value = "审核搜索开始时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime auditedStartTime;

    @ApiModelProperty(value = "审核搜索结束时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime auditedEndTime;

    @ApiModelProperty(value = "第几页")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
}
