package com.jusha.caselibrary.sickcase.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.config.FeignConfig;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.feign.api.CaselibRISApi;
import com.jusha.caselibrary.feign.api.DimseApi;
import com.jusha.caselibrary.feign.api.PacsApi;
import com.jusha.caselibrary.feign.dto.SeriesInstanceDto;
import com.jusha.caselibrary.feign.dto.StudyInstanceDto;
import com.jusha.caselibrary.feign.dto.req.MoveScuReq;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.mapper.StudyMapper;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseService;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.sickcase.dto.req.NotRelateDicomReq;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.AddRISCaseResp;
import com.jusha.caselibrary.sickcase.dto.resp.AddUploadCaseResp;
import com.jusha.caselibrary.sickcase.dto.resp.QueryCaseExistResp;
import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import com.jusha.caselibrary.sickcase.service.CaseAddService;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import com.jusha.caselibrary.sickcase.task.MoveDicomTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @ClassName CaseAddServiceImpl
 * @Description 新增病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/14 11:55
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseAddServiceImpl implements CaseAddService {

    private final CaselibRISApi caselibRISApi;
    private final PersonalCaseService personalCaseService;
    private final DeptCaseService deptCaseService;
    private final UserCaseService userCaseService;
    private final UserCatalogService userCatalogService;
    private final DepCaseClassifyService depCaseClassifyService;
    private final DepCaseTypeService depCaseTypeService;
    private final DepCaseService depCaseService;
    private final DiseaseService diseaseService;
    private final UserCaseCatalogService userCaseCatalogService;
    private final StudyService studyService;
    private final StudyRecordService studyRecordService;
    private final CaseStudyService caseStudyService;
    private final SeriesService seriesService;
    private final PacsApi pacsApi;
    private final DimseApi dimseApi;
    private final FollowService followService;
    private final TagService tagService;
    private final DepCaseTagService depCaseTagService;
    private final UserCaseTagService userCaseTagService;

    /**
     * 查询RIS中的列表
     * @param req
     * @return
     */
    @Override
    public List<RISStudy> getRisStudyList(RISStudyReq req) {
        ResultBean<List<RISStudy>> listResultBean = caselibRISApi.queryStudyList(req);
        if(!listResultBean.getState()){
            return new ArrayList<>();
        }
        List<RISStudy> risStudyList = listResultBean.getData();
        for(RISStudy risStudy : risStudyList){
            risStudy.setAccessNumber(risStudy.getInPatientNo()!=null?risStudy.getInPatientNo():risStudy.getOutPatientNo());
        }
        return risStudyList;
    }

    /**
     * 从RIS导入病历
     * @param addRisCaseReq
     * @return
     */
    @Override
    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    public AddRISCaseResp addCaseFromRis(AddRISCaseReq addRisCaseReq) {
        vertifyRisCase(addRisCaseReq);
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        //先定义一个caseId
        long caseId = 0L;
        long userCaseId = 0L;
        if(addRisCaseReq.getTargetLibrary() == 1){
            //要验证一下这个目录是不是存在
            if (userCatalogService.getById(addRisCaseReq.getTargetId()) == null) {
                throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
            }
            RISStudy risStudy = risStudyList.get(0);
            PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
            personalCaseCreateReq.setCatalogId(addRisCaseReq.getTargetId()).setCaseName("").setDiseaseId(addRisCaseReq.getDiseaseId())
                            .setCaseNo("").setDiagnosis(risStudy.getReportDiagnose()).setPatientId(risStudy.getPatientId())
                            .setPatientName(risStudy.getPatientName()).setPatientSex(risStudy.getPatientSex())
                            .setPatientBirthDate(risStudy.getPatientBirthDate()).setPatientAge(risStudy.getPatientAge())
                            .setMedicalHistory(risStudy.getMedicalHistory()).setSign(risStudy.getReportDescribe())
                            .setCaseAnalysis("").setFollowStatus("0").setSourceType("1");
            userCaseId = personalCaseService.createPersonalCaseInCatalog(personalCaseCreateReq);
            caseId = userCaseService.getById(userCaseId).getCaseId();
        }
        if(addRisCaseReq.getTargetLibrary() == 2){
            //验证如果非随访（病例总库），那么要验证该库是否存在
            if(addRisCaseReq.getTargetId()!=null && addRisCaseReq.getTargetId()!=-1 && depCaseTypeService.getById(addRisCaseReq.getTargetId())==null){
                throw new BusinessException(LocaleUtil.getLocale("dept.case.library.not.exist"));
            }
            //如果属于教学库，疾病类型不可以是空的
            if(addRisCaseReq.getTargetId()!=null && addRisCaseReq.getTargetId() == 1){
                if(addRisCaseReq.getDiseaseId()==null){
                    throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
                }else {
                    if(diseaseService.getById(addRisCaseReq.getDiseaseId()) ==null){
                        throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
                    }
                }
            }
            RISStudy risStudy = risStudyList.get(0);
            DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
            deptCaseCreateReq.setCaseTypeId(addRisCaseReq.getTargetId()).setCaseName("").setCaseNo("").setDiseaseId(addRisCaseReq.getDiseaseId())
                    .setDiagnosis(risStudy.getReportDiagnose()).setPatientId(risStudy.getPatientId()).setPatientName(risStudy.getPatientName())
                    .setPatientSex(risStudy.getPatientSex()).setPatientBirthDate(risStudy.getPatientBirthDate()).setPatientAge(risStudy.getPatientAge())
                    .setMedicalHistory(risStudy.getMedicalHistory()).setSign(risStudy.getReportDescribe()).setCaseAnalysis("")
//                    .setLmGpId(LoginUtil.lmGroupId())
                    .setFollowStatus("0").setSourceType("1");
            caseId = deptCaseService.createDeptCase(deptCaseCreateReq);
        }

        //对接了RIS就有Study信息，对接了影像的才会有序列信息
        int isExport = 0;
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            isExport = 1;
        }
        addCaseStudyFromRIS(risStudyList,caseId,isExport);

        //如果需要导入影像，分两种情况，如果
        // 1、教学用，那么凡是新增的病例都需要顺带导入影像
        // 2、晨会用，可能压根都不需要导入影像，打开别人的pacs就可以了
        // 3、病例库自己用，-1类型的（随访库或叫病例总库）不导入影像，其他类型的都需要导入影像
        AddRISCaseResp addRISCaseResp = new AddRISCaseResp();
        addRISCaseResp.setCaseId(caseId);

        //如果是科室病例库、并且属于教学库的情况下，不导入影像
        if(addRisCaseReq.getTargetLibrary() == 2 && addRisCaseReq.getTargetId()==-1){
            return addRISCaseResp;
        }
        //异步导入影像
        for(RISStudy risStudy : risStudyList){
            moveDicom(risStudy.getStudyUid());
        }

        return addRISCaseResp;
    }

    /**
     * 从随访库导入病历（只需要加关联关系）
     * @param addFollowCaseReq
     * @return
     */
    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.PERSON_CASE_INDEX_NAME)
    public long addCaseFromFollow(AddFollowCaseReq addFollowCaseReq) {
        //如果属于教学库，疾病类型不可以是空的
        if(addFollowCaseReq.getCaseTypeId() == 1){
            if(addFollowCaseReq.getDiseaseId()==null){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }else {
                if(diseaseService.getById(addFollowCaseReq.getDiseaseId()) ==null){
                    throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
                }
            }
        }
        if(depCaseTypeService.getById(addFollowCaseReq.getCaseTypeId())==null){
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        if(addFollowCaseReq.getCaseId()==null || depCaseService.getById(addFollowCaseReq.getCaseId())==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        if(depCaseClassifyService.lambdaQuery().eq(DepCaseClassify::getCaseId,addFollowCaseReq.getCaseId())
                .eq(DepCaseClassify::getCaseTypeId,addFollowCaseReq.getCaseTypeId()).count()==0){
            DepCaseClassify depCaseClassify = new DepCaseClassify();
            depCaseClassify.setId(YitIdHelper.nextId());
            depCaseClassify.setCaseId(addFollowCaseReq.getCaseId());
            depCaseClassify.setCaseTypeId(addFollowCaseReq.getCaseTypeId());
            depCaseClassifyService.save(depCaseClassify);
        }

        //如果没有导入影像的话，是不会有序列信息的
        List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getCaseId,addFollowCaseReq.getCaseId()).list();
        if(!caseStudyList.isEmpty()){
            for(CaseStudy caseStudy : caseStudyList){
                if(seriesService.lambdaQuery().eq(Series::getStudyUid,caseStudy.getStudyUid()).count()==0){
                    //只要其中一个检查下面没有序列，就重新导入该影像
                    moveDicom(caseStudy.getStudyUid());
                }
            }
        }
        return addFollowCaseReq.getCaseId();
    }

    /**
     * 本地上传添加病例
     * @param addUploadCaseReq
     * @return
     */
    @Override
    public AddUploadCaseResp addCaseFromUpload(AddUploadCaseReq addUploadCaseReq) {
        vertifyUploadCase(addUploadCaseReq);
        AddUploadCaseResp addUploadCaseResp = new AddUploadCaseResp();
        //先定义一个caseId
        long caseId;
        long userCaseId;
        if(addUploadCaseReq.getTargetLibrary() == 1){
            userCaseId = addUserCaseUpload(addUploadCaseReq);
            addUploadCaseResp.setCaseId(userCaseId);
        }
        if(addUploadCaseReq.getTargetLibrary() == 2){
            caseId = addDepCaseUpload(addUploadCaseReq);
            addUploadCaseResp.setCaseId(caseId);
        }
        return addUploadCaseResp;
    }

    /**
     * 本地上传的时候，新建一个个人病例
     * @param addUploadCaseReq
     * @return
     */
    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.PERSON_CASE_INDEX_NAME)
    private long addUserCaseUpload(AddUploadCaseReq addUploadCaseReq){
        int targetLibrary = addUploadCaseReq.getTargetLibrary();
        long userCaseId;
        //要验证一下这个目录是不是存在
        if (userCatalogService.getById(addUploadCaseReq.getTargetId()) == null) {
            throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
        }
        AddUploadCaseReq.Supplement supplement = addUploadCaseReq.getSupplement();
        List<AddUploadCaseReq.Follow> followList = supplement.getFollowInfoList();
        String[] tags = supplement.getTags();
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        //一次只让新建一个病例
        Study caseStudy = studyService.getById(studyInfoList.get(0).getStudyUid());
        PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
        personalCaseCreateReq.setCatalogId(addUploadCaseReq.getTargetId()).setDiseaseId(addUploadCaseReq.getDiseaseId()).setCaseName(supplement.getCaseName())
                .setCaseNo(supplement.getCaseNo()).setDiagnosis(supplement.getDiagnosis()).setPatientId(caseStudy.getPatientId())
                .setPatientName(caseStudy.getPatientName()).setPatientSex(caseStudy.getPatientSex())
                .setPatientBirthDate(caseStudy.getPatientBirthDate()).setPatientAge(caseStudy.getPatientAge())
                .setMedicalHistory(supplement.getMedicalHistory()).setSign(supplement.getSign())
                .setSelfComplaints(supplement.getSelfComplaints())
                .setCaseAnalysis(supplement.getCaseAnalysis()).setFollowStatus("0").setSourceType("2");
        if(followList!=null && !followList.isEmpty()){
            personalCaseCreateReq.setFollowStatus("1");
        }
        userCaseId = personalCaseService.createPersonalCaseInCatalog(personalCaseCreateReq);
        long caseId = userCaseService.getById(userCaseId).getCaseId();

        if(followList!=null && !followList.isEmpty()){
            dealWithFollow(followList,caseId);
        }
        if(tags!=null && tags.length>0){
            dealWithTags(tags,targetLibrary,caseId);
        }

        for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
            dealWithDicomInfo(studyInfo.getStudyUid(),caseId);
        }
        return userCaseId;
    }

    /**
     * 本地上传的时候，新建一个科室病例
     * @param addUploadCaseReq
     * @return
     */
    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    private long addDepCaseUpload(AddUploadCaseReq addUploadCaseReq){
        int targetLibrary = addUploadCaseReq.getTargetLibrary();
        //验证如果非随访（病例总库），那么要验证该库是否存在
        if(addUploadCaseReq.getTargetId()!=null && addUploadCaseReq.getTargetId()!=-1 && depCaseTypeService.getById(addUploadCaseReq.getTargetId())==null){
            throw new BusinessException(LocaleUtil.getLocale("dept.case.library.not.exist"));
        }
        //如果属于教学库，疾病类型不可以是空的
        if(addUploadCaseReq.getTargetId()!=null && addUploadCaseReq.getTargetId() == 1){
            if(addUploadCaseReq.getDiseaseId()==null){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }else {
                if(diseaseService.getById(addUploadCaseReq.getDiseaseId()) ==null){
                    throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
                }
            }
        }
        AddUploadCaseReq.Supplement supplement = addUploadCaseReq.getSupplement();
        List<AddUploadCaseReq.Follow> followList = supplement.getFollowInfoList();
        String[] tags = supplement.getTags();
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        //一次只让新建一个病例
        Study caseStudy = studyService.getById(studyInfoList.get(0).getStudyUid());
        DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
        deptCaseCreateReq.setCaseTypeId(addUploadCaseReq.getTargetId()).setDiseaseId(addUploadCaseReq.getDiseaseId()).setCaseName(supplement.getCaseName())
                .setCaseNo(supplement.getCaseNo()).setDiagnosis(supplement.getDiagnosis()).setPatientId(caseStudy.getPatientId())
                .setPatientName(caseStudy.getPatientName()).setPatientSex(caseStudy.getPatientSex())
                .setPatientBirthDate(caseStudy.getPatientBirthDate()).setPatientAge(caseStudy.getPatientAge())
                .setMedicalHistory(supplement.getMedicalHistory()).setSign(supplement.getSign())
                .setSelfComplaints(supplement.getSelfComplaints())
                .setCaseAnalysis(supplement.getCaseAnalysis())
//                    .setLmGpId(LoginUtil.lmGroupId())
                .setFollowStatus("0").setSourceType("2");
        if(followList!=null && !followList.isEmpty()){
            deptCaseCreateReq.setFollowStatus("1");
        }
        long caseId = deptCaseService.createDeptCase(deptCaseCreateReq);
        if(followList!=null && !followList.isEmpty()){
            dealWithFollow(followList,caseId);
        }
        if(tags!=null && tags.length>0){
            dealWithTags(tags,targetLibrary,caseId);
        }
        for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
            dealWithDicomInfo(studyInfo.getStudyUid(),caseId);
        }
        return caseId;
    }

    private void moveDicom(String studyUid){
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            //①调用pacs接口去CMove这些影像
            MoveScuReq moveScuReq = new MoveScuReq(Constant.STUDY_RETRIEVE_LEVEL,studyUid);
            ResultBean<String> resultBean = dimseApi.scuMove(moveScuReq);
            if(resultBean.getState()){
                String taskId = resultBean.getData();
                ContextHolder.getBean("cachedPool", ExecutorService.class).submit(
                        new MoveDicomTask(taskId,studyUid,dimseApi,studyService));
            }
        }
    }

    @Override
    public void dicomNotify(String studyInstanceUID, Long userId) {
        ResultBean<List<StudyInstanceDto>> resultBean = pacsApi.getStudyList(studyInstanceUID);
        if(!resultBean.getState()){
            log.error("======查询pacs检查信息失败========");
            return;
        }
        List<StudyInstanceDto> studyInstanceDtoList = resultBean.getData();
        if(studyInstanceDtoList.isEmpty()){
            log.error("======查询pacs检查信息失败，检查列表为空========");
            return;
        }
        StudyInstanceDto studyInstanceDto = studyInstanceDtoList.get(0);
        List<SeriesInstanceDto> seriesInstanceDtoList = studyInstanceDto.getSeriesList();
        if(seriesInstanceDtoList.isEmpty()){
            log.error("======查询pacs序列信息失败，序列列表为空========");
            return;
        }

        String studyDate = studyInstanceDto.getStudyDate();
        String studyTime = studyInstanceDto.getStudyTime();
        Date studyDateTime = DateUtil.parseToDate(studyDate, studyTime);
        List<StudyRecord> studyRecordList = studyRecordService.lambdaQuery()
                .eq(StudyRecord::getStudyUid,studyInstanceUID)
                .eq(StudyRecord::getAssociation,0)
                .eq(StudyRecord::getCreateBy,userId)
                .list();
        if(studyRecordList.isEmpty()){
            //在记录表中记录一下当前上传的记录
            StudyRecord studyRecord = new StudyRecord();
            studyRecord.setId(YitIdHelper.nextId());
            studyRecord.setStudyUid(studyInstanceDto.getStudyInstanceUID());
            studyRecord.setAccessNumber(studyInstanceDto.getAccessNumber());
            studyRecord.setStudyTime(studyDateTime);
            studyRecord.setStudyNo(studyInstanceDto.getStudyId());
            studyRecord.setPatientId(studyInstanceDto.getPatientId());
            studyRecord.setPatientName(studyInstanceDto.getPatientName());
            studyRecord.setPatientSex(studyInstanceDto.getPatientSex());
            studyRecord.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
            studyRecord.setPatientAge(studyInstanceDto.getPatientAge());
            studyRecord.setIsonline(0);
            studyRecord.setCreateBy(userId);
            studyRecordService.saveOrUpdate(studyRecord);
        }

        Study study = new Study();
        study.setStudyUid(studyInstanceDto.getStudyInstanceUID());
        study.setAccessNumber(studyInstanceDto.getAccessNumber());
        study.setStudyTime(studyDateTime);
        study.setStudyNo(studyInstanceDto.getStudyId());
        study.setPatientId(studyInstanceDto.getPatientId());
        study.setPatientName(studyInstanceDto.getPatientName());
        study.setPatientSex(studyInstanceDto.getPatientSex());
        study.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
        study.setPatientAge(studyInstanceDto.getPatientAge());
        study.setIsonline(0);
        study.setCreateBy(userId);
        studyService.saveOrUpdate(study);

        List<Series> caseSeriesList = new ArrayList<>();
        for(SeriesInstanceDto seriesInstanceDto : seriesInstanceDtoList){
            Series caseSeries = new Series();
            caseSeries.setSeriesUid(seriesInstanceDto.getSeriesInstanceUID());
            caseSeries.setPatientId(studyInstanceDto.getPatientId());
            caseSeries.setPatientName(studyInstanceDto.getPatientName());
            caseSeries.setPatientSex(studyInstanceDto.getPatientSex());
            caseSeries.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
            caseSeries.setPatientAge(studyInstanceDto.getPatientAge());
            caseSeries.setStudyUid(studyInstanceDto.getStudyInstanceUID());
            caseSeries.setAccessNumber(studyInstanceDto.getAccessNumber());
            caseSeries.setSeriesDescription(seriesInstanceDto.getSeriesDescription());
            caseSeries.setModality(seriesInstanceDto.getModality());
            caseSeries.setIsKeyframe(0);
            caseSeries.setCreateBy(userId);
            caseSeriesList.add(caseSeries);
        }
        seriesService.saveOrUpdateBatch(caseSeriesList);
    }

    @Override
    public PageInfo<StudyRecord> dicomNotRelate(NotRelateDicomReq notRelateDicomReq) {

        LambdaQueryChainWrapper<StudyRecord> wrapper = studyRecordService.lambdaQuery();
        wrapper.eq(StudyRecord::getCreateBy,LoginUtil.getLoginUserId())
                .eq(StudyRecord::getAssociation,0);
        if(notRelateDicomReq.getKeyword()!=null){
            wrapper.nested(i -> i.like(StudyRecord::getPatientName, notRelateDicomReq.getKeyword())
                    .or().like(StudyRecord::getPatientId, notRelateDicomReq.getKeyword()));
        }

        PageHelper.startPage(notRelateDicomReq.getPageNum(), notRelateDicomReq.getPageSize());
        List<StudyRecord> studyList = wrapper.list();

        if(!studyList.isEmpty()){
            for(StudyRecord study : studyList){
                HashSet<String> modalityList = new HashSet<>();
                List<Series> caseSeriesList = seriesService.lambdaQuery()
                        .eq(Series::getStudyUid,study.getStudyUid()).list();
                for(Series caseSeries : caseSeriesList){
                    if(!caseSeries.getModality().isEmpty()){
                        modalityList.add(caseSeries.getModality());
                    }
                }
                if(!modalityList.isEmpty()){
                    String modalityLists = modalityList.stream().map(Object::toString).collect(Collectors.joining(","));
                    study.setModalities(modalityLists);
                }else {
                    study.setModalities("");
                }
                if(study.getAccessNumber()==null){
                    study.setAccessNumber("");
                }
            }
        }
        return new PageInfo<>(studyList);
    }

    @Override
    public List<QueryCaseExistResp> queryCaseExist(QueryCaseExistReq queryCaseExistReq) {
        List<QueryCaseExistReq.StudyInfo> studyInfoList = queryCaseExistReq.getStudyInfoList();
        List<QueryCaseExistResp> queryCaseExistRespList = new ArrayList<>();
        for(QueryCaseExistReq.StudyInfo studyInfo : studyInfoList){
            String studyUid = studyInfo.getStudyUid();
            List<Study> studyList = studyService.lambdaQuery().eq(Study::getStudyUid,studyUid).list();
            if(!studyList.isEmpty()){
                for(Study study : studyList){
                    //如果这个检查没有绑定任何病例，就不管了
                    Set<Long> caseIds;
                    List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,study.getStudyUid()).list();
                    if(caseStudyList.isEmpty()){
                        continue;
                    }else {
                        caseIds = caseStudyList.stream().map(CaseStudy::getCaseId).collect(Collectors.toSet());
                    }
                    QueryCaseExistResp queryCaseExistResp = new QueryCaseExistResp();
                    queryCaseExistResp.setStudyUid(study.getStudyUid()).setAccessNumber(study.getAccessNumber()).setStudyTime(study.getStudyTime())
                            .setStudyNo(study.getStudyNo()).setPatientId(study.getPatientId())
                            .setPatientName(study.getPatientName()).setPatientSex(study.getPatientSex())
                            .setPatientBirthDate(study.getPatientBirthDate()).setPatientAge(study.getPatientAge());
                    List<QueryCaseExistResp.Parent> parents = new ArrayList<>();

                    if(queryCaseExistReq.getTargetLibrary()==1){
                        //个人库,该病例可能关联多个收藏夹   ①拿到所有当前用户下的该病例id的数据
                        List<UserCase> userCaseList = userCaseService.lambdaQuery().eq(UserCase::getCreateBy,LoginUtil.getLoginUserId()).in(UserCase::getCaseId,caseIds).list();
                        if(userCaseList.isEmpty()){
                            return new ArrayList<>();
                        }else{
                            //拿到所有主键，再拿到所有收藏夹id，找出对应的收藏夹name
                            Set<Long> userCaseIds = userCaseList.stream().map(UserCase::getUserCaseId).collect(Collectors.toSet());
                            List<UserCaseCatalog> userCaseCatalogList = userCaseCatalogService.lambdaQuery().in(UserCaseCatalog::getUserCaseId,userCaseIds).list();
                            if(!userCaseCatalogList.isEmpty()){
                                for(UserCaseCatalog userCaseCatalog : userCaseCatalogList){
                                    UserCatalog userCatalog = userCatalogService.getById(userCaseCatalog.getCatalogId());
                                    QueryCaseExistResp.Parent parent = new QueryCaseExistResp.Parent();
                                    parent.setParentName(userCatalog.getCatalogName());
                                    parent.setParentId(userCatalog.getCatalogId());
                                    parents.add(parent);
                                }
                            }
                            queryCaseExistResp.setParents(parents);
                            queryCaseExistResp.setCaseId(userCaseList.get(0).getCaseId());
                            queryCaseExistResp.setCaseName(userCaseList.get(0).getCaseName());
                            queryCaseExistResp.setCaseNo(userCaseList.get(0).getCaseNo());
                        }
                    }
                    if(queryCaseExistReq.getTargetLibrary()==2){
                        List<DepCase> depCaseList = depCaseService.lambdaQuery().in(DepCase::getCaseId,caseIds).list();
                        if(depCaseList.isEmpty()){
                            return new ArrayList<>();
                        }else {
                            List<DepCaseClassify> depCaseClassifyList = depCaseClassifyService.lambdaQuery().in(DepCaseClassify::getCaseId,caseIds).list();
                            if(!depCaseClassifyList.isEmpty()){
                                for(DepCaseClassify depCaseClassify : depCaseClassifyList){
                                    DepCaseType depCaseType = depCaseTypeService.getById(depCaseClassify.getCaseTypeId());
                                    QueryCaseExistResp.Parent parent = new QueryCaseExistResp.Parent();
                                    parent.setParentName(depCaseType.getCaseTypeName());
                                    parent.setParentId(depCaseType.getCaseTypeId());
                                    parents.add(parent);
                                }
                            }
                            queryCaseExistResp.setParents(parents);
                            queryCaseExistResp.setCaseId(depCaseList.get(0).getCaseId());
                            queryCaseExistResp.setCaseName(depCaseList.get(0).getCaseName());
                            queryCaseExistResp.setCaseNo(depCaseList.get(0).getCaseNo());
                        }
                    }
                    queryCaseExistRespList.add(queryCaseExistResp);
                }
            }
        }
        return queryCaseExistRespList;
    }

    @Override
    public long addStudyInfo(AddStudyInfoReq addStudyInfoReq) {
        RISStudy risStudy = addStudyInfoReq.getRisStudy();
        if(addStudyInfoReq.getCaseId()==null || depCaseService.getById(addStudyInfoReq.getCaseId())==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        int isExport = 0;
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            isExport = 1;
        }
        List<RISStudy> risStudyList = new ArrayList<>();
        risStudyList.add(risStudy);
        addCaseStudyFromRIS(risStudyList,addStudyInfoReq.getCaseId(),isExport);
        //异步导入影像
        moveDicom(risStudy.getStudyUid());

        return addStudyInfoReq.getCaseId();
    }

    /**
     * 处理随访
     * @param followList
     * @param caseId
     */
    private void dealWithFollow(List<AddUploadCaseReq.Follow> followList,long caseId){
        //处理随访
        if(followList!=null && !followList.isEmpty()){
            List<Follow> follows = new ArrayList<>();
            for(AddUploadCaseReq.Follow supplementFollow : followList){
                Follow follow = new Follow();
                follow.setFollowId(YitIdHelper.nextId());
                follow.setCaseId(caseId);
                follow.setFollowupResult(supplementFollow.getFollowupResult());
                follow.setFollowType(supplementFollow.getFollowType());
                follow.setCreatedBy(LoginUtil.getLoginUserId());
                follow.setCreateTime(new Date());
                follows.add(follow);
            }
            followService.saveBatch(follows);
        }
    }

    /**
     * 处理标签
     * @param tags
     * @param targetLibrary
     * @param caseId
     */
    private void dealWithTags(String[] tags,int targetLibrary,long caseId){
        for(String tag : tags){
            //第一步，新建标签
            long tagId;
            List<Tag> existTags = tagService.lambdaQuery().eq(Tag::getTagName,tag).list();
            if(!existTags.isEmpty()){
                tagId = existTags.get(0).getTagId();
            }else {
                tagId = YitIdHelper.nextId();
                Tag newTag = new Tag();
                newTag.setTagId(tagId);
                newTag.setTagName(tag);
                tagService.save(newTag);
            }
            //第二步，新建关联关系
            if(targetLibrary == 1){
                //个人病例库
                UserCaseTag userCaseTag = new UserCaseTag();
                userCaseTag.setId(YitIdHelper.nextId());
                userCaseTag.setUserCaseId(caseId);
                userCaseTag.setTagId(tagId);
                userCaseTagService.save(userCaseTag);
            }else {
                DepCaseTag depCaseTag = new DepCaseTag();
                depCaseTag.setId(YitIdHelper.nextId());
                depCaseTag.setCaseId(caseId);
                depCaseTag.setTagId(tagId);
                depCaseTagService.save(depCaseTag);
            }
        }
    }

    /**
     * 处理dicom信息
     * @param StudyUid
     * @param caseId
     */
    private void dealWithDicomInfo(String StudyUid ,long caseId){
        if(caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,StudyUid)
                        .eq(CaseStudy::getCaseId,caseId).count()>0){
            return;
        }
        CaseStudy caseStudy = new CaseStudy();
        caseStudy.setCaseId(caseId);
        caseStudy.setStudyUid(StudyUid);
        caseStudy.setId(YitIdHelper.nextId());
        caseStudyService.save(caseStudy);

        //把记录表中相关的记录改掉
        studyRecordService.lambdaUpdate()
                .eq(StudyRecord::getStudyUid,StudyUid)
                .eq(StudyRecord::getCreateBy,LoginUtil.getLoginUserId())
                .eq(StudyRecord::getAssociation,0)
                .set(StudyRecord::getAssociation,1).update();
    }

    private void addCaseStudyFromRIS(List<RISStudy> risStudyList,long caseId,int isExport){
        List<Study> caseStudyList = new ArrayList<>();
        for(RISStudy risStudy : risStudyList){
            Study study = new Study();
            study.setStudyUid(risStudy.getStudyUid());
            study.setAccessNumber(risStudy.getAccessNumber());
            study.setStudyTime(DateUtil.convertStrToDate(risStudy.getStudyTime(),DateUtil.DEFAULT_PATTERN));
            study.setStudyNo(risStudy.getStudyNo());
            study.setPatientId(risStudy.getPatientId());
            study.setPatientName(risStudy.getPatientName());
            study.setPatientSex(risStudy.getPatientSex());
            study.setPatientBirthDate(risStudy.getPatientBirthDate());
            study.setPatientAge(risStudy.getPatientAge());
            study.setPatientType(risStudy.getPatientType());
            study.setVisitDate(risStudy.getVisitDate());
            if(study.getPatientType().contains("住院")){
                study.setInPatientNo(risStudy.getOutPatientNo());
            }else {
                study.setOutPatientNo(risStudy.getInPatientNo());
            }
            study.setPhysicalSign(risStudy.getPhysicalSign());
            study.setClinicalDiagnosis(risStudy.getClinicalDiagnosis());
            study.setStudyItemName(risStudy.getStudyItemName());
            study.setPartName(risStudy.getPartName());
            study.setDeviceName(risStudy.getDeviceType());
            study.setDeviceType(risStudy.getDeviceType());
            study.setMedicalHistory(risStudy.getMedicalHistory());
            study.setSelfReportedSymptom(risStudy.getSelfReportedSymptom());
            study.setReportDescribe(risStudy.getReportDescribe());
            study.setReportDiagnose(risStudy.getReportDiagnose());
            study.setRegisterTime(risStudy.getRegisterTime());
            study.setReportTime(risStudy.getReportTime());
            study.setReporter(risStudy.getReporter());
            study.setChecker(risStudy.getChecker());
            study.setCheckTime(risStudy.getCheckTime());
            study.setApplyNumber(risStudy.getApplyNumber());
            study.setApplyDepartment(risStudy.getApplyDepartment());
            study.setApplyDoctor(risStudy.getApplyDoctor());
            study.setArtificer(risStudy.getArtificer());
            study.setIsonline(1);
            study.setIsExport(isExport);
            study.setAetId(1);
//            study.setStudyState(risStudy.getStudyStatus());
//            study.setIsPostive(risStudy.getPositiveStatus());
            caseStudyList.add(study);
        }
        for(RISStudy risStudy : risStudyList){
            dealWithDicomInfo(risStudy.getStudyUid(),caseId);
        }
        studyService.saveOrUpdateBatch(caseStudyList);
    }

    @Override
    public boolean copyCaseExist(CopyRISCaseReq copyRisCaseReq) {
        if(copyRisCaseReq.getTargetLibrary() ==1){
            UserCaseCatalog userCaseCatalog = new UserCaseCatalog();
            userCaseCatalog.setId(YitIdHelper.nextId());
            userCaseCatalog.setUserCaseId(copyRisCaseReq.getCaseId());
            userCaseCatalog.setCatalogId(copyRisCaseReq.getTargetId());
            return userCaseCatalogService.save(userCaseCatalog);
        }
        if(copyRisCaseReq.getTargetLibrary() == 2){
            if(copyRisCaseReq.getTargetId()==1 && copyRisCaseReq.getDiseaseId() == null){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }else {
                if(diseaseService.getById(copyRisCaseReq.getDiseaseId()) ==null){
                    throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
                }
            }
            DepCaseClassify depCaseClassify = new DepCaseClassify();
            depCaseClassify.setId(YitIdHelper.nextId());
            depCaseClassify.setCaseId(copyRisCaseReq.getCaseId());
            depCaseClassify.setCaseTypeId(copyRisCaseReq.getTargetId());
            return depCaseClassifyService.save(depCaseClassify);
        }
        return false;
    }

    private void vertifyRisCase(AddRISCaseReq addRisCaseReq){
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        if(risStudyList.isEmpty()){
            throw new BusinessException(LocaleUtil.getLocale("operate.fail"));
        }
        //如果是非随访库（科室库有具体的科室病例类型id），那单次导入的病例必须属于单个病人
        if(addRisCaseReq.getTargetLibrary() == 1 && addRisCaseReq.getTargetId()!=-1 && risStudyList.size()>1){
            HashSet<String> patientSet = new HashSet<>();
            for(RISStudy risStudy : risStudyList){
                patientSet.add(risStudy.getPatientId());
            }
            if(patientSet.size()>1){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }
    }

    private void vertifyUploadCase(AddUploadCaseReq addUploadCaseReq){
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        if(studyInfoList.isEmpty()){
            throw new BusinessException(LocaleUtil.getLocale("operate.fail"));
        }
        //如果是非随访库（科室库有具体的科室病例类型id），那单次导入的病例必须属于单个病人
        if(addUploadCaseReq.getTargetLibrary() == 1 && studyInfoList.size()>1){
            HashSet<String> patientSet = new HashSet<>();
            for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
                patientSet.add(studyInfo.getPatientId());
            }
            if(patientSet.size()>1){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }
    }

}
