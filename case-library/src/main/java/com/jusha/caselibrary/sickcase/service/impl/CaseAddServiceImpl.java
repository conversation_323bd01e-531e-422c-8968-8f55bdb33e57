package com.jusha.caselibrary.sickcase.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.mybatisplus.system.entity.AetTemplate;
import com.jusha.caselibrary.mybatisplus.system.service.AetTemplateService;
import com.jusha.caselibrary.search.service.ESSyncService;
import com.jusha.caselibrary.common.config.FeignConfig;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.feign.api.DimseApi;
import com.jusha.caselibrary.feign.api.PacsApi;
import com.jusha.caselibrary.feign.dto.SeriesInstanceDto;
import com.jusha.caselibrary.feign.dto.StudyInstanceDto;
import com.jusha.caselibrary.feign.dto.req.MoveScuReq;
import com.jusha.caselibrary.mybatisplus.entity.*;
import com.jusha.caselibrary.mybatisplus.service.*;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.sickcase.dto.AuditInfo;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.*;
import com.jusha.caselibrary.sickcase.service.CaseAddService;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import com.jusha.caselibrary.sickcase.task.MoveDicomTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @ClassName CaseAddServiceImpl
 * @Description 新增病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/14 11:55
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseAddServiceImpl implements CaseAddService {

    private final PersonalCaseService personalCaseService;
    private final DeptCaseService deptCaseService;
    private final UserCaseService userCaseService;
    private final UserCatalogService userCatalogService;
    private final DepCaseClassifyService depCaseClassifyService;
    private final DepCaseTypeService depCaseTypeService;
    private final DepCaseService depCaseService;
    private final StudyService studyService;
    private final StudyRecordService studyRecordService;
    private final CaseStudyService caseStudyService;
    private final SeriesService seriesService;
    private final PacsApi pacsApi;
    private final DimseApi dimseApi;
    private final FollowService followService;
    private final TagService tagService;
    private final DepCaseTagService depCaseTagService;
    private final UserCaseTagService userCaseTagService;
    private final AuditRecordService auditRecordService;
    private final DepCaseDiseaseService caseDiseaseService;
    private final ESSyncService esSyncService;
    private final AetTemplateService aetTemplateService;

    @Value("${CASE_COMBINE:}")
    private String caseCombine;

    private boolean combine = false;

    /**
     * 查询对应的病人id对应的病例是否已存在
     * @param queryCaseExistReq
     * @return
     */
    @Override
    public List<AuditCaseDetailResp> queryCaseExist(QueryCaseExistReq queryCaseExistReq) {
        long caseTypeId = queryCaseExistReq.getCaseTypeId();
        List<AuditCaseDetailResp> deptCaseDetailRespList = new ArrayList<>();
        List<String> patientIds = queryCaseExistReq.getPatientIds();
        if(!patientIds.isEmpty()){
            for(String patientId : patientIds) {
                if(patientId.contains("*") || ("0").equals(patientId)){
                    continue;
                }
                List<DepCase> depCaseList = depCaseService.lambdaQuery().eq(DepCase::getPatientId, patientId).list();
                if (!depCaseList.isEmpty()) {
                    for(DepCase depCase : depCaseList){
                        DeptCaseDetailResp resp = deptCaseService.getDeptCaseDetail(depCase.getCaseId(), caseTypeId);
                        AuditCaseDetailResp auditCaseDetailResp = new AuditCaseDetailResp();
                        BeanUtils.copyProperties(resp,auditCaseDetailResp);
                        if(caseTypeId == -1L){
                            deptCaseDetailRespList.add(auditCaseDetailResp);
                        }else{
                            //非随访库加入审核信息
                            List<AuditInfo> auditInfoResp = deptCaseService.getAuditRecordList(depCase.getCaseId(), caseTypeId);
                            if(!auditInfoResp.isEmpty()){
                                auditCaseDetailResp.setAuditInfo(auditInfoResp.get(0));
                                deptCaseDetailRespList.add(auditCaseDetailResp);
                            }
                        }
                    }
                }
            }
        }
        return deptCaseDetailRespList;
    }

    private long createDeptCaseRIS(AddRISCaseReq addRisCaseReq){
        List<RISStudy> studyUidList = addRisCaseReq.getRisStudyList();
        for(RISStudy risStudy : studyUidList){
            List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,risStudy.getStudyUid()).list();
            if(!caseStudyList.isEmpty()){
                List<Long> caseIds = caseStudyList.stream().map(CaseStudy::getCaseId).collect(Collectors.toList());
                for(Long caseId : caseIds) {
                    if (depCaseService.getById(caseId) != null) {
                        throw new BusinessException("【"+risStudy.getStudyTime()+risStudy.getStudyItemName()+"】"+LocaleUtil.getLocale("study.of.case.already.exist"));
                    }
                }
            }
        }

        RISStudy risStudy = addRisCaseReq.getRisStudyList().get(0);
        DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
        deptCaseCreateReq.setCaseTypeId(addRisCaseReq.getTargetId()).setCaseName("").setCaseNo("").setDiseaseIdList(addRisCaseReq.getDiseaseIdList())
                .setDiagnosis(risStudy.getClinicalDiagnosis()).setPatientId(risStudy.getPatientId()).setPatientName(risStudy.getPatientName())
                .setPatientSex(risStudy.getPatientSex()).setPatientBirthDate(risStudy.getPatientBirthDate()).setPatientAge(risStudy.getPatientAge())
                .setMedicalHistory(risStudy.getMedicalHistory()).setSign(risStudy.getReportDescribe()).setCaseAnalysis("")
                .setFollowStatus("0").setSourceType("1").setAuditType("1");
        return deptCaseService.createDeptCase(deptCaseCreateReq);
    }

    public long addPersonCaseFromRis(AddRISCaseReq addRisCaseReq){
        log.info("=============开始新建个人病例===============");
        long userCaseId;
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        //要验证一下这个目录是不是存在
        if (userCatalogService.getById(addRisCaseReq.getTargetId()) == null) {
            throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
        }
        RISStudy risStudy = risStudyList.get(0);
        PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
        personalCaseCreateReq.setCatalogIdList(Collections.singletonList(addRisCaseReq.getTargetId())).setCaseName("")
                .setDiseaseIdList(addRisCaseReq.getDiseaseIdList())
                .setCaseNo("").setDiagnosis(risStudy.getClinicalDiagnosis()).setPatientId(risStudy.getPatientId())
                .setPatientName(risStudy.getPatientName()).setPatientSex(risStudy.getPatientSex())
                .setPatientBirthDate(risStudy.getPatientBirthDate()).setPatientAge(risStudy.getPatientAge())
                .setMedicalHistory(risStudy.getMedicalHistory()).setSign(risStudy.getReportDescribe())
                .setCaseAnalysis("").setFollowStatus("0").setSourceType("1");
        userCaseId = personalCaseService.createPersonalCaseInCatalog(personalCaseCreateReq);
        log.info("=============结束新建个人病例==============="+userCaseId);

        esSyncService.syncPersonalCase(Collections.singletonList(userCaseId), Constant.OPERATION_TYPE_UPDATE);
        return userCaseId;
    }

    public long addDeptCaseFromRis(AddRISCaseReq addRisCaseReq){
        long caseId = 0L;
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        //验证如果非随访（病例总库），那么要验证该库是否存在
        if(addRisCaseReq.getTargetId()!=null && addRisCaseReq.getTargetId()!=-1 && depCaseTypeService.getById(addRisCaseReq.getTargetId())==null){
            throw new BusinessException(LocaleUtil.getLocale("dept.case.library.not.exist"));
        }
        //如果属于教学库，疾病类型不可以是空的
        if(addRisCaseReq.getTargetId()!=null && addRisCaseReq.getTargetId() == 1){
            if(addRisCaseReq.getDiseaseIdList()==null || addRisCaseReq.getDiseaseIdList().isEmpty()){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }
        }

        //0合并1新建  新建需要判断当前检查是否已关联其他病例，是的话抛异常  0合并的话要先判断是否存在这个病人，存在简单，直接调用合并接口，不存在就再新建
        if("0".equals(caseCombine)){
            String patientId = risStudyList.get(0).getPatientId();
            List<DepCase> depCaseList = depCaseService.lambdaQuery().eq(DepCase::getPatientId,patientId).list();
            //自动合并的情况下，只会存在一个相同patientId的病例，最多一个！
            if(depCaseList.isEmpty()){
                caseId = createDeptCaseRIS(addRisCaseReq);
            }else {
                DepCase depCase = depCaseList.get(0);
                AddStudyInfoRISReq addStudyInfoReq = new AddStudyInfoRISReq();
                addStudyInfoReq.setRisStudyList(risStudyList);
                addStudyInfoReq.setCaseTypeId(addRisCaseReq.getTargetId());
                addStudyInfoReq.setCaseId(depCase.getCaseId());
                //调用合并接口
                combine = true;
                return addStudyInfoRIS(addStudyInfoReq);
            }
        }
        if("1".equals(caseCombine)){
            caseId = createDeptCaseRIS(addRisCaseReq);
        }

        esSyncService.syncDepartmentCase(Collections.singletonList(caseId), Constant.OPERATION_TYPE_UPDATE);
        return caseId;
    }

    /**
     * 从RIS导入病历
     * @param addRisCaseReq
     * @return
     */
    @Override
    public AddRISCaseResp addCaseFromRis(AddRISCaseReq addRisCaseReq) {
        vertifyRisCase(addRisCaseReq);
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        //先定义一个caseId
        long caseId = 0L;
        if(addRisCaseReq.getTargetLibrary() == 1){
            long userCaseId = addPersonCaseFromRis(addRisCaseReq);
            caseId = userCaseService.getById(userCaseId).getCaseId();
        }
        if(addRisCaseReq.getTargetLibrary() == 2){
            combine = false;
            caseId = addDeptCaseFromRis(addRisCaseReq);
            //如果合并的话，就不走下一步了
            if(combine){
                AddRISCaseResp addRISCaseResp = new AddRISCaseResp();
                addRISCaseResp.setCaseId(caseId);
                return addRISCaseResp;
            }
        }

        //对接了RIS就有Study信息，对接了影像的才会有序列信息
        int isExport = 0;
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            isExport = 1;
        }
        addCaseStudyFromRIS(risStudyList,caseId,isExport);

        //教学用，那么凡是新增的病例都需要顺带导入影像。晨会用，可能压根都不需要导入影像，打开别人的pacs就可以了
        //病例库自己用，-1类型的（随访库或叫病例总库）不导入影像，其他类型的都需要导入影像
        AddRISCaseResp addRISCaseResp = new AddRISCaseResp();
        addRISCaseResp.setCaseId(caseId);

        //如果是科室病例库、并且属于随访库的情况下，不导入影像
        if(addRisCaseReq.getTargetLibrary() == 2 && addRisCaseReq.getTargetId()==-1){
            return addRISCaseResp;
        }
        //异步导入影像
        for(RISStudy risStudy : risStudyList){
            moveDicom(risStudy.getStudyUid());
        }
        return addRISCaseResp;
    }

    /**
     * 从随访库导入病历（只需要加关联关系）
     * @param addFollowCaseReq
     * @return
     */
    @Override
    @ESSync(type = ESSync.SyncType.UPDATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    public long addCaseFromFollow(AddFollowCaseReq addFollowCaseReq) {
        //如果属于教学库，疾病类型不可以是空的
        if(addFollowCaseReq.getCaseTypeId() == 1){
            if(addFollowCaseReq.getDiseaseIdList()==null || addFollowCaseReq.getDiseaseIdList().isEmpty()){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }else {
                List<Long> diseaseIdList = addFollowCaseReq.getDiseaseIdList();
                for(long diseaseId : diseaseIdList){
                    //增加一个疾病和病例关联关系
                    if(caseDiseaseService.lambdaQuery().eq(DepCaseDisease::getCaseId,addFollowCaseReq.getCaseId())
                            .eq(DepCaseDisease::getDiseaseId,diseaseId).count()==0){
                        DepCaseDisease caseDisease = new DepCaseDisease();
                        caseDisease.setId(YitIdHelper.nextId());
                        caseDisease.setCaseId(addFollowCaseReq.getCaseId());
                        caseDisease.setDiseaseId(diseaseId);
                        caseDiseaseService.save(caseDisease);
                    }
                }
            }
        }
        if(depCaseTypeService.getById(addFollowCaseReq.getCaseTypeId())==null){
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        if(addFollowCaseReq.getCaseId()==null || depCaseService.getById(addFollowCaseReq.getCaseId())==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        List<DepCaseClassify> depCaseClassifyList = depCaseClassifyService.lambdaQuery()
                .eq(DepCaseClassify::getCaseId,addFollowCaseReq.getCaseId())
                .eq(DepCaseClassify::getCaseTypeId,addFollowCaseReq.getCaseTypeId()).list();
        DepCaseType depCaseType = depCaseTypeService.getById(addFollowCaseReq.getCaseTypeId());

        if(depCaseClassifyList.isEmpty()){
            //没有关联，需要增加关联,需要审核的话增加审核记录，不需要审核不管
            DepCaseClassify depCaseClassify = new DepCaseClassify();
            depCaseClassify.setId(YitIdHelper.nextId());
            depCaseClassify.setCaseId(addFollowCaseReq.getCaseId());
            depCaseClassify.setCaseTypeId(addFollowCaseReq.getCaseTypeId());
            depCaseClassifyService.save(depCaseClassify);
            if("1".equals(depCaseType.getAudit())){
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setAuditId(YitIdHelper.nextId());
                auditRecord.setCaseId(addFollowCaseReq.getCaseId());
                auditRecord.setCaseTypeId(addFollowCaseReq.getCaseTypeId());
                auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
                auditRecord.setPubTime(new Date());
                auditRecordService.save(auditRecord);
            }
        }else {
            //先看看需不需要审核，不需要的话直接就提示已存在
            if("0".equals(depCaseType.getAudit())){
                //不需要审核的，又存在关联关系，直接抛异常就行了
                throw new BusinessException(LocaleUtil.getLocale("case.already.exist"));
            }

            //需要审核并且有关联，需要看看审核记录
            //对于病例在审核中或者通过，同一个人再导入提示成功
            //对于病例在拒绝状态，只要再次导入，则新加一条审核记录，并且caseId一样
            //审核记录 status 0-待审核 1-通过 2-驳回
            List<AuditRecord> auditRecordList = auditRecordService.lambdaQuery()
                    .eq(AuditRecord::getCaseId,addFollowCaseReq.getCaseId())
                    .eq(AuditRecord::getCaseTypeId,addFollowCaseReq.getCaseTypeId())
                    .orderByDesc(AuditRecord::getPubTime)
                    .list();
            //审核记录为空的话就添加审核
            if(auditRecordList.isEmpty()){
                addCaseFromFollowAudit(addFollowCaseReq.getCaseId(),addFollowCaseReq.getCaseTypeId());
            }
            //审核记录不为空要看最近一条是什么状态
            AuditRecord newestAuditRecord = auditRecordList.get(0);
            //如果最近一条是审核通过了，那么直接告诉用户已存在
            if("1".equals(newestAuditRecord.getStatus())){
                throw new BusinessException(LocaleUtil.getLocale("case.already.exist"));
            }
            //如果最近一条未审核，那么增加一个发布人
            if("0".equals(newestAuditRecord.getStatus())) {
                //如果有审核记录，那么追加本id
                String pubUserId = newestAuditRecord.getPubUserId();
                List<Long> userIds = Arrays.stream(pubUserId.split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (!userIds.contains(LoginUtil.getLoginUserId())) {
                    userIds.add(LoginUtil.getLoginUserId());
                }
                String newUserIds = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                newestAuditRecord.setPubUserId(newUserIds);
                auditRecordService.saveOrUpdate(newestAuditRecord);
            }
            //如果最近一条是被拒绝了，那么需要新增一条审核记录
            if("2".equals(newestAuditRecord.getStatus())) {
                addCaseFromFollowAudit(addFollowCaseReq.getCaseId(),addFollowCaseReq.getCaseTypeId());
            }
        }

        //如果没有导入影像的话，是不会有序列信息的
        List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getCaseId,addFollowCaseReq.getCaseId()).list();
        if(!caseStudyList.isEmpty()){
            for(CaseStudy caseStudy : caseStudyList){
                if(seriesService.lambdaQuery().eq(Series::getStudyUid,caseStudy.getStudyUid()).count()==0){
                    //只要其中一个检查下面没有序列，就重新导入该影像
                    moveDicom(caseStudy.getStudyUid());
                }
            }
        }
        return addFollowCaseReq.getCaseId();
    }

    /**
     * 创建审核记录
     * @param caseId
     * @param caseTypeId
     */
    private void addCaseFromFollowAudit(long caseId,long caseTypeId){
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setAuditId(YitIdHelper.nextId());
        auditRecord.setCaseId(caseId);
        auditRecord.setCaseTypeId(caseTypeId);
        auditRecord.setAuditType("3");
        auditRecord.setPubUserId(LoginUtil.getLoginUserId().toString());
        auditRecord.setPubTime(new Date());
        auditRecordService.save(auditRecord);
    }

    /**
     * 本地上传添加病例
     * @param addUploadCaseReq
     * @return
     */
    @Override
    public AddUploadCaseResp addCaseFromUpload(AddUploadCaseReq addUploadCaseReq) {
        vertifyUploadCase(addUploadCaseReq);
        AddUploadCaseResp addUploadCaseResp = new AddUploadCaseResp();
        //先定义一个caseId
        long caseId;
        long userCaseId;
        if(addUploadCaseReq.getTargetLibrary() == 1){
            userCaseId = addUserCaseUpload(addUploadCaseReq);
            addUploadCaseResp.setCaseId(userCaseId);
        }
        if(addUploadCaseReq.getTargetLibrary() == 2){
            caseId = addDepCaseUpload(addUploadCaseReq);
            addUploadCaseResp.setCaseId(caseId);
        }
        return addUploadCaseResp;
    }

    /**
     * 本地上传的时候，新建一个个人病例
     * @param addUploadCaseReq
     * @return
     */
    public long addUserCaseUpload(AddUploadCaseReq addUploadCaseReq){
        int targetLibrary = addUploadCaseReq.getTargetLibrary();
        long userCaseId;
        //要验证一下这个目录是不是存在
        if (userCatalogService.getById(addUploadCaseReq.getTargetId()) == null) {
            throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
        }
        AddUploadCaseReq.Supplement supplement = addUploadCaseReq.getSupplement();
        List<AddUploadCaseReq.Follow> followList = supplement.getFollowInfoList();
        String[] tags = supplement.getTags();
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        //一次只让新建一个病例
        Study caseStudy = studyService.getById(studyInfoList.get(0).getStudyUid());
        PersonalCaseCreateReq personalCaseCreateReq = new PersonalCaseCreateReq();
        personalCaseCreateReq.setCatalogIdList(Collections.singletonList(addUploadCaseReq.getTargetId()))
                .setDiseaseIdList(addUploadCaseReq.getDiseaseIdList()).setCaseName(supplement.getCaseName())
                .setCaseNo(supplement.getCaseNo()).setDiagnosis(supplement.getDiagnosis()).setPatientId(caseStudy.getPatientId())
                .setPatientName(caseStudy.getPatientName()).setPatientSex(caseStudy.getPatientSex())
                .setPatientBirthDate(caseStudy.getPatientBirthDate()).setPatientAge(caseStudy.getPatientAge())
                .setMedicalHistory(supplement.getMedicalHistory()).setSign(supplement.getSign())
                .setSelfComplaints(supplement.getSelfComplaints()).setPositionMatch(supplement.getPositionMatch())
                .setQualityMatch(supplement.getQualityMatch()).setDifficulty(supplement.getDifficulty()).setCaseCategory(supplement.getCaseCategory())
                .setCaseAnalysis(supplement.getCaseAnalysis()).setFollowStatus("0").setSourceType("2");
        if(followList!=null && !followList.isEmpty()){
            personalCaseCreateReq.setFollowStatus("1");
        }
        userCaseId = personalCaseService.createPersonalCaseInCatalog(personalCaseCreateReq);
        long caseId = userCaseService.getById(userCaseId).getCaseId();

        if(followList!=null && !followList.isEmpty()){
            dealWithFollow(followList,caseId);
        }
        if(tags!=null && tags.length>0){
            dealWithTags(tags,targetLibrary,caseId);
        }

        for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
            dealWithDicomInfo(studyInfo.getStudyUid(),caseId);
        }

        //用户同步ES
        esSyncService.syncPersonalCase(Collections.singletonList(userCaseId), Constant.OPERATION_TYPE_UPDATE);
        return userCaseId;
    }

    /**
     * 本地上传的时候，新建一个科室病例
     * @param addUploadCaseReq
     * @return
     */
    public long addDepCaseUpload(AddUploadCaseReq addUploadCaseReq){
        int targetLibrary = addUploadCaseReq.getTargetLibrary();
        //验证如果非随访（病例总库），那么要验证该库是否存在
        if(addUploadCaseReq.getTargetId()!=null && addUploadCaseReq.getTargetId()!=-1 && depCaseTypeService.getById(addUploadCaseReq.getTargetId())==null){
            throw new BusinessException(LocaleUtil.getLocale("dept.case.library.not.exist"));
        }
        //如果属于教学库，疾病类型不可以是空的
        if(addUploadCaseReq.getTargetId()!=null && addUploadCaseReq.getTargetId() == 1){
            if(addUploadCaseReq.getDiseaseIdList()==null || addUploadCaseReq.getDiseaseIdList().isEmpty()){
                throw new BusinessException(LocaleUtil.getLocale("teach.case.should.belongs.one.disease"));
            }
        }
        AddUploadCaseReq.Supplement supplement = addUploadCaseReq.getSupplement();
        List<AddUploadCaseReq.Follow> followList = supplement.getFollowInfoList();
        String[] tags = supplement.getTags();
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();

        for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
            List<CaseStudy> caseStudyList = caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,studyInfo.getStudyUid()).list();
            if(!caseStudyList.isEmpty()){
                List<Long> caseIds = caseStudyList.stream().map(CaseStudy::getCaseId).collect(Collectors.toList());
                for(Long caseId : caseIds) {
                    if (depCaseService.getById(caseId) != null) {
                        Study study = studyService.getById(studyInfo.getStudyUid());
                        throw new BusinessException("【" + DateUtil.convertDateToStr(study.getStudyTime(),DateUtil.DEFAULT_PATTERN)
                                + (study.getDeviceType()==null?"":study.getDeviceType()) + "】" + LocaleUtil.getLocale("study.of.case.already.exist"));
                    }
                }
            }
        }

        long caseId = 0L;
        //0合并1新建  新建需要判断当前检查是否已关联其他病例，是的话抛异常  0合并的话要先判断是否存在这个病人，存在简单，直接调用合并接口，不存在就再新建
        if("0".equals(caseCombine)){
            String patientId = studyInfoList.get(0).getPatientId();
            List<DepCase> depCaseList = depCaseService.lambdaQuery().eq(DepCase::getPatientId,patientId).list();
            //自动合并的情况下，只会存在一个相同patientId的病例，最多一个！
            if(depCaseList.isEmpty()){
                caseId = createDeptCaseUpload(addUploadCaseReq);
            }else {
                DepCase depCase = depCaseList.get(0);
                AddStudyInfoUploadReq addStudyInfoUploadReq = new AddStudyInfoUploadReq();
                List<AddUploadCaseReq.StudyInfo> studyInfos = addUploadCaseReq.getStudyInfoList();
                List<AddStudyInfoUploadReq.StudyInfo> studyInfos1 = new ArrayList<>();
                for(AddUploadCaseReq.StudyInfo studyInfo : studyInfos){
                    AddStudyInfoUploadReq.StudyInfo studyInfo1 = new AddStudyInfoUploadReq.StudyInfo();
                    BeanUtils.copyProperties(studyInfo,studyInfo1);
                    studyInfos1.add(studyInfo1);
                }
                addStudyInfoUploadReq.setStudyInfoList(studyInfos1);
                addStudyInfoUploadReq.setCaseId(depCase.getCaseId());
                addStudyInfoUploadReq.setCaseTypeId(addUploadCaseReq.getTargetId());
                //调用合并接口
                caseId = addStudyInfoUpload(addStudyInfoUploadReq);
                return caseId;
            }
        }
        if("1".equals(caseCombine)){
            caseId = createDeptCaseUpload(addUploadCaseReq);
        }

        if(followList!=null && !followList.isEmpty()){
            dealWithFollow(followList,caseId);
        }
        if(tags!=null && tags.length>0){
            dealWithTags(tags,targetLibrary,caseId);
        }
        for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
            dealWithDicomInfo(studyInfo.getStudyUid(),caseId);
        }

        esSyncService.syncDepartmentCase(Collections.singletonList(caseId), Constant.OPERATION_TYPE_UPDATE);
        return caseId;
    }

    private long createDeptCaseUpload(AddUploadCaseReq addUploadCaseReq){
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        AddUploadCaseReq.Supplement supplement = addUploadCaseReq.getSupplement();
        List<AddUploadCaseReq.Follow> followList = supplement.getFollowInfoList();
        //一次只让新建一个病例
        Study caseStudy = studyService.getById(studyInfoList.get(0).getStudyUid());
        DeptCaseCreateReq deptCaseCreateReq = new DeptCaseCreateReq();
        deptCaseCreateReq.setCaseTypeId(addUploadCaseReq.getTargetId()).setDiseaseIdList(addUploadCaseReq.getDiseaseIdList()).setCaseName(supplement.getCaseName())
                .setCaseNo(supplement.getCaseNo()).setDiagnosis(supplement.getDiagnosis()).setPatientId(caseStudy.getPatientId())
                .setPatientName(caseStudy.getPatientName()).setPatientSex(caseStudy.getPatientSex())
                .setPatientBirthDate(caseStudy.getPatientBirthDate()).setPatientAge(caseStudy.getPatientAge())
                .setMedicalHistory(supplement.getMedicalHistory()).setSign(supplement.getSign())
                .setSelfComplaints(supplement.getSelfComplaints()).setPositionMatch(supplement.getPositionMatch())
                .setQualityMatch(supplement.getQualityMatch()).setDifficulty(supplement.getDifficulty()).setCaseCategory(supplement.getCaseCategory())
                .setCaseAnalysis(supplement.getCaseAnalysis()).setFollowStatus("0").setSourceType("2").setAuditType("2");
        if(followList!=null && !followList.isEmpty()){
            deptCaseCreateReq.setFollowStatus("1");
        }
        return deptCaseService.createDeptCase(deptCaseCreateReq);
    }

    private void moveDicom(String studyUid){
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            //①调用pacs接口去CMove这些影像
            Study study = studyService.getById(studyUid);
            AetTemplate aetTemplate = aetTemplateService.getById(study.getAetId());
            MoveScuReq moveScuReq = new MoveScuReq(Constant.STUDY_RETRIEVE_LEVEL,studyUid);
            moveScuReq.setRemoteAet(aetTemplate.getAetName());

            ResultBean<String> resultBean = dimseApi.scuMove(moveScuReq);
            if(resultBean.getState()){
                String taskId = resultBean.getData();
                ContextHolder.getBean("cachedPool", ExecutorService.class).submit(
                        new MoveDicomTask(taskId,studyUid,dimseApi,studyService));
            }
        }
    }

    @Override
    public void dicomNotify(String studyInstanceUID, Long userId) {
        ResultBean<List<StudyInstanceDto>> resultBean = pacsApi.getStudyList(studyInstanceUID);
        if(!resultBean.getState()){
            log.error("======查询pacs检查信息失败========");
            return;
        }
        List<StudyInstanceDto> studyInstanceDtoList = resultBean.getData();
        if(studyInstanceDtoList.isEmpty()){
            log.error("======查询pacs检查信息失败，检查列表为空========");
            return;
        }
        StudyInstanceDto studyInstanceDto = studyInstanceDtoList.get(0);
        List<SeriesInstanceDto> seriesInstanceDtoList = studyInstanceDto.getSeriesList();
        if(seriesInstanceDtoList.isEmpty()){
            log.error("======查询pacs序列信息失败，序列列表为空========");
            return;
        }

        String studyDate = studyInstanceDto.getStudyDate();
        String studyTime = studyInstanceDto.getStudyTime();
        Date studyDateTime = DateUtil.parseToDate(studyDate, studyTime);
        List<StudyRecord> studyRecordList = studyRecordService.lambdaQuery()
                .eq(StudyRecord::getStudyUid,studyInstanceUID)
                .eq(StudyRecord::getAssociation,0)
                .eq(StudyRecord::getCreateBy,userId)
                .list();
        if(studyRecordList.isEmpty()){
            //在记录表中记录一下当前上传的记录
            StudyRecord studyRecord = new StudyRecord();
            studyRecord.setId(YitIdHelper.nextId());
            studyRecord.setStudyUid(studyInstanceDto.getStudyInstanceUID());
            studyRecord.setAccessNumber(studyInstanceDto.getAccessNumber());
            studyRecord.setStudyTime(studyDateTime);
            studyRecord.setStudyNo(studyInstanceDto.getStudyId());
            studyRecord.setPatientId(studyInstanceDto.getPatientId());
            studyRecord.setPatientName(studyInstanceDto.getPatientName());
            studyRecord.setPatientSex(studyInstanceDto.getPatientSex());
            studyRecord.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
            studyRecord.setPatientAge(studyInstanceDto.getPatientAge());
            studyRecord.setIsonline(0);
            studyRecord.setCreateBy(userId);
            studyRecordService.saveOrUpdate(studyRecord);
        }

        Study study = new Study();
        study.setStudyUid(studyInstanceDto.getStudyInstanceUID());
        study.setAccessNumber(studyInstanceDto.getAccessNumber());
        study.setStudyTime(studyDateTime);
        study.setStudyNo(studyInstanceDto.getStudyId());
        study.setPatientId(studyInstanceDto.getPatientId());
        study.setPatientName(studyInstanceDto.getPatientName());
        study.setPatientSex(studyInstanceDto.getPatientSex());
        study.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
        study.setPatientAge(studyInstanceDto.getPatientAge());
        study.setIsonline(0);
        study.setAetId(1);
        study.setIsExport(2);
        study.setCreateBy(userId);
        studyService.saveOrUpdate(study);

        List<Series> caseSeriesList = new ArrayList<>();
        for(SeriesInstanceDto seriesInstanceDto : seriesInstanceDtoList){
            Series caseSeries = new Series();
            caseSeries.setSeriesUid(seriesInstanceDto.getSeriesInstanceUID());
            caseSeries.setPatientId(studyInstanceDto.getPatientId());
            caseSeries.setPatientName(studyInstanceDto.getPatientName());
            caseSeries.setPatientSex(studyInstanceDto.getPatientSex());
            caseSeries.setPatientBirthDate(studyInstanceDto.getPatientBirthDate());
            caseSeries.setPatientAge(studyInstanceDto.getPatientAge());
            caseSeries.setStudyUid(studyInstanceDto.getStudyInstanceUID());
            caseSeries.setAccessNumber(studyInstanceDto.getAccessNumber());
            caseSeries.setSeriesDescription(seriesInstanceDto.getSeriesDescription());
            caseSeries.setModality(seriesInstanceDto.getModality());
            caseSeries.setIsKeyframe(0);
            caseSeries.setCreateBy(userId);
            caseSeriesList.add(caseSeries);
        }
        seriesService.saveOrUpdateBatch(caseSeriesList);
    }

    @Override
    public PageInfo<StudyRecord> dicomNotRelate(NotRelateDicomReq notRelateDicomReq) {

        LambdaQueryChainWrapper<StudyRecord> wrapper = studyRecordService.lambdaQuery();
        wrapper.eq(StudyRecord::getCreateBy,LoginUtil.getLoginUserId())
                .eq(StudyRecord::getAssociation,0);
        if(notRelateDicomReq.getKeyword()!=null){
            wrapper.nested(i -> i.like(StudyRecord::getPatientName, notRelateDicomReq.getKeyword())
                    .or().like(StudyRecord::getPatientId, notRelateDicomReq.getKeyword()));
        }

        PageHelper.startPage(notRelateDicomReq.getPageNum(), notRelateDicomReq.getPageSize());
        List<StudyRecord> studyList = wrapper.list();

        if(!studyList.isEmpty()){
            for(StudyRecord study : studyList){
                HashSet<String> modalityList = new HashSet<>();
                List<Series> caseSeriesList = seriesService.lambdaQuery()
                        .eq(Series::getStudyUid,study.getStudyUid()).list();
                for(Series caseSeries : caseSeriesList){
                    if(!caseSeries.getModality().isEmpty()){
                        modalityList.add(caseSeries.getModality());
                    }
                }
                if(!modalityList.isEmpty()){
                    String modalityLists = modalityList.stream().map(Object::toString).collect(Collectors.joining(","));
                    study.setModalities(modalityLists);
                }else {
                    study.setModalities("");
                }
                if(study.getAccessNumber()==null){
                    study.setAccessNumber("");
                }
            }
        }
        return new PageInfo<>(studyList);
    }

    /**
     * 从RIS导入中合并病例
     * @param addStudyInfoReq
     * @return
     */
    @Override
    public long addStudyInfoRIS(AddStudyInfoRISReq addStudyInfoReq) {
        List<RISStudy> risStudyList = addStudyInfoReq.getRisStudyList();
        if(addStudyInfoReq.getCaseId()==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        DepCase depCase = depCaseService.getById(addStudyInfoReq.getCaseId());
        if(depCase==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }

        //必须要属于同一个患者
        for(RISStudy risStudy : risStudyList){
            if(!depCase.getPatientId().equals(risStudy.getPatientId())){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }

        //增加关联关系
        makeConnections(addStudyInfoReq.getCaseId(),addStudyInfoReq.getCaseTypeId());

        int isExport = 0;
        if (FeignConfig.hasServer(Constant.DIMSE_SERVER_NAME)) {
            isExport = 1;
        }

        addCaseStudyFromRIS(risStudyList,addStudyInfoReq.getCaseId(),isExport);
        //异步导入影像
        for(RISStudy risStudy : risStudyList){
            moveDicom(risStudy.getStudyUid());
        }

        return addStudyInfoReq.getCaseId();
    }

    /**
     * 判断一下病例和病例库是否有关联，没有的话加一下
     * @param caseId
     * @param caseTypeId
     */
    private void makeConnections(long caseId,long caseTypeId){
        List<DepCaseClassify> depCaseClassifyList = depCaseClassifyService.lambdaQuery().eq(DepCaseClassify::getCaseId,caseId).list();
        if(!depCaseClassifyList.isEmpty()){
            List<Long> depCaseTypeList = depCaseClassifyList.stream().map(DepCaseClassify::getCaseTypeId).collect(Collectors.toList());
            if(!depCaseTypeList.contains(caseTypeId) && caseTypeId!=-1){
                DepCaseClassify depCaseClassify = new DepCaseClassify();
                depCaseClassify.setId(YitIdHelper.nextId());
                depCaseClassify.setCaseId(caseId);
                depCaseClassify.setCaseTypeId(caseTypeId);
                depCaseClassifyService.save(depCaseClassify);
            }
        }
    }

    /**
     * 从本地上传中合并病例
     * @param addStudyInfoUploadReq
     * @return
     */
    @Override
    public long addStudyInfoUpload(AddStudyInfoUploadReq addStudyInfoUploadReq) {
        if(addStudyInfoUploadReq.getCaseId()==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        DepCase depCase = depCaseService.getById(addStudyInfoUploadReq.getCaseId());
        if(depCase==null){
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }

        List<AddStudyInfoUploadReq.StudyInfo> studyInfoList = addStudyInfoUploadReq.getStudyInfoList();
        for(AddStudyInfoUploadReq.StudyInfo studyInfo : studyInfoList){
            List<StudyRecord> studyRecordList = studyRecordService.lambdaQuery().eq(StudyRecord::getStudyUid,studyInfo.getStudyUid()).list();
            if(!studyRecordList.isEmpty() && !depCase.getPatientId().equals(studyRecordList.get(0).getPatientId())){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }

        //增加关联关系
        makeConnections(addStudyInfoUploadReq.getCaseId(),addStudyInfoUploadReq.getCaseTypeId());

        for(AddStudyInfoUploadReq.StudyInfo studyInfo : studyInfoList){
            dealWithDicomInfo(studyInfo.getStudyUid(),addStudyInfoUploadReq.getCaseId());
        }
        return addStudyInfoUploadReq.getCaseId();
    }

    /**
     * 处理随访
     * @param followList
     * @param caseId
     */
    private void dealWithFollow(List<AddUploadCaseReq.Follow> followList,long caseId){
        //处理随访
        if(followList!=null && !followList.isEmpty()){
            List<Follow> follows = new ArrayList<>();
            for(AddUploadCaseReq.Follow supplementFollow : followList){
                Follow follow = new Follow();
                follow.setFollowId(YitIdHelper.nextId());
                follow.setCaseId(caseId);
                follow.setFollowupResult(supplementFollow.getFollowupResult());
                follow.setFollowType(supplementFollow.getFollowType());
                follow.setCreatedBy(LoginUtil.getLoginUserId());
                follow.setCreateTime(new Date());
                follows.add(follow);
            }
            followService.saveBatch(follows);
        }
    }

    /**
     * 处理标签
     * @param tags
     * @param targetLibrary
     * @param caseId
     */
    private void dealWithTags(String[] tags,int targetLibrary,long caseId){
        for(String tag : tags){
            //第一步，新建标签
            long tagId;
            List<Tag> existTags = tagService.lambdaQuery().eq(Tag::getTagName,tag).list();
            if(!existTags.isEmpty()){
                tagId = existTags.get(0).getTagId();
            }else {
                tagId = YitIdHelper.nextId();
                Tag newTag = new Tag();
                newTag.setTagId(tagId);
                newTag.setTagName(tag);
                tagService.save(newTag);
            }
            //第二步，新建关联关系
            if(targetLibrary == 1){
                //个人病例库
                UserCaseTag userCaseTag = new UserCaseTag();
                userCaseTag.setId(YitIdHelper.nextId());
                userCaseTag.setUserCaseId(caseId);
                userCaseTag.setTagId(tagId);
                userCaseTagService.save(userCaseTag);
            }else {
                DepCaseTag depCaseTag = new DepCaseTag();
                depCaseTag.setId(YitIdHelper.nextId());
                depCaseTag.setCaseId(caseId);
                depCaseTag.setTagId(tagId);
                depCaseTagService.save(depCaseTag);
            }
        }
    }

    /**
     * 处理dicom信息
     * @param StudyUid
     * @param caseId
     */
    private void dealWithDicomInfo(String StudyUid ,long caseId){
        //把记录表中相关的记录改掉
        studyRecordService.lambdaUpdate()
                .eq(StudyRecord::getStudyUid,StudyUid)
                .eq(StudyRecord::getCreateBy,LoginUtil.getLoginUserId())
                .eq(StudyRecord::getAssociation,0)
                .set(StudyRecord::getAssociation,1).update();

        //已经关联过了就不管了
        if(caseStudyService.lambdaQuery().eq(CaseStudy::getStudyUid,StudyUid)
                        .eq(CaseStudy::getCaseId,caseId).count()>0){
            return;
        }
        //没有关联过就创建一个关联信息
        CaseStudy caseStudy = new CaseStudy();
        caseStudy.setCaseId(caseId);
        caseStudy.setStudyUid(StudyUid);
        caseStudy.setId(YitIdHelper.nextId());
        caseStudyService.save(caseStudy);
    }

    private void addCaseStudyFromRIS(List<RISStudy> risStudyList,long caseId,int isExport){
        List<Study> caseStudyList = new ArrayList<>();
        for(RISStudy risStudy : risStudyList){
            Study study = new Study();
            study.setStudyUid(risStudy.getStudyUid()).setAccessNumber(risStudy.getAccessNumber())
                    .setStudyTime(DateUtil.convertStrToDate(risStudy.getStudyTime(),DateUtil.DEFAULT_PATTERN)).setStudyNo(risStudy.getStudyNo())
                    .setPatientId(risStudy.getPatientId()).setPatientName(risStudy.getPatientName()).setPatientSex(risStudy.getPatientSex())
                    .setPatientBirthDate(risStudy.getPatientBirthDate()).setPatientAge(risStudy.getPatientAge()).setPatientType(risStudy.getPatientType())
                    .setVisitDate(risStudy.getVisitDate()).setPhysicalSign(risStudy.getPhysicalSign()).setClinicalDiagnosis(risStudy.getClinicalDiagnosis())
                    .setStudyItemName(risStudy.getStudyItemName()).setPartName(risStudy.getPartName()).setDeviceName(risStudy.getDeviceType()).setDeviceType(risStudy.getDeviceType())
                    .setMedicalHistory(risStudy.getMedicalHistory()).setSelfReportedSymptom(risStudy.getSelfReportedSymptom()).setReportDescribe(risStudy.getReportDescribe())
                    .setReportDiagnose(risStudy.getReportDiagnose()).setRegisterTime(risStudy.getRegisterTime()).setReportTime(risStudy.getReportTime())
                    .setReporter(risStudy.getReporter()).setChecker(risStudy.getChecker()).setCheckTime(risStudy.getCheckTime()).setApplyNumber(risStudy.getApplyNumber())
                    .setApplyDepartment(risStudy.getApplyDepartment()).setApplyDoctor(risStudy.getApplyDoctor()).setArtificer(risStudy.getArtificer()).setIsonline(1)
                    .setIsExport(isExport).setAetId(risStudy.getAetId()).setInPatientNo(risStudy.getOutPatientNo()).setOutPatientNo(risStudy.getInPatientNo());
            caseStudyList.add(study);
        }
        for(RISStudy risStudy : risStudyList){
            dealWithDicomInfo(risStudy.getStudyUid(),caseId);
        }
        studyService.saveOrUpdateBatch(caseStudyList);
    }

    private void vertifyRisCase(AddRISCaseReq addRisCaseReq){
        List<RISStudy> risStudyList = addRisCaseReq.getRisStudyList();
        if(risStudyList.isEmpty()){
            throw new BusinessException(LocaleUtil.getLocale("operate.fail"));
        }
        //如果是非随访库（科室库有具体的科室病例类型id），那单次导入的病例必须属于单个病人
        if(risStudyList.size()>1){
            HashSet<String> patientSet = new HashSet<>();
            for(RISStudy risStudy : risStudyList){
                patientSet.add(risStudy.getPatientId());
            }
            if(patientSet.size()>1){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }
    }

    private void vertifyUploadCase(AddUploadCaseReq addUploadCaseReq){
        List<AddUploadCaseReq.StudyInfo> studyInfoList = addUploadCaseReq.getStudyInfoList();
        if(studyInfoList.isEmpty()){
            throw new BusinessException(LocaleUtil.getLocale("operate.fail"));
        }
        //如果是非随访库（科室库有具体的科室病例类型id），那单次导入的病例必须属于单个病人
        if(studyInfoList.size()>1){
            HashSet<String> patientSet = new HashSet<>();
            for(AddUploadCaseReq.StudyInfo studyInfo : studyInfoList){
                patientSet.add(studyInfo.getPatientId());
            }
            if(patientSet.size()>1){
                throw new BusinessException(LocaleUtil.getLocale("study.not.belongs.one.patient"));
            }
        }
    }

}
