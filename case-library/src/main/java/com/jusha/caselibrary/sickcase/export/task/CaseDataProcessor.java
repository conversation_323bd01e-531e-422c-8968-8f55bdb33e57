package com.jusha.caselibrary.sickcase.export.task;

import java.util.List;

/**
 * @ClassName CaseDataProcessor
 * @Description 病例数据处理器接口 - 专门处理数据转换
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
public interface CaseDataProcessor<R> {
    
    /**
     * 处理病例数据转换
     * 
     * @param rawDataList 原始数据列表
     * @return 处理后的数据列表
     */
    List<R> processData(List<R> rawDataList);
    
    /**
     * 清洗和格式化单个数据项
     * 
     * @param data 单个数据项
     * @return 清洗后的数据项
     */
    R cleanAndFormatData(R data);
    
    /**
     * 验证数据有效性
     * 
     * @param data 待验证的数据
     * @return 是否有效
     */
    default boolean validateData(R data) {
        return data != null;
    }
    
    /**
     * 获取处理器支持的数据类型
     * 
     * @return 数据类型的Class对象
     */
    Class<R> getSupportedDataType();
}