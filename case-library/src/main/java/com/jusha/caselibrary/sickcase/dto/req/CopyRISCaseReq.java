package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 从RIS导入
 * @date 2025/7/15
 */

@ApiModel
@Data
@Valid
public class CopyRISCaseReq {

    @ApiModelProperty("病例id")
    private long caseId;

    @ApiModelProperty("目的地，个人病例库1，科室病例库2")
    private int targetLibrary;

    @ApiModelProperty("病例库id，个人病例库收藏夹id，科室病例库id")
    private Long targetId;

    @ApiModelProperty("如果是教学病例库，还需要有疾病id")
    private Long diseaseId;
}
