package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName CollectCaseReq
 * @Description 收藏病例请求实体
 * <AUTHOR>
 * @Date 2025/7/28 08:59
 **/
@Data
public class CollectCaseReq {

    @ApiModelProperty(value = "病例Id")
    @NotNull(message = "病例Id不能为空")
    private Long caseId;

    @ApiModelProperty(value = "目录ID")
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;

    @ApiModelProperty(value = "标签ID列表")
    private List<Long> tagIdList;

}
