package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @description 检查请求类
 * @date 2025/7/12
 */

@ApiModel
@Data
public class RISStudyReq {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("申请单号")
    private String applyNo;

    @ApiModelProperty("检查开始时间")
    private String studyStartTime;

    @ApiModelProperty("检查结束时间")
    private String studyEndTime;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("检查类型s")
    private List<String> studyTypes;

}
