package com.jusha.caselibrary.sickcase.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName DeptCaseSearchReq
 * @Description 科室病例库列表请求实体
 * <AUTHOR>
 * @Date 2025/7/10 10:09
 **/
@Data
public class DeptCaseSearchReq {

    @ApiModelProperty(value = "病例Id", hidden = true)
    private Long caseId;

    @ApiModelProperty(value = "用户Id", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "审核ID", hidden = true)
    private Long auditId;

    @ApiModelProperty(value = "检查开始时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyStartTime;

    @ApiModelProperty(value = "检查结束时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyEndTime;

    @ApiModelProperty(value = "病例库类型Id")
    @NotNull(message = "病例库类型Id不能为空")
    private Long caseTypeId;

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    /**
     * 疾病名称（科室病例库）
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "检查类型")
    private String modality;

    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ApiModelProperty(value = "患者性别：男、女")
    private String patientSex;

    @ApiModelProperty(value = "随访类型：手术，超声，临床，病理")
    private String followType;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "关键字: 患者姓名、影像号、住院号、门诊号、" +
            "影像学表现、影像学诊断、随访结果、病例分析等")
    private String keyword;

    @ApiModelProperty(value = "第几页")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "导出字段列表，为空时导出所有字段")
    private List<String> exportFields;
}
