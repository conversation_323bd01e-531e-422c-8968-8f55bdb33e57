package com.jusha.caselibrary.sickcase.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.mybatisplus.entity.StudyRecord;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.*;

import java.util.List;

/**
 * @ClassName PersonalCaseService
 * @Description 新增病例服务接口，含RIS导入和本地上传（供科室和个人病例库调用）
 * <AUTHOR>
 * @Date 2025/7/14 10:15
 **/
public interface CaseAddService {

    /**
     * @description 新增病例之前需要看一下是否已存在对应病例
     * <AUTHOR>
     * @date 2025/7/16 11:14
     * @param queryCaseExistReq
     * @return DeptCaseDetailResp
     **/
    List<AuditCaseDetailResp> queryCaseExist(QueryCaseExistReq queryCaseExistReq);

    /**
     * @description 从RIS导入检查到病例库
     * <AUTHOR>
     * @date 2025/7/14 21:10
     * @param addRisCaseReq
     * @return boolean
     **/
    AddRISCaseResp addCaseFromRis(AddRISCaseReq addRisCaseReq);


    /**
     * @description PACS回调：通知有Dicom检查入库
     * <AUTHOR>
     * @date 2025/7/21 21:11
     * @param studyInstanceUID,userId
     */
    void dicomNotify(String studyInstanceUID,Long userId);

    /**
     * @description 查询所有未关联病例的影像
     * <AUTHOR>
     * @date 2025/7/22 20:31
     * @param notRelateDicomReq
     */
    PageInfo<StudyRecord> dicomNotRelate(NotRelateDicomReq notRelateDicomReq);

    /**
     * @description 从本地上传导入检查到病例库
     * <AUTHOR>
     * @date 2025/7/23 10:44
     * @param addCaseFromUpload
     * @return boolean
     **/
    AddUploadCaseResp addCaseFromUpload(AddUploadCaseReq addCaseFromUpload);

    /**
     * @description 从RIS导入检查到病例库
     * <AUTHOR>
     * @date 2025/7/29 19:35
     * @param
     * @return boolean
     **/
    long addCaseFromFollow(AddFollowCaseReq addFollowCaseReq);

    /**
     * @description 已存在的病例添加检查
     * <AUTHOR>
     * @date 2025/7/30 10:22
     * @param addStudyInfoReq
     * @return boolean
     **/
    long addStudyInfoRIS(AddStudyInfoRISReq addStudyInfoReq);

    /**
     * @description 已存在的病例添加检查（从上传影像）
     * <AUTHOR>
     * @date 2025/8/8 09:28
     * @param addStudyInfoUploadReq
     * @return boolean
     **/
    long addStudyInfoUpload(AddStudyInfoUploadReq addStudyInfoUploadReq);

}
