package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PersonalCaseExportStrategy
 * @Description 个人病例导出策略实现 - 返回PersonalCaseDetailResp类型
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Slf4j
public class PersonalCaseExportStrategy implements CaseExportStrategy<PersonalCaseSearchReq, PersonalCaseDetailResp> {
    
    private final PersonalCaseDataProcessor dataProcessor;
    
    public PersonalCaseExportStrategy() {
        this.dataProcessor = new PersonalCaseDataProcessor();
    }
    
    @Override
    public List<PersonalCaseDetailResp> queryCaseDetailList(PersonalCaseSearchReq searchRequest) {
        try {
            // 验证请求参数
            validateRequest(searchRequest);
            
            // 获取个人病例服务实例
            PersonalCaseService personalCaseService = ContextHolder.getBean(PersonalCaseService.class);
            
            // 查询个人病例详情列表
            List<PersonalCaseDetailResp> personalCaseList = personalCaseService.getPersonalCaseDetailList(searchRequest);
            
            if (personalCaseList == null || personalCaseList.isEmpty()) {
                log.info("个人病例查询结果为空，查询条件: {}", searchRequest);
                return new ArrayList<>();
            }
            
            // 使用数据处理器处理数据
            List<PersonalCaseDetailResp> processedList = dataProcessor.processData(personalCaseList);
            
            log.info("个人病例查询成功，共查询到 {} 条记录", processedList.size());
            return processedList;
            
        } catch (Exception e) {
            log.error("查询个人病例详情失败，查询条件: {}, 错误信息: {}", searchRequest, e.getMessage(), e);
            throw new BusinessException(LocaleUtil.getLocale("case.query.failed"));
        }
    }
    
    @Override
    public ExportType getSupportedExportType() {
        return ExportType.PERSONAL_CASE;
    }
    
    @Override
    public Class<PersonalCaseDetailResp> getReturnDataType() {
        return PersonalCaseDetailResp.class;
    }
    
    @Override
    public void validateRequest(PersonalCaseSearchReq searchRequest) {
        // 调用父接口的默认验证
        CaseExportStrategy.super.validateRequest(searchRequest);
        
        // 个人病例特定的验证逻辑
        if (searchRequest.getPageNum() != null && searchRequest.getPageNum() < 1) {
            throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
        }
        
        if (searchRequest.getPageSize() != null && searchRequest.getPageSize() < 1) {
            throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
        }
        
        log.debug("个人病例导出请求参数验证通过: {}", searchRequest);
    }
}