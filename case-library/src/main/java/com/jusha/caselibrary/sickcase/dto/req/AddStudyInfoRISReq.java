package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 已存在的病例添加检查
 * @date 2025/7/30
 */

@ApiModel
@Data
@Valid
public class AddStudyInfoRISReq {

    @ApiModelProperty("病例库id")
    private Long caseTypeId;

    @ApiModelProperty("病例id")
    private Long caseId;

    @ApiModelProperty("从RIS查询出来的参数")
    private List<RISStudy> risStudyList;

}