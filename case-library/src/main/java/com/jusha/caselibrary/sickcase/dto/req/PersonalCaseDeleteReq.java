package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName PersonalCaseDeleteReq
 * @Description 个人病例库删除请求实体
 * <AUTHOR>
 * @Date 2025/7/21 15:26
 **/
@Data
public class PersonalCaseDeleteReq {

    @ApiModelProperty(value = "个人病例Id")
    @NotNull(message = "个人病例Id不能为空")
    private Long userCaseId;

    @ApiModelProperty(value = "目录Id")
    @NotNull(message = "目录Id不能为空")
    private Long catalogId;
}
