package com.jusha.caselibrary.sickcase.schedule;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.search.service.ESSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName CaseDataSyncSchedule
 * @Description 病例数据同步定时任务
 * <AUTHOR>
 * @Date 2025/7/25 14:08
 **/
@Slf4j
@Component
public class CaseDataSyncSchedule {

    /**
     * 每天凌晨1点定时增量同步科室及个人病例数据
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void fullSyncCaseData() {
        log.info("开始增量同步前一天的病例数据");
        ESSyncService esSyncService = ContextHolder.getBean(ESSyncService.class);
        // 调用ES同步服务进行增量同步
        String startTime = DateUtil.convertDateToStr(DateUtil.getSomeDayDate(new Date(), -1), DateUtil.DEFAULT_PATTERN);
        String endTime = DateUtil.convertDateToStr(new Date(), DateUtil.DEFAULT_PATTERN);
        esSyncService.incrementalSyncDepartmentCases(startTime, endTime);
        esSyncService.incrementalSyncPersonalCases(startTime, endTime);
        log.info("增量同步病例数据完成");
    }
}
