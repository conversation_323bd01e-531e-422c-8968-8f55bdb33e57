package com.jusha.caselibrary.sickcase.controller;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.aop.NoDuplicate;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.entity.StudyRecord;
import com.jusha.caselibrary.sickcase.dto.req.*;
import com.jusha.caselibrary.sickcase.dto.resp.*;
import com.jusha.caselibrary.sickcase.service.CaseAddService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName CaseAddController
 * @Description 病例新增管理
 * <AUTHOR>
 * @Date 2025/7/10 09:27
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/case")
@Api(tags = "病例管理-本地及RIS对接导入")
@Validated
public class CaseAddController {

    private final CaseAddService caseAddService;

    @ApiOperation("新增病例之前需要看一下是否已存在对应病例")
    @PostMapping(value = "/queryCaseExist")
        public ResultBean<List<AuditCaseDetailResp>> queryCaseExist(@Valid @RequestBody QueryCaseExistReq queryCaseExistReq) {
        return ResultBean.success(caseAddService.queryCaseExist(queryCaseExistReq));
    }

    @ApiOperation("已存在的病例添加检查（影像）————也是RIS合并检查接口")
    @PostMapping(value = "/addStudyInfo")
    @NoDuplicate(keys = {"#addStudyInfoReq.caseId"}, waitTag = 2)
    public ResultBean<Long> addStudyInfo(@RequestBody AddStudyInfoRISReq addStudyInfoReq) {
        return ResultBean.success(caseAddService.addStudyInfoRIS(addStudyInfoReq));
    }

    @ApiOperation("已存在的病例添加检查（影像）————也是上传影像合并检查接口")
    @PostMapping(value = "/addStudyInfoUpload")
    @NoDuplicate(keys = {"#addStudyInfoReq.caseId"}, waitTag = 2)
    public ResultBean<Long> addStudyInfoUpload(@RequestBody AddStudyInfoUploadReq addStudyInfoReq) {
        return ResultBean.success(caseAddService.addStudyInfoUpload(addStudyInfoReq));
    }

    @ApiOperation("将RIS中检查导入病例库")
    @PostMapping(value = "/addCaseFromRIS")
    @NoDuplicate(keys = {"#addRisCaseReq.targetId"}, waitTag = 2)
    public ResultBean<AddRISCaseResp> addCaseFromRis(@RequestBody AddRISCaseReq addRisCaseReq) {
        AddRISCaseResp addRisCaseResp = caseAddService.addCaseFromRis(addRisCaseReq);
        return ResultBean.success(addRisCaseResp);
    }

    /**
     * PACS回调：通知有Dicom序列入库
     */
    @PostMapping(value = "/dicom/notify/forbid")
    @NoDuplicate(keys = {"#studyInstanceUID"}, waitTag = 2)
    public ResultBean<Void> dicomNotify(@RequestParam String studyInstanceUID,@RequestParam Long userId) {
        caseAddService.dicomNotify(studyInstanceUID,userId);
        return ResultBean.success();
    }

    @ApiOperation("查询当前账号上传的所有未关联的记录（未关联）")
    @GetMapping(value = "/dicom/notRelate")
    public ResultBean<PageInfo<StudyRecord>> dicomNotRelate(NotRelateDicomReq notRelateDicomReq) {
        return ResultBean.success(caseAddService.dicomNotRelate(notRelateDicomReq));
    }

    @ApiOperation("根据影像新建病例")
    @PostMapping(value = "/addCaseFromUpload")
    @NoDuplicate(keys = {"#addUploadCaseReq.targetId"}, waitTag = 2)
    public ResultBean<AddUploadCaseResp> addCaseFromUpload(@RequestBody AddUploadCaseReq addUploadCaseReq) {
        AddUploadCaseResp addUploadCaseResp = caseAddService.addCaseFromUpload(addUploadCaseReq);
        return ResultBean.success(addUploadCaseResp);
    }

    @ApiOperation("从随访库导入")
    @PostMapping(value = "/addCaseFromFollow")
    @NoDuplicate(keys = {"#addFollowCaseReq.caseTypeId"}, waitTag = 2)
    public ResultBean<Long> addCaseFromFollow(@RequestBody AddFollowCaseReq addFollowCaseReq) {
        return ResultBean.success(caseAddService.addCaseFromFollow(addFollowCaseReq));
    }
}
