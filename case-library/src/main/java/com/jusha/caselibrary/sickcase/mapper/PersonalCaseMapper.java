package com.jusha.caselibrary.sickcase.mapper;

import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName PersonalCaseMapper
 * @Description 个人病例Mapper
 * <AUTHOR>
 * @Date 2025/7/10 09:34
 **/
@Mapper
public interface PersonalCaseMapper {

    /**
     * @description 个人病例库id列表
     * <AUTHOR>
     * @date 2025/8/5 20:38
     * @param req
     * @return List<Long>
     **/
    List<Long> getPersonalCaseIdList(PersonalCaseSearchReq req);

    /**
     * @param req
     * @return List<PersonalCaseDetailResp>
     * @description 个人病例库详细列表
     * <AUTHOR>
     * @date 2025/7/11 15:29
     **/
    List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req);
}
