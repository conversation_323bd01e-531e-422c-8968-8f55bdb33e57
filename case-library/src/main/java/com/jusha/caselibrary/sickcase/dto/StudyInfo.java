package com.jusha.caselibrary.sickcase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.DictConvertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName StudyInfo
 * @Description 报告信息
 * <AUTHOR>
 * @Date 2025/7/8 17:25
 **/
@Data
public class StudyInfo {

    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ApiModelProperty(value = "检查流水号/影像号")
    private String accessNumber;

    @ApiModelProperty(value = "检查时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime studyTime;

    @ApiModelProperty(value = "RIS中的检查号")
    private String studyNo;

    @ApiModelProperty(value = "定性匹配")
    private String qualityMatch;

    @ApiModelProperty(value = "定性匹配标签")
    private String qualityMatchLabel;

    @ApiModelProperty(value = "定位匹配")
    private String positionMatch;

    @ApiModelProperty(value = "定位匹配标签")
    private String positionMatchLabel;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ApiModelProperty(value = "患者出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "就诊类型（门诊、住院、体检）")
    private String patientType;

    @ApiModelProperty(value = "就诊类型标签")
    private String patientTypeLabel;

    @ApiModelProperty(value = "就诊日期")
    private String visitDate;

    @ApiModelProperty(value = "门诊号")
    private String outPatientNo;

    @ApiModelProperty(value = "住院号")
    private String inPatientNo;

    @ApiModelProperty(value = "症状/体征")
    private String physicalSign;

    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty(value = "检查项目名称")
    private String studyItemName;

    @ApiModelProperty(value = "检查部位名称")
    private String partName;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "检查状态")
    private String studyState;

    @ApiModelProperty(value = "检查状态标签")
    private String studyStateLabel;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "病史")
    private String medicalHistory;

    @ApiModelProperty(value = "患者主诉")
    private String selfReportedSymptom;

    @ApiModelProperty(value = "影像学表现")
    private String reportDescribe;

    @ApiModelProperty(value = "影像学诊断")
    private String reportDiagnose;

    @ApiModelProperty(value = "阳性/阴性")
    private String isPostive;

    @ApiModelProperty(value = "阳性/阴性标签")
    private String isPostiveLabel;

    @ApiModelProperty(value = "登记时间")
    private String registerTime;

    @ApiModelProperty(value = "报告时间")
    private String reportTime;

    @ApiModelProperty(value = "报告人")
    private String reporter;

    @ApiModelProperty(value = "审核人")
    private String checker;

    @ApiModelProperty(value = "审核时间")
    private String checkTime;

    @ApiModelProperty(value = "申请号")
    private String applyNumber;

    @ApiModelProperty(value = "申请科室")
    private String applyDepartment;

    @ApiModelProperty(value = "申请医生")
    private String applyDoctor;

    @ApiModelProperty(value = "技师")
    private String artificer;

    @ApiModelProperty(value = "是否公有")
    private String isPublic;

    @ApiModelProperty(value = "是否公有标签")
    private String isPublicLabel;

    @ApiModelProperty(value = "导入状态，0无需导入，1正在导入，2导入完成，3导入失败")
    private int isExport;

    @ApiModelProperty(value = "来源AET的id")
    private int aetId;

    /**
     * 序列信息
     */
    @ApiModelProperty(value = "检查模态列表")
    private Set<String> modalities;

    // 关联的随访信息
    @ApiModelProperty(value = "随访信息")
    private List<FollowInfo> followInfoList;


    public void handleData() {
        // 处理StudyInfoDto中的字典字段
        this.setPatientTypeLabel(DictConvertUtil.convertPatientType(this.getPatientType()));
        this.setStudyStateLabel(DictConvertUtil.convertStudyState(this.getStudyState()));
        this.setIsPostiveLabel(DictConvertUtil.convertIsPositive(this.getIsPostive()));
        this.setIsPublicLabel(DictConvertUtil.convertIsPublic(this.getIsPublic()));
        this.setQualityMatchLabel(DictConvertUtil.convertQualityMatch(this.getQualityMatch()));
        this.setPositionMatchLabel(DictConvertUtil.convertPositionMatch(this.getPositionMatch()));
        if (CollectionUtils.isNotEmpty(this.followInfoList)) {
            this.setFollowInfoList(this.getFollowInfoList().stream()
                    .peek(follow -> {
                        follow.setFollowTypeLabel(DictConvertUtil.convertFollowType(follow.getFollowType()));
                    })
                    .collect(Collectors.toList()));
        }
        if (StringUtils.hasText(this.getDeviceType())) {
            Set<String> modalities = new HashSet<>();
            modalities.add(this.getDeviceType());
            this.setModalities(modalities);
        }
    }
}
