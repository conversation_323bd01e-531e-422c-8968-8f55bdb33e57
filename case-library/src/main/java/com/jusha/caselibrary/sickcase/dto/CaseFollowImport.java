package com.jusha.caselibrary.sickcase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName CaseFollowImport
 * @Description 导入病例随访请求实体
 * <AUTHOR>
 * @Date 2025/7/18 10:34
 **/
@Data
public class CaseFollowImport {

    @ApiModelProperty(value = "no")
    private Integer no;

    @ApiModelProperty(value = "检查流水号/影像号")
    private String accessNumber;

    @ApiModelProperty(value = "随访类型")
    private String followType = String.valueOf(1);

    @ApiModelProperty(value = "随访结果")
    private String followupResult;

}
