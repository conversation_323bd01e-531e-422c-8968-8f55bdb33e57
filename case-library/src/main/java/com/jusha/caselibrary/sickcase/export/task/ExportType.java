package com.jusha.caselibrary.sickcase.export.task;

import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import lombok.Getter;

/**
 * @ClassName ExportType
 * @Description 导出类型枚举
 * <AUTHOR>
 * @Date 2025/7/16 10:00
 **/
@Getter
public enum ExportType {
    
    /**
     * 科室病例导出
     */
    DEPT_CASE("DEPT_CASE", "科室病例导出"),
    
    /**
     * 个人病例导出
     */
    PERSONAL_CASE("PERSONAL_CASE", "个人病例导出");
    
    private final String code;
    private final String description;
    
    ExportType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据请求类型判断导出类型
     */
    public static ExportType fromRequestType(Object searchRequest) {
        if (searchRequest instanceof DeptCaseSearchReq) {
            return DEPT_CASE;
        } else if (searchRequest instanceof PersonalCaseSearchReq) {
            return PERSONAL_CASE;
        }
        throw new IllegalArgumentException("不支持的请求类型: " + searchRequest.getClass().getName());
    }
}