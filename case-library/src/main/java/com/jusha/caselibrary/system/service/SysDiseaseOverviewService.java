package com.jusha.caselibrary.system.service;

import com.jusha.caselibrary.mybatisplus.system.entity.DiseaseOverview;
import com.jusha.caselibrary.system.dto.req.DiseaseOverviewReq;
import com.jusha.caselibrary.system.dto.resp.DiseaseOverviewResp;

/**
 * 疾病概述管理 服务层
 *
 * <AUTHOR>
 */
public interface SysDiseaseOverviewService {

    /**
     * 根据疾病ID概述查询信息
     *
     * @param diseaseId 疾病概述ID
     * @return 疾病信息
     */
    public DiseaseOverviewResp selectDiseaseById(Long diseaseId);

    /**
     * 新增或者修改疾病概述信息
     *
     * @param diseaseOverviewReq 疾病概述信息
     * @return 结果
     */
    public DiseaseOverview editDiseaseOverview(DiseaseOverviewReq diseaseOverviewReq);

    /**
     * 删除疾病概述信息
     *
     * @param diseaseOverviewId 疾病概述ID
     * @return 结果
     */
    public boolean deleteDiseaseById(Long diseaseOverviewId);
}
