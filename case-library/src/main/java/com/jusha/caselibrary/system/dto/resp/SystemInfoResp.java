package com.jusha.caselibrary.system.dto.resp;

import com.jusha.caselibrary.mybatisplus.system.entity.AetTemplate;
import com.jusha.caselibrary.mybatisplus.system.entity.SearchAll;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
public class SystemInfoResp {

    @ApiModelProperty(value = "AET配置")
    private List<AetTemplate> AetInfo;

    @ApiModelProperty(value = "360配置")
    private List<SearchAll> searchAllInfo;

    @ApiModelProperty(value = "病例合并配置，0则合并，1自行决定合并不合并(到后端的话，0合并1新建)")
    private int caseCombine;

    @ApiModelProperty(value = "病理导入模版")
    private String followImportTemp;

    @ApiModelProperty(value = "minio的域名")
    private String endpoint;
}
