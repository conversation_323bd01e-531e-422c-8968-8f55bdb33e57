package com.jusha.caselibrary.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.resp.TreeSelect;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.system.entity.Disease;
import com.jusha.caselibrary.mybatisplus.system.mapper.DiseaseMapper;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseService;
import com.jusha.caselibrary.system.dto.req.DiseaseReq;
import com.jusha.caselibrary.system.dto.req.DiseaseUpdateReq;
import com.jusha.caselibrary.system.service.SysDiseaseService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 疾病树管理模块 服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysDiseaseServiceImpl implements SysDiseaseService {
    private static final Logger log = LoggerFactory.getLogger(SysDiseaseServiceImpl.class);

    private final DiseaseService sysDiseaseService;

    private final DiseaseMapper diseaseMapper;

    /**
     * 查询疾病数据
     *
     * @param diseaseReq 疾病信息查询入参
     * @return 疾病信息集合
     */
    @Override
    public List<Disease> selectDiseaseList(DiseaseReq diseaseReq) {
        LambdaQueryChainWrapper<Disease> wrapper = sysDiseaseService.lambdaQuery();
        if (diseaseReq.getDiseaseName() != null) {
            wrapper.like(Disease::getDiseaseName, diseaseReq.getDiseaseName());
        }
        if (diseaseReq.getDiseaseId() != null) {
            wrapper.eq(Disease::getDiseaseId, diseaseReq.getDiseaseId());
        }
        if (diseaseReq.getParentId() != null) {
            wrapper.eq(Disease::getParentId, diseaseReq.getParentId());
        }
        if(diseaseReq.getAncestors()!=null){
            //根据顶层疾病来查询树，加上顶层
            wrapper.apply( "FIND_IN_SET ( '"+ diseaseReq.getAncestors() +"',ancestors )");
            wrapper.or().eq(Disease::getDiseaseId,diseaseReq.getAncestors());
        }
        return wrapper.orderByAsc(Disease::getOrderNum).orderByDesc(Disease::getCreateTime).list();
    }

    /**
     * 根据疾病ID查询信息
     *
     * @param diseaseId 疾病ID
     * @return 疾病信息
     */
    @Override
    public Disease selectDiseaseById(Long diseaseId) {
        return sysDiseaseService.getById(diseaseId);
    }

    /**
     * 新增保存疾病信息
     *
     * @param disease 疾病信息
     * @return 结果
     */
    @Override
    public Disease insertDisease(Disease disease) {
        long diseaseId = YitIdHelper.nextId();
        disease.setDiseaseId(diseaseId);
        disease.setCreateTime(new Date());
        disease.setCreatedBy(LoginUtil.getLoginUserId());
        Disease info = sysDiseaseService.getById(disease.getParentId());
        if (info == null) {
            disease.setAncestors("0");
            sysDiseaseService.save(disease);
            return sysDiseaseService.getById(diseaseId);
        }
        disease.setAncestors(info.getAncestors() + Constant.SEPARATOR + disease.getParentId());
        sysDiseaseService.save(disease);
        return sysDiseaseService.getById(diseaseId);
    }

    /**
     * 查询疾病树结构信息
     *
     * @param diseaseReq 疾病信息查询入参
     * @return 疾病树信息集合
     */
    @Override
    public List<TreeSelect> selectDiseaseTreeList(DiseaseReq diseaseReq) {
        List<Disease> diseases = selectDiseaseList(diseaseReq);
        return buildDiseaseTreeSelect(diseases);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param diseases 疾病列表
     * @return 树结构列表
     */
    @Override
    public List<Disease> buildDiseaseTree(List<Disease> diseases) {
        List<Disease> returnList = new ArrayList<>();
        List<Long> tempList = diseases.stream().map(Disease::getDiseaseId).collect(Collectors.toList());
        for (Disease disease : diseases) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(disease.getParentId())) {
                recursionFn(diseases, disease);
                returnList.add(disease);
            }
        }
        if (returnList.isEmpty()) {
            returnList = diseases;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param diseases 疾病列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDiseaseTreeSelect(List<Disease> diseases) {
        List<Disease> diseaseTrees = buildDiseaseTree(diseases);
        return diseaseTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 是否存在子节点
     *
     * @param diseaseId 疾病ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDiseaseId(Long diseaseId) {
        long result = sysDiseaseService.lambdaQuery().eq(Disease::getParentId, diseaseId).count();
        return result > Constant.ZERO_LONG;
    }

    /**
     * 校验疾病名称是否唯一
     *
     * @param disease 疾病信息
     * @return 结果
     */
    @Override
    public boolean checkDiseaseNameUnique(Disease disease) {
        Long diseaseId = disease.getDiseaseId()==null ? Constant.ALL_MINUS1L : disease.getDiseaseId();
        List<Disease> diseaseInfos = sysDiseaseService.lambdaQuery()
                .eq(Disease::getDiseaseName, disease.getDiseaseName())
                .eq(Disease::getParentId, disease.getParentId()).list();
        if (!diseaseInfos.isEmpty() && diseaseInfos.get(0).getDiseaseId().longValue() != diseaseId.longValue()) {
            return Constant.NOT_UNIQUE;
        }
        return Constant.UNIQUE;
    }

    /**
     * 修改保存疾病信息
     *
     * @param diseaseUpdateReq 疾病信息
     * @return 结果
     */
    @Override
    public ResultBean updateDisease(DiseaseUpdateReq diseaseUpdateReq) {
        if(diseaseUpdateReq.getDiseaseId() == null){
            return ResultBean.error();
        }
        Disease disease = sysDiseaseService.getById(diseaseUpdateReq.getDiseaseId());
        if(disease == null){
            return ResultBean.error();
        }
        if(diseaseUpdateReq.getDiseaseName()!=null){
            disease.setDiseaseName(diseaseUpdateReq.getDiseaseName());
        }
        if(diseaseUpdateReq.getParentId()!=null){
            disease.setParentId(diseaseUpdateReq.getParentId());
        }
        if(diseaseUpdateReq.getOrderNum()!=null){
            disease.setOrderNum(diseaseUpdateReq.getOrderNum());
        }
        if (!checkDiseaseNameUnique(disease)) {
            //验证名称唯一性
            return ResultBean.error(LocaleUtil.getLocale("catalog.name.already.exist"));
        }
        if (diseaseUpdateReq.getParentId()!=null) {
            if(diseaseUpdateReq.getParentId().equals(disease.getDiseaseId())){
                //不可以把自己作为父分組
                return ResultBean.error(LocaleUtil.getLocale("catalog.parent.self"));
            }
            Disease newParentDisease = sysDiseaseService.lambdaQuery().eq(Disease::getDiseaseId, disease.getParentId()).one();
            Disease oldDisease = sysDiseaseService.lambdaQuery().eq(Disease::getDiseaseId, disease.getDiseaseId()).one();
            if (newParentDisease!=null && oldDisease!=null) {
                String newAncestors = newParentDisease.getAncestors() + "," + newParentDisease.getDiseaseId();
                String oldAncestors = oldDisease.getAncestors();
                disease.setAncestors(newAncestors);
                updateDiseaseChildren(disease.getDiseaseId(), newAncestors, oldAncestors);
            }
        }
        disease.setUpdateBy(LoginUtil.getLoginUserId());
        disease.setUpdateTime(new Date());

        boolean result = sysDiseaseService.updateById(disease);
        if (!result) {
            return ResultBean.error();
        }
        return ResultBean.success();
    }

    /**
     * 修改子元素关系
     *
     * @param diseaseId    被修改的疾病ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDiseaseChildren(Long diseaseId, String newAncestors, String oldAncestors) {
        List<Disease> children = diseaseMapper.selectChildrenDiseaseById(diseaseId);
        for (Disease child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            diseaseMapper.updateDiseaseChildren(children);
        }
    }

    /**
     * 删除疾病信息
     *
     * @param diseaseId 疾病ID
     * @return 结果
     */
    @Override
    public boolean deleteDiseaseById(Long diseaseId) {
        return sysDiseaseService.lambdaUpdate()
                .set(Disease::getUpdateBy, LoginUtil.getLoginUserId())
                .set(Disease::getUpdateTime, new Date())
                .set(Disease::getDelFlag, Constant.DELETE_FLAG)
                .eq(Disease::getDiseaseId, diseaseId).update();
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<Disease> list, Disease t) {
        // 得到子节点列表
        List<Disease> childList = getChildList(list, t);
        t.setChildren(childList);
        for (Disease tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<Disease> getChildList(List<Disease> list, Disease t) {
        List<Disease> tlist = new ArrayList<>();
        Iterator<Disease> it = list.iterator();
        while (it.hasNext()) {
            Disease n = it.next();
            if (n.getParentId()!=null && n.getParentId().longValue() == t.getDiseaseId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<Disease> list, Disease t) {
        return !getChildList(list, t).isEmpty();
    }
}