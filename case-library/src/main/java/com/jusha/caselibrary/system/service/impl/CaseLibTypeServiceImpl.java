package com.jusha.caselibrary.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.feign.api.AuthServerApi;
import com.jusha.caselibrary.feign.dto.req.SysMenu;
import com.jusha.caselibrary.mybatisplus.entity.DepCaseType;
import com.jusha.caselibrary.mybatisplus.service.DepCaseTypeService;
import com.jusha.caselibrary.system.dto.SysRole;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeSaveReq;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeUpdateReq;
import com.jusha.caselibrary.system.dto.resp.CaseLibTypeResp;
import com.jusha.caselibrary.system.service.CaseLibTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CaseLibTypeServiceImpl
 * @Description 病例库类型管理服务实现类
 * <AUTHOR>
 * @Date 2025/7/3 16:28
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseLibTypeServiceImpl implements CaseLibTypeService {

    private final DepCaseTypeService depCaseTypeService;
    private final AuthServerApi authServerApi;

    @Override
    public List<CaseLibTypeResp> getCaseLibTypeList() {
        List<DepCaseType> depCaseTypeList = depCaseTypeService.list();
        if (CollectionUtils.isNotEmpty(depCaseTypeList)) {
            // 转换为CaseLibTypeResp列表
            return depCaseTypeList.stream()
                    .filter(depCaseType -> !depCaseType.getCaseTypeId()
                            .equals(Constant.DEP_FOLLOW_CASE_TYPE_ID) && !depCaseType.getCaseTypeId()
                            .equals(Constant.DEP_TEACH_CASE_TYPE_ID))
                    .map(depCaseType -> {
                        CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
                        BeanUtils.copyProperties(depCaseType, caseLibTypeResp);
                        return caseLibTypeResp;
                    }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public PageInfo<CaseLibTypeResp> getCaseLibTypePage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<DepCaseType> depCaseTypeList = depCaseTypeService.list();
        PageInfo<DepCaseType> orgPpageInfo = new PageInfo<>(depCaseTypeList);
        if (CollectionUtils.isNotEmpty(depCaseTypeList)) {
            // 转换为CaseLibTypeResp列表
            List<CaseLibTypeResp> caseLibTypeRespList = depCaseTypeList.stream()
                    .filter(depCaseType -> !depCaseType.getCaseTypeId()
                            .equals(Constant.DEP_FOLLOW_CASE_TYPE_ID) && !depCaseType.getCaseTypeId()
                            .equals(Constant.DEP_TEACH_CASE_TYPE_ID))
                    .map(depCaseType -> {
                        CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
                        BeanUtils.copyProperties(depCaseType, caseLibTypeResp);
                        return caseLibTypeResp;
                    }).collect(Collectors.toList());
            PageInfo<CaseLibTypeResp> pageInfo = new PageInfo<>(caseLibTypeRespList);
            BeanUtils.copyProperties(orgPpageInfo, pageInfo, "list");
            return pageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCaseLibType(CaseLibTypeSaveReq req) {
        //检测名称参数不可重复
        if (depCaseTypeService.lambdaQuery().eq(DepCaseType::getCaseTypeName, req.getCaseTypeName()).count() > 0) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.name.already.exist"));
        }
        Long loginUserId = LoginUtil.getLoginUserId();
        //新增病例库类型
        DepCaseType depCaseType = new DepCaseType();
        depCaseType.setCaseTypeId(YitIdHelper.nextId());
        depCaseType.setCaseTypeName(req.getCaseTypeName());
        depCaseType.setAudit(req.getAudit());
        depCaseType.setCreateTime(new Date());
        depCaseType.setCreateBy(loginUserId);
        //调用权限系
        //1.获取科室病例库目录菜单
        List<SysMenu> sysMenuList = authServerApi.getMenuListByName(Constant.CASE_LIB_MENU_NAME).getData();
        if (CollectionUtils.isEmpty(sysMenuList)) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.menu.not.exist"));
        }
        SysMenu parentMenu = sysMenuList.stream().filter(s -> s.getParentId().equals(Constant.ROOT_MENU_ID)).findFirst()
                .orElseThrow(() -> new BusinessException(LocaleUtil.getLocale("case.lib.menu.not.exist")));
        //1.创建病例库分类子菜单
        SysMenu sysMenu = new SysMenu();
        sysMenu.setPlatId(ContextHolder.propertiesBean().getPlatId());
        sysMenu.setParentId(parentMenu.getMenuId());
        sysMenu.setMenuType(Constant.TYPE_MENU);
        sysMenu.setMenuName(req.getCaseTypeName());
        sysMenu.setOrderNum(0);
        //组装路径
        String path = chineseToPinyinWithCapitalization(req.getCaseTypeName());
        //如果存在同名路径，则在路径后加1，如path1、path2...
        Integer count = depCaseTypeService.lambdaQuery().likeRight(DepCaseType::getAddress, path).count();
        if (count > 0) {
            path = path + count;
        }
        depCaseType.setAddress(path);
        //菜单使用全路径
        String sysMenuPath = parentMenu.getPath() + "/" + path;
        sysMenu.setPath(sysMenuPath);
        sysMenu.setComponent(sysMenuPath);
        sysMenu.setVisible(Constant.NORMAL);
        SysMenu resultMenu = authServerApi.addMenu(sysMenu).getData();
        depCaseType.setMenuId(resultMenu.getMenuId());
        depCaseTypeService.save(depCaseType);
        //2.为病例库角色授权菜单
        List<SysRole> sysRoleList = authServerApi.roleList().getData();
        if (CollectionUtils.isNotEmpty(sysRoleList)) {
            for (SysRole sysRole : sysRoleList) {
                // 获取角色的菜单
                List<Long> menuIds = authServerApi.menuIdsByRoleId(sysRole.getRoleId()).getData();
                menuIds.add(resultMenu.getMenuId());
                sysRole.setMenuIds(menuIds.toArray(new Long[0]));
                authServerApi.editRole(sysRole);
            }
        }
    }


    /**
     * 将中文字符串转换为拼音，每个拼音单词首字母大写
     *
     * @param chinese 中文字符串
     * @return 拼音字符串，单词间用空格分隔，首字母大写
     */
    public String chineseToPinyinWithCapitalization(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }

        // 如果字符串只包含数字和小写字母，则直接返回
        if (chinese.matches("[0-9a-z]+")) {
            return chinese;
        }

        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
            format.setVCharType(HanyuPinyinVCharType.WITH_V);

            StringBuilder result = new StringBuilder();
            char[] chars = chinese.toCharArray();

            for (char c : chars) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    // 中文字符转拼音
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0]);
                    }
                } else if (Character.isLetter(c)) {
                    // 英文字符转小写
                    result.append(Character.toLowerCase(c));
                } else if (Character.isDigit(c)) {
                    // 数字直接添加
                    result.append(c);
                }
                // 其他字符忽略
            }
            return result.toString();
        } catch (Exception e) {
            log.error("中文转拼音失败: {}", chinese, e);
            return chinese.toLowerCase();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCaseLibType(Long caseTypeId) {
        //删除病例库类型
        DepCaseType depCaseType = depCaseTypeService.getById(caseTypeId);
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        depCaseTypeService.removeById(caseTypeId);
        //删除权限系统下的菜单
        authServerApi.removeMenu(depCaseType.getMenuId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCaseLibType(CaseLibTypeUpdateReq req) {
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        String path = null;
        // 修改病例库类型
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        if (req.getCaseTypeName() != null) {
            // 判断是否重复
            if (depCaseTypeService.lambdaQuery().eq(DepCaseType::getCaseTypeName, req.getCaseTypeName())
                    .ne(DepCaseType::getCaseTypeId, req.getCaseTypeId()).count() > 0) {
                throw new BusinessException(LocaleUtil.getLocale("case.lib.type.name.already.exist"));
            }
            depCaseType.setCaseTypeName(req.getCaseTypeName());
            // 获取新的地址
            path = chineseToPinyinWithCapitalization(req.getCaseTypeName());
            //如果存在同名路径，则在路径后加1，如path1、path2...
            Integer count = depCaseTypeService.lambdaQuery()
                    .ne(DepCaseType::getCaseTypeId, req.getCaseTypeId())
                    .likeRight(DepCaseType::getAddress, path).count();
            if (count > 0) {
                path = path + count;
            }
            depCaseType.setAddress(path);
        }
        if (req.getAudit() != null) {
            depCaseType.setAudit(req.getAudit());
        }
        depCaseTypeService.updateById(depCaseType);
        //修改权限系统下的菜单
        if (req.getCaseTypeName() != null) {
            SysMenu sysMenu = authServerApi.getMenuById(depCaseType.getMenuId()).getData();
            sysMenu.setMenuName(req.getCaseTypeName());
            // 修改地址
            if (path != null) {
                //菜单使用全路径
                String sysMenuPath = sysMenu.getPath().substring(0, sysMenu.getPath().lastIndexOf("/") + 1) + path;
                sysMenu.setPath(sysMenuPath);
                sysMenu.setComponent(sysMenuPath);
            }
            authServerApi.editMenu(sysMenu);
        }
    }

    @Override
    public CaseLibTypeResp getCaseLibTypeDetail(Long caseTypeId) {
        DepCaseType depCaseType = depCaseTypeService.getById(caseTypeId);
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
        caseLibTypeResp.setCaseTypeId(depCaseType.getCaseTypeId());
        caseLibTypeResp.setCaseTypeName(depCaseType.getCaseTypeName());
        caseLibTypeResp.setAudit(depCaseType.getAudit());
        caseLibTypeResp.setAddress(depCaseType.getAddress());
        caseLibTypeResp.setCreateTime(depCaseType.getCreateTime());
        caseLibTypeResp.setUpdateTime(depCaseType.getUpdateTime());
        return caseLibTypeResp;
    }
}
