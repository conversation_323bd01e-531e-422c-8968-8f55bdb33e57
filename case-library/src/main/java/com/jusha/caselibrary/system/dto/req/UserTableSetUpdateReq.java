package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName UserTableSetUpdateReq
 * @Description 用户表设置更新请求
 * <AUTHOR>
 * @Date 2025/7/31 19:53
 **/
@Data
public class UserTableSetUpdateReq {

    @ApiModelProperty(value = "用户表设置id")
    @NotNull(message = "用户表设置id不能为空")
    private Long id;

    @ApiModelProperty(value = "表格类型")
    @NotNull(message = "表格类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "表格设置json")
    @NotNull(message = "表格设置不能为空")
    private String setData;
}
