package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.aop.NoDuplicate;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.entity.UserTable;
import com.jusha.caselibrary.system.dto.req.UserTableSetCreateReq;
import com.jusha.caselibrary.system.dto.req.UserTableSetUpdateReq;
import com.jusha.caselibrary.system.dto.resp.UserTableSetResp;
import com.jusha.caselibrary.system.service.UserTableSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName UserTableSetController
 * @Description 用户表设置控制器
 * <AUTHOR>
 * @Date 2025/7/31 19:39
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/table")
@Api(tags = "表格表头设置")
public class UserTableSetController {

    private final UserTableSetService userTableSetService;

    @ApiOperation("新增表头设置")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Void> createTableSet(@Validated @RequestBody UserTableSetCreateReq req) {
        userTableSetService.createTableSet(req);
        return ResultBean.success();
    }

    @ApiOperation("修改表头设置")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<Void> updateTableSet(@Validated @RequestBody UserTableSetUpdateReq req) {
        userTableSetService.updateTableSet(req);
        return ResultBean.success();
    }

    @ApiOperation("查询用户个人表头设置")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<UserTableSetResp> getTableSet(@RequestParam("type") Integer type) {
        UserTableSetResp userTableSetResp = userTableSetService.getTableSet(type);
        return ResultBean.success(userTableSetResp);
    }

    @ApiOperation("删除用户个人表头设置")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> deleteTableSet(@RequestBody UserTable userTable) {
        userTableSetService.deleteTableSet(userTable);
        return ResultBean.success();
    }

}
