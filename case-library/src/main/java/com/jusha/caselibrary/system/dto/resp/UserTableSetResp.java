package com.jusha.caselibrary.system.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName UserTableSetResp
 * @Description 用户表设置响应
 * <AUTHOR>
 * @Date 2025/7/31 20:09
 **/
@Data
public class UserTableSetResp {

    @ApiModelProperty(value = "用户表设置id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "表格类型")
    @NotNull(message = "表格类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "表格设置json")
    @NotNull(message = "表格设置不能为空")
    private String setData;
}
