package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.system.dto.req.UserCatalogReq;
import com.jusha.caselibrary.system.dto.req.UserCatalogUpdateReq;
import com.jusha.caselibrary.system.service.SysUserCatalogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 目录信息
 * <AUTHOR>
 * @Date 2025/7/7 14:53
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "个人目录管理")
@RequestMapping("/system/userCatalog")
public class SysUserCatalogController {
    
    private final SysUserCatalogService sysUserCatalogService;

    @EscapeWildcard
    @GetMapping("/list")
    @ApiOperation(value = "获取目录列表")
    public ResultBean list(UserCatalogReq userCatalogReq) {
        return ResultBean.success(sysUserCatalogService.selectUserCatalogList(userCatalogReq));
    }

    /**
     * 查询目录列表（排除节点）
     */
    @GetMapping("/list/exclude")
    @ApiOperation(value = "获取目录列表不含自己")
    public ResultBean excludeChild(@RequestParam Long userCatalogId) {
        UserCatalogReq userCatalogReq = new UserCatalogReq();
        List<UserCatalog> userCatalogs = sysUserCatalogService.selectUserCatalogList(userCatalogReq);
        userCatalogs.removeIf(d -> d.getCatalogId().equals(userCatalogId) ||
                ArrayUtils.contains(StringUtils.split(d.getAncestors(), Constant.SEPARATOR), userCatalogId + Constant.EMPTY_STRING));
        return ResultBean.success(userCatalogs);
    }

    /**
     * 获取对应角色分组树列表
     */
    @ApiOperation("获取目录树列表")
    @GetMapping(value = "/tree/list")
    public ResultBean groupTree(UserCatalogReq userCatalogReq) {
        return ResultBean.success(sysUserCatalogService.selectUserCatalogTreeList(userCatalogReq));
    }

    /**
     * 根据目录编号获取详细信息
     */
    @GetMapping(value = "/query")
    @ApiOperation(value = "获取目录详情")
    public ResultBean getInfo(@RequestParam Long userCatalogId) {
        return ResultBean.success(sysUserCatalogService.selectUserCatalogById(userCatalogId));
    }

    /**
     * 新增目录
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增目录")
    public ResultBean add(@Validated @RequestBody UserCatalog userCatalog) {
        if(userCatalog.getParentId()==null){
            return ResultBean.error(LocaleUtil.getLocale(("catalog.has.no.parent")));
        }
        //同一目录目录下，目录名称不可以重复
        if (!sysUserCatalogService.checkUserCatalogNameUnique(userCatalog)) {
            return ResultBean.error(LocaleUtil.getLocale(("catalog.name.already.exist")));
        }
        return ResultBean.success(sysUserCatalogService.insertUserCatalog(userCatalog));
    }

    /**
     * 修改目录
     */
    @PostMapping(value = "/edit")
    @ApiOperation(value = "修改目录")
    public ResultBean edit(@Validated @RequestBody UserCatalogUpdateReq userCatalogUpdateReq) {
        return sysUserCatalogService.updateUserCatalog(userCatalogUpdateReq);
    }

    /**
     * 删除目录
     */
    @PostMapping("/remove")
    @ApiOperation(value = "删除目录")
    public ResultBean remove(@Validated @RequestBody UserCatalog userCatalog) {
        if(userCatalog.getCatalogId()==null){
            return ResultBean.error();
        }
        if (sysUserCatalogService.hasChildByCatalogId(userCatalog.getCatalogId())) {
            return ResultBean.error(LocaleUtil.getLocale(("catalog.child.exist.delete")));
        }
        return ResultBean.success(sysUserCatalogService.deleteUserCatalogById(userCatalog.getCatalogId()));
    }
}
