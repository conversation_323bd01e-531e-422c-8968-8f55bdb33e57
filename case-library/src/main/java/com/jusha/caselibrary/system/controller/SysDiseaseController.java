package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.system.entity.Disease;
import com.jusha.caselibrary.system.dto.req.DiseaseReq;
import com.jusha.caselibrary.system.dto.req.DiseaseUpdateReq;
import com.jusha.caselibrary.system.service.SysDiseaseService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 疾病信息
 * <AUTHOR>
 * @Date 2025/7/3 21:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/disease")
public class SysDiseaseController {
    
    private final SysDiseaseService sysDiseaseService;

    @EscapeWildcard
    @GetMapping("/list")
    public ResultBean list(DiseaseReq diseaseReq) {
        return ResultBean.success(sysDiseaseService.selectDiseaseList(diseaseReq));
    }

    /**
     * 查询疾病列表（排除节点）
     */
    @GetMapping("/list/exclude")
    public ResultBean excludeChild(@RequestParam Long diseaseId) {
        DiseaseReq diseaseReq = new DiseaseReq();
        List<Disease> diseases = sysDiseaseService.selectDiseaseList(diseaseReq);
        diseases.removeIf(d -> d.getDiseaseId().equals(diseaseId) ||
                ArrayUtils.contains(StringUtils.split(d.getAncestors(), Constant.SEPARATOR), diseaseId + Constant.EMPTY_STRING));
        return ResultBean.success(diseases);
    }

    /**
     * 获取对应角色分组树列表
     */
    @ApiOperation("获取疾病树列表")
    @GetMapping(value = "/tree/list")
    public ResultBean groupTree(DiseaseReq diseaseReq) {
        return ResultBean.success(sysDiseaseService.selectDiseaseTreeList(diseaseReq));
    }

    /**
     * 根据疾病编号获取详细信息
     */
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long diseaseId) {
        return ResultBean.success(sysDiseaseService.selectDiseaseById(diseaseId));
    }

    /**
     * 新增疾病
     */
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody Disease disease) {
        return ResultBean.success(sysDiseaseService.insertDisease(disease));
    }

    /**
     * 修改疾病
     */
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody DiseaseUpdateReq diseaseUpdateReq) {
        if(sysDiseaseService.updateDisease(diseaseUpdateReq)){
            return ResultBean.success();
        }else {
            return ResultBean.error();
        }
    }

    /**
     * 修改疾病排序
     */
    @PostMapping(value = "/editOrderNum")
    public ResultBean editOrderNum(@Validated @RequestBody List<DiseaseUpdateReq> diseaseUpdateReqList) {
        sysDiseaseService.editOrderNum(diseaseUpdateReqList);
        return ResultBean.success();
    }

    /**
     * 删除疾病
     */
    @PostMapping("/remove")
    public ResultBean remove(@Validated @RequestBody Disease disease) {
        if(disease.getDiseaseId()==null){
            return ResultBean.error();
        }
        return ResultBean.success(sysDiseaseService.deleteDiseaseById(disease.getDiseaseId()));
    }
}
