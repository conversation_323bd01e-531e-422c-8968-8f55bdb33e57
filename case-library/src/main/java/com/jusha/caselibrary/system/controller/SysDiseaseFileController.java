package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.system.dto.req.DiseaseFileRemoveReq;
import com.jusha.caselibrary.system.dto.req.DiseaseFileReq;
import com.jusha.caselibrary.system.dto.resp.DiseaseFileResp;
import com.jusha.caselibrary.system.service.SysDiseaseFileService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 疾病信息
 * <AUTHOR>
 * @Date 2025/7/30 21:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/file")
public class SysDiseaseFileController {
    
    private final SysDiseaseFileService sysDiseaseFileService;

    @ApiOperation("文件上报，给某个疾病id增加文件关联")
    @PostMapping(value = "/add")
    public ResultBean<Void> addDiseaseFile(@RequestBody DiseaseFileReq diseaseFileReq) {
        if(sysDiseaseFileService.addDiseaseFile(diseaseFileReq)){
            return ResultBean.success();
        }
        return ResultBean.error();
    }

    @ApiOperation("文件查询，查询某个疾病下所有关联文件")
    @GetMapping(value = "/list")
    public ResultBean<List<DiseaseFileResp>> queryDiseaseFiles(@RequestParam Long diseaseId , String fileName) {
        return ResultBean.success(sysDiseaseFileService.queryDiseaseFiles(diseaseId,fileName));
    }


    @ApiOperation("删除某个文件")
    @PostMapping(value = "/remove")
    public ResultBean<Void> removeDiseaseFile(@RequestBody DiseaseFileRemoveReq diseaseFileRemoveReq) {
        if(sysDiseaseFileService.removeDiseaseFile(diseaseFileRemoveReq)){
            return ResultBean.success();
        }
        return ResultBean.error();
    }
}
