package com.jusha.caselibrary.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.system.dto.req.DiseaseFileRemoveReq;
import com.jusha.caselibrary.system.dto.req.DiseaseFileReq;
import com.jusha.caselibrary.mybatisplus.system.entity.DiseaseFile;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseFileService;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseService;
import com.jusha.caselibrary.system.dto.resp.DiseaseFileResp;
import com.jusha.caselibrary.system.service.SysDiseaseFileService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 疾病关联文件管理模块 服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysDiseaseFileServiceImpl implements SysDiseaseFileService {
    private static final Logger log = LoggerFactory.getLogger(SysDiseaseFileServiceImpl.class);

    private final DiseaseService diseaseService;
    private final DiseaseFileService diseaseFileService;

    @Value("${minio.endpoint:}")
    private String endpoint;

    @Override
    public boolean addDiseaseFile(DiseaseFileReq diseaseFileReq) {

        if(diseaseFileReq.getDiseaseId()==null || diseaseService.getById(diseaseFileReq.getDiseaseId()) ==null){
            throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
        }

        List<String> fileIdList = diseaseFileReq.getFileIdList();
        List<DiseaseFile> diseaseFileList = new ArrayList<>();
        for(String fileId : fileIdList){
            DiseaseFile diseaseFile = new DiseaseFile();
            diseaseFile.setFileId(YitIdHelper.nextId());
            diseaseFile.setDiseaseId(diseaseFileReq.getDiseaseId());
            String fileName = fileId.contains(Constant.FILE_SEPARATOR)?fileId.substring(fileId.lastIndexOf('/') + 1) : fileId;
            diseaseFile.setFileName(fileName);
            String fileType = fileId.contains(Constant.FILE_EXTENSION)?fileId.substring(fileId.lastIndexOf('.') + 1) :  "未知";
            diseaseFile.setFileType(fileType);
            diseaseFile.setFileUrl(fileId);
            diseaseFile.setCreatedBy(LoginUtil.getLoginUserId());
            diseaseFile.setCreateTime(new Date());

            diseaseFileList.add(diseaseFile);
        }
        return diseaseFileService.saveOrUpdateBatch(diseaseFileList);
    }

    @Override
    public List<DiseaseFileResp> queryDiseaseFiles(Long diseaseId,String fileName) {
        List<DiseaseFileResp> diseaseFileRespList = new ArrayList<>();

        if(diseaseId==null || diseaseService.getById(diseaseId) ==null){
            throw new BusinessException(LocaleUtil.getLocale("disease.not.exist"));
        }
        LambdaQueryChainWrapper<DiseaseFile> wrapper = diseaseFileService.lambdaQuery().eq(DiseaseFile::getDiseaseId,diseaseId);
        if(fileName!=null){
            wrapper.like(DiseaseFile::getFileName,diseaseId);
        }
        List<DiseaseFile> diseaseFileList = wrapper.list();

        for(DiseaseFile diseaseFile : diseaseFileList){
            DiseaseFileResp diseaseFileResp = new DiseaseFileResp();
            diseaseFileResp.setFileId(diseaseFile.getFileId()).setDiseaseId(diseaseFile.getDiseaseId()).setFileName(diseaseFile.getFileName())
                    .setFileType(diseaseFile.getFileType()).setFileUrl(endpoint + Constant.FILE_SEPARATOR + Constant.UPLOAD_BUCKET + diseaseFile.getFileUrl())
                    .setCreateTime(diseaseFile.getCreateTime()).setCreateBy(diseaseFile.getCreatedBy());
            diseaseFileRespList.add(diseaseFileResp);
        }
        return diseaseFileRespList;
    }

    @Override
    public boolean removeDiseaseFile(DiseaseFileRemoveReq diseaseFileRemoveReq) {
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        DiseaseFile diseaseFile = diseaseFileService.getById(diseaseFileRemoveReq.getFileId());
        fileService.removeFile(diseaseFile.getFileUrl());
        return diseaseFileService.removeById(diseaseFileRemoveReq.getFileId());
    }
}