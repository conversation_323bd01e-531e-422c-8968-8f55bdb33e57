package com.jusha.caselibrary.system.service;

import com.jusha.caselibrary.mybatisplus.entity.UserTable;
import com.jusha.caselibrary.system.dto.req.UserTableSetCreateReq;
import com.jusha.caselibrary.system.dto.req.UserTableSetUpdateReq;
import com.jusha.caselibrary.system.dto.resp.UserTableSetResp;

/**
 * @InterfaceName UserTableSetService
 * @Description 用户表设置服务接口
 * <AUTHOR>
 * @Date 2025/7/31 19:55
 **/
public interface UserTableSetService {

    /**
     * @description 新增用户表头设置
     * <AUTHOR>
     * @date 2025/7/31 19:56
     * @param req
     * @return void
     **/
    void createTableSet(UserTableSetCreateReq req);

    /**
     * @description 更新用户表头设置
     * <AUTHOR>
     * @date 2025/7/31 20:02
     * @param req
     * @return void
     **/
    void updateTableSet(UserTableSetUpdateReq req);

    /**
     * @description 查询用户个人表头设置
     * <AUTHOR>
     * @date 2025/7/31 20:10
     * @param type
     * @return void
     **/
    UserTableSetResp getTableSet(Integer type);

    /**
     * @description 删除用户个人表头设置
     * <AUTHOR>
     * @date 2025/7/31 20:15
     * @param userTable
     * @return void
     **/
    void deleteTableSet(UserTable userTable);
}
