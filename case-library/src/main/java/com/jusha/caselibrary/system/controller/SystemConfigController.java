package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.system.service.SearchAllService;
import com.jusha.caselibrary.system.dto.resp.SystemInfoResp;
import com.jusha.caselibrary.system.service.SystemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName PacsController
 * @Description 获取pacs相关信息
 * <AUTHOR>
 * @Date 2025/7/31 17:24
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/system")
@Api(tags = "获取系统相关信息")
public class SystemConfigController {

    private final SystemConfigService pacsDockingService;

    private final SearchAllService searchAllService;

    @ApiOperation("获取系统信息")
    @GetMapping(value = "/getSystemConfig")
    public ResultBean<SystemInfoResp> getSystemConfig() {
        return ResultBean.success(pacsDockingService.getSystemInfo());
    }
}
