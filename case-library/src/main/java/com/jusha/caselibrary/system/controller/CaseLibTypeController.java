package com.jusha.caselibrary.system.controller;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.aop.NoDuplicate;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeSaveReq;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeUpdateReq;
import com.jusha.caselibrary.system.dto.resp.CaseLibTypeResp;
import com.jusha.caselibrary.system.service.CaseLibTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName CaseLibTypeController
 * @Description 病例库配置管理
 * <AUTHOR>
 * @Date 2025/7/3 15:52
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/caseLibType")
@Api(tags = "平台设置-病例库配置管理")
public class CaseLibTypeController {

    private final CaseLibTypeService caseLibTypeService;

    @ApiOperation("病例库类型列表")
    @RequestMapping(value = "/list/open", method = RequestMethod.GET)
    public ResultBean<List<CaseLibTypeResp>> list () {
        List<CaseLibTypeResp> resp = caseLibTypeService.getCaseLibTypeList();
        return ResultBean.success(resp);
    }

    @ApiOperation("分页病例库类型列表")
    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public ResultBean<PageInfo<CaseLibTypeResp>> page (@RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        PageInfo<CaseLibTypeResp> resp = caseLibTypeService.getCaseLibTypePage(pageNum, pageSize);
        return ResultBean.success(resp);
    }

    @ApiOperation("新增病例库类型")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultBean<Void> add (@RequestBody CaseLibTypeSaveReq req) {
        caseLibTypeService.addCaseLibType(req);
        return ResultBean.success();
    }

    @ApiOperation("删除病例库类型")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete (@RequestBody CaseLibTypeUpdateReq req) {
        caseLibTypeService.deleteCaseLibType(req.getCaseTypeId());
        return ResultBean.success();
    }

    @ApiOperation("修改病例库类型")
    @NoDuplicate(keys = "T(com.jusha.caselibrary.common.util.LoginUtil).getLoginUserId()")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<Void> update (@RequestBody CaseLibTypeUpdateReq req) {
        caseLibTypeService.updateCaseLibType(req);
        return ResultBean.success();
    }

    @ApiOperation("获取病例库类型详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<CaseLibTypeResp> detail (@RequestParam("caseTypeId") Long caseTypeId) {
        CaseLibTypeResp resp = caseLibTypeService.getCaseLibTypeDetail(caseTypeId);
        return ResultBean.success(resp);
    }
}
