package com.jusha.caselibrary.system.service.impl;

import com.jusha.caselibrary.mybatisplus.system.entity.AetTemplate;
import com.jusha.caselibrary.mybatisplus.system.entity.SearchAll;
import com.jusha.caselibrary.mybatisplus.system.service.AetTemplateService;
import com.jusha.caselibrary.mybatisplus.system.service.SearchAllService;
import com.jusha.caselibrary.system.dto.resp.SystemInfoResp;
import com.jusha.caselibrary.system.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @ClassName PacsDockingServiceImpl
 * @Description 获取pacs相关信息实现类
 * <AUTHOR>
 * @Date 2025/7/31 20:42
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemConfigServiceImpl implements SystemConfigService {

    private final AetTemplateService aetTemplateService;
    private final SearchAllService searchAllService;

    @Override
    public SystemInfoResp getSystemInfo() {
        SystemInfoResp systemInfoResp = new SystemInfoResp();
        List<AetTemplate> aetInfo = aetTemplateService.lambdaQuery().list();
        List<SearchAll> searchAllInfo = searchAllService.lambdaQuery().list();
        systemInfoResp.setAetInfo(aetInfo);
        systemInfoResp.setSearchAllInfo(searchAllInfo);
        return systemInfoResp;
    }
}
