package com.jusha.caselibrary.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.resp.TreeSelect;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.mybatisplus.system.mapper.UserCatalogMapper;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.system.dto.req.UserCatalogReq;
import com.jusha.caselibrary.system.dto.req.UserCatalogUpdateReq;
import com.jusha.caselibrary.system.service.SysUserCatalogService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 目录树管理模块 服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysUserCatalogServiceImpl implements SysUserCatalogService {
    private static final Logger log = LoggerFactory.getLogger(SysUserCatalogServiceImpl.class);

    private final UserCatalogService sysUserCatalogService;

    private final UserCatalogMapper UserCatalogMapper;

    /**
     * 查询目录数据
     *
     * @param userCatalogReq 目录信息查询入参
     * @return 目录信息集合
     */
    @Override
    public List<UserCatalog> selectUserCatalogList(UserCatalogReq userCatalogReq) {
        LambdaQueryChainWrapper<UserCatalog> wrapper = sysUserCatalogService.lambdaQuery();
        wrapper.eq(UserCatalog::getCreatedBy,LoginUtil.getLoginUserId());
        if (userCatalogReq.getCatalogName() != null) {
            wrapper.like(UserCatalog::getCatalogName, userCatalogReq.getCatalogName());
        }
        if (userCatalogReq.getCatalogId() != null) {
            wrapper.eq(UserCatalog::getCatalogId, userCatalogReq.getCatalogId());
        }
        if (userCatalogReq.getParentId() != null) {
            wrapper.eq(UserCatalog::getParentId, userCatalogReq.getParentId());
        }
        if(userCatalogReq.getAncestors()!=null){
            //根据顶层目录来查询树，加上顶层
            wrapper.apply( "FIND_IN_SET ( '"+ userCatalogReq.getAncestors() +"',ancestors )");
            wrapper.or().eq(UserCatalog::getCatalogId,userCatalogReq.getAncestors());
        }
        return wrapper.orderByAsc(UserCatalog::getOrderNum).orderByDesc(UserCatalog::getCreateTime).list();
    }

    /**
     * 根据目录ID查询信息
     *
     * @param catalogId 目录ID
     * @return 目录信息
     */
    @Override
    public UserCatalog selectUserCatalogById(Long catalogId) {
        return sysUserCatalogService.getById(catalogId);
    }

    /**
     * 新增保存目录信息
     *
     * @param userCatalog 目录信息
     * @return 结果
     */
    @Override
    public UserCatalog insertUserCatalog(UserCatalog userCatalog) {
        long catalogId = YitIdHelper.nextId();
        userCatalog.setCatalogId(catalogId);
        userCatalog.setCreateTime(new Date());
        userCatalog.setCreatedBy(LoginUtil.getLoginUserId());
        UserCatalog info = sysUserCatalogService.getById(userCatalog.getParentId());
        if (info == null) {
            userCatalog.setAncestors("0");
            sysUserCatalogService.save(userCatalog);
            return sysUserCatalogService.getById(catalogId);
        }
        userCatalog.setAncestors(info.getAncestors() + Constant.SEPARATOR + userCatalog.getParentId());
        sysUserCatalogService.save(userCatalog);
        return sysUserCatalogService.getById(catalogId);
    }

    /**
     * 查询目录树结构信息
     *
     * @param userCatalogReq 目录信息查询入参
     * @return 目录树信息集合
     */
    @Override
    public List<TreeSelect> selectUserCatalogTreeList(UserCatalogReq userCatalogReq) {
        List<UserCatalog> userCatalogs = selectUserCatalogList(userCatalogReq);
        return buildUserCatalogTreeSelect(userCatalogs);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param userCatalogs 目录列表
     * @return 树结构列表
     */
    @Override
    public List<UserCatalog> buildUserCatalogTree(List<UserCatalog> userCatalogs) {
        List<UserCatalog> returnList = new ArrayList<>();
        List<Long> tempList = userCatalogs.stream().map(UserCatalog::getCatalogId).collect(Collectors.toList());
        for (UserCatalog UserCatalog : userCatalogs) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(UserCatalog.getParentId())) {
                recursionFn(userCatalogs, UserCatalog);
                returnList.add(UserCatalog);
            }
        }
        if (returnList.isEmpty()) {
            returnList = userCatalogs;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param userCatalogs 目录列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildUserCatalogTreeSelect(List<UserCatalog> userCatalogs) {
        List<UserCatalog> UserCatalogTrees = buildUserCatalogTree(userCatalogs);
        return UserCatalogTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 是否存在子节点
     *
     * @param catalogId 目录ID
     * @return 结果
     */
    @Override
    public boolean hasChildByCatalogId(Long catalogId) {
        long result = sysUserCatalogService.lambdaQuery().eq(UserCatalog::getParentId, catalogId).count();
        return result > Constant.ZERO_LONG;
    }

    /**
     * 校验目录名称是否唯一
     *
     * @param userCatalog 目录信息
     * @return 结果
     */
    @Override
    public boolean checkUserCatalogNameUnique(UserCatalog userCatalog) {
        Long catalogId = userCatalog.getCatalogId()==null ? Constant.ALL_MINUS1L : userCatalog.getCatalogId();
        List<UserCatalog> userCatalogInfos = sysUserCatalogService.lambdaQuery()
                .eq(UserCatalog::getCatalogName, userCatalog.getCatalogName())
                .eq(UserCatalog::getParentId, userCatalog.getParentId())
                .eq(UserCatalog::getCreatedBy,LoginUtil.getLoginUserId()).list();
        if (!userCatalogInfos.isEmpty() && userCatalogInfos.get(0).getCatalogId().longValue() != catalogId.longValue()) {
            return Constant.NOT_UNIQUE;
        }
        return Constant.UNIQUE;
    }

    /**
     * 修改保存目录信息
     *
     * @param userCatalogUpdateReq 目录信息
     * @return 结果
     */
    @Override
    public ResultBean updateUserCatalog(UserCatalogUpdateReq userCatalogUpdateReq) {
        if(userCatalogUpdateReq.getCatalogId() == null){
            return ResultBean.error();
        }
        UserCatalog userCatalog = sysUserCatalogService.getById(userCatalogUpdateReq.getCatalogId());
        if(userCatalog == null){
            return ResultBean.error();
        }
        if(userCatalogUpdateReq.getCatalogName()!=null){
            userCatalog.setCatalogName(userCatalogUpdateReq.getCatalogName());
        }
        if(userCatalogUpdateReq.getParentId()!=null){
            userCatalog.setParentId(userCatalogUpdateReq.getParentId());
        }
        if(userCatalogUpdateReq.getOrderNum()!=null){
            userCatalog.setOrderNum(userCatalogUpdateReq.getOrderNum());
        }
        if (!checkUserCatalogNameUnique(userCatalog)) {
            //验证名称唯一性
            return ResultBean.error(LocaleUtil.getLocale("catalog.name.already.exist"));
        }
        if (userCatalogUpdateReq.getParentId()!=null) {
            if(userCatalogUpdateReq.getParentId().equals(userCatalog.getCatalogId())){
                //不可以把自己作为父分組
                return ResultBean.error(LocaleUtil.getLocale("catalog.parent.self"));
            }
            UserCatalog newParentUserCatalog = sysUserCatalogService.lambdaQuery().eq(UserCatalog::getCatalogId, userCatalog.getParentId()).one();
            UserCatalog oldUserCatalog = sysUserCatalogService.lambdaQuery().eq(UserCatalog::getCatalogId, userCatalog.getCatalogId()).one();
            if (newParentUserCatalog!=null && oldUserCatalog!=null) {
                String newAncestors = newParentUserCatalog.getAncestors() + "," + newParentUserCatalog.getCatalogId();
                String oldAncestors = oldUserCatalog.getAncestors();
                userCatalog.setAncestors(newAncestors);
                updateUserCatalogChildren(userCatalog.getCatalogId(), newAncestors, oldAncestors);
            }
        }
        userCatalog.setUpdateBy(LoginUtil.getLoginUserId());
        userCatalog.setUpdateTime(new Date());

        boolean result = sysUserCatalogService.updateById(userCatalog);
        if (!result) {
            return ResultBean.error();
        }
        return ResultBean.success();
    }

    /**
     * 修改子元素关系
     *
     * @param catalogId    被修改的目录ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateUserCatalogChildren(Long catalogId, String newAncestors, String oldAncestors) {
        List<UserCatalog> children = UserCatalogMapper.selectChildrenUserCatalogById(catalogId);
        for (UserCatalog child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            UserCatalogMapper.updateUserCatalogChildren(children);
        }
    }

    /**
     * 删除目录信息
     *
     * @param catalogId 目录ID
     * @return 结果
     */
    @Override
    public boolean deleteUserCatalogById(Long catalogId) {
        return sysUserCatalogService.lambdaUpdate()
                .set(UserCatalog::getUpdateBy, LoginUtil.getLoginUserId())
                .set(UserCatalog::getUpdateTime, new Date())
                .set(UserCatalog::getDelFlag, Constant.DELETE_FLAG)
                .eq(UserCatalog::getCatalogId, catalogId).update();
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<UserCatalog> list, UserCatalog t) {
        // 得到子节点列表
        List<UserCatalog> childList = getChildList(list, t);
        t.setChildren(childList);
        for (UserCatalog tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<UserCatalog> getChildList(List<UserCatalog> list, UserCatalog t) {
        List<UserCatalog> tlist = new ArrayList<>();
        Iterator<UserCatalog> it = list.iterator();
        while (it.hasNext()) {
            UserCatalog n = it.next();
            if (n.getParentId()!=null && n.getParentId().longValue() == t.getCatalogId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<UserCatalog> list, UserCatalog t) {
        return !getChildList(list, t).isEmpty();
    }
}