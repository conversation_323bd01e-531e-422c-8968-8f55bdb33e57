package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName UserTableSetCreateReq
 * @Description 用户表设置创建请求
 * <AUTHOR>
 * @Date 2025/7/31 19:53
 **/
@Data
public class UserTableSetCreateReq {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "表格类型")
    @NotNull(message = "表格类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "表格设置json")
    @NotNull(message = "表格设置不能为空")
    private String setData;
}
