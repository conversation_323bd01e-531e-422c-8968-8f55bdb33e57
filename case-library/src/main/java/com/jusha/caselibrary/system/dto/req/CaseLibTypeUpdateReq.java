package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName CaseLibTypeReq
 * @Description 病例库类型管理请求类
 * <AUTHOR>
 * @Date 2025/7/3 17:01
 **/
@Data
public class CaseLibTypeUpdateReq {

    @ApiModelProperty(value = "病例库类型id")
    @NotBlank(message = "病例库类型id不能为空")
    private Long caseTypeId;

    @ApiModelProperty(value = "病例库类型名称")
    private String caseTypeName;

    @ApiModelProperty(value = "是否需要审核0不需要1需要")
    private String audit;

    @ApiModelProperty(value = "是否需要疾病树0不需要1需要")
    private String diseaseTree;
}
