package com.jusha.caselibrary.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.entity.UserTable;
import com.jusha.caselibrary.mybatisplus.service.UserTableService;
import com.jusha.caselibrary.system.dto.req.UserTableSetCreateReq;
import com.jusha.caselibrary.system.dto.req.UserTableSetUpdateReq;
import com.jusha.caselibrary.system.dto.resp.UserTableSetResp;
import com.jusha.caselibrary.system.service.UserTableSetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName UserTableSetServiceImpl
 * @Description 用户表设置服务实现类
 * <AUTHOR>
 * @Date 2025/7/31 19:55
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTableSetServiceImpl implements UserTableSetService {

    private final UserTableService userTableService;
    @Override
    public void createTableSet(UserTableSetCreateReq req) {
        Long userId = req.getUserId();
        if (userId == null) {
            userId = LoginUtil.getLoginUserId();
        }
        userTableService.lambdaQuery().eq(UserTable::getType, req.getType())
                .eq(UserTable::getUserId, userId).oneOpt().ifPresent(userTable -> {
            throw new BusinessException(LocaleUtil.getLocale("user.table.set.exist"));
        });
        UserTable userTable = new UserTable();
        userTable.setId(YitIdHelper.nextId());
        userTable.setUserId(userId);
        userTable.setType(req.getType());
        userTable.setSetData(req.getSetData());
        userTableService.save(userTable);
    }

    @Override
    public void updateTableSet(UserTableSetUpdateReq req) {
        UserTable userTable = userTableService.getById(req.getId());
        if (userTable == null) {
            throw new BusinessException(LocaleUtil.getLocale("user.table.set.not.exist"));
        }
        userTableService.lambdaQuery().eq(UserTable::getType, req.getType())
                .eq(UserTable::getUserId, LoginUtil.getLoginUserId()).ne(UserTable::getId, req.getId()).oneOpt()
                .ifPresent(existing -> {
                    throw new BusinessException(LocaleUtil.getLocale("user.table.set.exist"));
                });
        BeanUtil.copyProperties(req, userTable, true);
        userTableService.updateById(userTable);
    }

    @Override
    public UserTableSetResp getTableSet(Integer type) {
        UserTableSetResp userTableSetResp = new UserTableSetResp();
        UserTable userTable = userTableService.lambdaQuery().eq(UserTable::getUserId, LoginUtil.getLoginUserId())
                .eq(UserTable::getType, type).one();
        if (userTable == null) {
            return null;
        }
        BeanUtil.copyProperties(userTable, userTableSetResp, true);
        return userTableSetResp;
    }

    @Override
    public void deleteTableSet(UserTable userTable) {
        if (userTable == null || userTable.getId() == null) {
            throw new BusinessException(LocaleUtil.getLocale("common.param.error"));
        }
        UserTable existing = userTableService.getById(userTable.getId());
        if (existing == null) {
            throw new BusinessException(LocaleUtil.getLocale("user.table.set.not.exist"));
        }
        userTableService.removeById(userTable.getId());
    }
}
