package com.jusha.caselibrary.system.service;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.resp.TreeSelect;
import com.jusha.caselibrary.mybatisplus.system.entity.Disease;
import com.jusha.caselibrary.system.dto.req.DiseaseReq;
import com.jusha.caselibrary.system.dto.req.DiseaseUpdateReq;

import java.util.List;

/**
 * 疾病管理 服务层
 *
 * <AUTHOR>
 */
public interface SysDiseaseService {

    /**
     * 查询疾病管理数据
     *
     * @param diseaseReq 疾病信息查询入参
     * @return 疾病信息集合
     */
    public List<Disease> selectDiseaseList(DiseaseReq diseaseReq);

    /**
     * 新增保存疾病信息
     *
     * @param disease 疾病信息
     * @return 结果
     */
    public Disease insertDisease(Disease disease);

    /**
     * 查询疾病树结构信息
     *
     * @param diseaseReq 疾病信息查询入参
     * @return 疾病树信息集合
     */
    public List<TreeSelect> selectDiseaseTreeList(DiseaseReq diseaseReq);

    /**
     * 构建前端所需要树结构
     *
     * @param diseases 疾病列表
     * @return 树结构列表
     */
    public List<Disease> buildDiseaseTree(List<Disease> diseases);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param diseases 疾病列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDiseaseTreeSelect(List<Disease> diseases);

    /**
     * 根据疾病ID查询信息
     *
     * @param diseaseId 疾病ID
     * @return 疾病信息
     */
    public Disease selectDiseaseById(Long diseaseId);

    /**
     * 是否存在疾病子节点
     *
     * @param diseaseId 疾病ID
     * @return 结果
     */
    public boolean hasChildByDiseaseId(Long diseaseId);

    /**
     * 校验疾病名称是否唯一
     *
     * @param disease 疾病信息
     * @return 结果
     */
    public boolean checkDiseaseNameUnique(Disease disease);

    /**
     * 修改保存疾病信息
     *
     * @param diseaseUpdateReq 疾病信息
     * @return 结果
     */
    public ResultBean updateDisease(DiseaseUpdateReq diseaseUpdateReq);

    /**
     * 删除疾病管理信息
     *
     * @param diseaseId 疾病ID
     * @return 结果
     */
    public boolean deleteDiseaseById(Long diseaseId);
}
