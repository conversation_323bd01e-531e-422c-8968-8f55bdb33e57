package com.jusha.caselibrary.system.service;

import com.jusha.caselibrary.system.dto.req.DiseaseFileRemoveReq;
import com.jusha.caselibrary.system.dto.req.DiseaseFileReq;
import com.jusha.caselibrary.system.dto.resp.DiseaseFileResp;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 疾病关联文件管理模块 服务层
 *
 * <AUTHOR>
 */
public interface SysDiseaseFileService {

    /**
     * 新增疾病相关联文件
     *
     * @param diseaseFileReq 疾病关联文件请求体
     * @return 疾病信息
     */
    boolean addDiseaseFile(DiseaseFileReq diseaseFileReq);

    /**
     * 根据疾病ID查询文件信息
     *
     * @param diseaseId 疾病id
     * @return 疾病文件返回列表
     */
    List<DiseaseFileResp> queryDiseaseFiles(Long diseaseId,String fileName);

    /**
     * 删除文件信息
     *
     * @param diseaseFileRemoveReq 删除id
     * @return 删除结果
     */
    boolean removeDiseaseFile(@RequestBody DiseaseFileRemoveReq diseaseFileRemoveReq);

}
