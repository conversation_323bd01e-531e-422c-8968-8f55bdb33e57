package com.jusha.caselibrary.system.service.impl;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.system.entity.DiseaseOverview;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseOverviewService;
import com.jusha.caselibrary.system.dto.req.DiseaseOverviewReq;
import com.jusha.caselibrary.system.dto.resp.DiseaseOverviewResp;
import com.jusha.caselibrary.system.service.SysDiseaseOverviewService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 疾病概述管理模块 服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysDiseaseOverviewServiceImpl implements SysDiseaseOverviewService {
    private static final Logger log = LoggerFactory.getLogger(SysDiseaseOverviewServiceImpl.class);

    private final DiseaseOverviewService diseaseOverviewService;

    @Value("${minio.endpoint:}")
    private String endpoint;

    /**
     * 根据疾病ID查询信息
     * @param diseaseId 疾病ID 疾病概述直接用疾病的ID做主键
     * @return 疾病概述信息
     */
    @Override
    public DiseaseOverviewResp selectDiseaseById(Long diseaseId) {
        DiseaseOverview diseaseOverview = diseaseOverviewService.getById(diseaseId);
        DiseaseOverviewResp diseaseOverviewResp = new DiseaseOverviewResp();
        if(diseaseOverview!=null){
            diseaseOverviewResp.setDiseaseId(diseaseOverview.getDiseaseId()).setOverview(diseaseOverview.getOverview())
                    .setPathology(diseaseOverview.getPathology()).setClinical(diseaseOverview.getClinical())
                    .setImaging(diseaseOverview.getImaging()).setTreatment(diseaseOverview.getTreatment())
                    .setRemark(diseaseOverview.getRemark())
                    .setDifferential(diseaseOverview.getDifferential());

            List<String> endPointKeyframes = new ArrayList<>();
            diseaseOverviewResp.setKeyframe(new ArrayList<>());
            if(diseaseOverview.getKeyframe()!=null && !diseaseOverview.getKeyframe().isEmpty()){
                List<String> keyframes = Arrays.asList(diseaseOverview.getKeyframe().split(","));
                if(!keyframes.isEmpty()){
                    for(String keyframe : keyframes){
                        if(!keyframe.startsWith(endpoint)){
                            keyframe = endpoint + Constant.FILE_SEPARATOR + Constant.UPLOAD_BUCKET + keyframe;
                            endPointKeyframes.add(keyframe);
                        }
                    }
                }
                if(endPointKeyframes.isEmpty()){
                    diseaseOverviewResp.setKeyframe(keyframes);
                }else {
                    diseaseOverviewResp.setKeyframe(endPointKeyframes);
                }

            }
        }
        return diseaseOverviewResp;
    }

    /**
     * 新增保存疾病信息
     *
     * @param diseaseOverviewReq 疾病概述信息
     * @return 结果
     */
    @Override
    public DiseaseOverview editDiseaseOverview(DiseaseOverviewReq diseaseOverviewReq) {
        DiseaseOverview diseaseOverview = diseaseOverviewService.getById(diseaseOverviewReq.getDiseaseId());
        if(diseaseOverview == null){
            //没有，需要新增
            diseaseOverview = new DiseaseOverview();
            diseaseOverview.setDiseaseId(diseaseOverviewReq.getDiseaseId());
            diseaseOverview.setCreateTime(new Date());
            diseaseOverview.setCreatedBy(LoginUtil.getLoginUserId());
        }else {
            diseaseOverview.setUpdateTime(new Date());
            diseaseOverview.setUpdateBy(LoginUtil.getLoginUserId());
        }
        if(diseaseOverviewReq.getOverview()!=null){
            diseaseOverview.setOverview(diseaseOverviewReq.getOverview());
        }
        if(diseaseOverviewReq.getPathology()!=null){
            diseaseOverview.setPathology(diseaseOverviewReq.getPathology());
        }
        if(diseaseOverviewReq.getClinical()!=null){
            diseaseOverview.setClinical(diseaseOverviewReq.getClinical());
        }
        if(diseaseOverviewReq.getImaging()!=null){
            diseaseOverview.setImaging(diseaseOverviewReq.getImaging());
        }
        if(diseaseOverviewReq.getTreatment()!=null){
            diseaseOverview.setTreatment(diseaseOverviewReq.getTreatment());
        }
        if(diseaseOverviewReq.getRemark()!=null){
            diseaseOverview.setRemark(diseaseOverviewReq.getRemark());
        }
        if(diseaseOverviewReq.getDifferential()!=null){
            diseaseOverview.setDifferential(diseaseOverviewReq.getDifferential());
        }
        if(diseaseOverviewReq.getKeyframe()!=null){
            diseaseOverview.setKeyframe(String.join(",", diseaseOverviewReq.getKeyframe()));
        }
        diseaseOverviewService.saveOrUpdate(diseaseOverview);
        return diseaseOverviewService.getById(diseaseOverview.getDiseaseId());
    }

    /**
     * 删除疾病信息
     *
     * @param overviewId 疾病概述ID
     * @return 结果
     */
    @Override
    public boolean deleteDiseaseById(Long overviewId) {
        return diseaseOverviewService.removeById(overviewId);
    }
}