package com.jusha.caselibrary.system.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 疾病相关联文件返回实体
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
public class DiseaseFileResp {

    @ApiModelProperty(value = "主键")
    private Long fileId;

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件URL")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建人用户名")
    private String createByName;

}
