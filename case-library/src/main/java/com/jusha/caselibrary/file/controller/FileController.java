package com.jusha.caselibrary.file.controller;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName FileController
 * @Description 文件管理控制器
 * <AUTHOR>
 * @Date 2025/7/11 11:00
 **/
@Api(tags = "文件上传")
@RequestMapping("/file")
@Slf4j
@RequiredArgsConstructor
@RestController
public class FileController {

    private final DiseaseFileService resourceService;

    /**
     * Upload result bean.
     * @return the result bean
     */
    @ApiOperation(value = "获取上传策略")
    @GetMapping(value = "/getPostPolicy")
    public ResultBean<SignatureBucketResponse> getPostPolicy(@RequestParam("fileName")String fileName, String bucketName, String path){
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        SignatureBucketResponse signatureBucketResponse = fileService.getPostPolicy(fileName,bucketName,path);
        return ResultBean.success(signatureBucketResponse);
    }
}
