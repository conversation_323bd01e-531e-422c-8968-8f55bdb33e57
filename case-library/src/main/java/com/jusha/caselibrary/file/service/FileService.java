package com.jusha.caselibrary.file.service;

import com.jusha.caselibrary.file.resp.SignatureBucketResponse;

/**
 * @ClassName FileService
 * @Description 文件服务接口
 * <AUTHOR>
 * @Date 2025/7/11 11:03
 **/
public interface FileService {

    /**
     * 单个文件上传到minio
     * @param objectKey,filePath
     * @return
     */
    boolean uploadFile(String objectKey, String filePath);

    /**
     * 文件删除
     * @param objectKey
     */
    void removeFile(String objectKey);

    /**
     * 获取上传凭证，有效期只有十分钟
     * @param fileName
     * @return
     */
    SignatureBucketResponse getPostPolicy(String fileName, String bucket);
}
