package com.jusha.caselibrary.file.dto;

import com.tencent.cloud.Response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * COS上传凭证
 *
 * <AUTHOR>
 * @date 2025/02/07
 **/
@Data
public class PostPolicyCOS {

    @ApiModelProperty(value = "COS目标桶")
    private String bucketName;

    @ApiModelProperty(value = "文件全路径")
    private String key;

    @ApiModelProperty(value = "COS上传地区")
    private String region;

    @ApiModelProperty(value = "COS端点")
    private String endpoint;

    @ApiModelProperty(value = "签名信息")
    private Response response;
}
