package com.jusha.caselibrary.file.service.impl;

import com.jusha.caselibrary.common.acHolder.PropertiesBean;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.file.dto.PostPolicyMinio;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseFileService;
import io.minio.*;
import io.minio.errors.MinioException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service("fileService_LAN")
public class FileServiceImpl implements FileService {

    private final DiseaseFileService resourceService;
    private final PropertiesBean propertiesBean;

    @Value("${minio.endpoint:}")
    private String endpoint;


    @Value("${minio.accessKey:}")
    private String accessKey;

    @Value("${minio.secretKey:}")
    private String secretKey;

    /**
     * 桶占位符
     */
    private static final String BUCKET_PARAM = "${bucket}";
    /**
     * bucket权限-只读
     */
    private static final String READ_ONLY = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucket\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";
    /**
     * bucket权限-只读
     */
    private static final String WRITE_ONLY = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucketMultipartUploads\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:AbortMultipartUpload\",\"s3:DeleteObject\",\"s3:ListMultipartUploadParts\",\"s3:PutObject\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";
    /**
     * bucket权限-读写
     */
    private static final String READ_WRITE = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucket\",\"s3:ListBucketMultipartUploads\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:DeleteObject\",\"s3:GetObject\",\"s3:ListMultipartUploadParts\",\"s3:PutObject\",\"s3:AbortMultipartUpload\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";


    @PostConstruct
    public void init() {
        createMinioClient();
        createDefaultBucket(Constant.UPLOAD_BUCKET);
    }

    private MinioClient minioClient = null;

    private void createMinioClient(){
        MinioClient.Builder builder = MinioClient.builder();
        builder.endpoint(endpoint);
        if (StringUtils.isNotBlank(accessKey) && StringUtils.isNotBlank(secretKey)) {
            builder.credentials(accessKey,secretKey);
        }
        minioClient = builder.build();
    }

    private void createDefaultBucket(String bucketName){
        try {
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!isExist) {
                // 新建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                setBucketPolicy(bucketName,"read-write");
            }
        }catch (Exception e){
            log.error("新建存储桶失败"+e);
        }

    }

    /**
     * 更新桶权限策略
     *
     * @param bucket 桶
     * @param policy 权限
     */
    public void setBucketPolicy(String bucket, String policy){
        try {
            switch (policy) {
                case "read-only":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(READ_ONLY.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "write-only":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(WRITE_ONLY.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "read-write":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(READ_WRITE.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "none":
                default:
                    break;
            }
        }catch (Exception e){
            log.error("设置存储桶策略失败"+e);
        }
    }

    /**
     * 上传本地文件
     *
     * @param objectKey 文件key
     * @param filePath  文件路径
     * @return 文件url
     */
    @Override
    public boolean uploadFile(String objectKey, String filePath){
        try {
            ObjectWriteResponse response = minioClient.uploadObject(UploadObjectArgs.builder().bucket(Constant.UPLOAD_BUCKET).object(objectKey).filename(filePath).build());
            if(response.versionId()!=null){
                return true;
            }
        } catch (Exception e) {
            log.error("==============上传文件错误=============");
        }
        return false;
    }

    /**
     * 删除文件
     *
     * @param bucket    桶
     * @param objectKey 文件key
     * @return 文件url
     */
    public void removeFile(String bucket, String objectKey){
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucket).object(objectKey).build());
        } catch (Exception e) {
            log.error("==============删除文件错误=============");
        }
    }


    /**
     * 删除文件
     * @param objectKey 文件key
     * @return 文件url
     */
    @Override
    public void removeFile(String objectKey){
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(Constant.UPLOAD_BUCKET).object(objectKey).build());
        } catch (Exception e) {
            log.error("==============删除文件错误=============");
        }
    }

    @Override
    public SignatureBucketResponse getPostPolicy(String fileName, String bucket) {
        SignatureBucketResponse signatureBucketResponse = new SignatureBucketResponse();
        signatureBucketResponse.setServerType("LAN");
        PostPolicyMinio postPolicyMinio = createMinioPostPolicy(fileName,bucket);
        signatureBucketResponse.setPostPolicyMinio(postPolicyMinio);
        return signatureBucketResponse;
    }

    private PostPolicyMinio createMinioPostPolicy(String fileName, String bucket) {
        PostPolicyMinio postPolicyMinio = new PostPolicyMinio();
        // 设置凭证过期时间
        ZonedDateTime expirationDate = ZonedDateTime.now().plusMinutes(Constant.upload_expire_time);
        // 创建一个凭证
        String key = "";
        if(!fileName.endsWith(".zip") && !fileName.endsWith(".rar")){
            key = Constant.FILE_SEPARATOR + DateUtil.convertDateToStr(new Date(),DateUtil.YEAR_AND_MONTH_PATTERN) + Constant.FILE_SEPARATOR + fileName;
        }else {
            key = fileName;
        }

        PostPolicy policy = new PostPolicy(bucket==null?Constant.UPLOAD_BUCKET:bucket, key , expirationDate);
        // 限制文件大小，单位是字节byte，也就是说可以设置如：只允许10M以内的文件上传
        // policy.setContentRange(1, 10 * 1024);
        // 限制上传文件请求的ContentType
        // policy.setContentType("image/png");
        try {
            // 生成凭证并返回
            final Map<String, String> map = minioClient.presignedPostPolicy(policy);
            postPolicyMinio.setBucket(map.get("bucket"));
            postPolicyMinio.setEndpoint(endpoint);
            postPolicyMinio.setXAmzDate(map.get("x-amz-date"));
            postPolicyMinio.setXAmzSignature(map.get("x-amz-signature"));
            postPolicyMinio.setKey(map.get("key"));
            postPolicyMinio.setXAmzAlgorithm(map.get("x-amz-algorithm"));
            postPolicyMinio.setXAmzCredential(map.get("x-amz-credential"));
            postPolicyMinio.setPolicy(map.get("policy"));
        } catch (MinioException | InvalidKeyException | IOException | NoSuchAlgorithmException e) {
            log.error("===========获取minio上传凭证有误====="+e);
        }
        return postPolicyMinio;
    }
}