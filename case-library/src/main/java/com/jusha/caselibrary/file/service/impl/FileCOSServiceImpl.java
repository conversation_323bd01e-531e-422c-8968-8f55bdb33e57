package com.jusha.caselibrary.file.service.impl;

import com.alibaba.fastjson.JSON;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.file.dto.PostPolicyCOS;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseFileService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.model.UploadResult;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RequiredArgsConstructor
@Service("fileService_WAN")
public class FileCOSServiceImpl implements FileService {

    private final DiseaseFileService resourceService;

    private COSClient cosClient;

    @Value("${cos.bucketName:}")
    private String bucketName;

    @Value("${cos.endpoint:}")
    private String endpointCOS;

    @Value("${cos.secretId}")
    private String secretId;

    @Value("${cos.secretKey}")
    private String secretKey;

    @Value("${cos.region}")
    private String region;


    @PostConstruct
    public void init() {
        COSCredentials cosCred = new BasicCOSCredentials(secretId, secretKey);
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setRegion(new Region(region));
        cosClient = new COSClient(cosCred, clientConfig);
    }

    @Override
    public void removeFile(String objectKey) {
        try {
            cosClient.deleteObject(bucketName,objectKey);
        } catch (CosServiceException e) {
            log.error("CosServiceException:", e);
        } catch (CosClientException e) {
            log.error("CosClientException:", e);
        }
    }

    @Override
    public boolean uploadFile(String fileName,String absolutePath) {
        String dateString = DateUtil.convertDateToStr(new Date(),DateUtil.DAY_OF_YEAR_PATTERN);
        String objectKey = Constant.UPLOAD_BUCKET + Constant.FILE_SEPARATOR + dateString +Constant.FILE_SEPARATOR + fileName;
        ExecutorService threadPool = Executors.newFixedThreadPool(8);
        TransferManager transferManager = new TransferManager(cosClient, threadPool);
        try (
                InputStream inputStream = new BufferedInputStream(Files.newInputStream(Paths.get(absolutePath)))) {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(inputStream.available());
            objectMetadata.setContentDisposition("inline");
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, inputStream, objectMetadata);
            putObjectRequest.setStorageClass(StorageClass.Standard);
            Upload upload = transferManager.upload(putObjectRequest);
            UploadResult uploadResult = upload.waitForUploadResult();
            if(uploadResult.getKey()!=null){
                log.info("Upload success: {}", JSON.toJSONString(uploadResult));
                return true;
            }
        } catch (Exception e) {
            log.error("Upload to COS failed", e);
            return false;
        } finally {
            transferManager.shutdownNow(true);
            threadPool.shutdown();
        }
        return false;
    }


    /**
     * 获取上传凭证，有效期只有十分钟
     * @param fileName
     * @return
     */
    @Override
    public SignatureBucketResponse getPostPolicy(String fileName,String bucket,String path){
        SignatureBucketResponse signatureBucketResponse = new SignatureBucketResponse();
        PostPolicyCOS postPolicyCOS = createMinioPostCOS(fileName,path);
        signatureBucketResponse.setPostPolicyCOS(postPolicyCOS);
        signatureBucketResponse.setPostPolicyMinio(null);
        return signatureBucketResponse;
    }

    private PostPolicyCOS createMinioPostCOS(String fileName,String path){
        PostPolicyCOS postPolicyCOS = new PostPolicyCOS();
        TreeMap<String, Object> config = new TreeMap<String, Object>();
        try {
            config.put("secretId", secretId);
            config.put("secretKey", secretKey);
            // 设置域名,可通过此方式设置内网域名
            //config.put("host", "sts.internal.tencentcloudapi.com");
            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 600);
            config.put("bucket", bucketName);
            // bucket 所在地区
            config.put("region", region);
            config.put("allowPrefixes", new String[]{"*"});
            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                    // 简单上传
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    // 分片上传
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
            };
            config.put("allowActions", allowActions);
            Response response = CosStsClient.getCredential(config);
            postPolicyCOS.setBucketName(bucketName);

            String diseasePath = path == null?"": Constant.FILE_SEPARATOR + path;
            String datePath = Constant.FILE_SEPARATOR + DateUtil.convertDateToStr(new Date(),DateUtil.DAY_OF_YEAR_PATTERN);
            postPolicyCOS.setKey(bucketName+ diseasePath + datePath + fileName);
            postPolicyCOS.setRegion(region);
            postPolicyCOS.setEndpoint(endpointCOS);
            postPolicyCOS.setResponse(response);
        } catch (Exception e) {
            log.error("===========获取COS上传凭证有误====="+e);
        }
        return postPolicyCOS;
    }

}