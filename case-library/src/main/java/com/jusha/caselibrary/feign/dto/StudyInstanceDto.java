package com.jusha.caselibrary.feign.dto;

import lombok.Data;

import java.util.HashSet;
import java.util.List;

/**
 * The type Study dto.
 */
@Data
public class StudyInstanceDto {

    private String studyInstanceUID;

    private String accessNumber;

    private String studyId;

    private String studyDate;

    private String studyTime;

    private String studyDescription;

    private Integer numberOfSeries;

    private HashSet<String> modalities;

    private String institutionName;

    private String patientId;

    private String patientName;

    private String patientSex;

    private String patientAge;

    private String patientBirthDate;

    private String patientWeight;

    private String dispatched;

    private List<SeriesInstanceDto> seriesList;

    private String metaBucket;

    private String endpoint;
}