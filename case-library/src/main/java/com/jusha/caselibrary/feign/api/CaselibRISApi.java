package com.jusha.caselibrary.feign.api;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.aop.StateCheck;
import com.jusha.caselibrary.sickcase.dto.req.RISStudyReq;
import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@StateCheck
@FeignClient(name = "caselibrary-ris", path = "/caselibrary-ris")
@Component
public interface CaselibRISApi {

    /**
     * 获取当前用户的所属联盟信息
     * @return
     */
    @PostMapping(value = "/forbid/study/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultBean<List<RISStudy>> queryStudyList(@RequestBody RISStudyReq studyReq);
}
