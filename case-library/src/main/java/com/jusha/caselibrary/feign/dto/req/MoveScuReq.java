package com.jusha.caselibrary.feign.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * The type Move scu req.
 */
@ApiModel(value = "MoveScuReq对象", description = "请求参数")
@Data
public class MoveScuReq {

    @ApiModelProperty(value = "SCP的AET")
    private String remoteAet;

    @ApiModelProperty(value = "检索级别枚举：STUDY/SERIES")
    private String queryRetrieveLevel;

    @ApiModelProperty(value = "检查UID")
    @NotBlank(message = "检查UID不能为空")
    private String studyInstanceUID;

    @ApiModelProperty(value = "序列UID")
    private String seriesInstanceUID;

    public MoveScuReq(String queryRetrieveLevel, String studyInstanceUID) {
        this.queryRetrieveLevel = queryRetrieveLevel;
        this.studyInstanceUID = studyInstanceUID;
    }
}