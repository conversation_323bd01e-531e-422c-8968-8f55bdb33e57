package com.jusha.caselibrary.feign.dto.req;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.*;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@Data
public class SysMenu {

    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

    @ApiModelProperty(value = "所属平台id")
    private Long platId;

    @ApiModelProperty(value = "菜单名称")
    @Size(message = "菜单名称长度不合法",max = 20)
    private String menuName;

    @ApiModelProperty(value = "父菜单ID")
    private Long parentId;

    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    @ApiModelProperty(value = "路由地址")
    @Size(message = "路由地址长度不合法",max = 50)
    private String path;

    @ApiModelProperty(value = "访问路径")
    @Size(message = "访问路径长度不合法",max = 50)
    private String component;

    @ApiModelProperty(value = "菜单类型（M目录 C菜单 F按钮）")
    private String menuType;

    @ApiModelProperty(value = "菜单状态（0显示 1隐藏）")
    private String visible;

    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    private String status;

    @ApiModelProperty(value = "关键字")
    @Size(message = "关键字长度不合法",max = 50)
    private String keyWord;

    @ApiModelProperty(value = "关键字描述")
    private String keyDescribe;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

}
