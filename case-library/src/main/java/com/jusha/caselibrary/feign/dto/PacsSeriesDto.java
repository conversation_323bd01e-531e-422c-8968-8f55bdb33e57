package com.jusha.caselibrary.feign.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
@Data
public class PacsSeriesDto {

    @ApiModelProperty(value = "(0010,0020)#患者ID-------------")
    private String patientId;

    @ApiModelProperty(value = "(0010,0010)#患者姓名")
    private String patientName;

    @ApiModelProperty(value = "(0010,0030)#患者出生日期: YYYYMMDD")
    private String birthDate;

    @ApiModelProperty(value = "(0010,0040)#患者性别: M")
    private String sex;

    @ApiModelProperty(value = "(0020,000D)#检查UID--------------------")
    @NotBlank(message = "studyUid不能为空")
    @JSONField(name = "studyInstanceUID")
    private String studyInstanceUID;

    @ApiModelProperty(value = "(0008,0050)#检查号")
    private String accessNumber;

    @ApiModelProperty(value = "(0020,000E)#序列UID--------------------")
    @NotBlank(message = "seriesUid不能为空")
    @JSONField(name = "seriesInstanceUID")
    private String seriesInstanceUID;

    @ApiModelProperty(value = "(0008,103E)#序列描述")
    @JSONField(name = "seriesDescription")
    private String seriesDescription;

    @ApiModelProperty(value = "(0008,0060)#检查模态(设备)")
    private String modality;

    @ApiModelProperty(value = "上传人")
    private Long uploadUserId;

}