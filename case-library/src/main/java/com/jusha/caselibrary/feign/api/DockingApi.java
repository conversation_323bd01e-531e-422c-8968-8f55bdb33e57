package com.jusha.caselibrary.feign.api;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.aop.StateCheck;
import com.jusha.caselibrary.sickcase.dto.resp.RISStudy;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

@StateCheck
@FeignClient(name = "docking", path = "/docking")
@Component
public interface DockingApi {

    /**
     * 对接RIS
     * @return
     */
    @GetMapping(value = "/verify/get/studyInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultBean<List<RISStudy>> getStudyInfo(@RequestParam("keyword") String keyword);
}
