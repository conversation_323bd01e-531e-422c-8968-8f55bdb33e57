package com.jusha.caselibrary.feign.api;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.aop.StateCheck;
import com.jusha.caselibrary.feign.dto.GroupMsgDto;
import com.jusha.caselibrary.feign.dto.req.SysMenu;
import com.jusha.caselibrary.system.dto.RedisUser;
import com.jusha.caselibrary.system.dto.SysRole;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@StateCheck
@FeignClient(name = "auth-server", path = "/auth-server")
@Component
public interface AuthServerApi {

    /**
     * 获取当前用户的所属联盟信息
     * @return
     */
    @GetMapping(value = "/forbid/group/getAllianceMsg", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultBean<GroupMsgDto> getAllianceMsg();

    /**
     * 获取用户详细信息
     * @return
     */
    @GetMapping(value = "/forbid/user/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultBean<RedisUser.SysUser> getSysUser(@RequestParam("userId") Long userId);

    /**
     * @description 根据菜单名称获取菜单列表
     * <AUTHOR>
     * @date 2025/7/4 14:34
     * @param menuName
     * @return ResultBean
     **/
    @GetMapping("/forbid/menu/getMenuByName")
    ResultBean<List<SysMenu>> getMenuListByName(@RequestParam("menuName") String menuName);


    /**
     * @description 添加菜单
     * <AUTHOR>
     * @date 2025/7/4 14:35
     * @param menu
     * @return ResultBean
     **/
    @PostMapping("/forbid/menu/add")
    ResultBean<SysMenu> addMenu(@RequestBody SysMenu menu);

    /**
     * @description 修改菜单（只允许修改基本属性）
     * <AUTHOR>
     * @date 2025/7/4 14:35
     * @param menu
     * @return ResultBean
     **/
    @PostMapping("/forbid/menu/edit")
    ResultBean<Boolean> editMenu(@RequestBody SysMenu menu);

    /**
     * @description 删除菜单
     * <AUTHOR>
     * @date 2025/7/4 15:30
     * @param menuId
     * @return ResultBean
     **/
    @PostMapping("/forbid/menu/remove")
    ResultBean<Boolean> removeMenu(@RequestParam("menuId") Long menuId);

    /**
     * @description 获取角色列表
     * <AUTHOR>
     * @date 2025/7/4 16:08
     * @return ResultBean
     **/
    @GetMapping("/forbid/role/noPage/list")
    ResultBean<List<SysRole>> roleList();


    /**
     * @description 根据角色id查询菜单id和父级id
     * <AUTHOR>
     * @date 2025/7/4 16:12
     * @param roleId
     * @return ResultBean
     **/
    @GetMapping("/forbid/menu/menuIdsByRoleId/list")
    ResultBean<List<Long>> menuIdsByRoleId(@ApiParam(required = true, name = "角色ID") @RequestParam("roleId") Long roleId);

    /**
     * @description 修改角色
     * <AUTHOR>
     * @date 2025/7/4 16:01
     * @param role
     * @return ResultBean
     **/
    @PostMapping(value = "/forbid/role/edit")
    ResultBean<Boolean> editRole(@Validated @RequestBody SysRole role);

    /**
     * @description 根据菜单id获取当前菜单详情
     * <AUTHOR>
     * @date 2025/7/4 16:12
     * @param menuId
     * @return ResultBean
     **/
    @GetMapping("/forbid/menu/getMenuById")
    ResultBean<SysMenu> getMenuById(@RequestParam("menuId") Long menuId);


    /**
     * @description 获取所有用户信息
     * <AUTHOR>
     * @date 2025/8/4 10:22
     * @return ResultBean<List<SysUser>>
     **/
    @GetMapping("/forbid/user/getAllUsers")
    ResultBean<List<RedisUser.SysUser>> getAllUsers();
}
