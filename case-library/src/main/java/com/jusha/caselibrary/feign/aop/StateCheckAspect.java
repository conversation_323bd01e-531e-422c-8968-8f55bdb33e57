package com.jusha.caselibrary.feign.aop;

import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.resp.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @StateCheck注解对应的Aspect类
 */
@Aspect
@Slf4j
@Component
public class StateCheckAspect {

    @Around(value = "@within(com.jusha.caselibrary.feign.aop.StateCheck)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        //获取方法
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        Method method = methodSignature.getMethod();
        //方法执行
        Object result = pjp.proceed();

        //获取方法注解
        StateCheckNot checkNot = method.getAnnotation(StateCheckNot.class);
        if(checkNot != null){
            return result;
        }
        if(result instanceof ResultBean){
            ResultBean resultBean = (ResultBean) result;
            if(!resultBean.getState()) throw new BusinessException(resultBean.getErrorCode(), resultBean.getMessage());
        }
        return result;
    }

}