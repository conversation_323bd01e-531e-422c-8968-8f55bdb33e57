package com.jusha.caselibrary.feign.api;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.aop.StateCheck;
import com.jusha.caselibrary.feign.dto.StudyInstanceDto;
import com.jusha.caselibrary.feign.dto.req.MoveScuReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @title PacsFeign
 * @description
 * @date 2025/7/16
 */

@StateCheck
@FeignClient(name = "dimse", path = "/dimse")
@Component
public interface DimseApi {

    @ApiOperation(value = "主动向医院的PACS发送Move请求")
    @PostMapping(value = "/scu/move", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultBean<String> scuMove(@RequestBody MoveScuReq moveScuReq);

    @ApiOperation(value = "查看PACS的Move结果")
    @GetMapping("/scu/move/state")
    ResultBean<Boolean> scuMoveState(@RequestParam("taskId") String taskId);
}
