package com.jusha.caselibrary.feign.api;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.feign.aop.StateCheck;
import com.jusha.caselibrary.feign.dto.StudyInstanceDto;
import com.jusha.caselibrary.feign.dto.req.MoveScuReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @title PacsFeign
 * @description
 * @date 2025/7/16
 */

@StateCheck
@FeignClient(name = "pacs", path = "/pacs")
@Component
public interface PacsApi {

    @ApiOperation(value = "根据检查唯一编号获取study列表")
    @GetMapping("/study/getList")
    ResultBean<List<StudyInstanceDto>> getStudyList(@RequestParam("studyInstanceUID") String studyInstanceUID);
}
