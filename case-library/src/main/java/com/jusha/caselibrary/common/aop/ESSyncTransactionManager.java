package com.jusha.caselibrary.common.aop;

import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.dto.SyncMessage;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.support.TransactionSynchronization;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ESSyncTransactionManager
 * @Description ES同步事务管理器
 * 实现TransactionSynchronization接口，确保ES同步在事务提交后执行
 * 管理单个事务内的所有ES同步消息，支持消息收集、批量发送、回滚清理
 * <AUTHOR>
 * @Date 2025/7/24 10:03
 **/
@Slf4j
@RequiredArgsConstructor
public class ESSyncTransactionManager implements TransactionSynchronization {

    private final RedisTemplate<String, Object> redisTemplate;
    private final SearchConfig searchConfig;
    
    /**
     * 存储当前事务中的所有同步消息
     */
    private final List<SyncMessageWrapper> syncMessages = new ArrayList<>();

    /**
     * 同步消息包装类，包含消息和相关配置
     */
    @Getter
    private static class SyncMessageWrapper {
        private final SyncMessage message;
        private final ESSync esSync;

        public SyncMessageWrapper(SyncMessage message, ESSync esSync) {
            this.message = message;
            this.esSync = esSync;
        }

    }

    /**
     * 添加同步消息到当前事务
     * @param message 同步消息
     * @param esSync ES同步注解配置
     */
    public void addSyncMessage(SyncMessage message, ESSync esSync) {
        if (message == null) {
            log.warn("尝试添加空的同步消息到事务管理器");
            return;
        }
        
        syncMessages.add(new SyncMessageWrapper(message, esSync));
        log.debug("添加ES同步消息到事务管理器，消息ID：{}，当前消息数量：{}", 
                message.getMessageId(), syncMessages.size());
    }

    /**
     * 事务提交后执行
     * 批量发送所有收集的同步消息
     */
    @Override
    public void afterCommit() {
        if (syncMessages.isEmpty()) {
            log.debug("事务提交后没有需要同步的ES消息");
            return;
        }

        log.info("事务提交成功，开始批量发送ES同步消息，消息数量：{}", syncMessages.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (SyncMessageWrapper wrapper : syncMessages) {
            try {
                sendToQueue(wrapper.getMessage(), wrapper.getEsSync());
                successCount++;
                
                log.debug("ES同步消息发送成功，消息ID：{}，操作类型：{}，索引类型：{}",
                        wrapper.getMessage().getMessageId(), 
                        wrapper.getMessage().getOperation(), 
                        wrapper.getMessage().getIndexType());
                        
            } catch (Exception e) {
                failureCount++;
                log.error("发送ES同步消息失败，消息ID：{}，错误：{}", 
                        wrapper.getMessage().getMessageId(), e.getMessage(), e);
            }
        }
        
        log.info("事务提交后ES同步消息发送完成，成功：{}，失败：{}", successCount, failureCount);
    }

    /**
     * 事务完成后清理资源
     * @param status 事务状态
     */
    @Override
    public void afterCompletion(int status) {
        try {
            if (status == STATUS_COMMITTED) {
                log.debug("事务已提交，ES同步消息已处理，清理资源");
            } else if (status == STATUS_ROLLED_BACK) {
                log.info("事务已回滚，清理未发送的ES同步消息，消息数量：{}", syncMessages.size());
            } else {
                log.warn("事务完成状态未知：{}，清理ES同步消息", status);
            }
        } finally {
            // 无论事务状态如何，都要清理资源
            syncMessages.clear();
            log.debug("ESSyncTransactionManager资源清理完成");
        }
    }

    /**
     * 事务挂起时的处理
     * 在嵌套事务场景中，外层事务挂起时调用
     */
    @Override
    public void suspend() {
        log.debug("事务挂起，当前ES同步消息数量：{}", syncMessages.size());
    }

    /**
     * 事务恢复时的处理
     * 在嵌套事务场景中，外层事务恢复时调用
     */
    @Override
    public void resume() {
        log.debug("事务恢复，当前ES同步消息数量：{}", syncMessages.size());
    }

    /**
     * 事务刷新前的处理
     * 在事务刷新（flush）前调用
     */
    @Override
    public void beforeCommit(boolean readOnly) {
        log.debug("事务即将提交，只读事务：{}，待发送ES同步消息数量：{}", readOnly, syncMessages.size());
        
        if (readOnly && !syncMessages.isEmpty()) {
            log.warn("只读事务中存在ES同步消息，这可能表示配置错误");
        }
    }

    /**
     * 事务完成前的处理
     * 在事务完成前调用，此时事务状态尚未确定
     */
    @Override
    public void beforeCompletion() {
        log.debug("事务即将完成，待处理ES同步消息数量：{}", syncMessages.size());
    }

    /**
     * 发送消息到Redis队列
     * @param message 同步消息
     * @param esSync ES同步注解配置
     */
    private void sendToQueue(SyncMessage message, ESSync esSync) {
        try {
            String queueName = esSync.delay() > 0 ? 
                    searchConfig.getMq().getQueues().getDelayQueue() :
                    searchConfig.getMq().getQueues().getSyncQueue();

            if (esSync.async()) {
                // 异步发送
                redisTemplate.opsForList().leftPush(queueName, message);
                log.debug("ES同步消息已异步发送到队列：{}", queueName);
            } else {
                // 同步发送（立即处理）
                redisTemplate.opsForList().leftPush(searchConfig.getMq().getQueues().getDelayQueue(), message);
                log.debug("ES同步消息已同步发送到延迟队列");
            }
        } catch (Exception e) {
            log.error("发送ES同步消息到Redis队列失败，消息ID：{}", message.getMessageId(), e);
            throw e;
        }
    }

    /**
     * 获取当前待处理的消息数量
     * @return 消息数量
     */
    public int getPendingMessageCount() {
        return syncMessages.size();
    }

    /**
     * 检查是否有待处理的消息
     * @return true如果有待处理的消息
     */
    public boolean hasPendingMessages() {
        return !syncMessages.isEmpty();
    }
}