package com.jusha.caselibrary.common.config;

import lombok.SneakyThrows;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.util.Arrays;


/**
 * RestTemplate配置
 **/
@Configuration
public class RestTemplateConfig {

    @SneakyThrows
    @Bean(name = "restTemplate")
    public RestTemplate restTemplate() {

        //sslSocketFactory
        TrustStrategy acceptingTrustStrategy = ((x509Certificates, authType) -> true);
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
        //requestConfig
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(100 * 1000)     //服务器返回数据(response)的时间，超过该时间抛出read timeout
                .setConnectTimeout(5 * 1000)      //连接上服务器(握手成功)的时间，超出该时间抛出connect timeout
                .setConnectionRequestTimeout(1000)   //从连接池中获取连接的超时时间，超过该时间未拿到可用连接，会抛出ConnectionPoolTimeoutException
                .build();
        //connectionManager
        int defaultMaxPerRoute = 200;
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", sslSocketFactory)
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(2 * defaultMaxPerRoute);        //设置整个连接池最大连接数
        connectionManager.setDefaultMaxPerRoute(defaultMaxPerRoute);  //路由是对maxTotal的细分
        //keepAliveStrategy
        ConnectionKeepAliveStrategy keepAliveStrategy = (httpResponse, httpContext) -> 90000;
        ///HttpClient
        CloseableHttpClient build = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .setKeepAliveStrategy(keepAliveStrategy)
                .build();
        
        //messageConverters
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_HTML, MediaType.TEXT_PLAIN));
        ///RestTemplate
        RestTemplate restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory(build));
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);
        
        return restTemplate;
    }

}