package com.jusha.caselibrary.common.util;

import cn.hutool.json.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.acHolder.PropertiesBean;
import com.jusha.caselibrary.common.constant.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * redis工具类
 */
public class RedisUtil {

    /**
     * 异步任务结果在redis中的key
     * @return
     */
    public static String asyncTaskKey(String taskName, String taskId){
        String applicationName = ContextHolder.propertiesBean().getApplicationName();

        return StringUtils.join(applicationName, ":task:", taskName, ":", taskId);
    }


    /**
     * 通用加锁的key (application名:lockKey:Class名.method名_参数)
     */
    public static String lockKey(String className, String methodName, List<String> params){
        String applicationName = ContextHolder.getBean(PropertiesBean.class).getApplicationName();

        StringBuilder sb = new StringBuilder();
        sb.append(applicationName).append(":lockKey:").append(className).append(".").append(methodName);
        if(CollectionUtils.isNotEmpty(params)){
            for(String param : params){
                sb.append("_").append(param);
            }
        }
        return sb.toString();
    }


    /**
     * 加锁
     * @param lockKey
     * @param lockVal
     * @param outSeconds：单位秒
     * @return: true-获取锁成功，false-获取锁失败
     */
    public static boolean lock(String lockKey, String lockVal, int outSeconds){
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        return redisTemplate.opsForValue().setIfAbsent(lockKey, lockVal, outSeconds, TimeUnit.SECONDS);
    }


    /**
     * 解锁
     * @param lockKey
     * @param lockVal
     */
    public static boolean unlock(String lockKey, String lockVal){
        if (StringUtils.isBlank(lockKey) || StringUtils.isBlank(lockVal))
            return false;

        boolean unlockResult = false;
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        if (lockVal.equals(redisTemplate.opsForValue().get(lockKey))) {
            unlockResult = redisTemplate.opsForValue().getOperations().delete(lockKey);
        }
        return unlockResult;
    }

    /**
     * @description 病例数据文件导出-任务在redis中的key
     * <AUTHOR>
     * @date 2024/2/21 9:25
     * @param taskId
     * @return String
     **/
    public static String caseExportTaskKey(String taskId){
        String applicationName = ContextHolder.getBean(PropertiesBean.class).getApplicationName();

        return StringUtils.join(applicationName, ":task:caseExport:", taskId);
    }

    /**
     * 获取字典配置 - 返回 dictValue -> dictLabel 的映射
     * @param dictType 字典类型
     * @return Map<dictValue, dictLabel>
     */
    public static Map<String, String> getDictConfig(String dictType) {
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        String sysDictData = redisTemplate.opsForValue().get(Constant.REDIS_DICT_KEY + dictType);
        Map<String, String> dictMap = new HashMap<>();

        if (StringUtils.isNotBlank(sysDictData)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(sysDictData);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject dictItem = jsonArray.getJSONObject(i);
                    String dictValue = dictItem.getString("dictValue");
                    String dictLabel = dictItem.getString("dictLabel");
                    if (StringUtils.isNotBlank(dictValue) && StringUtils.isNotBlank(dictLabel)) {
                        dictMap.put(dictValue, dictLabel);
                    }
                }
            } catch (Exception e) {
                // 解析异常时记录日志或处理
                e.printStackTrace();
            }
        }

        return dictMap;
    }

    /**
     * 获取字典配置 - 返回 dictLabel -> dictValue 的映射
     * @param dictType 字典类型
     * @return Map<dictLabel, dictValue>
     */
    public static Map<String, String> getDictConfigByLabel(String dictType) {
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        String sysDictData = redisTemplate.opsForValue().get(Constant.REDIS_DICT_KEY + dictType);
        Map<String, String> dictMap = new HashMap<>();

        if (StringUtils.isNotBlank(sysDictData)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(sysDictData);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject dictItem = jsonArray.getJSONObject(i);
                    String dictValue = dictItem.getString("dictValue");
                    String dictLabel = dictItem.getString("dictLabel");
                    if (StringUtils.isNotBlank(dictValue) && StringUtils.isNotBlank(dictLabel)) {
                        dictMap.put(dictLabel, dictValue);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dictMap;
    }

    /**
     * 根据字典值获取字典标签
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    public static String getDictLabel(String dictType, String dictValue) {
        Map<String, String> dictConfig = getDictConfig(dictType);
        return dictConfig.get(dictValue);
    }

    /**
     * 根据字典标签获取字典值
     * @param dictType 字典类型
     * @param dictLabel 字典标签
     * @return 字典值
     */
    public static String getDictValue(String dictType, String dictLabel) {
        Map<String, String> dictConfig = getDictConfigByLabel(dictType);
        return dictConfig.get(dictLabel);
    }

    /**
     * 获取完整的字典数据对象列表
     * @param dictType 字典类型
     * @return JSONArray 字典数据列表
     */
    public static JSONArray getDictFullData(String dictType) {
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        String sysDictData = redisTemplate.opsForValue().get(Constant.REDIS_DICT_KEY + dictType);

        if (StringUtils.isNotBlank(sysDictData)) {
            try {
                return JSONArray.parseArray(sysDictData);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return new JSONArray();
    }
}