package com.jusha.caselibrary.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 字典转换工具类
 */
public class DictConvertUtil {

    /**
     * 字典类型常量
     */
    public static class DictType {
        public static final String CASE_DIFFICULTY = "caseDifficulty";        // 难度等级
        public static final String CASE_CATEGORY = "caseCategory";            // 是否典型
        public static final String SOURCE_TYPE = "sourceType";                // 来源
        public static final String FOLLOW_STATUS = "followStatus";            // 随访状态
        public static final String QUALITY_MATCH = "qualityMatch";            // 定性匹配
        public static final String POSITION_MATCH = "positionMatch";          // 定位匹配
        public static final String PATIENT_TYPE = "patientType";              // 就诊类型
        public static final String STUDY_STATE = "studyState";                // 检查状态
        public static final String IS_POSITIVE = "isPositive";                // 阳性/阴性
        public static final String IS_PUBLIC = "isPublic";                    // 是否公有
        public static final String FOLLOW_TYPE = "followType";                // 随访类型
        public static final String AUDIT_TYPE = "auditType";                // 审核类型
        public static final String STATUS = "status";                // 审核状态
    }

    /**
     * 转换字典值为标签
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签，如果转换失败则返回原值
     */
    public static String convertToLabel(String dictType, String dictValue) {
        if (StringUtils.isBlank(dictValue) || StringUtils.isBlank(dictType)) {
            return dictValue;
        }

        String label = RedisUtil.getDictLabel(dictType, dictValue);
        return StringUtils.isNotBlank(label) ? label : dictValue;
    }

    /**
     * 转换难度等级
     */
    public static String convertDifficulty(String value) {
        return convertToLabel(DictType.CASE_DIFFICULTY, value);
    }

    /**
     * 转换是否典型
     */
    public static String convertCaseCategory(String value) {
        return convertToLabel(DictType.CASE_CATEGORY, value);
    }

    /**
     * 转换来源
     */
    public static String convertSourceType(String value) {
        return convertToLabel(DictType.SOURCE_TYPE, value);
    }

    /**
     * 转换随访状态
     */
    public static String convertFollowStatus(String value) {
        return convertToLabel(DictType.FOLLOW_STATUS, value);
    }

    /**
     * 转换定性匹配
     */
    public static String convertQualityMatch(String value) {
        return convertToLabel(DictType.QUALITY_MATCH, value);
    }

    /**
     * 转换审核类型
     */
    public static String convertAuditType(String value) {
        return convertToLabel(DictType.AUDIT_TYPE, value);
    }

    /**
     * 转换审核状态
     */
    public static String convertStatus(String value) {
        return convertToLabel(DictType.STATUS, value);
    }


    /**
     * 转换定位匹配
     */
    public static String convertPositionMatch(String value) {
        return convertToLabel(DictType.POSITION_MATCH, value);
    }

    /**
     * 转换就诊类型
     */
    public static String convertPatientType(String value) {
        return convertToLabel(DictType.PATIENT_TYPE, value);
    }

    /**
     * 转换检查状态
     */
    public static String convertStudyState(String value) {
        return convertToLabel(DictType.STUDY_STATE, value);
    }

    /**
     * 转换阳性/阴性
     */
    public static String convertIsPositive(String value) {
        return convertToLabel(DictType.IS_POSITIVE, value);
    }

    /**
     * 转换是否公有
     */
    public static String convertIsPublic(String value) {
        return convertToLabel(DictType.IS_PUBLIC, value);
    }

    /**
     * 转换随访类型
     */
    public static String convertFollowType(String value) {
        return convertToLabel(DictType.FOLLOW_TYPE, value);
    }
}
