package com.jusha.caselibrary.common.acHolder;


import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LoginUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * application.yml中配置的持有对象
 */
@RefreshScope
@Getter
@Component
public class PropertiesBean {

    @Value("${PLAT_ID}")
    private Long platId;            //平台ID

    @Value("${SERVER_NAME}")
    private String applicationName;    //服务名

    @Value("${ENVIRONMENT:}")
    private String environment;     //环境(dev、test、prod)

    @Value("${minio.bucketName:upload-bucket}")
    private String minioBucketName;


    @Value("${spring.cloud.nacos.config.server-addr}")
    private String nacosAddr;         //nacos地址

    @Value("${spring.cloud.nacos.config.namespace}")
    private String nacosNamespace;   //nacos命名空间

}