package com.jusha.caselibrary.common.util;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import com.jusha.caselibrary.common.exception.BusinessException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;

@Slf4j
public class Zip4jUtil {

    /**
     * 压缩文件夹
     * @param srcDirPath 需要压缩的目录路径
     * @param zipFilePath 生成的 ZIP文件路径
     * @param password 可选的密码，用于加密 ZIP 文件
     */
    @SneakyThrows
    public static void compressDirectory(String srcDirPath, String zipFilePath, String password) {
        ZipFile zipFile = new ZipFile(zipFilePath);

        // 设置压缩参数
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setCompressionLevel(CompressionLevel.FASTEST); // 设置压缩级别
        zipParameters.setIncludeRootFolder(true);   // 包括根目录

        if (StringUtils.isNotBlank(password)) {
            zipParameters.setEncryptFiles(true); // 启用文件加密
            zipParameters.setEncryptionMethod(EncryptionMethod.ZIP_STANDARD); //设置加密方法
            zipFile.setPassword(password.toCharArray()); // 设置密码
        }
        File sourceDir = new File(srcDirPath);
        zipFile.addFolder(sourceDir, zipParameters);
    }

    public static void unzipWithPassword(String zipFilePath, String destDirectory, String password){
        try {
            File destDir = new File(destDirectory);
            if (!destDir.exists()) {
                destDir.mkdir();
            }
            ZipFile zipFile = new ZipFile(zipFilePath);
            if (zipFile.isEncrypted()) {
                if(StringUtils.isNotBlank(password)){
                    zipFile.setPassword(password.toCharArray());
                }else{
                    throw new BusinessException("文件已被加密,但未输入密码");
                }
            }
            zipFile.extractAll(destDirectory);
        } catch (ZipException e) {
            e.printStackTrace();
            throw new BusinessException("文件解压失败,请确认密码");
        }
    }

    public static void extractRar(String rarFilePath, String destinationFolder){
        File rarFile = new File(rarFilePath);
        File destinationDir = new File(destinationFolder);
        if (!destinationDir.exists()) {
            destinationDir.mkdirs();
        }

        try (Archive archive = new Archive(rarFile)) {
            FileHeader fileHeader = archive.nextFileHeader();
            while (fileHeader != null) {
                String fileName = fileHeader.getFileNameString().trim();
                File outputFile = new File(destinationDir, fileName);
                if (fileHeader.isDirectory()) {
                    outputFile.mkdirs();
                } else {
                    try (OutputStream outputStream = new FileOutputStream(outputFile)) {
                        archive.extractFile(fileHeader, outputStream);
                    }
                }
                fileHeader = archive.nextFileHeader();
            }
        } catch (Exception e) {
            throw new BusinessException("文件解压失败");
        }
    }

    public static boolean isCompressedFile(MultipartFile multipartFile) {
        String originalFilename = multipartFile.getOriginalFilename();
        if (originalFilename == null) {
            return false;
        }
        String fileExtension = getFileExtension(originalFilename);
        return ".zip".equalsIgnoreCase(fileExtension) || ".rar".equalsIgnoreCase(fileExtension);
    }

    private static String getFileExtension(String fileName) {
        int index = fileName.lastIndexOf('.');
        if (index > 0 && index < fileName.length() - 1) {
            return fileName.substring(index).toLowerCase();
        }
        return "";
    }
}