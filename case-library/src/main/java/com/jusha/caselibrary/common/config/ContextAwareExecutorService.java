package com.jusha.caselibrary.common.config;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;

/**
 * @ClassName ContextAwareExecutorService
 * @Description 线程池包装器
 * <AUTHOR>
 * @Date 2025/8/7 20:18
 **/
public class ContextAwareExecutorService implements ExecutorService {

    private final ExecutorService delegate;

    public ContextAwareExecutorService(ExecutorService delegate) {
        this.delegate = delegate;
    }

    @Override
    public Future<?> submit(Runnable task) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context), result);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context));
    }

    @Override
    public void execute(Runnable command) {
        ThreadContext context = ThreadContext.capture();
        delegate.execute(wrapWithContext(command, context));
    }

    /**
     * 包装Runnable，添加上下文传递
     */
    private Runnable wrapWithContext(Runnable task, ThreadContext context) {
        return () -> {
            if (context != null) {
                context.apply();
            }
            try {
                task.run();
            } finally {
                if (context != null) {
                    context.clear();
                }
            }
        };
    }

    /**
     * 包装Callable，添加上下文传递
     */
    private <T> Callable<T> wrapWithContext(Callable<T> task, ThreadContext context) {
        return () -> {
            if (context != null) {
                context.apply();
            }
            try {
                return task.call();
            } finally {
                if (context != null) {
                    context.clear();
                }
            }
        };
    }

    // 委托其他方法到原始ExecutorService
    @Override
    public void shutdown() {
        delegate.shutdown();
    }

    @Override
    public List<Runnable> shutdownNow() {
        return delegate.shutdownNow();
    }

    @Override
    public boolean isShutdown() {
        return delegate.isShutdown();
    }

    @Override
    public boolean isTerminated() {
        return delegate.isTerminated();
    }

    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return delegate.awaitTermination(timeout, unit);
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
                .map(task -> wrapWithContext(task, context))
                .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAll(wrappedTasks);
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
            throws InterruptedException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
                .map(task -> wrapWithContext(task, context))
                .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAll(wrappedTasks, timeout, unit);
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks)
            throws InterruptedException, ExecutionException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
                .map(task -> wrapWithContext(task, context))
                .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAny(wrappedTasks);
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
            throws InterruptedException, ExecutionException, TimeoutException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
                .map(task -> wrapWithContext(task, context))
                .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAny(wrappedTasks, timeout, unit);
    }
}
