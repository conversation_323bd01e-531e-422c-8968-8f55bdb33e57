package com.jusha.caselibrary.common.config;


import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

/**
 * 用户类型校验
 */
@Slf4j
public class UserVerifyUtil {

    /**
     * 有顶层权限
     * @return
     */
    public static boolean hasTopAuthority(){
        Set<String> roleTypes = LoginUtil.getLoginUser().getRoleTypes();
        if(CollectionUtils.isEmpty(roleTypes))
            return false;

        return roleTypes.contains(Constant.ROLE_TYPE_1)?true:false;
    }

}