package com.jusha.caselibrary.common.config;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.system.dto.RedisUser;
import lombok.Data;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

/**
 * @ClassName ThreadContext
 * @Description 线程上下文
 * <AUTHOR>
 * @Date 2025/8/7 20:17
 **/
@Data
public class ThreadContext {
    private final Long userId;
    private final RedisUser user;
    private final String traceId;
    private final ServletRequestAttributes requestAttributes;
    private final Map<String, String> mdcContext;

    private ThreadContext(Long userId, RedisUser user, String traceId,
                          ServletRequestAttributes requestAttributes, Map<String, String> mdcContext) {
        this.userId = userId;
        this.user = user;
        this.traceId = traceId;
        this.requestAttributes = requestAttributes;
        this.mdcContext = mdcContext;
    }

    /**
     * 捕获当前线程的上下文
     */
    public static ThreadContext capture() {
        try {
            Long userId = LoginUtil.getLoginUserId();
            RedisUser user = LoginUtil.getLoginUser();
            String traceId = MDC.get(Constant.TRACEID);
            ServletRequestAttributes requestAttributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            Map<String, String> mdcContext = MDC.getCopyOfContextMap();

            return new ThreadContext(userId, user, traceId, requestAttributes, mdcContext);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 应用上下文到当前线程
     */
    public void apply() {
        if (requestAttributes != null) {
            RequestContextHolder.setRequestAttributes(requestAttributes);
        }
        if (mdcContext != null) {
            MDC.setContextMap(mdcContext);
        }
    }

    /**
     * 清理上下文
     */
    public void clear() {
        RequestContextHolder.resetRequestAttributes();
        MDC.clear();
    }
}
