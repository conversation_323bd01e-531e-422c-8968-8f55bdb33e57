package com.jusha.caselibrary.common.util;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.feign.api.AuthServerApi;
import com.jusha.caselibrary.system.dto.RedisUser;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName UserUtil
 * @Description 用户工具类
 * <AUTHOR>
 * @Date 2025/8/4 10:31
 **/
@Slf4j
public class UserUtil {

    private static AuthServerApi authServerApi;
    private static Map<String, RedisUser.SysUser> userCache = new ConcurrentHashMap<>();
    private static volatile long lastRefreshTime = 0;
    // 10分钟过期
    private static final long CACHE_EXPIRE_TIME = 10 * 60 * 1000;
    private static volatile boolean cacheInitialized = false;
    private static ScheduledExecutorService scheduler;

    static {
        try {
            authServerApi = ContextHolder.getContext().getBean(AuthServerApi.class);
            // 启动定时刷新任务
            startScheduledRefresh();
        } catch (Exception e) {
            log.error("初始化AuthServerApi失败", e);
        }
    }

    /**
     * 启动定时刷新任务
     */
    private static void startScheduledRefresh() {
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "UserCache-Refresh-Thread");
            t.setDaemon(true);
            return t;
        });
        
        // 每5分钟刷新一次缓存
        scheduler.scheduleAtFixedRate(() -> {
            try {
                refreshUserCache();
            } catch (Exception e) {
                log.error("定时刷新用户缓存失败", e);
            }
        }, CACHE_EXPIRE_TIME, CACHE_EXPIRE_TIME, TimeUnit.MILLISECONDS);
    }

    /**
     * 检查缓存是否过期
     */
    private static boolean isCacheExpired() {
        return System.currentTimeMillis() - lastRefreshTime > CACHE_EXPIRE_TIME;
    }

    /**
     * 初始化或刷新用户缓存
     */
    private static synchronized void initUserCache() {
        if (authServerApi == null) {
            return;
        }
        
        try {
            List<RedisUser.SysUser> allUsers = authServerApi.getAllUsers().getData();
            if (allUsers != null) {
                userCache = allUsers.stream()
                    .collect(Collectors.toConcurrentMap(
                        user -> String.valueOf(user.getUserId()),
                        user -> user,
                        (existing, replacement) -> replacement
                    ));
                lastRefreshTime = System.currentTimeMillis();
                cacheInitialized = true;
                log.debug("用户缓存刷新完成，共缓存{}个用户", userCache.size());
            }
        } catch (Exception e) {
            log.error("刷新用户缓存失败", e);
        }
    }

    /**
     * 根据用户ID获取用户信息（带缓存过期检查）
     */
    public static RedisUser.SysUser getUserById(String userId) {
        // 检查缓存是否需要刷新
        if (!cacheInitialized || isCacheExpired()) {
            initUserCache();
        }
        return userCache.get(userId);
    }

    /**
     * 根据用户ID获取用户信息（实时查询，不使用缓存）
     */
    public static RedisUser.SysUser getUserByIdRealTime(String userId) {
        if (authServerApi == null) {
            return null;
        }
        
        try {
            List<RedisUser.SysUser> sysUserList = authServerApi.getAllUsers().getData();
            if (sysUserList != null) {
                return sysUserList.stream()
                    .filter(user -> String.valueOf(user.getUserId()).equals(userId))
                    .findFirst()
                    .orElse(null);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("实时查询用户信息失败，userId: {}", userId, e);
            // 如果实时查询失败，回退到缓存
            return userCache.get(userId);
        }
    }

    /**
     * 根据用户ID获取用户信息（优先使用缓存，缓存未命中时实时查询）
     */
    public static RedisUser.SysUser getUserByIdWithFallback(String userId) {
        // 先尝试从缓存获取
        if (cacheInitialized && !isCacheExpired()) {
            RedisUser.SysUser cachedUser = userCache.get(userId);
            if (cachedUser != null) {
                return cachedUser;
            }
        }
        
        // 缓存未命中，实时查询
        RedisUser.SysUser user = getUserByIdRealTime(userId);
        if (user != null) {
            // 更新缓存
            userCache.put(userId, user);
        }
        return user;
    }

    // 重载方法支持Long类型
    public static RedisUser.SysUser getUserById(Long userId) {
        return userId != null ? getUserById(String.valueOf(userId)) : null;
    }

    public static RedisUser.SysUser getUserByIdRealTime(Long userId) {
        return userId != null ? getUserByIdRealTime(String.valueOf(userId)) : null;
    }

    public static RedisUser.SysUser getUserByIdWithFallback(Long userId) {
        return userId != null ? getUserByIdWithFallback(String.valueOf(userId)) : null;
    }

    /**
     * 根据用户ID获取用户姓名
     */
    public static String getUserNameById(String userId) {
        return getRealNameById(userId);
    }

    public static String getUserNameById(Long userId) {
        return userId != null ? getUserNameById(String.valueOf(userId)) : "未知用户";
    }

    /**
     * 根据用户ID获取用户真实姓名
     */
    public static String getRealNameById(String userId) {
        RedisUser.SysUser user = getUserByIdWithFallback(userId);
        return user != null ? user.getUserName() : "未知用户";
    }

    public static String getRealNameById(Long userId) {
        return userId != null ? getRealNameById(String.valueOf(userId)) : "未知用户";
    }

    /**
     * 手动刷新用户缓存
     */
    public static synchronized void refreshUserCache() {
        cacheInitialized = false;
        initUserCache();
    }

    /**
     * 清除缓存，强制下次查询时重新加载
     */
    public static void clearCache() {
        userCache.clear();
        cacheInitialized = false;
        lastRefreshTime = 0;
    }

    /**
     * 获取缓存状态信息
     */
    public static String getCacheStatus() {
        return String.format("缓存大小: %d, 最后刷新时间: %d, 是否过期: %b", 
            userCache.size(), lastRefreshTime, isCacheExpired());
    }

    /**
     * 关闭定时器
     */
    public static void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}