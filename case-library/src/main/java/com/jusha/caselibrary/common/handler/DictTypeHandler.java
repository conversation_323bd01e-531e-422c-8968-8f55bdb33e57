package com.jusha.caselibrary.common.handler;

import com.jusha.caselibrary.common.util.RedisUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.commons.lang3.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 字典值转换TypeHandler
 * 将数据库中的字典值转换为对应的标签
 */
public class DictTypeHandler extends BaseTypeHandler<String> {

    private String dictType;

    public DictTypeHandler() {
        // 默认构造函数
    }

    public DictTypeHandler(String dictType) {
        this.dictType = dictType;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return convertToLabel(value);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return convertToLabel(value);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return convertToLabel(value);
    }

    private String convertToLabel(String value) {
        if (StringUtils.isBlank(value) || StringUtils.isBlank(dictType)) {
            return value;
        }

        String label = RedisUtil.getDictLabel(dictType, value);
        return StringUtils.isNotBlank(label) ? label : value;
    }
}
