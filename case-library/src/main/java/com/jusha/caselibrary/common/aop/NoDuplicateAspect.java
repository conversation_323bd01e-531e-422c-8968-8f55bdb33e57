package com.jusha.caselibrary.common.aop;

import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @NoDuplicate注解对应的Aspect类
 */
@Aspect
@Slf4j
@Component
public class NoDuplicateAspect {

    private SpelExpressionParser spelParser = new SpelExpressionParser();
    private DefaultParameterNameDiscoverer parameterNameDiscoverer= new DefaultParameterNameDiscoverer();


    @Around(value = "@annotation(com.jusha.caselibrary.common.aop.NoDuplicate)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        String lockKey = null;
        String lockVal = null;
        try {
            //获取方法
            MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
            Method method = methodSignature.getMethod();
            //获取方法注解
            NoDuplicate noDuplicate = method.getAnnotation(NoDuplicate.class);
            if(noDuplicate != null && noDuplicate.keys() != null && noDuplicate.keys().length > 0){
                String[] keys = noDuplicate.keys();
                int expireSeconds = noDuplicate.expireSeconds();
                int waitTag = noDuplicate.waitTag();

                List<String> params = analySpelKey(keys, pjp);
                if(CollectionUtils.isNotEmpty(params)){
                    String className = method.getDeclaringClass().getName();  //方法所在类名
                    String methodName = method.getName();  //方法名
                    //锁
                    lockKey = RedisUtil.lockKey(className, methodName, params);
                    lockVal = String.valueOf(YitIdHelper.nextId());
                    //尝试获取锁
                    if(waitTag == 1){
                        if(!RedisUtil.lock(lockKey, lockVal, expireSeconds)) {
                            throw new BusinessException(LocaleUtil.getLocale("common.repeat.request"));
                        }
                    }
                    else {
                        long interval = 0;    //时间间隔(秒)
                        long beginMill = System.currentTimeMillis()/1000;
                        int waitExpire = noDuplicate.waitExpire();   //尝试获取锁的超时时间
                        boolean lockResult = RedisUtil.lock(lockKey, lockVal, expireSeconds);
                        while (!lockResult && interval <= waitExpire){
                            Thread.sleep(100);
                            lockResult = RedisUtil.lock(lockKey, lockVal, expireSeconds);
                            interval = System.currentTimeMillis()/1000 - beginMill;
                        }
                        if(!lockResult) {
                            throw new BusinessException(LocaleUtil.getLocale("common.getlock.time.out"));
                        }
                    }
                    log.info("获取redis锁成功--> {} {}", lockKey, lockVal);
                }
            }
            //继续方法执行，并返回结果
            return pjp.proceed();
        }
        catch (Throwable e){
            throw e;
        }
        finally {
            //解锁
            if(RedisUtil.unlock(lockKey, lockVal)) {
                log.info("解redis锁--> {}  {}", lockKey, lockVal);
            }
        }
    }


    /**
     * 通过keys的spel表达式解析，生成动态参数列表
     * @param keys
     * @param pjp
     * @return
     */
    private List<String> analySpelKey(String[] keys, ProceedingJoinPoint pjp) {
        List<String> params = new ArrayList<>();

        if(keys != null && keys.length > 0){
            //Method
            MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
            Method method = methodSignature.getMethod();
            //paramNames
            String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
            //EvaluationContext
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = pjp.getArgs();
            if(args != null && args.length > 0){
                for(int i = 0 ; i < args.length ; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }
            //依次解析
            Arrays.stream(keys).forEach(item -> {
                Object paramObj = spelParser.parseExpression(item).getValue(context);
                if(paramObj != null){
                    params.add(paramObj.toString());
                }
            });
        }

        return params;
    }

}