package com.jusha.caselibrary.common.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * @EscapeWildcard注解对应的Aspect类
 */
@Aspect
@Component
public class EscapeWildcardAspect {

    @Before("@annotation(EscapeWildcard)")
    public void escapeWildcardParams(JoinPoint joinPoint) throws IllegalAccessException {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof String) {
                    // 对字符串类型的参数进行转义
                    String param = (String) arg;
                    if (containsWildcard(param)) {
                        // 处理通配符
                        escape(param);
                    }
                }
                if (arg instanceof Object) {
                    // 对于其他复杂对象，可以递归检查它的字段
                    escapeFields(arg);
                }
            }
        }
    }

    // 判断是否包含通配符
    private boolean containsWildcard(String param) {
        return param.contains("%");
    }

    // 转义字符串中的通配符
    private String escape(String param) {
        return param.replace("%", "\\%");
    }

    // 递归遍历对象及其父类字段进行转义
    private void escapeFields(Object obj) throws IllegalAccessException {
        Class<?> clazz = obj.getClass();
        while (clazz != null) { // 遍历当前类及其父类
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value instanceof String) {
                    String strValue = (String) value;
                    if (containsWildcard(strValue)) {
                        field.set(obj, escape(strValue)); // 转义字段值
                    }
                }
            }
            clazz = clazz.getSuperclass(); // 处理父类
        }
    }
}