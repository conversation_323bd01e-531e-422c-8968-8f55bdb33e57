package com.jusha.caselibrary.common.config;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.search.service.ESSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import javax.annotation.PostConstruct;

/**
 * @ClassName ESRestClientConfig
 * @Description ES RestClient 配置 - 使用SearchConfig中的超时和连接配置
 * <AUTHOR>
 * @Date 2025/7/7 13:44
 **/
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ESRestClientConfig {

    @Value("${ES_HOST}")
    private String servers;

    @Value("${ES_PORT}")
    private int port;

    @Value("${ES_USER}")
    private String user;

    @Value("${ES_PWD}")
    private String pwd;

    private final SearchConfig searchConfig;

    @PostConstruct
    public void configureElasticsearchProperties() {
        String uris = searchConfig.getRest().getUris() != null ? 
            searchConfig.getRest().getUris() : servers + ":" + port;
        
        // 动态设置 Spring Boot Elasticsearch 属性
        System.setProperty("spring.elasticsearch.rest.uris", uris);
        System.setProperty("spring.elasticsearch.rest.connection-timeout", 
            searchConfig.getRest().getConnectionTimeout() != null ? 
            searchConfig.getRest().getConnectionTimeout() : searchConfig.getConnectTimeout() + "ms");
        System.setProperty("spring.elasticsearch.rest.read-timeout", 
            searchConfig.getRest().getReadTimeout() != null ? 
            searchConfig.getRest().getReadTimeout() : searchConfig.getSocketTimeout() + "ms");
        
        // 使用 SearchConfig 中的认证信息
        String username = searchConfig.getRest().getUsername() != null ? 
            searchConfig.getRest().getUsername() : user;
        String password = searchConfig.getRest().getPassword() != null ? 
            searchConfig.getRest().getPassword() : pwd;
            
        if (username != null && !username.isEmpty()) {
            System.setProperty("spring.elasticsearch.rest.username", username);
        }
        if (password != null && !password.isEmpty()) {
            System.setProperty("spring.elasticsearch.rest.password", password);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        log.info("应用启动完成，开始初始化 Elasticsearch");
        
        // 异步执行初始化，避免阻塞应用启动
        java.util.concurrent.CompletableFuture.runAsync(() -> {
            try {
                // 1. 记录配置信息
                logElasticsearchConfig();
                // 2. 确保索引存在
                ESSyncService esSyncService = ContextHolder.getBean(ESSyncService.class);
                esSyncService.ensureIndexesExistOrCreate();
                log.info("Elasticsearch 初始化完成");
            } catch (Exception e) {
                log.error("Elasticsearch 初始化失败，但不影响应用启动", e);
            }
        });
    }
    
    /**
     * 记录 Elasticsearch 配置信息
     */
    private void logElasticsearchConfig() {
        String uris = searchConfig.getRest().getUris() != null ? 
            searchConfig.getRest().getUris() : servers + ":" + port;
        String username = searchConfig.getRest().getUsername() != null ? 
            searchConfig.getRest().getUsername() : user;
            
        log.info("Elasticsearch 连接配置 (来自 SearchConfig/Nacos):");
        log.info("  - 主机: {}", uris);
        log.info("  - 用户: {}", username != null && !username.isEmpty() ? username : "无认证");
        log.info("  - 连接超时: {}ms", searchConfig.getConnectTimeout());
        log.info("  - 套接字超时: {}ms", searchConfig.getSocketTimeout());
        log.info("  - 连接请求超时: {}ms", searchConfig.getConnectionRequestTimeout());
        log.info("  - 最大连接数: {}", searchConfig.getMaxConnTotal());
        log.info("  - 每路由最大连接: {}", searchConfig.getMaxConnPerRoute());
        log.info("  - 查询超时: {}ms", searchConfig.getPerformance().getQueryTimeoutMs());
        log.info("  - 慢查询阈值: {}ms", searchConfig.getPerformance().getSlowQueryThresholdMs());
    }

}