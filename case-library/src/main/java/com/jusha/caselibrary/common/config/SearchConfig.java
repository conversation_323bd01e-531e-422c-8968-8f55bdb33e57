package com.jusha.caselibrary.common.config;

import com.jusha.caselibrary.common.constant.Constant;
import lombok.Data;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @ClassName SearchConfig
 * @Description 搜索配置类
 * <AUTHOR>
 * @Date 2025/7/7 15:25
 **/
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "spring.elasticsearch")
public class SearchConfig {


    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;

    /**
     * 套接字超时时间（毫秒）
     */
    private int socketTimeout = 30000;

    /**
     * 连接请求超时时间（毫秒）
     */
    private int connectionRequestTimeout = 5000;

    /**
     * 最大连接数
     */
    private int maxConnTotal = 100;

    /**
     * 每个路由的最大连接数
     */
    private int maxConnPerRoute = 10;

    private Indexes indexes = new Indexes();
    private Rest rest = new Rest();
    private Page page = new Page();
    private Highlight highlight = new Highlight();
    private Mq mq = new Mq();
    private Sync sync = new Sync();
    private Cache cache = new Cache();
    private Performance performance = new Performance();
    private QueryOptimization queryOptimization = new QueryOptimization();

    @Data
    public static class Rest {
        private String uris;
        private String username;
        private String password;
        private String connectionTimeout;
        private String readTimeout;
    }

    @Data
    public static class Indexes {
        private String departmentCases;
        private String personalCases;
    }

    @Data
    public static class Page {
        private Integer defaultSize = 20;
        private Integer maxSize = 100;
    }

    @Data
    public static class Highlight {
        private String preTags;
        private String postTags;
        private Integer fragmentSize = 150;
        private Integer numberOfFragments = 3;
    }

    @Data
    public static class Mq {
        private Queues queues = new Queues();
        private Consumer consumer = new Consumer();

        @Data
        public static class Queues {
            private String syncQueue;
            private String delayQueue;
            private String retryQueue;
        }

        @Data
        public static class Consumer {
            private Integer batchSize = 10;
            private Long pullTimeout = 1000L;
            private Integer retryTimes = 3;
        }
    }

    @Data
    public static class Sync {
        private Integer batchSize = 100;
        private Integer retryCount = 3;
        private Long delayTime = 1000L;
    }

    @Data
    public static class Cache {
        private Boolean enabled = true;
        private String keyPrefix = "case_search:";
        private Integer defaultExpireSeconds = 300;
        private Integer maxCacheSize = 10000;
        private String cacheType = "redis"; // redis, caffeine, hybrid
        
        // Redis缓存配置
        private Redis redis = new Redis();
        
        // 本地缓存配置
        private Local local = new Local();
        
        @Data
        public static class Redis {
            private String keyPrefix = "search:";
            private Integer database = 1;
            private Integer expireSeconds = 300;
            private Boolean enableCompression = true;
        }
        
        public Boolean getEnableCompression() {
            return redis.getEnableCompression();
        }
        
        @Data
        public static class Local {
            private Integer maxSize = 1000;
            private Integer expireAfterWriteMinutes = 5;
            private Integer expireAfterAccessMinutes = 2;
        }
    }

    @Data
    public static class Performance {
        private Integer maxResultWindow = 10000;
        private Integer searchAfterThreshold = 5000;
        private Long queryTimeoutMs = 30000L;
        private Integer maxConcurrentSearches = 100;
        private Boolean enableSlowQueryLog = true;
        private Long slowQueryThresholdMs = 1000L;
        
        // 字段权重配置
        private FieldWeights fieldWeights = new FieldWeights();
        
        @Data
        public static class FieldWeights {
            private Float patientName = 2.0f;
            private Float diagnosis = 2.0f;
            private Float clinicalDiagnosis = 1.5f;
            private Float reportDiagnose = 1.5f;
            private Float reportDescribe = 1.0f;
            private Float medicalHistory = 1.0f;
            private Float caseName = 2.0f;
            private Float caseAnalysis = 1.5f;
            private Float followupResult = 1.2f;
            private Float pathology = 1.3f;
            private Float clinical = 1.3f;
            private Float imaging = 1.2f;
            private Float tags = 1.0f;
        }
        
        // 添加兼容性方法
        public Long getQueryTimeout() {
            return queryTimeoutMs;
        }
        
        public Long getSlowQueryThreshold() {
            return slowQueryThresholdMs;
        }
    }

    @Data
    public static class QueryOptimization {
        private Boolean enableSmartOptimization = true;
        private Boolean enableQueryTemplate = true;
        private Boolean enableQueryCache = true;
        private String minimumShouldMatch = "70%";
        private Integer maxClauseCount = 1024;
        
        // 复杂查询优化配置
        private ComplexQuery complexQuery = new ComplexQuery();
        
        // 深度分页优化配置
        private DeepPagination deepPagination = new DeepPagination();
        
        @Data
        public static class ComplexQuery {
            private Integer conditionThreshold = 5;
            private Boolean enableParallelExecution = true;
            private Integer maxParallelThreads = 4;
            private Boolean enableResultCaching = true;
        }
        
        @Data
        public static class DeepPagination {
            private Integer threshold = 10000;
            private Boolean forceSearchAfter = true;
            private Boolean enableScrollApi = false;
            private String scrollKeepAlive = "1m";
        }
    }
}