package com.jusha.caselibrary.common.config;

import com.google.common.base.Predicate;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Swagger配置类
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public Docket api() {
        boolean enableFlag = !Constant.SPRING_PROFILES_PROD.equals(ContextHolder.propertiesBean().getEnvironment());
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder().title("API接口文档").build())   //api文档的详细信息
                .select()
                .apis(RequestHandlerSelectors.basePackage(StringUtils.join("com.jusha.", ContextHolder.propertiesBean().getApplicationName())))
                .apis(new SwaggerFilter())
                .paths(PathSelectors.any())
                .build()
                .enable(enableFlag);
    }


    private class SwaggerFilter implements Predicate<RequestHandler> {
        @Override
        public boolean apply(@Nullable RequestHandler requestHandler) {
            assert requestHandler != null;
            return requestHandler.findAnnotation(ApiOperation.class).isPresent();
        }
    }

}
