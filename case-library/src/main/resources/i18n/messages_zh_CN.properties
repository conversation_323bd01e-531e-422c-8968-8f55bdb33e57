#common
common.bad.request = 请求后台错误
common.getlock.time.out = 获取锁超时
common.login.out = 无效用户
common.repeat.request = 重复请求
common.param.error = 参数错误
common.not.permitted = 无权访问
common.resource.not.exists = 资源不存在
common.excel.date.error = Excel解析异常,请使用正确模板并按要求填写
common.not.exists = %s不存在
common.cannot.null = %s不能为空

#case
case.relate.dicom.delete = %s，由%s编辑，删除关联影像, 检查UID: %s, 序列UID: %s
case.relate.dicom.add = %s，由%s编辑，添加关联影像, 检查UID: %s
case.dicom.delete.info = 检查:%s 已关联病例，无法删除
case.related.education = 该病例已关联教学:%s，无法删除
case.related.task = 该病例已被任务:%s 使用，无法删除
case.is.published = 请勿重复发布
case.is.verified = 病例已审核
case.search.difficulty = 难度等级有误
case.analysis.not.null = 教学病例的病例解析不能为空
case.export.data.is.null = 无增量病例数据
case.dicom.pack.invalid = 影响压缩包文件不可用

#message
message.case.verify = %s:%s 发布申请已提交,请审核
message.case.verify.result = 您%s发布的%s:%s,审核%s

#systemconfig
system.config.name.exists = 名称已存在
system.config.delete.item.referenced = 删除项在其他地方已被使用，无法删除
system.config.organ.no.exists = 器官系统编号已存在
system.config.disease.type.name.exists = 疾病类型名称已存在

#case library
case.lib.menu.not.exist = 科室病例库目录不存在
case.lib.type.not.exist = 病例库类型不存在
case.lib.type.has.case = 该病例库类型下存在病例，无法删除
case.lib.type.name.already.exist = 病例库类型名称已存在

operate.fail = 操作失败

catalog.name.already.exist = 同一目录下，该名称已存在
catalog.child.exist.delete = 存在下级目录，不允许删除
catalog.has.related.case = 存在关联的病例，不允许删除
catalog.has.no.parent = 父级节点不可以为空
catalog.parent.self = 父级节点不可以为自身

case.not.exist = 病例不存在
catalog.not.exist = 目录不存在
audit.record.not.exist = 审核记录不存在
audit.record.has.been.verified = 审核记录已被审核
audit.record.not.reject = 审核记录未被拒绝

#es
check.es.index.exists.failed = 检查ES索引并创建失败

#export
export.task.start=开始导出任务...
export.task.querying.data=正在查询病例数据...
export.task.complete.no.data=导出完成，但没有找到符合条件的数据
export.task.processing.data=正在处理导出数据...
export.task.generating.file=正在生成Excel文件...
export.task.uploading.file=正在上传导出文件...
export.task.complete.success=导出任务完成
export.task.failed=导出失败: {0}
export.task.query.data.found=已查询 {0} 条病例数据
case.export.strategy.not.found = 未找到对应的导出策略
case.export.type.not.null = 导出类型不能为空
common.request.not.null = 请求参数不能为空
case.export.processor.not.found = 未找到对应的导出处理器
case.query.failed = 查询病例失败
follow.not.exist = 随访记录不存在
case.already.exist = 该病例已存在
study.of.case.already.exist = 该检查已存在关联病例
case.exist.in.other.library = 该病例存在于其他类型库，无法删除
import.data.is.null = 导入失败，数据为空
follow.case.not.exist = 随访导入失败，未找到对应的病例报告

study.not.belongs.one.patient = 导入的检查不属于同一患者，请检查确认
teach.case.should.belongs.one.disease = 疾病类型id不可以为空
disease.not.exist = 该疾病不存在
dept.case.library.not.exist = 该科室病例库不存在
page.param.not.mull = 分页参数不能为空

user.table.set.not.exist = 用户表设置不存在
user.table.set.exist = 用户表设置已存在

tag.list.too.much = 标签过多