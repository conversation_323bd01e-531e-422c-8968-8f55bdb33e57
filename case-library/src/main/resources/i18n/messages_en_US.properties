#common
common.bad.request = Bad request
common.getlock.time.out = Acquire lock time out
common.login.out = Invalid user
common.repeat.request = duplicate request
common.param.error = parameter error
common.not.permitted = not permitted
common.resource.not.exists = resource does not exist
common.excel.date.error = Excel parsing exception, please use the correct template and fill in as required
common.not.exists = %s do not exist
common.cannot.null = %s can not be null

#case
case.relate.dicom.delete = %s, edit by %s, delete relate dicom, studyUID: %s, seriesUID: %s
case.relate.dicom.add = %s, edit by %s, add relate dicom, studyUID: %s
case.dicom.delete.info = study:%s is already associated with a case, cannot be deleted
case.related.education = This case is already associated with education:%s, cannot be deleted
case.related.task = This case is already used by task:%s, cannot be deleted
case.is.published = Please do not publish again
case.is.verified = Case has been verified
case.search.difficulty = Difficulty level incorrect
case.analysis.not.null = The case analysis of teaching cases cannot be empty
case.export.data.is.null = No incremental case data
case.dicom.pack.invalid = Dicom compressed pack invalid

#message
message.case.verify = The publishing application for %s:%s has been submitted, please review it
message.case.verify.result = Your posted %s:%s, reviewed %s

#systemconfig
system.config.name.exists = the name already exists
system.config.delete.item.referenced = the deleted item is used and cannot deleted
system.config.organ.no.exists = the number of organ already exists
system.config.disease.type.name.exists = Disease type name already exists

#case library
case.lib.menu.not.exist = The case library menu does not exist
case.lib.type.not.exist = The case library type does not exist
case.lib.type.has.case = The case library type has cases and cannot be deleted
case.lib.type.name.already.exist = The case library type name already exists

operate.fail = Operation failed

catalog.name.already.exist = the name already exists in this catalog
catalog.child.exist.delete = There are children nodes below, and it cannot be deleted
catalog.has.related.case = There are related cases, and it cannot be deleted
catalog.has.no.parent = the parent node cannot be empty
catalog.parent.self = the parent node cannot be itself

case.not.exist = The case does not exist
catalog.not.exist = The catalog does not exist
audit.record.not.exist = The audit record does not exist
audit.record.has.been.verified = The audit record has been verified
audit.record.not.reject = = The audit record has not been rejected

#es
check.es.index.exists.failed = Check ES index and create failed

#export
export.task.start=Starting export task...
export.task.querying.data=Querying case data...
export.task.complete.no.data=Export complete, but no matching data found
export.task.processing.data=Processing export data...
export.task.generating.file=Generating Excel file...
export.task.uploading.file=Uploading exported file...
export.task.complete.success=Export task completed
export.task.failed=Export failed: {0}
export.task.query.data.found=Found {0} case data records
case.export.strategy.not.found = Export strategy not found
case.export.type.not.null = Export type cannot be null
common.request.not.null = Request cannot be null
case.export.processor.not.found = Export processor not found
case.query.failed = CaseQuery failed
follow.not.exist = The followup record does not exist
case.already.exist = The case has already exist
study.of.case.already.exist = The related case of this study has already existed
case.exist.in.other.library = The case exists in other library
import.data.is.null = Import failed, data is null
follow.case.not.exist = The followup case does not exist

study.not.belongs.one.patient = The imported studies do not belong to the same patient. Please verify
teach.case.should.belongs.one.disease = diseaseId should not empty
disease.not.exist = the disease not exist
dept.case.library.not.exist = the case library not exist
page.param.not.mull = the page param not null

user.table.set.not.exist = The user table set does not exist
user.table.set.exist = The user table set already exists

tag.list.too.much = The tag list is too much