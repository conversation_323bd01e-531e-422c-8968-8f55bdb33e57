<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.search.mapper.ESDocumentMapper">
    <resultMap id="DeptCaseDocument" type="com.jusha.caselibrary.search.document.DepartmentCaseDocument">
        <id property="caseId" column="case_id"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diseaseId" column="disease_id"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="caseCategory" column="case_category"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="meetings" column="meetings"/>
        <result property="qualityMatch" column="quality_match"/>
        <result property="positionMatch" column="position_match"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="remark" column="remark"/>
        <result property="selfComplaints" column="self_complaints"/>
        <collection property="auditInfoList" ofType="com.jusha.caselibrary.search.document.AuditInfo">
            <result property="auditId" column="audit_id"/>
            <result property="caseId" column="audit_case_id"/>
            <result property="caseTypeId" column="audit_case_type_id"/>
            <result property="auditType" column="audit_type"/>
            <result property="status" column="audit_status"/>
            <result property="pubUserId" column="pub_user_id"/>
            <result property="pubTime" column="pub_time"/>
            <result property="auditedBy" column="audited_by"/>
            <result property="auditedTime" column="audited_time"/>
            <result property="auditComment" column="audit_comment"/>
        </collection>
        <collection property="studyInfoList" ofType="com.jusha.caselibrary.search.document.StudyInfo">
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <!-- 使用 collection 映射 tags -->
            <collection property="modalities" ofType="java.lang.String">
                <result column="set_modality"/>
            </collection>
        </collection>
        <collection property="followInfoList" ofType="com.jusha.caselibrary.search.document.FollowInfo">
            <result property="followId" column="follow_id"/>
            <result property="followType" column="follow_type"/>
            <result property="followupResult" column="followup_result"/>
            <result property="createTime" column="follow_create_time"/>
        </collection>
        <!-- 使用 collection 映射 caseTypeIds -->
        <collection property="caseTypeInfoList" ofType="com.jusha.caselibrary.search.document.CaseTypeInfo">
            <result property="caseTypeId" column="case_type_id"/>
            <result property="caseTypeName" column="case_type_name"/>
            <result property="audit" column="audit"/>
            <result property="address" column="address"/>
        </collection>
        <!-- 使用 collection 映射 tags -->
        <collection property="tags" ofType="java.lang.String">
            <result column="tag_name"/>
        </collection>
        <collection property="tagInfoList" ofType="com.jusha.caselibrary.search.document.TagInfo">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
    </resultMap>

    <resultMap id="PersonalCaseDocument" type="com.jusha.caselibrary.search.document.PersonalCaseDocument">
        <id property="userCaseId" column="user_case_id"/>
        <result property="caseId" column="case_id"/>
        <result property="diseaseId" column="disease_id"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="caseCategory" column="case_category"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="qualityMatch" column="quality_match"/>
        <result property="positionMatch" column="position_match"/>
        <result property="remark" column="remark"/>
        <result property="selfComplaints" column="self_complaints"/>
        <collection property="studyInfoList" ofType="com.jusha.caselibrary.search.document.StudyInfo">
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <!-- 使用 collection 映射 modality -->
            <collection property="modalities" ofType="java.lang.String">
                <result column="set_modality"/>
            </collection>
        </collection>
        
        <collection property="followInfoList" ofType="com.jusha.caselibrary.search.document.FollowInfo">
            <result property="followId" column="follow_id"/>
            <result property="followType" column="follow_type"/>
            <result property="followupResult" column="followup_result"/>
            <result property="createTime" column="follow_create_time"/>
        </collection>
        
        <!-- 使用 collection 映射 catalogs -->
        <collection property="catalogIds" ofType="java.lang.String">
            <result column="catalog_id"/>
        </collection>

        <!-- 使用 collection 映射 tags -->
        <collection property="tags" ofType="java.lang.String">
            <result column="tag_name"/>
        </collection>

        <collection property="tagInfoList" ofType="com.jusha.caselibrary.search.document.TagInfo">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
    </resultMap>

    <select id="buildDeptCaseDocument" resultMap="DeptCaseDocument">
        SELECT distinct dc.case_id,
               dc.case_name,
               dc.case_no,
               dc.disease_id,
               dc.diagnosis,
               dc.patient_id,
               dc.patient_name,
               dc.patient_sex,
               dc.patient_birth_date,
               dc.patient_age,
               dc.medical_history,
               dc.difficulty,
               dc.case_category,
               dc.sign,
               dc.case_analysis,
               dc.source_type,
               dc.follow_status,
               dc.create_by,
               dc.create_time,
               dc.meetings,
               dc.quality_match,
               dc.position_match,
               dc.remark,
               dc.self_complaints,
               d.disease_name,
               dct.case_type_id,
               dct.case_type_name,
               dct.audit,
               dct.address,
               t.tag_name,
               t.tag_id,
               s.study_uid,
               s.access_number,
               s.study_time,
               s.study_no,
               s.patient_id          AS study_patient_id,
               s.patient_name        AS study_patient_name,
               s.patient_sex         AS study_patient_sex,
               s.patient_birth_date  AS study_patient_birth_date,
               s.patient_age         AS study_patient_age,
               s.patient_type,
               s.visit_date,
               s.out_patient_no,
               s.in_patient_no,
               s.physical_sign,
               s.clinical_diagnosis,
               s.study_item_name,
               s.part_name,
               s.device_type,
               s.study_state,
               s.device_name,
               s.medical_history     AS study_medical_history,
               s.self_reported_symptom,
               s.report_describe,
               s.report_diagnose,
               s.is_postive,
               s.register_time,
               s.report_time,
               s.reporter,
               s.checker,
               s.check_time,
               s.apply_number,
               s.apply_department,
               s.apply_doctor,
               s.artificer,
               s.is_public,
               cs.modality as set_modality,
               f.follow_id,
               f.follow_type,
               f.followup_result,
               f.create_time         AS follow_create_time,
               ar.audit_id,
               ar.case_id as audit_case_id,
               dct.case_type_id as audit_case_type_id,
               ar.audit_type,
               ar.status             AS audit_status,
               ar.pub_user_id,
               ar.pub_time,
               ar.audited_by,
               ar.audited_time,
               ar.audit_comment
        FROM t_dep_case dc
                 LEFT JOIN t_disease d ON dc.disease_id = d.disease_id and d.del_flag = 0
                 LEFT JOIN t_follow f ON dc.case_id = f.case_id and f.del_flag = 0
                 LEFT JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
                 INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
                 LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
                 LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
                 LEFT JOIN t_case_study tcs ON dc.case_id = tcs.case_id
                 LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
                 LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
                 LEFT JOIN t_audit_record ar ON dc.case_id = ar.case_id
        where dc.case_id = #{caseId} and dc.del_flag = 0
    </select>

    <select id="buildPersonalCaseDocument" resultMap="PersonalCaseDocument">
        SELECT distinct uc.user_case_id,
                        uc.case_id,
                        uc.disease_id,
                        d.disease_name,
                        uc.case_name,
                        uc.case_no,
                        uc.diagnosis,
                        uc.patient_id,
                        uc.patient_name,
                        uc.patient_sex,
                        uc.patient_birth_date,
                        uc.patient_age,
                        uc.case_category,
                        uc.medical_history,
                        uc.difficulty,
                        uc.sign,
                        uc.case_analysis,
                        uc.source_type,
                        uc.follow_status,
                        uc.create_by,
                        uc.create_time,
                        uc.quality_match,
                        uc.position_match,
                        uc.remark,
                        uc.self_complaints,
                        c.catalog_id,
                        t.tag_name,
                        t.tag_id,
                        s.study_uid,
                        s.access_number,
                        s.study_time,
                        s.study_no,
                        s.patient_id         AS study_patient_id,
                        s.patient_name       AS study_patient_name,
                        s.patient_sex        AS study_patient_sex,
                        s.patient_birth_date AS study_patient_birth_date,
                        s.patient_age        AS study_patient_age,
                        s.patient_type,
                        s.visit_date,
                        s.out_patient_no,
                        s.in_patient_no,
                        s.physical_sign,
                        s.clinical_diagnosis,
                        s.study_item_name,
                        s.part_name,
                        s.device_type,
                        s.study_state,
                        s.device_name,
                        s.medical_history    AS study_medical_history,
                        s.self_reported_symptom,
                        s.report_describe,
                        s.report_diagnose,
                        s.is_postive,
                        s.register_time,
                        s.report_time,
                        s.reporter,
                        s.checker,
                        s.check_time,
                        s.apply_number,
                        s.apply_department,
                        s.apply_doctor,
                        s.artificer,
                        s.is_public,
                        cs.modality          as set_modality,
                        f.follow_id,
                        f.follow_type,
                        f.followup_result,
                        f.create_time        AS follow_create_time
        FROM t_user_case uc
                 LEFT JOIN t_user_case_catalog pcc ON uc.user_case_id = pcc.user_case_id
                 INNER JOIN t_user_catalog c ON pcc.catalog_id = c.catalog_id and c.del_flag = 0
                 LEFT JOIN t_user_case_tag pctt ON uc.user_case_id = pctt.user_case_id
                 LEFT JOIN t_tag t ON pctt.tag_id = t.tag_id
                 LEFT JOIN t_case_study tcs ON uc.case_id = tcs.case_id
                 LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
                 LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
                 LEFT JOIN t_follow f ON uc.case_id = f.case_id and f.del_flag = 0
                 LEFT JOIN t_disease d ON uc.disease_id = d.disease_id and d.del_flag = 0
        where uc.user_case_id = #{userCaseId}
          and uc.del_flag = 0
    </select>

    <select id="buildSyncDeptCasesByIds" resultMap="DeptCaseDocument">
        SELECT distinct dc.case_id,
        dc.case_name,
        dc.case_no,
        dc.disease_id,
        dc.diagnosis,
        dc.patient_id,
        dc.patient_name,
        dc.patient_sex,
        dc.patient_birth_date,
        dc.patient_age,
        dc.medical_history,
        dc.difficulty,
        dc.case_category,
        dc.sign,
        dc.case_analysis,
        dc.source_type,
        dc.follow_status,
        dc.create_by,
        dc.create_time,
        dc.meetings,
        dc.quality_match,
        dc.position_match,
        dc.remark,
        dc.self_complaints,
        d.disease_name,
        dct.case_type_id,
        dct.case_type_name,
        dct.audit,
        dct.address,
        t.tag_name,
        t.tag_id,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        cs.modality as set_modality,
        f.follow_id,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time,
        ar.audit_id,
        ar.case_id as audit_case_id,
        dct.case_type_id as audit_case_type_id,
        ar.audit_type,
        ar.status             AS audit_status,
        ar.pub_user_id,
        ar.pub_time,
        ar.audited_by,
        ar.audited_time,
        ar.audit_comment
        FROM t_dep_case dc
        LEFT JOIN t_disease d ON dc.disease_id = d.disease_id and d.del_flag = 0
        LEFT JOIN t_follow f ON dc.case_id = f.case_id and f.del_flag = 0
        LEFT JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        LEFT JOIN t_audit_record ar ON dc.case_id = ar.case_id
        <where>
            dc.del_flag = 0
            <if test="caseIdList != null and caseIdList.size() > 0">
                and dc.case_id IN
                <foreach item="item" index="index" collection="caseIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="buildSyncPersonalCasesByUserCaseIds" resultMap="PersonalCaseDocument">
        SELECT distinct uc.user_case_id,
        uc.case_id,
        uc.disease_id,
        d.disease_name,
        uc.create_by,
        uc.case_name,
        uc.case_no,
        uc.diagnosis,
        uc.patient_id,
        uc.patient_name,
        uc.patient_sex,
        uc.patient_birth_date,
        uc.patient_age,
        uc.case_category,
        uc.medical_history,
        uc.difficulty,
        uc.sign,
        uc.case_analysis,
        uc.source_type,
        uc.follow_status,
        uc.create_by,
        uc.create_time,
        uc.quality_match,
        uc.position_match,
        uc.remark,
        uc.self_complaints,
        c.catalog_id,
        t.tag_name,
        t.tag_id,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        cs.modality as set_modality,
        f.follow_id,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time
        FROM t_user_case uc
        LEFT JOIN t_user_case_catalog pcc ON uc.user_case_id = pcc.user_case_id
        INNER JOIN t_user_catalog c ON pcc.catalog_id = c.catalog_id and c.del_flag = 0
        LEFT JOIN t_user_case_tag pctt ON uc.user_case_id = pctt.user_case_id
        LEFT JOIN t_tag t ON pctt.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON uc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        LEFT JOIN t_follow f ON uc.case_id = f.case_id and f.del_flag = 0
        LEFT JOIN t_disease d ON uc.disease_id = d.disease_id and d.del_flag = 0
        <where>
            uc.del_flag = 0
            <if test="userCaseIdList != null and userCaseIdList.size() > 0">
                and uc.user_case_id IN
                <foreach item="item" index="index" collection="userCaseIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>