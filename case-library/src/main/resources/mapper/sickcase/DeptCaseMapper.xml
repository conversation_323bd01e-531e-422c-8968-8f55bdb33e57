<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper">
    <resultMap id="DeptCaseDetail" type="com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp">
        <id property="caseId" column="case_id"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="caseCategory" column="case_category"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="meetings" column="meetings"/>
        <result property="remark" column="remark"/>
        <result property="selfComplaints" column="self_complaints"/>
        <result property="favoriteCatalogIdList" column="favorite_catalog_ids" typeHandler="com.jusha.caselibrary.search.document.StringToLongListTypeHandler"/>
        <collection property="studyInfoList" ofType="com.jusha.caselibrary.sickcase.dto.StudyInfo">
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="qualityMatch" column="quality_match"/>
            <result property="positionMatch" column="position_match"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <result property="isExport" column="is_export"/>
            <result property="aetId" column="aet_id"/>
            <!-- 使用 collection 映射 modalities -->
            <collection property="modalities" ofType="java.lang.String">
                <result column="study_modality"/>
            </collection>
            <collection property="followInfoList" ofType="com.jusha.caselibrary.sickcase.dto.FollowInfo">
                <result property="followId" column="follow_id"/>
                <result property="studyUid" column="follow_study_uid"/>
                <result property="followType" column="follow_type"/>
                <result property="followupResult" column="followup_result"/>
                <result property="createTime" column="follow_create_time"/>
            </collection>
        </collection>
        <!-- 使用 collection 映射 tags -->
        <collection property="tagInfoList" ofType="com.jusha.caselibrary.sickcase.dto.TagInfo">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
        <collection property="caseTypeInfoList" ofType="com.jusha.caselibrary.sickcase.dto.CaseTypeInfo">
            <result property="caseTypeId" column="case_type_id"/>
            <result property="caseTypeName" column="case_type_name"/>
            <result property="audit" column="audit"/>
            <result property="address" column="address"/>
            <result property="diseaseTree" column="disease_tree"/>
        </collection>
        <collection property="diseaseInfoList" ofType="com.jusha.caselibrary.sickcase.dto.DiseaseInfo">
            <result property="diseaseId" column="disease_id"/>
            <result property="diseaseName" column="disease_name"/>
        </collection>
    </resultMap>

    <resultMap id="AuditInfo" type="com.jusha.caselibrary.sickcase.dto.AuditInfo">
        <id property="auditId" column="audit_id"/>
        <result property="auditType" column="audit_type"/>
        <result property="caseId" column="audit_case_id"/>
        <result property="caseTypeId" column="audit_case_type_id"/>
        <result property="pubUserId" column="pub_user_id"/>
        <result property="pubTime" column="pub_time"/>
        <result property="auditedBy" column="audited_by"/>
        <result property="auditedTime" column="audited_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap id="AuditCaseDetailResp" type="com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp"
               extends="DeptCaseDetail">
        <id column="case_id"/>
        <id column="case_type_id"/>
        <association property="auditInfo" resultMap="AuditInfo"/>
    </resultMap>

    <select id="getDeptCaseIdList" resultType="long">
        SELECT DISTINCT dc.case_id
        FROM t_dep_case dc
        <if test="caseTypeId != null and caseTypeId != -1">
            INNER JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
            INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        </if>
        <if test="diseaseId != null and diseaseId != ''">
            INNER JOIN t_dep_case_disease cd ON dc.case_id = cd.case_id
            INNER JOIN t_disease d ON cd.disease_id = d.disease_id and d.del_flag = 0 AND d.disease_id = #{diseaseId}
        </if>
        <if test="(studyStartTime != null) or (studyEndTime != null)
        or (modality != null and modality != '') or (followType != null and followType != '')">
            LEFT JOIN t_case_study cs ON dc.case_id = cs.case_id
            LEFT JOIN t_study s ON cs.study_uid = s.study_uid AND s.del_flag = 0
            <if test="modality != null and modality != ''">
                LEFT JOIN t_series series ON s.study_uid = series.study_uid AND series.del_flag = 0
            </if>
            <if test="followType != null and followType != ''">
                INNER JOIN t_follow f ON s.study_uid = f.study_uid and f.del_flag = 0
                and f.case_id = dc.case_id AND f.follow_type = #{followType}
            </if>
        </if>

        <!-- 添加标签名称数组查询需要的关联 -->
        <if test="tagNameList != null and tagNameList.size() > 0">
            INNER JOIN t_dep_case_tag dct_tag ON dc.case_id = dct_tag.case_id
            INNER JOIN t_tag tag_filter ON dct_tag.tag_id = tag_filter.tag_id
        </if>
        <where>
            dc.del_flag = 0
            <if test="tagNameList != null and tagNameList.size() > 0">
                AND tag_filter.tag_name IN
                <foreach collection="tagNameList" item="tagName" open="(" separator="," close=")">
                    #{tagName}
                </foreach>
            </if>
            <if test="caseTypeId != null and caseTypeId != -1">
                AND dcc.case_type_id = #{caseTypeId}
                AND (
                    dct.audit = 0
                    OR (
                        dct.audit = 1
                        AND EXISTS (
                            SELECT 1 FROM (
                                SELECT case_id, status,
                                       ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY pub_time DESC, audit_id DESC) as rn
                                FROM t_audit_record
                                WHERE case_type_id = #{caseTypeId}
                            ) ranked_audit
                            WHERE rn = 1 AND status = 1 AND ranked_audit.case_id = dc.case_id
                        )
                    )
                )
            </if>
            <if test="studyStartTime != null">
                AND s.study_time >= #{studyStartTime}
            </if>
            <if test="studyEndTime != null">
                AND s.study_time &lt;= #{studyEndTime}
            </if>
            <if test="modality != null and modality != ''">
                AND (s.device_type = #{modality} OR series.modality = #{modality})
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND dc.patient_sex = #{patientSex}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND dc.follow_status = #{followStatus}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND dc.difficulty = #{difficulty}
            </if>
        </where>
    </select>

    <select id="getCaseDetailAttributes" resultMap="DeptCaseDetail">
        SELECT DISTINCT dc.case_id,
        dc.case_name,
        dc.case_no,
        dc.diagnosis,
        dc.patient_id,
        dc.patient_name,
        dc.patient_sex,
        dc.patient_birth_date,
        dc.patient_age,
        dc.medical_history,
        dc.difficulty,
        dc.case_category,
        dc.sign,
        dc.case_analysis,
        dc.source_type,
        dc.follow_status,
        dc.create_by,
        dc.create_time,
        dc.meetings,
        dc.remark,
        dc.self_complaints,
        d.disease_id,
        d.disease_name,
        <if test="caseTypeId != null and caseTypeId != -1">
            dct.case_type_id,
            dct.case_type_name,
            dct.audit,
            dct.address,
            dct.disease_tree,
        </if>
        t.tag_id,
        t.tag_name,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.quality_match,
        s.position_match,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        s.is_export,
        s.aet_id,
        cs.modality as study_modality,
        f.follow_id,
        f.study_uid          as follow_study_uid,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time,
        -- 查询该病例在个人病例库中当前用户收藏的目录ID列表
        (SELECT GROUP_CONCAT(DISTINCT ucc.catalog_id)
        FROM t_user_case uc2
        INNER JOIN t_user_case_catalog ucc ON uc2.user_case_id = ucc.user_case_id
        INNER JOIN t_user_catalog cat ON ucc.catalog_id = cat.catalog_id
        WHERE uc2.case_id = dc.case_id
        AND uc2.create_by = #{userId}
        AND uc2.del_flag = 0
        AND cat.del_flag = 0
        ) AS favorite_catalog_ids
        FROM t_dep_case dc
        LEFT JOIN t_dep_case_disease cd ON dc.case_id = cd.case_id
        LEFT JOIN t_disease d ON cd.disease_id = d.disease_id and d.del_flag = 0
        <if test="caseTypeId != null and caseTypeId != -1">
            INNER JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
            INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id
            and dct.del_flag = 0
        </if>
        LEFT  JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT  JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT  JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT  JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_follow f ON s.study_uid = f.study_uid and f.case_id = dc.case_id and f.del_flag = 0
        LEFT  JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        LEFT  JOIN t_audit_record ar ON dc.case_id = ar.case_id
        <where>
            <if test="caseIdList != null and caseIdList.size() > 0">
                AND dc.case_id IN
                <foreach collection="caseIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY dc.create_time DESC,
        -- 报告排序：区间内的按时间正序在前，区间外的按时间正序在后
        <choose>
            <when test="(studyStartTime != null) or (studyEndTime != null)">
                CASE
                WHEN 1=1
                <if test="studyStartTime != null">
                    AND s.study_time >= #{studyStartTime}
                </if>
                <if test="studyEndTime != null">
                    AND s.study_time &lt;= #{studyEndTime}
                </if>
                THEN 0 ELSE 1
                END,
                s.study_time DESC
            </when>
            <otherwise>
                s.study_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getCaseAuditIdList" resultType="com.jusha.caselibrary.sickcase.dto.CaseBaseInfo">
        <choose>
            <!-- 当有关键字搜索时，在基础查询外套一层关键字过滤 -->
            <when test="keyword != null and keyword != ''">
                SELECT base_cases.caseId, base_cases.caseTypeId
                FROM (
            </when>
        </choose>
        
        <!-- 基础查询逻辑 -->
        SELECT dc.case_id as caseId, dct.case_type_id as caseTypeId
        FROM t_dep_case dc
        LEFT JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        LEFT JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        INNER JOIN (
            SELECT case_id, audit_id, audit_type, pub_user_id, pub_time,
                   audited_by, audited_time, status
            FROM (
                SELECT case_id, audit_id, audit_type, pub_user_id, pub_time,
                        audited_by, audited_time, status,
                        ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY pub_time DESC, audit_id DESC) as rn
                FROM t_audit_record
            ) ranked_audit
            WHERE rn = 1
        ) ar ON ar.case_id = dc.case_id
        <if test="(studyStartTime != null) or (studyEndTime != null)
        or (modality != null and modality != '') or (followType != null and followType != '')">
            LEFT JOIN t_case_study cs ON dc.case_id = cs.case_id
            LEFT JOIN t_study s ON cs.study_uid = s.study_uid AND s.del_flag = 0
            <if test="modality != null and modality != ''">
                LEFT JOIN t_series series ON s.study_uid = series.study_uid AND series.del_flag = 0
            </if>
            <if test="followType != null and followType != ''">
                LEFT JOIN t_follow f ON s.study_uid = f.study_uid and f.case_id = dc.case_id
                                     and f.del_flag = 0 AND f.follow_type = #{followType}
            </if>
        </if>
        <!-- 添加标签名称数组查询需要的关联 -->
        <if test="tagNameList != null and tagNameList.size() > 0">
            INNER JOIN t_dep_case_tag dct_tag ON dc.case_id = dct_tag.case_id
            INNER JOIN t_tag tag_filter ON dct_tag.tag_id = tag_filter.tag_id
        </if>
        WHERE dc.del_flag = 0 and dct.audit = 1
        <if test="userId != null">
            AND FIND_IN_SET(#{userId}, ar.pub_user_id) > 0
        </if>
        <if test="caseId != null">
            AND dc.case_id = #{caseId}
        </if>
        <if test="caseTypeId != null">
            AND dcc.case_type_id = #{caseTypeId}
        </if>
        <if test="status != null and status != ''">
            AND ar.status = #{status}
        </if>
        <if test="auditType != null and auditType != ''">
            AND ar.audit_type = #{auditType}
        </if>
        <if test="pubUserId != null">
            AND ar.pub_user_id = #{pubUserId}
        </if>
        <if test="pubStartTime != null">
            AND ar.pub_time >= #{pubStartTime}
        </if>
        <if test="pubEndTime != null">
            AND ar.pub_time &lt;= #{pubEndTime}
        </if>
        <if test="auditedBy != null">
            AND ar.audited_by = #{auditedBy}
        </if>
        <if test="auditedStartTime != null">
            AND ar.audited_time >= #{auditedStartTime}
        </if>
        <if test="auditedEndTime != null">
            AND ar.audited_time &lt;= #{auditedEndTime}
        </if>
        <if test="studyStartTime != null">
            AND s.study_time >= #{studyStartTime}
        </if>
        <if test="studyEndTime != null">
            AND s.study_time &lt;= #{studyEndTime}
        </if>
        <if test="modality != null and modality != ''">
            AND (s.device_type = #{modality} OR series.modality = #{modality})
        </if>
        <if test="patientSex != null and patientSex != ''">
            AND dc.patient_sex = #{patientSex}
        </if>
        <if test="followStatus != null and followStatus != ''">
            AND dc.follow_status = #{followStatus}
        </if>
        <if test="difficulty != null and difficulty != ''">
            AND dc.difficulty = #{difficulty}
        </if>
        <!-- 标签名称数组过滤条件 -->
        <if test="tagNameList != null and tagNameList.size() > 0">
            AND tag_filter.tag_name IN
            <foreach collection="tagNameList" item="tagName" open="(" separator="," close=")">
                #{tagName}
            </foreach>
        </if>
        GROUP BY dc.case_id, dct.case_type_id
        
        <choose>
            <!-- 当有关键字搜索时，在基础查询结果上再进行关键字过滤 -->
            <when test="keyword != null and keyword != ''">
                ) base_cases
                WHERE EXISTS (
                    -- 关键字模糊搜索
                    SELECT 1 FROM t_dep_case dc2
                    WHERE dc2.case_id = base_cases.caseId
                    AND (
                        dc2.case_name LIKE CONCAT('%', #{keyword}, '%')
                        OR dc2.diagnosis LIKE CONCAT('%', #{keyword}, '%')
                        OR dc2.patient_name LIKE CONCAT('%', #{keyword}, '%')
                        OR dc2.case_analysis LIKE CONCAT('%', #{keyword}, '%')
                        OR dc2.remark LIKE CONCAT('%', #{keyword}, '%')
                        OR dc2.self_complaints LIKE CONCAT('%', #{keyword}, '%')
                    )
                    UNION ALL
                    SELECT 1 FROM t_case_study cs2
                    LEFT JOIN t_study s2 ON cs2.study_uid = s2.study_uid AND s2.del_flag = 0
                    WHERE cs2.case_id = base_cases.caseId
                    AND (
                        s2.access_number LIKE CONCAT('%', #{keyword}, '%')
                        OR s2.in_patient_no LIKE CONCAT('%', #{keyword}, '%')
                        OR s2.out_patient_no LIKE CONCAT('%', #{keyword}, '%')
                        OR s2.report_describe LIKE CONCAT('%', #{keyword}, '%')
                        OR s2.report_diagnose LIKE CONCAT('%', #{keyword}, '%')
                    )
                    <if test="followType != null and followType != ''">
                        UNION ALL
                        SELECT 1 FROM t_follow f2
                        WHERE f2.case_id = base_cases.caseId
                        AND f2.del_flag = 0
                        AND f2.followup_result LIKE CONCAT('%', #{keyword}, '%')
                    </if>
                )
            </when>
        </choose>
    </select>

    <select id="getAuditCaseList" resultMap="AuditCaseDetailResp">
        SELECT
        dc.case_id,
        dc.case_name,
        dc.case_no,
        dc.diagnosis,
        dc.patient_id,
        dc.patient_name,
        dc.patient_sex,
        dc.patient_birth_date,
        dc.patient_age,
        dc.medical_history,
        dc.difficulty,
        dc.case_category,
        dc.sign,
        dc.case_analysis,
        dc.source_type,
        dc.follow_status,
        dc.create_by,
        dc.create_time,
        dc.meetings,
        dc.remark,
        dc.self_complaints,
        d.disease_id,
        d.disease_name,
        dct.case_type_id,
        dct.case_type_name,
        dct.audit,
        dct.address,
        dct.disease_tree,
        t.tag_id,
        t.tag_name,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.quality_match,
        s.position_match,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        cs.modality AS study_modality,
        f.follow_id,
        f.study_uid          as follow_study_uid,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time,
        ar.audit_id,
        dc.case_id as audit_case_id,
        ar.audit_type,
        dct.case_type_id as audit_case_type_id,
        ar.pub_user_id,
        ar.pub_time,
        ar.audited_by,
        ar.audited_time,
        ar.status
        FROM t_dep_case dc
        LEFT JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        LEFT JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        INNER JOIN (
            -- 使用窗口函数优化获取每个病例最新的审核记录
            SELECT case_id, audit_id, audit_type, pub_user_id, pub_time,
                   audited_by, audited_time, status
            FROM (
                SELECT case_id, audit_id, audit_type, pub_user_id, pub_time,
                       audited_by, audited_time, status,
                       ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY pub_time DESC, audit_id DESC) as rn
                FROM t_audit_record
            ) ranked_audit
            WHERE rn = 1
        ) ar ON ar.case_id = dc.case_id
        LEFT JOIN t_dep_case_disease cd ON dc.case_id = cd.case_id
        LEFT JOIN t_disease d ON cd.disease_id = d.disease_id and d.del_flag = 0
        LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_follow f ON s.study_uid = f.study_uid and f.case_id = dc.case_id and f.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        <where>
            dc.del_flag = 0
            <if test="caseBaseInfoList != null and caseBaseInfoList.size() > 0">
                AND (
                <foreach collection="caseBaseInfoList" item="caseInfo" separator=" OR ">
                    (dc.case_id = #{caseInfo.caseId} AND dct.case_type_id = #{caseInfo.caseTypeId})
                </foreach>
                )
            </if>
        </where>
        ORDER BY dc.create_time DESC,
        -- 报告排序：区间内的按时间正序在前，区间外的按时间正序在后
        <choose>
            <when test="(studyStartTime != null) or (studyEndTime != null)">
                CASE
                WHEN 1=1
                <if test="studyStartTime != null">
                    AND s.study_time >= #{studyStartTime}
                </if>
                <if test="studyEndTime != null">
                    AND s.study_time &lt;= #{studyEndTime}
                </if>
                THEN 0 ELSE 1
                END,
                s.study_time DESC
            </when>
            <otherwise>
                s.study_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
