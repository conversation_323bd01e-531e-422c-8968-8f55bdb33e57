<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper">
    <resultMap id="DeptCaseDetail" type="com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp">
        <id property="caseId" column="case_id"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diseaseId" column="disease_id"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="caseCategory" column="case_category"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="meetings" column="meetings"/>
        <result property="qualityMatch" column="quality_match"/>
        <result property="positionMatch" column="position_match"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="remark" column="remark"/>
        <result property="selfComplaints" column="self_complaints"/>
        <collection property="studyInfoList" ofType="com.jusha.caselibrary.sickcase.dto.StudyInfo">
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <result property="isExport" column="is_export"/>
            <result property="aetId" column="aet_id"/>
            <!-- 使用 collection 映射 tags -->
            <collection property="modalities" ofType="java.lang.String">
                <result column="modality"/>
            </collection>
        </collection>
        <collection property="followInfoList" ofType="com.jusha.caselibrary.sickcase.dto.FollowInfo">
            <result property="followId" column="follow_id"/>
            <result property="followType" column="follow_type"/>
            <result property="followupResult" column="followup_result"/>
            <result property="createTime" column="follow_create_time"/>
        </collection>
        <!-- 使用 collection 映射 tags -->
        <collection property="tagInfoList" ofType="com.jusha.caselibrary.sickcase.dto.TagInfo">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
        <collection property="caseTypeInfoList" ofType="com.jusha.caselibrary.sickcase.dto.CaseTypeInfo">
            <result property="caseTypeId" column="case_type_id"/>
            <result property="caseTypeName" column="case_type_name"/>
            <result property="audit" column="audit"/>
            <result property="address" column="address"/>
        </collection>
    </resultMap>

    <resultMap id="AuditInfo" type="com.jusha.caselibrary.sickcase.dto.AuditInfo">
        <id property="auditId" column="audit_id"/>
        <result property="auditType" column="audit_type"/>
        <result property="caseTypeId" column="audit_case_type_id"/>
        <result property="pubUserId" column="pub_user_id"/>
        <result property="pubTime" column="pub_time"/>
        <result property="auditedBy" column="audited_by"/>
        <result property="auditedTime" column="audited_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap id="AuditCaseDetailResp" type="com.jusha.caselibrary.sickcase.dto.resp.AuditCaseDetailResp"
               extends="DeptCaseDetail">
        <association property="auditInfo" resultMap="AuditInfo"/>
    </resultMap>

    <select id="getDeptCaseDetailList" resultMap="DeptCaseDetail">
        SELECT DISTINCT dc.case_id,
        dc.case_name,
        dc.case_no,
        dc.disease_id,
        dc.diagnosis,
        dc.patient_id,
        dc.patient_name,
        dc.patient_sex,
        dc.patient_birth_date,
        dc.patient_age,
        dc.medical_history,
        dc.difficulty,
        dc.case_category,
        dc.sign,
        dc.case_analysis,
        dc.source_type,
        dc.follow_status,
        dc.create_by,
        dc.create_time,
        dc.meetings,
        dc.quality_match,
        dc.position_match,
        dc.remark,
        dc.self_complaints,
        d.disease_name,
        dct.case_type_id,
        dct.case_type_name,
        dct.audit,
        dct.address,
        t.tag_id,
        t.tag_name,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        s.is_export,
        s.aet_id,
        cs.modality,
        f.follow_id,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time
        FROM t_dep_case dc
        LEFT JOIN t_disease d ON dc.disease_id = d.disease_id and d.del_flag = 0
        LEFT JOIN t_follow f ON dc.case_id = f.case_id and f.del_flag = 0
        INNER JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        LEFT  JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT  JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT  JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT  JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT  JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        LEFT  JOIN t_audit_record ar ON dc.case_id = ar.case_id
        <where>
            (dct.audit = 0 or ar.status = 1) and dc.del_flag = 0
            <if test="caseId != null">
                AND dc.case_id = #{caseId}
            </if>
            <if test="userId != null">
                AND (dc.create_by = #{userId} OR ar.audited_by = #{userId})
            </if>
            <if test="caseTypeId != null">
                AND dcc.case_type_id = #{caseTypeId}
            </if>
            <!-- 如果有时间区间条件，则要求该病例至少有一个报告在时间区间内 -->
            <if test="(studyStartTime != null) or (studyEndTime != null)">
                AND dc.case_id IN (
                SELECT DISTINCT cs2.case_id
                FROM t_case_study cs2
                INNER JOIN t_study st2 ON cs2.study_uid = st2.study_uid
                WHERE 1=1
                <if test="studyStartTime != null">
                    AND st2.study_time >= #{studyStartTime}
                </if>
                <if test="studyEndTime != null">
                    AND st2.study_time &lt;= #{studyEndTime}
                </if>
                )
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND dc.patient_sex = #{patientSex}
            </if>
            <if test="followType != null and followType != ''">
                AND f.follow_type = #{followType}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND dc.follow_status = #{followStatus}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND dc.difficulty = #{difficulty}
            </if>
            <if test="modality != null and modality != ''">
                AND (s.device_type = #{modality} or cs.modality = #{modality})
            </if>
        </where>
        ORDER BY dc.case_id,
        -- 报告排序：区间内的按时间正序在前，区间外的按时间正序在后
        <choose>
            <when test="(studyStartTime != null) or (studyEndTime != null)">
                CASE
                WHEN 1=1
                <if test="studyStartTime != null">
                    AND s.study_time >= #{studyStartTime}
                </if>
                <if test="studyEndTime != null">
                    AND s.study_time &lt;= #{studyEndTime}
                </if>
                THEN 0 ELSE 1
                END,
                s.study_time ASC
            </when>
            <otherwise>
                s.study_time ASC
            </otherwise>
        </choose>
    </select>

    <select id="getAuditCaseList" resultMap="AuditCaseDetailResp">
        SELECT DISTINCT 
               ar.audit_id,
               ar.audit_type,
               dct.case_type_id as audit_case_type_id,
               ar.pub_user_id,
               ar.pub_time,
               ar.audited_by,
               ar.audited_time,
               ar.status,
               dc.case_id,
               dc.case_name,
               dc.case_no,
               dc.disease_id,
               dc.diagnosis,
               dc.patient_id,
               dc.patient_name,
               dc.patient_sex,
               dc.patient_birth_date,
               dc.patient_age,
               dc.medical_history,
               dc.difficulty,
               dc.case_category,
               dc.sign,
               dc.case_analysis,
               dc.source_type,
               dc.follow_status,
               dc.create_by,
               dc.create_time,
               dc.meetings,
               dc.quality_match,
               dc.position_match,
               dc.remark,
               dc.self_complaints,
               d.disease_name,
               t.tag_id,
               t.tag_name,
               s.study_uid,
               s.access_number,
               s.study_time,
               s.study_no,
               s.patient_id AS study_patient_id,
               s.patient_name AS study_patient_name,
               s.patient_sex AS study_patient_sex,
               s.patient_birth_date AS study_patient_birth_date,
               s.patient_age AS study_patient_age,
               s.patient_type,
               s.visit_date,
               s.out_patient_no,
               s.in_patient_no,
               s.physical_sign,
               s.clinical_diagnosis,
               s.study_item_name,
               s.part_name,
               s.device_type,
               s.study_state,
               s.device_name,
               s.medical_history AS study_medical_history,
               s.self_reported_symptom,
               s.report_describe,
               s.report_diagnose,
               s.is_postive,
               s.register_time,
               s.report_time,
               s.reporter,
               s.checker,
               s.check_time,
               s.apply_number,
               s.apply_department,
               s.apply_doctor,
               s.artificer,
               s.is_public,
               cs.modality,
               f.follow_id,
               f.follow_type,
               f.followup_result,
               f.create_time AS follow_create_time
        FROM t_audit_record ar
        INNER JOIN t_dep_case dc ON ar.case_id = dc.case_id and dc.del_flag = 0
        LEFT JOIN t_disease d ON dc.disease_id = d.disease_id and d.del_flag = 0
        LEFT JOIN t_follow f ON dc.case_id = f.case_id and f.del_flag = 0
        INNER JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        <where>
            1=1
            <if test="auditId != null">
                AND ar.audit_id = #{auditId}
            </if>
            <if test="caseId != null">
                AND ar.case_id = #{caseId}
            </if>
            <if test="caseTypeId != null">
                AND dcc.case_type_id = #{caseTypeId}
            </if>
            <if test="status != null and status != ''">
                AND ar.status = #{status}
            </if>
            <if test="auditType != null and auditType != ''">
                AND ar.audit_type = #{auditType}
            </if>
            <if test="pubUserId != null">
                AND ar.pub_user_id = #{pubUserId}
            </if>
            <if test="pubStartTime != null">
                AND ar.pub_time >= #{pubStartTime}
            </if>
            <if test="pubEndTime != null">
                AND ar.pub_time &lt;= #{pubEndTime}
            </if>
            <if test="auditedBy != null">
                AND ar.audited_by = #{auditedBy}
            </if>
            <if test="auditedStartTime != null">
                AND ar.audited_time >= #{auditedStartTime}
            </if>
            <if test="auditedEndTime != null">
                AND ar.audited_time &lt;= #{auditedEndTime}
            </if>
            <!-- 如果有时间区间条件，则要求该病例至少有一个报告在时间区间内 -->
            <if test="(studyStartTime != null) or (studyEndTime != null)">
                AND dc.case_id IN (
                    SELECT DISTINCT cs2.case_id
                    FROM t_case_study cs2
                    INNER JOIN t_study st2 ON cs2.study_uid = st2.study_uid
                    WHERE 1=1
                    <if test="studyStartTime != null">
                        AND st2.study_time >= #{studyStartTime}
                    </if>
                    <if test="studyEndTime != null">
                        AND st2.study_time &lt;= #{studyEndTime}
                    </if>
                )
            </if>
            <if test="modality != null and modality != ''">
                AND s.device_type = #{modality}
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND dc.patient_sex = #{patientSex}
            </if>
            <if test="followType != null and followType != ''">
                AND f.follow_type = #{followType}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND dc.follow_status = #{followStatus}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND dc.difficulty = #{difficulty}
            </if>
            <if test="modality != null and modality != ''">
                AND (s.device_type = #{modality} or cs.modality = #{modality})
            </if>
        </where>
        ORDER BY ar.pub_time DESC, ar.audit_id DESC
    </select>

    <select id="getMyAuditCaseList" resultMap="AuditCaseDetailResp">
        SELECT DISTINCT 
               ar.audit_id,
               ar.audit_type,
               dct.case_type_id as audit_case_type_id,
               ar.pub_user_id,
               ar.pub_time,
               ar.audited_by,
               ar.audited_time,
               ar.status,
               dc.case_id,
               dc.case_name,
               dc.case_no,
               dc.disease_id,
               dc.diagnosis,
               dc.patient_id,
               dc.patient_name,
               dc.patient_sex,
               dc.patient_birth_date,
               dc.patient_age,
               dc.medical_history,
               dc.difficulty,
               dc.case_category,
               dc.sign,
               dc.case_analysis,
               dc.source_type,
               dc.follow_status,
               dc.create_by,
               dc.create_time,
               dc.meetings,
               dc.quality_match,
               dc.position_match,
               dc.remark,
               dc.self_complaints,
               d.disease_name,
               t.tag_id,
               t.tag_name,
               s.study_uid,
               s.access_number,
               s.study_time,
               s.study_no,
               s.patient_id AS study_patient_id,
               s.patient_name AS study_patient_name,
               s.patient_sex AS study_patient_sex,
               s.patient_birth_date AS study_patient_birth_date,
               s.patient_age AS study_patient_age,
               s.patient_type,
               s.visit_date,
               s.out_patient_no,
               s.in_patient_no,
               s.physical_sign,
               s.clinical_diagnosis,
               s.study_item_name,
               s.part_name,
               s.device_type,
               s.study_state,
               s.device_name,
               s.medical_history AS study_medical_history,
               s.self_reported_symptom,
               s.report_describe,
               s.report_diagnose,
               s.is_postive,
               s.register_time,
               s.report_time,
               s.reporter,
               s.checker,
               s.check_time,
               s.apply_number,
               s.apply_department,
               s.apply_doctor,
               s.artificer,
               s.is_public,
               cs.modality,
               f.follow_id,
               f.follow_type,
               f.followup_result,
               f.create_time AS follow_create_time
        FROM t_audit_record ar
        INNER JOIN t_dep_case dc ON ar.case_id = dc.case_id and dc.del_flag = 0
        LEFT JOIN t_disease d ON dc.disease_id = d.disease_id and d.del_flag = 0
        LEFT JOIN t_follow f ON dc.case_id = f.case_id and f.del_flag = 0
        INNER JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        INNER JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id and dct.del_flag = 0
        LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON dc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid and cs.del_flag = 0
        <where>
            dc.create_by = #{userId}
            <if test="auditId != null">
                AND ar.audit_id = #{auditId}
            </if>
            <if test="caseId != null">
                AND ar.case_id = #{caseId}
            </if>
            <if test="caseTypeId != null">
                AND dcc.case_type_id = #{caseTypeId}
            </if>
            <if test="status != null and status != ''">
                AND ar.status = #{status}
            </if>
            <if test="auditType != null and auditType != ''">
                AND ar.audit_type = #{auditType}
            </if>
            <if test="pubUserId != null">
                AND ar.pub_user_id = #{pubUserId}
            </if>
            <if test="pubStartTime != null">
                AND ar.pub_time >= #{pubStartTime}
            </if>
            <if test="pubEndTime != null">
                AND ar.pub_time &lt;= #{pubEndTime}
            </if>
            <if test="auditedStartTime != null">
                AND ar.audited_time >= #{auditedStartTime}
            </if>
            <if test="auditedEndTime != null">
                AND ar.audited_time &lt;= #{auditedEndTime}
            </if>
            <!-- 如果有时间区间条件，则要求该病例至少有一个报告在时间区间内 -->
            <if test="(studyStartTime != null) or (studyEndTime != null)">
                AND dc.case_id IN (
                    SELECT DISTINCT cs2.case_id
                    FROM t_case_study cs2
                    INNER JOIN t_study st2 ON cs2.study_uid = st2.study_uid
                    WHERE 1=1
                    <if test="studyStartTime != null">
                        AND st2.study_time >= #{studyStartTime}
                    </if>
                    <if test="studyEndTime != null">
                        AND st2.study_time &lt;= #{studyEndTime}
                    </if>
                )
            </if>
            <if test="modality != null and modality != ''">
                AND s.device_type = #{modality}
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND dc.patient_sex = #{patientSex}
            </if>
            <if test="followType != null and followType != ''">
                AND f.follow_type = #{followType}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND dc.follow_status = #{followStatus}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND dc.difficulty = #{difficulty}
            </if>
            <if test="modality != null and modality != ''">
                AND (s.device_type = #{modality} or cs.modality = #{modality})
            </if>
        </where>
        ORDER BY ar.audited_time DESC, ar.audit_id DESC
    </select>
</mapper>
