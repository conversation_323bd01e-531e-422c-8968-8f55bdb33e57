<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.sickcase.mapper.PersonalCaseMapper">
    <resultMap id="PersonalCaseDetail" type="com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp">
        <id property="userCaseId" column="user_case_id"/>
        <result property="caseId" column="case_id"/>
        <result property="diseaseId" column="disease_id"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="caseCategory" column="case_category"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="qualityMatch" column="quality_match"/>
        <result property="positionMatch" column="position_match"/>
        <result property="catalogName" column="catalog_name"/>
        <result property="remark" column="remark"/>
        <collection property="studyInfoList" ofType="com.jusha.caselibrary.sickcase.dto.StudyInfo">
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <result property="isExport" column="is_export"/>
            <result property="aetId" column="aet_id"/>
            <!-- 使用 collection 映射 tags -->
            <collection property="modalities" ofType="java.lang.String">
                <result column="modality"/>
            </collection>
        </collection>
        <collection property="followInfoList" ofType="com.jusha.caselibrary.sickcase.dto.FollowInfo">
            <result property="followId" column="follow_id"/>
            <result property="followType" column="follow_type"/>
            <result property="followupResult" column="followup_result"/>
            <result property="createTime" column="follow_create_time"/>
        </collection>
        <!-- 使用 collection 映射 tags -->
        <collection property="tagInfoList" ofType="com.jusha.caselibrary.sickcase.dto.TagInfo">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
    </resultMap>

    <select id="getPersonalCaseDetailList" resultMap="PersonalCaseDetail">
        SELECT distinct uc.user_case_id,
        uc.case_id,
        uc.case_name,
        uc.disease_id,
        d.disease_name,
        uc.case_no,
        uc.diagnosis,
        uc.patient_id,
        uc.patient_name,
        uc.patient_sex,
        uc.patient_birth_date,
        uc.patient_age,
        uc.medical_history,
        uc.difficulty,
        uc.case_category,
        uc.sign,
        uc.case_analysis,
        uc.source_type,
        uc.follow_status,
        uc.create_by,
        uc.create_time,
        uc.quality_match,
        uc.position_match,
        uc.remark,
        c.catalog_name,
        t.tag_id,
        t.tag_name,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        s.is_export,
        s.aet_id,
        cs.modality,
        f.follow_id,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time
        FROM t_user_case uc
        LEFT JOIN t_follow f ON uc.case_id = f.case_id AND f.del_flag = 0
        LEFT JOIN t_user_case_catalog ucc ON uc.user_case_id = ucc.user_case_id
        INNER JOIN t_user_catalog c ON ucc.catalog_id = c.catalog_id and c.del_flag = 0
        LEFT JOIN t_user_case_tag uct ON uc.user_case_id = uct.user_case_id
        LEFT JOIN t_tag t ON uct.tag_id = t.tag_id
        LEFT JOIN t_case_study tcs ON uc.case_id = tcs.case_id
        LEFT JOIN t_study s ON s.study_uid = tcs.study_uid and s.del_flag = 0
        LEFT JOIN t_series cs ON s.study_uid = cs.study_uid AND cs.del_flag = 0
        LEFT JOIN t_disease d ON uc.disease_id = d.disease_id and d.del_flag = 0
        <where>
            uc.del_flag = 0
            <if test="userCaseId != null">
                uc.user_case_id = #{userCaseId}
            </if>
            <if test="catalogId != null">
                AND c.catalog_id = #{catalogId}
            </if>
            <if test="diseaseId != null">
                AND uc.disease_id = #{diseaseId}
            </if>
            <!-- 如果有时间区间条件，则要求该病例至少有一个报告在时间区间内 -->
            <if test="(studyStartTime != null) or (studyEndTime != null)">
                AND uc.case_id IN (
                SELECT DISTINCT cs2.case_id
                FROM t_case_study cs2
                INNER JOIN t_study st2 ON cs2.study_uid = st2.study_uid
                WHERE 1=1
                <if test="studyStartTime != null">
                    AND st2.study_time >= #{studyStartTime}
                </if>
                <if test="studystudyEndTime != null">
                    AND st2.study_time &lt;= #{studystudyEndTime}
                </if>
                )
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND uc.patient_sex = #{patientSex}
            </if>
            <if test="followType != null and followType != ''">
                AND f.follow_type = #{followType}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND uc.follow_status = #{followStatus}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND dc.difficulty = #{difficulty}
            </if>
            <if test="modality != null and modality != ''">
                AND (s.device_type = #{modality} or cs.modality = #{modality})
            </if>
        </where>
        ORDER BY uc.case_id,
        -- 报告排序：区间内的按时间正序在前，区间外的按时间正序在后
        <choose>
            <when test="(studyStartTime != null) or (studyEndTime != null)">
                CASE
                WHEN 1=1
                <if test="studyStartTime != null">
                    AND s.study_time >= #{studyStartTime}
                </if>
                <if test="studyEndTime != null">
                    AND s.study_time &lt;= #{studyEndTime}
                </if>
                THEN 0 ELSE 1
                END,
                s.study_time ASC
            </when>
            <otherwise>
                s.study_time ASC
            </otherwise>
        </choose>
    </select>

</mapper>