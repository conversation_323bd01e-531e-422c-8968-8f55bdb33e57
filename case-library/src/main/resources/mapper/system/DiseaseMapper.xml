<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.mybatisplus.system.mapper.DiseaseMapper">

    <resultMap type="com.jusha.caselibrary.mybatisplus.system.entity.Disease" id="DiseaseResult">
        <id     property="diseaseId"     column="disease_id"  />
        <result property="parentId"      column="parent_id"   />
        <result property="ancestors"     column="ancestors"   />
        <result property="diseaseName"   column="disease_name"/>
        <result property="orderNum"      column="order_num"   />
        <result property="delFlag"       column="del_flag"    />
        <result property="createdBy"     column="created_by"  />
        <result property="create_time"   column="create_time" />
        <result property="updateBy"      column="update_by"   />
        <result property="updateTime"    column="update_time" />
    </resultMap>

    <select id="selectChildrenDiseaseById" parameterType="Long" resultMap="DiseaseResult">
        select * from t_disease where find_in_set(#{diseaseId}, ancestors)
    </select>

    <update id="updateDiseaseChildren" parameterType="java.util.List">
        update t_disease set ancestors =
        <foreach collection="diseases" item="item" index="index"
                 separator=" " open="case diseases_id" close="end">
            when #{item.diseaseId} then #{item.ancestors}
        </foreach>
        where diseases_id in
        <foreach collection="diseases" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.diseaseId}
        </foreach>
    </update>

</mapper>
