<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.mybatisplus.mapper.StudyMapper">

    <resultMap id="StudyResultMap" type="com.jusha.caselibrary.mybatisplus.entity.Study">
        <id property="studyUid" column="study_uid" />
        <result property="accessNumber" column="access_number"/>
        <result property="studyTime" column="study_time"/>
        <result property="studyNo" column="study_no"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="patientType" column="patient_type"/>
        <result property="visitDate" column="visit_date"/>
        <result property="outPatientNo" column="out_patient_no"/>
        <result property="inPatientNo" column="in_patient_no"/>
        <result property="physicalSign" column="physical_sign"/>
        <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
        <result property="studyItemName" column="study_item_name"/>
        <result property="partName" column="part_name"/>
        <result property="deviceType" column="device_type"/>
        <result property="studyState" column="study_state"/>
        <result property="deviceName" column="device_name"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="selfReportedSymptom" column="self_reported_symptom"/>
        <result property="reportDescribe" column="report_describe"/>
        <result property="reportDiagnose" column="report_diagnose"/>
        <result property="isPostive" column="is_postive"/>
        <result property="registerTime" column="register_time"/>
        <result property="reportTime" column="report_time"/>
        <result property="reporter" column="reporter"/>
        <result property="checker" column="checker"/>
        <result property="checkTime" column="check_time"/>
        <result property="applyNumber" column="apply_number"/>
        <result property="applyDepartment" column="apply_department"/>
        <result property="applyDoctor" column="apply_doctor"/>
        <result property="artificer" column="artificer"/>
        <result property="isPublic" column="is_public"/>
        <result property="isonline" column="isonline"/>
        <result property="isExport" column="is_export"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryNotRelateDicom" resultMap="StudyResultMap">
        SELECT ts.study_uid,ts.access_number,ts.study_time,ts.study_no,
        ts.patient_id,ts.patient_name,ts.patient_sex,ts.patient_birth_date,ts.patient_age,
        ts.patient_type,ts.visit_date,ts.out_patient_no,ts.in_patient_no,ts.physical_sign,ts.clinical_diagnosis,
        ts.study_item_name,ts.part_name,ts.device_type,ts.medical_history,ts.self_reported_symptom,ts.report_describe,ts.is_postive,
        ts.register_time,ts.report_time,ts.reporter,ts.checker,ts.check_time,ts.apply_number,ts.apply_department,ts.apply_doctor,
        ts.artificer,ts.is_public,ts.isonline,ts.is_export
        FROM t_study ts
        LEFT JOIN t_case_study tcs ON ts.study_uid = tcs.study_uid
        WHERE ts.create_by = #{userId}
        AND tcs.case_id IS NULL
        AND (
            #{keyword} IS NULL
            OR ts.patient_name LIKE CONCAT('%', #{keyword}, '%')
            OR ts.patient_id LIKE CONCAT('%', #{keyword}, '%')
        )
    </select>

</mapper>
