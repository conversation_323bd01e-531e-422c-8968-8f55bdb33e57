{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "ESNext", "useDefineForClassFields": true, "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM", "es2017"], "skipLibCheck": true, "types": ["vitest", "vite/client"], "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}]}