# 病例导出字段选择功能说明

## 功能概述

新增了病例导出字段选择功能，前端可以指定要导出的字段列表，系统会根据选择的字段进行导出。如果不指定字段列表，则导出所有可用字段。

## 修改内容

### 1. 请求参数修改

#### DeptCaseSearchReq.java
添加了 `exportFields` 参数：
```java
@ApiModelProperty(value = "导出字段列表，为空时导出所有字段")
private List<String> exportFields;
```

#### PersonalCaseSearchReq.java
添加了 `exportFields` 参数：
```java
@ApiModelProperty(value = "导出字段列表，为空时导出所有字段")
private List<String> exportFields;
```

### 2. 导出工具类修改

#### CaseExportUtil.java
- 添加了新的重载方法支持字段过滤：
  - `exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath, List<String> exportFields)`
  - `exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath, List<String> exportFields)`
- 添加了字段过滤逻辑 `getExportFieldInfoWithFilter()` 方法

### 3. 导出任务类修改

#### CaseDetailExportTask.java
- 修改了 `generateExportFile()` 方法，支持从请求参数中提取字段列表
- 添加了 `getExportFieldsFromRequest()` 方法，使用反射从请求对象中获取字段列表

## 使用方法

### 前端请求示例

#### 1. 导出所有字段（原有行为）
```json
{
  "caseTypeId": "dept_case_001",
  "patientSex": "男",
  "pageNum": 1,
  "pageSize": 10
  // 不传 exportFields 参数或传 null/[]
}
```

#### 2. 导出指定字段
```json
{
  "caseTypeId": "dept_case_001",
  "patientSex": "男",
  "pageNum": 1,
  "pageSize": 10,
  "exportFields": [
    "caseId",
    "caseName",
    "patientName",
    "patientSex",
    "patientAge",
    "diseaseName",
    "diagnosis"
  ]
}
```

### 可用的导出字段列表

以下是CaseExportDataDto中所有可用的导出字段名称：

#### 基本病例信息
- `caseId` - 病例ID
- `caseName` - 病例名称
- `caseNo` - 病例编号
- `diseaseName` - 疾病名称
- `diagnosis` - 最终诊断
- `patientId` - 患者ID
- `patientName` - 患者姓名
- `patientSex` - 患者性别
- `patientBirthDate` - 患者生日
- `patientAge` - 患者年龄
- `medicalHistory` - 患者病史
- `difficulty` - 难度等级
- `caseCategory` - 是否典型
- `sign` - 征象
- `caseAnalysis` - 病例分析
- `remark` - 备注
- `createByName` - 创建人
- `qualityMatch` - 定性匹配
- `positionMatch` - 定位匹配
- `sourceType` - 来源
- `followStatus` - 随访状态
- `caseType` - 病例库类型
- `createTimeText` - 创建时间
- `tagInfoText` - 标签信息

#### 随访结果分类
- `surgeryResult` - 手术随访结果
- `ultrasoundResult` - 超声随访结果
- `clinicalResult` - 临床随访结果
- `pathologyResult` - 病理随访结果

#### 检查报告信息
- `studyId` - 检查ID
- `studySourceTime` - 检查来源/检查时间
- `studyTimePart` - 检查时间的检查部位
- `studyDate` - 检查日期
- `outpatientNo` - 门诊号
- `inpatientNo` - 住院号
- `studyNo` - 检查号
- `studyItem` - 检查项目
- `studyItemName` - 检查项目名称
- `partName` - 检查部位
- `deviceName` - 设备名称
- `routine` - 常规
- `reportDescribe` - 影像学表现
- `reportDiagnose` - 影像学诊断
- `reportTime` - 报告时间
- `reporter` - 报告人
- `checker` - 审核人
- `checkTime` - 审核时间
- `requestNo` - 申请号
- `requestDept` - 申请科室
- `requestDoctor` - 申请医生
- `technician` - 技师
- `opinion` - 意见公告
- `studyDescription` - 检查描述

## 实现原理

1. **字段过滤**：系统会根据传入的字段列表过滤 `@ExportField` 注解的字段
2. **索引重排**：过滤后的字段会重新分配索引，确保Excel列的连续性
3. **兼容性**：如果不传字段列表或传空列表，系统会导出所有标记为 `exportable=true` 的字段
4. **错误处理**：如果指定的字段名不存在，系统会记录警告并忽略该字段

## 注意事项

1. **字段名大小写敏感**：传入的字段名必须与Java字段名完全匹配
2. **字段顺序**：导出的字段顺序会按照 `exportFields` 列表中的顺序排列
3. **向后兼容**：现有的导出接口不需要修改，新功能向后兼容
4. **性能优化**：字段信息会被缓存，避免重复解析反射信息

## 测试建议

1. 测试不传 `exportFields` 参数的情况（应该导出所有字段）
2. 测试传空数组的情况（应该导出所有字段）
3. 测试传入有效字段名列表的情况
4. 测试传入无效字段名的情况（应该忽略无效字段并记录警告）
5. 测试字段顺序是否按照传入顺序排列
