import type { MockMethod } from 'vite-plugin-mock'
// 微服务名-资源管理部分，包括文件上传、经典病例、课件资源、资料分类
// const microServiceResource = 'riemanRes'
// 微服务名-权限管理部分，包括分组管理、用户管理、角色管理、菜单路由管理、登录登出等
const microServiceAuth = 'auth-server'
// 微服务名-教学考勤部分，包括教学回顾、教学计划、规培授课、课程预习等
// const microServiceEdu = 'riemanEdu'
// 微服务名-基础部分，包括个人主页、入科登记、出科管理、用户等
// const microServiceBase = 'riemanBase'
const users = [
  {
    name: 'admin',
    password: '96e79218965eb72c92a549dd5a330112',
    token: 'admin',
    info: {
      userID: 1, userName: 'admin', userType: 99,
    },
  },
  {
    name: 'editor',
    password: '123456',
    token: 'editor',
    info: {
      userID: 2, userName: 'editor', userType: 1, token: 'editor',
    },
  },
  {
    name: 'test',
    password: '123456',
    token: 'test',
    info: {
      userID: 3, userName: 'test', userType: 1, token: 'test',
    },
  },
]
export default [
  {
    url: '/mock/api/auth-server/open/login',
    method: 'post',
    response: ({ body }) => {
      const user = users.find((user) => {
        return body.username === user.name && body.password === user.password
      })
      if (user) {
        return {
          state: true,
          errorCode: 0,
          message: '操作成功',
          data: user.token,
        }
      }
      else {
        return {
          state: false,
          errorCode: 1048576,
          message: '账户名或密码输入不正确',
          data: {},
        }
      }
    },
  },
  {
    url: '/mock/archimedes/open/getKeys',
    method: 'get',
    response: () => {
      return {
        state: true,
        errorCode: 0,
        message: '操作成功',
        data: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDddMItQ8gw1i5qqdCuPSWyrJ9TnRK/4n1N/xIImX+BLEvatbbF+T+RXfmIYcdRAxXuziiQ7nrVI7/G36Los3U9dsJdWF0EPij4e1tdR/spJgr22uMwZfkBbcPYelHIS2ehLkXjgOmy+XS/sEMCI7zRlPO+/HvHo1fz+HA5nhLHrQIDAQAB',
      }
    },
  },
  {
    url: '/mock/archimedes/org/user/getUserInfo',
    method: 'get',
    response: () => {
      return {
        state: true,
        errorCode: 0,
        message: '操作成功',
        data: {
          user:
           {
             id: 1,
             sysUserId: 95011920003141,
             sysGpId: 76584883825413,
             userName: 'admin',
             phone: 'rieman-admin',
             jobNumber: null,
             userNo: null,
             proTitle: null,
             headPic: null,
             studentTypeId: null,
             schoolId: null,
             profession: null,
             tutorName: null,
             workplaceId: null,
             interestedIds: null,
             teacherUserId: null,
             actualTeachTime: null,
             remark: null,
             actualEnterTime: null,
             outTime: null,
             actualOutTime: null,
             grade: null,
             outStatus: 0,
             registerStatus: 0,
             applyStatus: false,
             canExam: false,
             canOut: false,
             layout: 6,
             createTime: '2023-12-14 11:50',
             updateTime: null,
             delFlag: false,
             userTypeList: [],
           },
        },
      }
    },
  },
  {
    url: `/mock/api/${microServiceAuth}/user/authRole/query`,
    method: 'get',
    response: () => {
      return {
        state: true,
        errorCode: 0,
        message: '操作成功',
        data: [
          {
            roleId: 1000001,
            platId: 76584883768069,
            roleName: '超管',
            status: '0',
            delFlag: '0',
            createBy: null,
            createTime: null,
            updateBy: 1,
            updateTime: '2024-03-04T15:38:14.000+08:00',
            menuIds: null,
            groupIds: null,
            permissions: null,
            flag: true,
            admin: false,
          },
          {
            roleId:
            1000002,
            platId: 76584883768069,
            roleName: '管理员',
            status: '0',
            delFlag: '0',
            createBy: null,
            createTime: null,
            updateBy: 1,
            updateTime: '2024-03-04T15:39:21.000+08:00',
            menuIds: null,
            groupIds: null,
            permissions: null,
            flag: false,
            admin: false,
          },
          {
            roleId: 1000003,
            platId: 76584883768069,
            roleName: '教学秘书',
            status: '0',
            delFlag: '0',
            createBy: null,
            createTime: null,
            updateBy: 1,
            updateTime: '2024-03-05T09:21:21.000+08:00',
            menuIds: null,
            groupIds: null,
            permissions: null,
            flag: false,
            admin: false,
          },
          {
            roleId: 1000004,
            platId: 76584883768069,
            roleName: '老师',
            status: '0',
            delFlag: '0',
            createBy: null,
            createTime: null,
            updateBy: 1,
            updateTime: '2024-03-05T09:23:37.000+08:00',
            menuIds: null,
            groupIds: null,
            permissions: null,
            flag: false,
            admin: false,
          },
          {
            roleId: 1000005,
            platId: 76584883768069,
            roleName: '学员',
            status: '0',
            delFlag: '0',
            createBy: null,
            createTime: null,
            updateBy: 1,
            updateTime: '2024-03-05T09:24:19.000+08:00',
            menuIds: null,
            groupIds: null,
            permissions: null,
            flag: false,
            admin: false,
          }],
      }
    },
  },
  {
    url: '/mock/archimedes/menu/getMenuRouters',
    method: 'get',
    response: () => {
      return {
        state: true,
        errorCode: 0,
        message: '操作成功',
        data: [
          {
            path: '/',
            hidden: false,
            component: 'Layout',
            children: [
              {
                name: '/dashboard',
                path: '/dashboard',
                hidden: false,
                component: '/dashboard',
                meta: {
                  title: '个人主页',
                  icon: '#',
                  keyWords: [],
                },
              },
            ],
          },
          {
            path: '/case-manage',
            hidden: false,
            component: '/case-manage',
            children: [
              {
                name: '/case-manage',
                path: '/case-manage',
                hidden: false,
                component: '/case-manage/[id]',
                meta: {
                  title: '病案管理',
                  icon: '#',
                  keyWords: [],
                },
              },
            ],
          },
          // {
          //   path: '/case-detail',
          //   hidden: true,
          //   component: '/case-detail',
          //   children: [
          //     {
          //       name: '/case-detail',
          //       path: '/case-detail',
          //       hidden: false,
          //       component: '/case-detail',
          //       meta: {
          //         title: '病案详情',
          //         icon: '#',
          //         keyWords: [],
          //       },
          //     },
          //   ],
          // },
          // {
          //   name: '/case',
          //   path: '//case',
          //   hidden: false,
          //   redirect: 'noRedirect',
          //   component: '/case',
          //   alwaysShow: true,
          //   meta: {
          //     title: '病案管理',
          //     icon: '#',
          //     keyWords: [],
          //   },
          //   children: [
          //     {
          //       name: '/case/case-manage',
          //       path: '/case/case-manage',
          //       hidden: false,
          //       component: '/case/case-manage',
          //       meta: {
          //         title: '病案管理',
          //         icon: '#',
          //         keyWords: [
          //           'PAGE_SET',
          //         ],
          //       },
          //     },
          //     {
          //       name: '/case/case-detail',
          //       path: '/case/case-detail',
          //       hidden: true,
          //       component: '/case/case-detail',
          //       meta: {
          //         title: '病案详情',
          //         icon: '#',
          //         keyWords: [
          //           'PAGE_SET',
          //         ],
          //       },
          //     },
          //   ],
          // },
          {
            name: '/organization',
            path: '//organization',
            hidden: false,
            redirect: 'noRedirect',
            component: '/organization',
            alwaysShow: true,
            meta: {
              title: '组织架构',
              icon: '#',
              keyWords: [],
            },
            children: [
              {
                name: '/organization/department-manage',
                path: '/organization/department-manage',
                hidden: false,
                component: '/organization/department-manage',
                meta: {
                  title: '科室管理',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
              {
                name: '/organization/user-manage',
                path: '/organization/user-manage',
                hidden: false,
                component: '/organization/user-manage',
                meta: {
                  title: '用户管理',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
              {
                name: '/organization/role-manage',
                path: '/organization/role-manage',
                hidden: false,
                component: '/organization/role-manage',
                meta: {
                  title: '角色管理',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
            ],
          },
          {
            name: '/settings',
            path: '//settings',
            hidden: false,
            redirect: 'noRedirect',
            component: '/settings',
            alwaysShow: true,
            meta: {
              title: '系统管理',
              icon: '#',
              keyWords: [],
            },
            children: [
              {
                name: '/settings/room-manage',
                path: '/settings/room-manage',
                hidden: false,
                component: '/settings/room-manage',
                meta: {
                  title: '房间管理',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
              {
                name: '/settings/device-manage',
                path: '/settings/device-manage',
                hidden: false,
                component: '/settings/device-manage',
                meta: {
                  title: '设备管理',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
              {
                name: '/settings/system-info',
                path: '/settings/system-info',
                hidden: false,
                component: '/settings/system-info',
                meta: {
                  title: '系统信息',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
              {
                name: '/settings/operation-log',
                path: '/settings/operation-log',
                hidden: false,
                component: '/settings/operation-log',
                meta: {
                  title: '操作日志',
                  icon: '#',
                  keyWords: [
                    'PAGE_SET',
                  ],
                },
              },
            ],
          },
        ],
      }
    },
  },
  {
    url: '/mock/api/user/info',
    method: 'post',
    response: ({ body }) => {
      const { token } = body
      const info = users.find((user) => {
        return user.token === token
      })?.info
      if (info) {
        return {
          code: 200,
          data: {
            info,
          },
        }
      }
      else {
        return {
          code: 403,
          data: {},
          msg: '无访问权限',
        }
      }
    },
  },
  {
    url: '/mock/api/user/out',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {},
        msg: 'success',
      }
    },
  },
  {
    url: '/mock/api/user/passwordChange',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {},
        msg: 'success',
      }
    },
  },
  {
    url: '/mock/api/user/list',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          'list|30': [{
            'id|+1': 0,
            'userName': '@cname',
            'userType': 1,
          }],
        },
        msg: '',
      }
    },
  },
  {
    url: '/mock/archimedes/case/getAllCase',
    method: 'get',
    response: () => {
      return {
        state: true,
        errorCode: 0,
        message: '操作成功',
        data: [
          {
            title: '颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: true,
          }, {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: false,
          },
          {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: true,
          }, {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: false,
          },
          {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: true,
          }, {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: false,
          }, {
            title: '颅骨骨瘤切除术',
            name: '曾晓杰',
            department: '脑科',
            patientID: 'NK56854',
            note: '术后病情稳定',
            time: '2023年9月20日',
            picPath: 'pic1.png',
            isChecked: true,
          }],
      }
    },
  },
] as MockMethod[]
