import type { MockMethod } from 'vite-plugin-mock'
export default [
  {
    url: '/mock/api/role',
    method: 'get',
    response: ({ query }) => {
      const { page, limit } = query
      const retData: any = []
      for (let i = (page - 1) * limit; i < page * limit; i++) {
        retData.push({
          id: i + 1,
          name: `测试角色${i + 1}`,
          description: `测试角色${i + 1}`,
        })
      }
      return {
        page,
        total: 100,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/role/insert',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/role/update',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/role/delete',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/role/assign/menu',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/role/assign/data',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/role/assign/operation',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
] as MockMethod[]
