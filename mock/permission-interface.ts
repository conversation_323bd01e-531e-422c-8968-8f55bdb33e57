export default [
  {
    url: '/mock/api/permission/interface',
    method: 'get',
    response: ({ query }) => {
      const { page, limit } = query
      const retData: any = []
      for (let i = (page - 1) * limit; i < page * limit; i++) {
        retData.push({
          id: i + 1,
          name: `测试权限${i + 1}`,
          description: `测试权限${i + 1}`,
        })
      }
      return {
        page,
        total: 100,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/interface/operation/3',
    method: 'get',
    response: () => {
      const retData: any = []
      retData.push({
        id: 1,
        name: `测试权限${1}`,
        description: `测试权限${1}`,
      })
      retData.push({
        id: 7,
        name: `测试权限${8}`,
        description: `测试权限${8}`,
      })
      return {
        page: 1,
        total: 5,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/backend/interface',
    method: 'get',
    response: ({ query }) => {
      const { page, limit } = query
      const retData: any = []
      for (let i = (page - 1) * limit; i < page * limit; i++) {
        retData.push({
          id: i + 1,
          name: `后台权限${i + 1}`,
          description: `后台权限${i + 1}`,
        })
      }
      return {
        page,
        total: 30,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/interface/insert',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/interface/update',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/interface/delete/1',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
]
