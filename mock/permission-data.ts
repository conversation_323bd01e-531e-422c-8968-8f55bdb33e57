export default [
  {
    url: '/mock/api/permission/data',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      retData.push({
        id: 1,
        parentId: 0,
        name: '大区经理',
        description: '销售大区经理数据权限',
      },
      {
        id: 2,
        parentId: 0,
        name: '市场经理',
        description: '市场经理数据权限',
      },
      {
        id: 3,
        parentId: 0,
        name: '研发经理',
        description: '研发经理数据权限',
      })
      return {
        page: 1,
        total: 3,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/1',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/2',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/3',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      retData.push({
        id: 4,
        parentId: 3,
        name: '研发硬件主管',
        description: '研发硬件主管数据权限',
      },
      {
        id: 5,
        parentId: 3,
        name: '研发解决方案主管',
        description: '研发解决方案主管数据权限',
      },
      {
        id: 6,
        parentId: 3,
        name: '研发软件主管',
        description: '研发软件主管数据权限',
      })
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/6',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      retData.push({
        id: 8,
        parentId: 3,
        name: '研发软件组长',
        description: '研发软件组长数据权限',
      })
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/8',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      retData.push({
        id: 9,
        parentId: 3,
        name: '研发软件开发',
        description: '研发软件开发数据权限',
      })
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/4',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/5',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/data/parent/9',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        name: string
        description: string
      }[]
      return {
        page: 1,
        total: 1,
        list: retData,
      }
    },
  },
]
