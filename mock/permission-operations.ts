export default [
  {
    url: '/mock/api/permission/operation',
    method: 'get',
    response: ({ query }) => {
      const { page, limit } = query
      const retData: any = []
      for (let i = (page - 1) * limit; i < page * limit; i++) {
        retData.push({
          id: i + 1,
          key: `key${i + 1}`,
          name: `操作权限${i + 1}`,
          description: `操作权限${i + 1}`,
        })
      }
      return {
        page,
        total: 30,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/operation/insert',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/operation/update',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/operation/delete/1',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/operation/interface/remove/3',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/operation/interface/add',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
]
