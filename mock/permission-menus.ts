export default [
  {
    url: '/mock/api/permission/menu',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        route: string
        name: string
        description: string
      }[]
      retData.push({
        id: 1,
        parentId: 0,
        route: '/account',
        name: '账户管理',
        description: '账户管理权限',
      },
      {
        id: 2,
        parentId: 0,
        route: '/role',
        name: '角色管理',
        description: '角色管理权限',
      },
      {
        id: 3,
        parentId: 0,
        route: '/permissions',
        name: '权限管理',
        description: '权限管理权限',
      })
      return {
        page: 1,
        total: 3,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/menu/parent/1',
    method: 'get',
    response: () => {
      return {
        page: 1,
        total: 1,
        list: [],
      }
    },
  },
  {
    url: '/mock/api/permission/menu/parent/2',
    method: 'get',
    response: () => {
      return {
        page: 1,
        total: 1,
        list: [],
      }
    },
  },
  {
    url: '/mock/api/permission/menu/parent/3',
    method: 'get',
    response: () => {
      const retData = [] as {
        id: number
        parentId: number
        route: string
        name: string
        description: string
      }[]
      retData.push({
        id: 4,
        parentId: 0,
        route: '/menu',
        name: '菜单权限菜单',
        description: '菜单权限菜单管理权限',
      },
      {
        id: 5,
        parentId: 0,
        route: '/operation',
        name: '操作权限菜单',
        description: '操作权限菜单管理权限',
      },
      {
        id: 6,
        parentId: 0,
        route: '/data',
        name: '数据权限菜单',
        description: '数据权限菜单管理权限',
      },
      {
        id: 7,
        parentId: 0,
        route: '/interface',
        name: '接口权限菜单',
        description: '接口权限菜单管理权限',
      })
      return {
        page: 1,
        total: 4,
        list: retData,
      }
    },
  },
  {
    url: '/mock/api/permission/menu/insert',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/menu/update',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
  {
    url: '/mock/api/permission/menu/delete/1',
    method: 'post',
    response: () => {
      return {
        state: true,
      }
    },
  },
]
