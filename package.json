{"name": "starter", "type": "module", "version": "0.0.1", "private": true, "packageManager": "pnpm@7.8.0", "author": {"name": "<PERSON><PERSON><PERSON>"}, "scripts": {"dev": "vite --host", "build": "vite build", "prod": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@intlify/unplugin-vue-i18n": "6.0.3", "@types/nprogress": "0.2.0", "@vueuse/core": "9.0.0", "@vueuse/head": "0.7.7", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.27.2", "cos-js-sdk-v5": "1.8.6", "echarts": "5.6.0", "element-plus": "2.8.3", "js-base64": "3.7.5", "jsencrypt": "^3.3.1", "mitt": "^3.0.0", "nprogress": "0.2.0", "pinia": "2.0.17", "sass": "1.54.8", "socket.io-client": "4.5.1", "throttle-debounce": "5.0.0", "ts-md5": "^1.3.1", "unplugin-icons": "^0.15.1", "vue": "3.2.37", "vue-i18n": "11.1.2", "vue-router": "4.1.3"}, "devDependencies": {"@antfu/eslint-config": "0.26.3", "@iconify-json/carbon": "^1.1.14", "@iconify-json/eos-icons": "^1.1.6", "@iconify-json/mdi": "^1.1.50", "@iconify-json/mi": "^1.1.4", "@iconify/vue": "^4.1.0", "@types/throttle-debounce": "^5.0.2", "@vitejs/plugin-vue": "5.2.1", "eslint": "8.23.1", "typescript": "4.6.4", "unplugin-auto-import": "0.10.1", "unplugin-vue-components": "0.21.2", "vite": "5.4.14", "vite-plugin-inspect": "0.8.9", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^2.0.1", "vitest": "3.0.8", "vue-tsc": "0.38.4"}}