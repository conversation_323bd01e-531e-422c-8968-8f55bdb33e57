# 线程池环境下的用户上下文传递解决方案

## 问题分析

您的场景特点：
1. 使用线程池提交任务：`ContextHolder.getBean("caseExport", ExecutorService.class).submit(...)`
2. 任务类：`CaseDetailExportTask`
3. 深层调用链：Task -> Strategy -> Service -> Mapper，需要在深层获取用户信息
4. 不想一层层传递userId参数

## 最佳解决方案：装饰线程池 + 上下文传递

### 方案一：创建上下文传递的线程池装饰器（推荐）

#### 1. 创建上下文信息类

```java
package com.jusha.caselibrary.common.util;

import com.jusha.caselibrary.system.dto.RedisUser;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

/**
 * 线程上下文信息
 */
public class ThreadContext {
    private final Long userId;
    private final RedisUser user;
    private final String traceId;
    private final ServletRequestAttributes requestAttributes;
    private final Map<String, String> mdcContext;
    
    private ThreadContext(Long userId, RedisUser user, String traceId, 
                         ServletRequestAttributes requestAttributes, Map<String, String> mdcContext) {
        this.userId = userId;
        this.user = user;
        this.traceId = traceId;
        this.requestAttributes = requestAttributes;
        this.mdcContext = mdcContext;
    }
    
    /**
     * 捕获当前线程的上下文
     */
    public static ThreadContext capture() {
        try {
            Long userId = LoginUtil.getLoginUserId();
            RedisUser user = LoginUtil.getLoginUser();
            String traceId = MDC.get("traceId");
            ServletRequestAttributes requestAttributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            Map<String, String> mdcContext = MDC.getCopyOfContextMap();
            
            return new ThreadContext(userId, user, traceId, requestAttributes, mdcContext);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 应用上下文到当前线程
     */
    public void apply() {
        if (requestAttributes != null) {
            RequestContextHolder.setRequestAttributes(requestAttributes);
        }
        if (mdcContext != null) {
            MDC.setContextMap(mdcContext);
        }
    }
    
    /**
     * 清理上下文
     */
    public void clear() {
        RequestContextHolder.resetRequestAttributes();
        MDC.clear();
    }
    
    // getters
    public Long getUserId() { return userId; }
    public RedisUser getUser() { return user; }
    public String getTraceId() { return traceId; }
    public ServletRequestAttributes getRequestAttributes() { return requestAttributes; }
    public Map<String, String> getMdcContext() { return mdcContext; }
}
```

#### 2. 创建上下文传递的ExecutorService装饰器

```java
package com.jusha.caselibrary.common.util;

import java.util.concurrent.*;
import java.util.Collection;
import java.util.List;

/**
 * 支持上下文传递的ExecutorService装饰器
 */
public class ContextAwareExecutorService implements ExecutorService {
    
    private final ExecutorService delegate;
    
    public ContextAwareExecutorService(ExecutorService delegate) {
        this.delegate = delegate;
    }
    
    @Override
    public Future<?> submit(Runnable task) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context));
    }
    
    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context), result);
    }
    
    @Override
    public <T> Future<T> submit(Callable<T> task) {
        ThreadContext context = ThreadContext.capture();
        return delegate.submit(wrapWithContext(task, context));
    }
    
    @Override
    public void execute(Runnable command) {
        ThreadContext context = ThreadContext.capture();
        delegate.execute(wrapWithContext(command, context));
    }
    
    /**
     * 包装Runnable，添加上下文传递
     */
    private Runnable wrapWithContext(Runnable task, ThreadContext context) {
        return () -> {
            if (context != null) {
                context.apply();
            }
            try {
                task.run();
            } finally {
                if (context != null) {
                    context.clear();
                }
            }
        };
    }
    
    /**
     * 包装Callable，添加上下文传递
     */
    private <T> Callable<T> wrapWithContext(Callable<T> task, ThreadContext context) {
        return () -> {
            if (context != null) {
                context.apply();
            }
            try {
                return task.call();
            } finally {
                if (context != null) {
                    context.clear();
                }
            }
        };
    }
    
    // 委托其他方法到原始ExecutorService
    @Override
    public void shutdown() {
        delegate.shutdown();
    }
    
    @Override
    public List<Runnable> shutdownNow() {
        return delegate.shutdownNow();
    }
    
    @Override
    public boolean isShutdown() {
        return delegate.isShutdown();
    }
    
    @Override
    public boolean isTerminated() {
        return delegate.isTerminated();
    }
    
    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return delegate.awaitTermination(timeout, unit);
    }
    
    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
            .map(task -> wrapWithContext(task, context))
            .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAll(wrappedTasks);
    }
    
    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) 
            throws InterruptedException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
            .map(task -> wrapWithContext(task, context))
            .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAll(wrappedTasks, timeout, unit);
    }
    
    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks) 
            throws InterruptedException, ExecutionException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
            .map(task -> wrapWithContext(task, context))
            .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAny(wrappedTasks);
    }
    
    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) 
            throws InterruptedException, ExecutionException, TimeoutException {
        ThreadContext context = ThreadContext.capture();
        Collection<Callable<T>> wrappedTasks = tasks.stream()
            .map(task -> wrapWithContext(task, context))
            .collect(java.util.stream.Collectors.toList());
        return delegate.invokeAny(wrappedTasks, timeout, unit);
    }
}
```

#### 3. 修改线程池配置

在您的Spring配置中，将原有的线程池包装为支持上下文传递的版本：

```java
@Configuration
public class ExecutorConfig {
    
    @Bean("caseExport")
    public ExecutorService caseExportExecutor() {
        // 创建原始线程池
        ThreadPoolExecutor originalExecutor = new ThreadPoolExecutor(
            5, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadFactoryBuilder().setNameFormat("case-export-%d").build()
        );
        
        // 包装为支持上下文传递的线程池
        return new ContextAwareExecutorService(originalExecutor);
    }
}
```

#### 4. 现在您的代码无需任何修改

```java
// 原有代码保持不变，自动支持上下文传递
ContextHolder.getBean("caseExport", ExecutorService.class)
    .submit(new CaseDetailExportTask<CaseExportDataDto, DeptCaseSearchReq>(taskId, req, tmpPath, false));
```

在`CaseDetailExportTask`的深层调用中，现在可以正常使用：
```java
// 在任何深层调用中都可以正常获取用户信息
req.setUserId(LoginUtil.getLoginUserId());
```

### 方案二：使用TransmittableThreadLocal（阿里巴巴方案）

如果您希望使用更成熟的解决方案，可以使用阿里巴巴的TTL：

#### 1. 添加依赖

```xml
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>transmittable-thread-local</artifactId>
    <version>2.14.2</version>
</dependency>
```

#### 2. 修改LoginUtil使用TTL

```java
// 在LoginUtil中添加TTL支持
private static final TransmittableThreadLocal<RedisUser> USER_HOLDER = new TransmittableThreadLocal<>();

public static void setCurrentUser(RedisUser user) {
    USER_HOLDER.set(user);
}

public static RedisUser getCurrentUser() {
    return USER_HOLDER.get();
}

// 在getLoginUser()方法中，获取到用户后设置到TTL
public static RedisUser getLoginUser(){
    // ... 原有逻辑
    RedisUser loginUser = // 获取用户逻辑
    
    // 设置到TTL中
    setCurrentUser(loginUser);
    
    return loginUser;
}
```

#### 3. 装饰线程池

```java
@Bean("caseExport")
public ExecutorService caseExportExecutor() {
    ThreadPoolExecutor originalExecutor = new ThreadPoolExecutor(
        5, 10, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(100),
        new ThreadFactoryBuilder().setNameFormat("case-export-%d").build()
    );
    
    // 使用TTL装饰线程池
    return TtlExecutors.getTtlExecutorService(originalExecutor);
}
```

### 方案三：简化版本 - 只传递用户信息

如果您只需要传递用户信息，可以使用更简单的方案：

#### 1. 修改CaseDetailExportTask构造函数

```java
public class CaseDetailExportTask<D, T> implements Runnable {
    
    private final String taskId;
    private final T searchRequest;
    private final String tmpPath;
    private final Boolean followExport;
    private final Long userId; // 添加用户ID字段
    private final RedisUser user; // 添加用户信息字段
    
    public CaseDetailExportTask(String taskId, T searchRequest, String tmpPath, Boolean followExport) {
        this.taskId = taskId;
        this.searchRequest = searchRequest;
        this.tmpPath = tmpPath;
        this.followExport = followExport;
        // 在构造时获取用户信息
        this.userId = LoginUtil.getLoginUserId();
        this.user = LoginUtil.getLoginUser();
    }
    
    // 提供获取用户信息的方法
    public Long getCurrentUserId() {
        return userId;
    }
    
    public RedisUser getCurrentUser() {
        return user;
    }
}
```

#### 2. 在需要用户信息的地方使用

在策略类或服务类中，可以通过某种方式获取到当前的Task实例，然后获取用户信息。

## 推荐方案

我强烈推荐使用**方案一（装饰线程池）**，因为：

1. **透明性**：对现有代码完全透明，无需修改
2. **完整性**：传递完整的上下文信息，不仅仅是用户ID
3. **可维护性**：集中管理上下文传递逻辑
4. **扩展性**：可以轻松扩展传递其他上下文信息

## 实施步骤

1. 创建`ThreadContext`类
2. 创建`ContextAwareExecutorService`装饰器
3. 修改线程池配置，使用装饰器包装原有线程池
4. 测试验证：在`CaseDetailExportTask`的深层调用中使用`LoginUtil.getLoginUserId()`

这样，您就可以在不修改任何业务代码的情况下，让所有通过线程池提交的任务都能正常获取到用户上下文信息！