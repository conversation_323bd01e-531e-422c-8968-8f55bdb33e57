<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.rimex.docking.db2Dao.DockingDb2Dao">

    <select id="getStudyInfoStandardDb2" parameterType="string" resultMap="StudyInfoStandardDb2Map">
        SELECT * FROM study_info WHERE access_number = #{reqid} OR patient_name = #{reqid}
        order by study_time desc
    </select>


    <resultMap id="StudyInfoStandardDb2Map" type="com.jusha.rimex.docking.beans.resp.StudyInfoReturn">
        <id column="id" property="studyNo"/>
        <result column="study_uid" property="studyUid"/>
        <result column="access_number" property="accessNumber"/>
        <result column="out_patient_id" property="outPatientNo"/>
        <result column="in_patient_id" property="inPatientNo"/>
        <result column="visit_date" property="visitDate"/>
        <result column="patient_type" property="patientType"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_age" property="patientAge"/>
        <result column="patient_birth" property="patientBirthDate"/>
        <result column="patient_sex" property="patientSex"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="study_item_name" property="studyItemName"/>
        <result column="part_name" property="partName"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="apply_doctor" property="applyDoctor"/>
        <result column="apply_department" property="applyDepartment"/>
        <result column="report_describe" property="reportDescribe"/>
        <result column="report_diagnose" property="reportDiagnose"/>
        <result column="study_time" property="studyTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="report_doctor" property="reporter"/>
        <result column="report_time" property="reportTime"/>
        <result column="check_doctor" property="checker"/>
        <result column="check_time" property="checkTime"/>
        <result column="artificer" property="artificer"/>
        <result column="physical_sign" property="physicalSign"/>
        <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        <result column="self_reported_symptom" property="selfReportedSymptom"/>
        <result column="history_disease" property="medicalHistory"/>
    </resultMap>

</mapper>