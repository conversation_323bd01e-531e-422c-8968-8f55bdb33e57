<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.rimex.docking.dao.DockingDao">

<!--    <select id="getStudyInfoXtdiyy" parameterType="string" resultMap="com.jusha.rimex.docking.db.StudyInfoReturnMap">-->
<!--        SELECT * FROM study_info WHERE id = #{reqid} OR patient_name = #{reqid} OR patient_sfz = #{reqid} OR patient_out_patient_id = #{reqid}-->
<!--        OR patient_in_patient_id = #{reqid}-->
<!--    </select>-->

    <select id="getImagePathXtdiyy" parameterType="string" resultMap="com.jusha.rimex.docking.db.ImagePathReturnMap">
        SELECT
        <include refid="com.jusha.rimex.docking.db.ImagePathReturnMap_Column_List"/>
        FROM study_info st
        LEFT JOIN series_info se ON st.id = se.id
        LEFT JOIN image_info im ON se.series_id = im.series_id
        WHERE st.id = #{reqid}
    </select>

    <select id="getStudyUId" parameterType="string" resultMap="com.jusha.rimex.docking.db.ImagePathReturnMap">
        SELECT TOP 1
        <include refid="com.jusha.rimex.docking.db.ImagePathReturnMap_Column_List"/>
        FROM study_info st
        LEFT JOIN series_info se ON st.id = se.id
        LEFT JOIN image_info im ON se.series_id = im.series_id
        WHERE st.id = #{studyId}
    </select>

    <select id="getStudyInfoStandard" parameterType="string" resultMap="com.jusha.rimex.docking.db.StudyInfoStandardMap">
        SELECT * FROM study_info WHERE
        access_number = #{reqid}
        OR patient_name = #{reqid}
        OR patient_id = #{reqid}
        OR in_patient_id = #{reqid}
        OR out_patient_id = #{reqid}
    </select>

    <select id="getAllStudyInfo" parameterType="string" resultMap="com.jusha.rimex.docking.db.StudyInfoStandardMap">
        SELECT * FROM study_info
    </select>

    <select id="getAccByStudyId" parameterType="string" resultMap="com.jusha.rimex.docking.db.AccReturnMap">
        select
        <include refid="com.jusha.rimex.docking.db.AccReturnMap_Column_List"/>
        from study_info
        where study_id = #{studyId}
    </select>

    <select id="getStudyInfoByStudyId" parameterType="string" resultMap="com.jusha.rimex.docking.db.StudyInfoStandardMap">
        SELECT * FROM study_info WHERE id = #{studyId} OR study_id = #{studyId}
    </select>

<!--筛选检查列表-->
    <select id="searchStudyPage" parameterType="com.jusha.rimex.docking.beans.req.StudySearchPage"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from study_info
        <where>
            1=1
            <if test="studySearchPage.studyStartTime != null and studySearchPage.studyEndTime != null">
                and study_time &gt;= #{studySearchPage.studyStartTime}
                and study_time &lt;= #{studySearchPage.studyEndTime}
            </if>
            <if test="studySearchPage.studyPart != null and studySearchPage.studyPart != ''">
                and part_name like concat('%', #{studySearchPage.studyPart}, '%')
            </if>
            <if test="studySearchPage.studyNumber != null and studySearchPage.studyNumber != ''">
                and id = #{studySearchPage.studyNumber}
            </if>
            <if test="studySearchPage.deviceName != null and studySearchPage.deviceName != ''">
                and device_name = #{studySearchPage.deviceName}
            </if>
            <if test="studySearchPage.deviceType != null and studySearchPage.deviceType != ''">
                and device_type = #{studySearchPage.deviceType}
            </if>
            <if test="studySearchPage.patientNumber != null and studySearchPage.patientNumber != ''">
                and (out_patient_id = #{studySearchPage.patientNumber}
                or in_patient_id = #{studySearchPage.patientNumber})
            </if>
            <if test="studySearchPage.patientType != null and studySearchPage.patientType != ''">
                and patient_type = #{studySearchPage.patientType}
            </if>
            <if test="studySearchPage.studyItemName != null and studySearchPage.studyItemName != ''">
                and study_item = #{studySearchPage.studyItemName}
            </if>
            <if test="studySearchPage.artificers != null and studySearchPage.artificers.size > 0">
                and artificer in 
                <foreach collection="studySearchPage.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
            <if test="studySearchPage.reportDoctors != null and studySearchPage.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="studySearchPage.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
            <if test="studySearchPage.checkDoctors != null and studySearchPage.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="studySearchPage.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
        order by study_time desc
    </select>

    <select id="getStudyDetails" parameterType="String" resultMap="com.jusha.rimex.docking.db.StudyDetailsReturnMap">
        select <include refid="com.jusha.rimex.docking.db.StudyDetailsReturnMap_Column_List"/>
        from study_info
        where id = #{studyNumber}
    </select>

    <select id="getStudyForbid" parameterType="String" resultMap="com.jusha.rimex.docking.db.AddQcStudyMap">
        select <include refid="com.jusha.rimex.docking.db.AddQcStudyMap_Column_List"/>
        from study_info
        where id = #{studyNumber}
    </select>

<!--抽样测试-->
    <select id="getStudySample" resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from (
            select *, row_number() over (partition by study_info.check_doctor order by rand()) as row_num
            from study_info
        ) si
        where row_num &lt;= 5
        order by check_doctor
    </select>


<!--随机抽样-->
    <select id="getStudySampleRand" parameterType="com.jusha.rimex.docking.beans.req.SampleStudy"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from study_info
        <where>
            1=1
            <if test="sampleStudy.studyStartTime != null and sampleStudy.studyEndTime != null">
                and study_time &gt;= #{sampleStudy.studyStartTime}
                and study_time &lt;= #{sampleStudy.studyEndTime}
            </if>
            <if test="sampleStudy.studyPart != null and sampleStudy.studyPart != ''">
                and part_name like concat('%', #{sampleStudy.studyPart}, '%')
            </if>
            <if test="sampleStudy.studyNumber != null and sampleStudy.studyNumber != ''">
                and id = #{sampleStudy.studyNumber}
            </if>
            <if test="sampleStudy.deviceName != null and sampleStudy.deviceName != ''">
                and device_name = #{sampleStudy.deviceName}
            </if>
            <if test="sampleStudy.deviceType != null and sampleStudy.deviceType != ''">
                and device_type = #{sampleStudy.deviceType}
            </if>
            <if test="sampleStudy.patientNumber != null and sampleStudy.patientNumber != ''">
                and (out_patient_id = #{sampleStudy.patientNumber}
                or in_patient_id = #{sampleStudy.patientNumber})
            </if>
            <if test="sampleStudy.patientType != null and sampleStudy.patientType != ''">
                and patient_type = #{sampleStudy.patientType}
            </if>
            <if test="sampleStudy.studyItemName != null and sampleStudy.studyItemName != ''">
                and study_item = #{sampleStudy.studyItemName}
            </if>
            <if test="sampleStudy.artificers != null and sampleStudy.artificers.size > 0">
                and artificer in
                <foreach collection="sampleStudy.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
            <if test="sampleStudy.reportDoctors != null and sampleStudy.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="sampleStudy.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
            <if test="sampleStudy.checkDoctors != null and sampleStudy.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="sampleStudy.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
        order by rand()
        limit #{sampleStudy.sampleSize}
    </select>

<!--每审核医生抽样-->
    <select id="getStudySampleByCheckDoctor" parameterType="com.jusha.rimex.docking.beans.req.SampleStudy"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from (
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>,
        row_number() over (partition by study_info.check_doctor order by rand()) as row_num
        from study_info
            <where>
                1=1
                <if test="sampleStudy.studyStartTime != null and sampleStudy.studyEndTime != null">
                    and study_time &gt;= #{sampleStudy.studyStartTime}
                    and study_time &lt;= #{sampleStudy.studyEndTime}
                </if>
                <if test="sampleStudy.studyPart != null and sampleStudy.studyPart != ''">
                    and part_name like concat('%', #{sampleStudy.studyPart}, '%')
                </if>
                <if test="sampleStudy.studyNumber != null and sampleStudy.studyNumber != ''">
                    and id = #{sampleStudy.studyNumber}
                </if>
                <if test="sampleStudy.deviceName != null and sampleStudy.deviceName != ''">
                    and device_name = #{sampleStudy.deviceName}
                </if>
                <if test="sampleStudy.deviceType != null and sampleStudy.deviceType != ''">
                    and device_type = #{sampleStudy.deviceType}
                </if>
                <if test="sampleStudy.patientNumber != null and sampleStudy.patientNumber != ''">
                    and (out_patient_id = #{sampleStudy.patientNumber}
                    or in_patient_id = #{sampleStudy.patientNumber})
                </if>
                <if test="sampleStudy.patientType != null and sampleStudy.patientType != ''">
                    and patient_type = #{sampleStudy.patientType}
                </if>
                <if test="sampleStudy.studyItemName != null and sampleStudy.studyItemName != ''">
                    and study_item = #{sampleStudy.studyItemName}
                </if>
                <if test="sampleStudy.artificers != null and sampleStudy.artificers.size > 0">
                    and artificer in
                    <foreach collection="sampleStudy.artificers" item="artificer" open="(" close=")" separator=",">
                        #{artificer}
                    </foreach>
                </if>
                <if test="sampleStudy.reportDoctors != null and sampleStudy.reportDoctors.size > 0">
                    and report_doctor in
                    <foreach collection="sampleStudy.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                        #{reportDoctor}
                    </foreach>
                </if>
                <if test="sampleStudy.checkDoctors != null and sampleStudy.checkDoctors.size > 0">
                    and check_doctor in
                    <foreach collection="sampleStudy.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                        #{checkDoctor}
                    </foreach>
                </if>
            </where>
        ) si
        where row_num &lt;= #{sampleStudy.sampleSize}
        order by check_doctor
    </select>

<!--每报告医生抽样-->
    <select id="getStudySampleByReportDoctor" parameterType="com.jusha.rimex.docking.beans.req.SampleStudy"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from (
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>,
        row_number() over (partition by study_info.report_doctor order by rand()) as row_num
        from study_info
        <where>
            1=1
            <if test="sampleStudy.studyStartTime != null and sampleStudy.studyEndTime != null">
                and study_time &gt;= #{sampleStudy.studyStartTime}
                and study_time &lt;= #{sampleStudy.studyEndTime}
            </if>
            <if test="sampleStudy.studyPart != null and sampleStudy.studyPart != ''">
                and part_name like concat('%', #{sampleStudy.studyPart}, '%')
            </if>
            <if test="sampleStudy.studyNumber != null and sampleStudy.studyNumber != ''">
                and id = #{sampleStudy.studyNumber}
            </if>
            <if test="sampleStudy.deviceName != null and sampleStudy.deviceName != ''">
                and device_name = #{sampleStudy.deviceName}
            </if>
            <if test="sampleStudy.deviceType != null and sampleStudy.deviceType != ''">
                and device_type = #{sampleStudy.deviceType}
            </if>
            <if test="sampleStudy.patientNumber != null and sampleStudy.patientNumber != ''">
                and (out_patient_id = #{sampleStudy.patientNumber}
                or in_patient_id = #{sampleStudy.patientNumber})
            </if>
            <if test="sampleStudy.patientType != null and sampleStudy.patientType != ''">
                and patient_type = #{sampleStudy.patientType}
            </if>
            <if test="sampleStudy.studyItemName != null and sampleStudy.studyItemName != ''">
                and study_item = #{sampleStudy.studyItemName}
            </if>
            <if test="sampleStudy.artificers != null and sampleStudy.artificers.size > 0">
                and artificer in
                <foreach collection="sampleStudy.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
            <if test="sampleStudy.reportDoctors != null and sampleStudy.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="sampleStudy.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
            <if test="sampleStudy.checkDoctors != null and sampleStudy.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="sampleStudy.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
        ) si
        where row_num &lt;= #{sampleStudy.sampleSize}
        order by report_doctor
    </select>

<!--每技师抽样-->
    <select id="getStudySampleByArtificer" parameterType="com.jusha.rimex.docking.beans.req.SampleStudy"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from (
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>,
        row_number() over (partition by study_info.artificer order by rand()) as row_num
        from study_info
        <where>
            1=1
            <if test="sampleStudy.studyStartTime != null and sampleStudy.studyEndTime != null">
                and study_time &gt;= #{sampleStudy.studyStartTime}
                and study_time &lt;= #{sampleStudy.studyEndTime}
            </if>
            <if test="sampleStudy.studyPart != null and sampleStudy.studyPart != ''">
                and part_name like concat('%', #{sampleStudy.studyPart}, '%')
            </if>
            <if test="sampleStudy.studyNumber != null and sampleStudy.studyNumber != ''">
                and id = #{sampleStudy.studyNumber}
            </if>
            <if test="sampleStudy.deviceName != null and sampleStudy.deviceName != ''">
                and device_name = #{sampleStudy.deviceName}
            </if>
            <if test="sampleStudy.deviceType != null and sampleStudy.deviceType != ''">
                and device_type = #{sampleStudy.deviceType}
            </if>
            <if test="sampleStudy.patientNumber != null and sampleStudy.patientNumber != ''">
                and (out_patient_id = #{sampleStudy.patientNumber}
                or in_patient_id = #{sampleStudy.patientNumber})
            </if>
            <if test="sampleStudy.patientType != null and sampleStudy.patientType != ''">
                and patient_type = #{sampleStudy.patientType}
            </if>
            <if test="sampleStudy.studyItemName != null and sampleStudy.studyItemName != ''">
                and study_item = #{sampleStudy.studyItemName}
            </if>
            <if test="sampleStudy.artificers != null and sampleStudy.artificers.size > 0">
                and artificer in
                <foreach collection="sampleStudy.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
            <if test="sampleStudy.reportDoctors != null and sampleStudy.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="sampleStudy.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
            <if test="sampleStudy.checkDoctors != null and sampleStudy.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="sampleStudy.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
        ) si
        where row_num &lt;= #{sampleStudy.sampleSize}
        order by artificer
    </select>

<!--每设备抽样-->
    <select id="getStudySampleByDeviceName" parameterType="com.jusha.rimex.docking.beans.req.SampleStudy"
            resultMap="com.jusha.rimex.docking.db.QcStudyInfoReturnMap">
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>
        from (
        select <include refid="com.jusha.rimex.docking.db.QcStudyInfoReturnMap_Column_List"/>,
        row_number() over (partition by study_info.device_name order by rand()) as row_num
        from study_info
        <where>
            1=1
            <if test="sampleStudy.studyStartTime != null and sampleStudy.studyEndTime != null">
                and study_time &gt;= #{sampleStudy.studyStartTime}
                and study_time &lt;= #{sampleStudy.studyEndTime}
            </if>
            <if test="sampleStudy.studyPart != null and sampleStudy.studyPart != ''">
                and part_name like concat('%', #{sampleStudy.studyPart}, '%')
            </if>
            <if test="sampleStudy.studyNumber != null and sampleStudy.studyNumber != ''">
                and id = #{sampleStudy.studyNumber}
            </if>
            <if test="sampleStudy.deviceName != null and sampleStudy.deviceName != ''">
                and device_name = #{sampleStudy.deviceName}
            </if>
            <if test="sampleStudy.deviceType != null and sampleStudy.deviceType != ''">
                and device_type = #{sampleStudy.deviceType}
            </if>
            <if test="sampleStudy.patientNumber != null and sampleStudy.patientNumber != ''">
                and (out_patient_id = #{sampleStudy.patientNumber}
                or in_patient_id = #{sampleStudy.patientNumber})
            </if>
            <if test="sampleStudy.patientType != null and sampleStudy.patientType != ''">
                and patient_type = #{sampleStudy.patientType}
            </if>
            <if test="sampleStudy.studyItemName != null and sampleStudy.studyItemName != ''">
                and study_item = #{sampleStudy.studyItemName}
            </if>
            <if test="sampleStudy.artificers != null and sampleStudy.artificers.size > 0">
                and artificer in
                <foreach collection="sampleStudy.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
            <if test="sampleStudy.reportDoctors != null and sampleStudy.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="sampleStudy.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
            <if test="sampleStudy.checkDoctors != null and sampleStudy.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="sampleStudy.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
        ) si
        where row_num &lt;= #{sampleStudy.sampleSize}
        order by device_name
    </select>

    <select id="getReportWorkload" parameterType="com.jusha.rimex.docking.beans.req.QueryReportWorkload"
            resultMap="com.jusha.rimex.docking.db.ReportWorkloadBeanMap">
        select <include refid="com.jusha.rimex.docking.db.ReportWorkloadBeanMap_Column_List"/>
        from study_info
        <where>
            1=1
            <if test="queryReportWorkload.studyStartTime != null and queryReportWorkload.studyEndTime != null">
                and study_time &gt;= #{queryReportWorkload.studyStartTime}
                and study_time &lt;= #{queryReportWorkload.studyEndTime}
            </if>
            <if test="queryReportWorkload.reportDoctors != null and queryReportWorkload.reportDoctors.size > 0">
                and report_doctor in
                <foreach collection="queryReportWorkload.reportDoctors" item="reportDoctor" open="(" close=")" separator=",">
                    #{reportDoctor}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCheckWorkload" parameterType="com.jusha.rimex.docking.beans.req.QueryCheckWorkload"
            resultMap="com.jusha.rimex.docking.db.CheckWorkloadBeanMap">
        select <include refid="com.jusha.rimex.docking.db.CheckWorkloadBeanMap_Column_List"/>
        from study_info
        <where>
            1=1
            <if test="queryCheckWorkload.studyStartTime != null and queryCheckWorkload.studyEndTime != null">
                and study_time &gt;= #{queryCheckWorkload.studyStartTime}
                and study_time &lt;= #{queryCheckWorkload.studyEndTime}
            </if>
            <if test="queryCheckWorkload.checkDoctors != null and queryCheckWorkload.checkDoctors.size > 0">
                and check_doctor in
                <foreach collection="queryCheckWorkload.checkDoctors" item="checkDoctor" open="(" close=")" separator=",">
                    #{checkDoctor}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getArtificerWorkload" parameterType="com.jusha.rimex.docking.beans.req.QueryArtificerWorkload"
            resultMap="com.jusha.rimex.docking.db.ArtificerWorkloadBeanMap">
        select <include refid="com.jusha.rimex.docking.db.ArtificerWorkloadBeanMap_Column_List"/>
        from study_info
        <where>
            1=1
            <if test="queryArtificerWorkload.studyStartTime != null and queryArtificerWorkload.studyEndTime != null">
                and study_time &gt;= #{queryArtificerWorkload.studyStartTime}
                and study_time &lt;= #{queryArtificerWorkload.studyEndTime}
            </if>
            <if test="queryArtificerWorkload.artificers != null and queryArtificerWorkload.artificers.size > 0">
                and artificer in
                <foreach collection="queryArtificerWorkload.artificers" item="artificer" open="(" close=")" separator=",">
                    #{artificer}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getWorkloadAll" parameterType="com.jusha.rimex.docking.beans.req.QueryWorkloadAll"
            resultType="String">
        select device_type
        from study_info
        <where>
            1=1
            <if test="queryWorkloadAll.studyStartTime != null and queryWorkloadAll.studyEndTime != null">
                and study_time &gt;= #{queryWorkloadAll.studyStartTime}
                and study_time &lt;= #{queryWorkloadAll.studyEndTime}
            </if>
        </where>
    </select>

</mapper>