<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.rimex.docking.db">

    
    <resultMap id="StudyInfoStandardMap" type="com.jusha.rimex.docking.beans.resp.StudyInfoReturn">
        <id column="id" property="studyNo"/>
        <result column="study_uid" property="studyUid"/>
        <result column="access_number" property="accessNumber"/>
        <result column="out_patient_id" property="outPatientNo"/>
        <result column="in_patient_id" property="inPatientNo"/>
        <result column="visit_date" property="visitDate"/>
        <result column="patient_type" property="patientType"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_age" property="patientAge"/>
        <result column="patient_birth" property="patientBirthDate"/>
        <result column="patient_sex" property="patientSex"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="study_item_name" property="studyItemName"/>
        <result column="part_name" property="partName"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="apply_doctor" property="applyDoctor"/>
        <result column="apply_department" property="applyDepartment"/>
        <result column="report_describe" property="reportDescribe"/>
        <result column="report_diagnose" property="reportDiagnose"/>
        <result column="study_time" property="studyTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="report_doctor" property="reporter"/>
        <result column="report_time" property="reportTime"/>
        <result column="check_doctor" property="checker"/>
        <result column="check_time" property="checkTime"/>
        <result column="artificer" property="artificer"/>
        <result column="physical_sign" property="physicalSign"/>
        <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        <result column="self_reported_symptom" property="selfReportedSymptom"/>
        <result column="history_disease" property="medicalHistory"/>
    </resultMap>

    <resultMap id="AccReturnMap" type="com.jusha.rimex.docking.beans.resp.AccBean">
        <id column="study_id" property="studyId"/>
        <result column="id" property="acc"/>
    </resultMap>

    <sql id="AccReturnMap_Column_List">
        study_id, id
    </sql>

    <resultMap id="ImagePathReturnMap" type="com.jusha.rimex.docking.beans.ImagePathReturn">
        <id column="st_id" property="examNumber"/>
<!--        <result column="st_device_type" property="examName"/>-->
<!--        <result column="st_study_time" property="studydate"/>-->
<!--        <result column="se_series_id" property="seriesId"/>-->
<!--        <result column="im_image_id" property="imageId"/>-->
        <result column="im_path" property="path"/>
    </resultMap>

    <resultMap id="QcStudyInfoReturnMap" type="com.jusha.rimex.docking.beans.QcStudyInfoReturn">
        <id column="id" property="studyNumber"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_type" property="patientType"/>
        <result column="out_patient_id" property="outPatientNumber"/>
        <result column="in_patient_id" property="inPatientNumber"/>
        <result column="part_name" property="studyPart"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="study_time" property="studyTime"/>
        <result column="report_doctor" property="reportDoctor"/>
        <result column="check_doctor" property="checkDoctor"/>
        <result column="artificer" property="artificer"/>
    </resultMap>
    
    <resultMap id="StudyDetailsReturnMap" type="com.jusha.rimex.docking.beans.resp.StudyDetailsReturn">
        <id column="id" property="studyNumber"/>
        <result column="study_item_name" property="studyItem"/>
        <result column="device_type" property="deviceType"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="apply_time" property="applyTime"/>
        <result column="apply_doctor" property="applyDoctor"/>
        <result column="apply_department" property="applyDepartment"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_sex" property="patientSex"/>
        <result column="patient_age" property="patientAge"/>
        <result column="out_patient_id" property="outPatientNumber"/>
        <result column="in_patient_id" property="inPatientNumber"/>
        <result column="sick_bed" property="sickbed"/>
        <result column="sick_room" property="sickroom"/>
        <result column="history_disease" property="historyDisease"/>
        <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        <result column="report_describe" property="reportDescribe"/>
        <result column="report_diagnose" property="reportDiagnose"/>
        <result column="report_doctor" property="reportDoctor"/>
        <result column="check_doctor" property="checkDoctor"/>
        <result column="artificer" property="artificer"/>
        <result column="study_time" property="studyTime"/>
        <result column="report_time" property="reportTime"/>
        <result column="check_time" property="checkTime"/>
    </resultMap>

    <resultMap id="AddQcStudyMap" type="com.jusha.rimex.docking.beans.req.AddQcStudy">
        <id column="id" property="studyNumber"/>
        <result column="study_item_name" property="studyItem"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="apply_time" property="applyTime"/>
        <result column="apply_doctor" property="applyDoctor"/>
        <result column="apply_department" property="applyDepartment"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_sex" property="patientSex"/>
        <result column="patient_age" property="patientAge"/>
        <result column="patient_type" property="patientType"/>
        <result column="out_patient_id" property="outPatientNumber"/>
        <result column="in_patient_id" property="inPatientNumber"/>
        <result column="sick_bed" property="sickBed"/>
        <result column="sick_room" property="sickRoom"/>
        <result column="history_disease" property="historyDisease"/>
        <result column="clinical_diagnosis" property="clinicalDiagnose"/>
        <result column="self_reported_symptom" property="selfReportedSymptom"/>
        <result column="physical_sign" property="physicalSign"/>
        <result column="study_time" property="studyTime"/>
        <result column="part_name" property="studyPart"/>
        <result column="artificer" property="artificer"/>
        <result column="report_doctor" property="reportDoctor"/>
        <result column="report_time" property="reportTime"/>
        <result column="check_doctor" property="checkDoctor"/>
        <result column="check_time" property="checkTime"/>
        <result column="report_describe" property="reportDescribe"/>
        <result column="report_diagnose" property="reportDiagnose"/>
    </resultMap>
    
    <resultMap id="ReportWorkloadBeanMap" type="com.jusha.rimex.docking.beans.resp.ReportWorkloadBean">
        <id column="id" property="studyNumber"/>
        <result column="report_doctor" property="reportDoctor"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>

    <resultMap id="CheckWorkloadBeanMap" type="com.jusha.rimex.docking.beans.resp.CheckWorkloadBean">
        <id column="id" property="studyNumber"/>
        <result column="check_doctor" property="checkDoctor"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>

    <resultMap id="ArtificerWorkloadBeanMap" type="com.jusha.rimex.docking.beans.resp.ArtificerWorkloadBean">
        <id column="id" property="studyNumber"/>
        <result column="artificer" property="artificer"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>

    <sql id="ArtificerWorkloadBeanMap_Column_List">
        id, artificer, device_type
    </sql>

    <sql id="ReportWorkloadBeanMap_Column_List">
        id, report_doctor, device_type
    </sql>
    
    <sql id="CheckWorkloadBeanMap_Column_List">
        id, check_doctor, device_type
    </sql>

    <sql id="AddQcStudyMap_Column_List">
        id, study_item_name, device_type, device_name, apply_number, apply_time, apply_doctor, apply_department, patient_name,
        patient_sex, patient_age, patient_type, out_patient_id, in_patient_id, sick_bed, sick_room, history_disease, clinical_diagnosis,
        self_reported_symptom, physical_sign, study_time, part_name, artificer, report_doctor, report_time, check_doctor, check_time, report_describe, report_diagnose
    </sql>




    <sql id="StudyDetailsReturnMap_Column_List">
        id, study_item_name, device_type, device_name, apply_number, apply_time, apply_doctor, apply_department, patient_name, patient_sex, patient_age, out_patient_id,
        in_patient_id, sick_bed, sick_room, history_disease, clinical_diagnosis, report_describe, report_diagnose, report_doctor,
        check_doctor, artificer, study_time, report_time, check_time
    </sql>

    <sql id="QcStudyInfoReturnMap_Column_List">
        id, patient_name, patient_type, out_patient_id, in_patient_id, part_name, device_type, device_name,
        study_time, report_doctor, check_doctor, artificer
    </sql>
    


    <sql id="ImagePathReturnMap_Column_List">
        st.id as st_id,
<!--        st.device_type as st_device_type, st.study_time as st_study_time,-->
<!--        se.series_id as se_series_id, im.image_id as im_image_id, -->
        im.path as im_path
    </sql>





<!--    <sql id="RmGroupMap_Column_List">-->
<!--        gp.id as gp_id, gp.name as gp_name, gp.description as gp_description-->
<!--    </sql>-->
</mapper>