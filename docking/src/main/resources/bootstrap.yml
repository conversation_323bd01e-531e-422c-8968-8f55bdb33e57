##需要修改的配置
#服务名和端口
SERVER_PORT: 9089
SERVER_NAME: docking
#nacos
NACOS_ADDR: 127.0.0.1:8848
NACOS_NAMESPACE: pacs-test   #命名空间Id,为空表示取默认命名空间(public)


server:
  port: ${SERVER_PORT}
  servlet:
    context-path: /${SERVER_NAME}
  undertow:
    threads:
      io: 8                 #IO线程数
      worker: 512            #阻塞任务线程池
    buffer-size: 1024
    direct-buffers: true
    max-parameters: 2000

spring:
  application:
    name: ${SERVER_NAME}
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_ADDR}
        namespace:  ${NACOS_NAMESPACE}
        group: DEFAULT_GROUP
        name: ${SERVER_NAME}
        file-extension: yml
        username: nacos
        password: <PERSON><PERSON>@1996
      discovery:
        server-addr: ${NACOS_ADDR}
        namespace:  ${NACOS_NAMESPACE}
        username: nacos
        password: <PERSON><PERSON>@1996

feign:
  client:
    config:
      default:
        connectTimeout: 2000  #连接超时
        readTimeout: 30000    #读超时
  httpclient:
    enabled: true