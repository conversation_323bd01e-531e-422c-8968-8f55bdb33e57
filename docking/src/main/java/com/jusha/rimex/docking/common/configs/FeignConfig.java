package com.jusha.rimex.docking.common.configs;

import com.alibaba.nacos.api.naming.NamingService;
import feign.RequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Objects;

/**
 * feign配置
 */
@Configuration
public class FeignConfig {

    /**
     * NamingService
     */
    private static NamingService namingService;


    /**
     * feign拦截器：添加Header和Cookie
     */
    @Bean("requestInterceptor")
    public RequestInterceptor requestInterceptor() {
        // 创建拦截器
        return template -> {
            // 使用RequestContextHolder拿到原生请求的请求头信息（下文环境保持器）
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                // 如果使用线程池进行远程调用，则request是空的（因为RequestContextHolder.getRequestAttributes()是从threadlocal里拿的值）
                HttpServletRequest oldRequest = requestAttributes.getRequest();
                if (Objects.nonNull(oldRequest)) {
                    //同步老请求里的cookie
                    String oldCookie = oldRequest.getHeader("Cookie");
                    if(StringUtils.isNotBlank(oldCookie)){
                        template.header("Cookie", oldCookie);
                    }
                    //同步老请求里的header
                    Enumeration<String> oldHeaderNames = oldRequest.getHeaderNames();
                    if (null != oldHeaderNames) {
                        while (oldHeaderNames.hasMoreElements()) {
                            String headerName = oldHeaderNames.nextElement();
                            if(template.headers().keySet().contains(headerName)) {
                                continue;
                            }
                            String headerValue = oldRequest.getHeader(headerName);
                            template.header(headerName, headerValue);
                        }
                    }
                }
            }
        };
    }

}

