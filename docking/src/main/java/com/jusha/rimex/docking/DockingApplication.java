package com.jusha.rimex.docking;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
//import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.StringUtils;


@SpringBootApplication
@EnableFeignClients(basePackages = "com.jusha.rimex.docking.feign")
@EnableCaching
@EnableTransactionManagement
@MapperScan({"com.jusha.rimex.docking.dao"})
@EnableScheduling
@EnableAsync
public class DockingApplication {

	private static ConfigurableApplicationContext context;

	public static void main(String[] args){
		SpringApplicationBuilder springApplicationBuilder = new SpringApplicationBuilder(DockingApplication.class)
				.beanNameGenerator(new CustomGenerator());
		context = springApplicationBuilder.run(args);
	}


	/**
	 * 自定义Spring的BeanName生成器
	 *
	 */
	public static class CustomGenerator extends AnnotationBeanNameGenerator {

		@Override
		public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {
			String fullClassName = definition.getBeanClassName();
			if(fullClassName.contains("com.jusha.")){   //项目代码
				//如果自己配了beanName，则用配的
				if (definition instanceof AnnotatedBeanDefinition){
					String beanName = super.determineBeanNameFromAnnotation((AnnotatedBeanDefinition)definition);
					if(StringUtils.hasText(beanName)) return beanName;
				}
				//否则用全名
				return fullClassName;
			}
			else {   //依赖框架jar里代码
				return super.generateBeanName(definition, registry);
			}
		}
	}

	//主动停止服务
	public static void exit() {
		SpringApplication.exit(context, () -> 0);
	}

}