package com.jusha.rimex.docking.beans.dto.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @title StudyReportResp
 * @description
 * @date 2025/2/26
 */

@ApiModel
@Data
@Accessors(chain = true)
public class StudyResp {

    @ApiModelProperty("检查id")
    private String studyId;

    @ApiModelProperty("申请单号")
    private String applyNumber;

    @ApiModelProperty(value = "申请科室")
    private String applyDepartment;

    @ApiModelProperty(value = "申请医生")
    private String applyDoctor;

    @ApiModelProperty(value = "技师")
    private String artificer;

    @ApiModelProperty(value = "就诊时间")
    private String visitDate;

    @ApiModelProperty(value = "登记时间")
    private String registerTime;

    @ApiModelProperty("检查号")
    private String studyNo;

    @ApiModelProperty("影像Uid")
    private String studyUid;

    @ApiModelProperty("影像号")
    private String accessNumber;

    @ApiModelProperty("患者id")
    private String patientId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者性别")
    private String patientSex;

    @ApiModelProperty("患者出生日期")
    private String patientBirthDate;

    @ApiModelProperty("患者号")
    private String patientNo;

    @ApiModelProperty("患者年龄")
    private String patientAge;

    @ApiModelProperty("患者类型,1门诊2住院")
    private String patientType;

    @ApiModelProperty("检查时间")
    private String studyTime;

    @ApiModelProperty("检查设备")
    private String deviceType;

    @ApiModelProperty("检查项目")
    private String studyItemName;

    @ApiModelProperty("检查部位")
    private String partName;

    @ApiModelProperty("检查目的")
    private String studyPurpose;

    @ApiModelProperty("体征")
    private String physicalSign;

    @ApiModelProperty("患者主诉")
    private String selfReportedSymptom;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty("报告医生")
    private String reporter;

    @ApiModelProperty("报告时间")
    private String reportTime;

    @ApiModelProperty("审核医生")
    private String checker;

    @ApiModelProperty("审核时间")
    private String checkTime;

    @ApiModelProperty("报告id")
    private String reportId;

    @ApiModelProperty("影像所见")
    private String reportDescribe;

    @ApiModelProperty("诊断意见")
    private String reportDiagnose;

    @ApiModelProperty("是否阳性 0-未知（默认） 1-阴性 2-阳性 3-重大阳性")
    private String isPostive;

    @ApiModelProperty(value = "门诊号")
    @TableField("out_patient_no")
    private String outPatientNo;

    @ApiModelProperty(value = "住院号")
    @TableField("in_patient_no")
    private String inPatientNo;
}
