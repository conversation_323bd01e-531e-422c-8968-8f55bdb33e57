package com.jusha.rimex.docking.feign;

import com.jusha.rimex.docking.beans.AddRISCaseReq;
import com.jusha.rimex.docking.beans.AddRISCaseResp;
import com.jusha.rimex.docking.common.beans.ResultBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(name = "caselibrary", path = "/caselibrary")
public interface CaseLibraryClient {


    /**
     * 生成随访病例
     *
     * @param addRisCaseReq
     * @return
     */
    @RequestMapping(value = "/case/addCaseFromRIS", method = RequestMethod.POST)
    public ResultBean<AddRISCaseResp> addCaseFromRis(@RequestBody AddRISCaseReq addRisCaseReq);

}
