package com.jusha.rimex.docking.services;

import com.jusha.rimex.docking.beans.dto.req.StudyReq;
import com.jusha.rimex.docking.beans.dto.resp.StudyResp;

import java.util.List;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
public interface StudyService {

    /**
     * 模拟对接数据
     *
     * @param studyReq
     */
    public List<StudyResp> queryStudyList(StudyReq studyReq) throws Exception;


    /**
     * 手动创建病例
     */
    void addCaseDemo();
}
