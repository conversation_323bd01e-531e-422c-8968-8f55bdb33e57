package com.jusha.rimex.docking.controller;

import com.jusha.rimex.docking.beans.dto.req.StudyReq;
import com.jusha.rimex.docking.beans.dto.resp.StudyResp;
import com.jusha.rimex.docking.common.beans.ResultBean;
import com.jusha.rimex.docking.services.StudyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @title StudyController
 * @description
 * @date 2025/7/12
 */

@RestController
@Api(tags = "实际RIS数据对接，使用HTTP示例")
@RequiredArgsConstructor
public class StudyController {

    private final StudyService studyService;

    @ApiOperation("添加检查、申请单信息")
    @PostMapping("/forbid/study/list")
    public ResultBean<List<StudyResp>> queryStudyList(@RequestBody StudyReq studyReq) throws Exception{
        List<StudyResp> studyRespList = studyService.queryStudyList(studyReq);
        return ResultBean.success(studyRespList);
    }

    @ApiOperation("添加检查、申请单信息")
    @PostMapping("/addCase/demo")
    public void addCaseDemo() throws Exception{
        studyService.addCaseDemo();
    }

}
