package com.jusha.rimex.docking.services.impls;

import com.baomidou.mybatisplus.extension.api.R;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.jusha.rimex.docking.beans.AddRISCaseReq;
import com.jusha.rimex.docking.beans.AddRISCaseResp;
import com.jusha.rimex.docking.beans.RISStudy;
import com.jusha.rimex.docking.beans.dto.req.StudyReq;
import com.jusha.rimex.docking.beans.dto.resp.StudyApplyResp;
import com.jusha.rimex.docking.beans.dto.resp.StudyReportResp;
import com.jusha.rimex.docking.beans.dto.resp.StudyResp;
import com.jusha.rimex.docking.beans.resp.StudyInfoReturn;
import com.jusha.rimex.docking.common.beans.ResultBean;
import com.jusha.rimex.docking.common.defines.Constant;
import com.jusha.rimex.docking.common.exception.BusinessException;
import com.jusha.rimex.docking.common.utils.HttpClientUtil;
import com.jusha.rimex.docking.common.utils.LoginUtil;
import com.jusha.rimex.docking.dao.DockingDao;
import com.jusha.rimex.docking.dao.DockingDaoService;
import com.jusha.rimex.docking.feign.CaseLibraryClient;
import com.jusha.rimex.docking.feign.RisBaseClient;
import com.jusha.rimex.docking.feign.StudyClient;
import com.jusha.rimex.docking.services.StudyService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
@Service
@RequiredArgsConstructor
public class StudyImplService implements StudyService {

    private static final Logger log = LoggerFactory.getLogger(StudyImplService.class);

    @Autowired
    private StudyClient studyClient;

    @Autowired
    private RisBaseClient risBaseClient;

    @Value("${ris.studyList.url:}")
    private String studyListUrl;

    @Value("${ris.reportList.url:}")
    private String reportListUrl;

    @Value("${PLAT_ID:}")
    private String PLAT_ID;

    @Autowired
    DockingDao dockingDao;

    @Autowired
    private CaseLibraryClient caseLibraryClient;

    @Override
    public void addCaseDemo() {
        List<StudyInfoReturn> studyInfoReturns = dockingDao.getAllStudyInfo();
        int i = 0;
        int j = 0;
        for(StudyInfoReturn studyInfoReturn : studyInfoReturns){
            studyInfoReturn.setAetId(1);
            RISStudy risStudy = new RISStudy();
            BeanUtils.copyProperties(studyInfoReturn,risStudy);
            List<RISStudy> risStudyList = new ArrayList<>();
            risStudyList.add(risStudy);

            AddRISCaseReq addRISCaseReq = new AddRISCaseReq();
            addRISCaseReq.setTargetLibrary(2);
            addRISCaseReq.setTargetId(-1L);
            addRISCaseReq.setRisStudyList(risStudyList);
            ResultBean<AddRISCaseResp> addRISCaseRespResultBean = caseLibraryClient.addCaseFromRis(addRISCaseReq);
            if(addRISCaseRespResultBean.getState()){
                i++;
                log.info(studyInfoReturn.getStudyNo()+"导入成功了"+i);
            }else {
                j++;
                log.info(studyInfoReturn.getStudyNo()+"导入失败了"+j);
            }
        }
    }

    /**
     * 模拟对接数据
     *
     * @param studyReq
     */
    public List<StudyResp> queryStudyList(StudyReq studyReq) throws Exception{
        studyReq.setOutInPatientNo(studyReq.getKeyword());
        Map<String, String> header = new HashMap<>();
        header.put("platId",PLAT_ID);
        header.put("authorization", LoginUtil.getRequestHeader(Constant.TOKEN_KEY));
        if(studyListUrl == null || studyListUrl.isEmpty()){
            return new ArrayList<>();
        }
        List<StudyResp> studyRespList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(studyReq);
        String studyText = HttpClientUtil.doPostDefaultSecurity(studyListUrl,json,header);
        log.info("入参===================="+json);
        log.info("studyText================="+studyText);
        ResultBean<PageInfo<StudyApplyResp>> studyApplyResultBean = objectMapper.readValue(studyText, new TypeReference<ResultBean<PageInfo<StudyApplyResp>>>(){});
        if(studyApplyResultBean.getState()!=null && studyApplyResultBean.getState()){
            List<StudyApplyResp> studyApplyRespList = studyApplyResultBean.getData().getList();

            for(StudyApplyResp studyApplyResp : studyApplyRespList){
                StudyResp studyResp = new StudyResp();
                studyResp.setStudyId(studyApplyResp.getStudyId()).setApplyNumber(studyApplyResp.getApplyNo())
                        .setStudyNo(studyApplyResp.getStudyNo()).setStudyUid(studyApplyResp.getStudyUid())
                        .setAccessNumber(studyApplyResp.getAccessNumber()).setPatientId(studyApplyResp.getPatientId())
                        .setPatientName(studyApplyResp.getPatientName())
                        .setPatientNo(studyApplyResp.getPatientNo()).setPatientAge(studyApplyResp.getPatientAge())
                        .setPatientType(studyApplyResp.getPatientType()).setStudyTime(studyApplyResp.getStudyTime())
                        .setDeviceType(studyApplyResp.getStudyType())
                        .setStudyItemName(studyApplyResp.getStudyItems()).setPartName(studyApplyResp.getStudyParts())
                        .setOutPatientNo(studyApplyResp.getOutInPatientNo()).setInPatientNo(studyApplyResp.getOutInPatientNo());

                if("0".equals(studyApplyResp.getPatientSex()) || "M".equals(studyApplyResp.getPatientSex())){
                    studyResp.setPatientSex("男");
                }else if("1".equals(studyApplyResp.getPatientSex()) || "F".equals(studyApplyResp.getPatientSex())){
                    studyResp.setPatientSex("女");
                }else{
                    studyResp.setPatientSex("未知");
                }

                String reportText = HttpClientUtil.doGetDefaultSecurity(reportListUrl+"234633307525189",header);
//                log.info("reportText================="+reportText);
                if(reportText!=null){
                    ResultBean<StudyReportResp> studyReportResultBean = objectMapper.readValue(reportText, new TypeReference<ResultBean<StudyReportResp>>(){});
                    if(studyReportResultBean.getState()!=null && studyReportResultBean.getState()){
                        StudyReportResp studyReportResp = studyReportResultBean.getData();
                        studyResp.setStudyPurpose(studyReportResp.getStudyPurpose()).setPhysicalSign(studyReportResp.getPhysicalSign())
                                .setApplyDepartment(studyReportResp.getApplyDepartment())
                                .setApplyDoctor("").setArtificer("").setVisitDate("").setRegisterTime("")
                                .setSelfReportedSymptom(studyReportResp.getSelfComplaints()).setMedicalHistory(studyReportResp.getHistoryDisease())
                                .setMedicalHistory(studyReportResp.getHistoryDisease()).setClinicalDiagnosis(studyReportResp.getClinicalDiagnosis())
                                .setReporter(studyReportResp.getReportDoctor()).setReportTime(studyReportResp.getReportTime())
                                .setChecker(studyReportResp.getCheckDoctor()).setCheckTime(studyReportResp.getCheckTime())
                                .setReportId(studyReportResp.getReportId()).setReportDescribe(studyReportResp.getReportDescribe())
                                .setReportDiagnose(studyReportResp.getReportDiagnosis()).setIsPostive(studyReportResp.getPositiveStatus());
                    }
                }
                studyRespList.add(studyResp);
            }
           return studyRespList;
        }
        throw new BusinessException(studyApplyResultBean.getMessage());
    }

}
