package com.jusha.rimex.docking.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jusha.rimex.docking.beans.ImagePathReturn;
import com.jusha.rimex.docking.beans.QcStudyInfoReturn;
import com.jusha.rimex.docking.beans.req.*;
import com.jusha.rimex.docking.beans.resp.StudyDetailsReturn;
import com.jusha.rimex.docking.beans.resp.StudyInfoReturn;
import com.jusha.rimex.docking.beans.resp.AccBean;
import com.jusha.rimex.docking.beans.resp.ArtificerWorkloadBean;
import com.jusha.rimex.docking.beans.resp.CheckWorkloadBean;
import com.jusha.rimex.docking.beans.resp.ReportWorkloadBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DockingDao extends BaseMapper<Object> {

    /**
     * 查询所有检查信息
     *
     * @return
     */
    List<StudyInfoReturn> getAllStudyInfo();

    /**
     * 根据唯一检查号获取检查啊信息
     *
     * @param reqid
     * @return
     */
    List<StudyInfoReturn> getStudyInfoStandard(@Param("reqid") String reqid);

    /**
     * 查询acc
     *
     * @param studyId
     * @return
     */
    AccBean getAccByStudyId(@Param("studyId") String studyId);
    
    /**
     * 根据检查号获取检查信息
     *
     * @param studyId
     * @return
     */
    StudyInfoReturn getStudyInfoByStudyId(@Param("studyId") String studyId);
    
    /**
     * 根据检查号获取影像文件路径(邢台市第一医院)
     *
     * @param reqid
     * @return
     */
    List<ImagePathReturn> getImagePathXtdiyy(@Param("reqid") String reqid);

    ImagePathReturn getStudyUId(@Param("studyId") String studyId);

    /**
     * 筛选检查列表
     *
     * @param page
     * @param studySearchPage
     * @return
     */
    List<QcStudyInfoReturn> searchStudyPage(@Param("page") Page page, @Param("studySearchPage") StudySearchPage studySearchPage);

    /**
     * 查询检查详细信息
     *
     * @param studyNumber
     * @return
     */
    StudyDetailsReturn getStudyDetails(@Param("studyNumber") String studyNumber);

    /**
     * 查询检查详细信息
     *
     * @param studyNumber
     * @return
     */
    AddQcStudy getStudyForbid(@Param("studyNumber") String studyNumber);

    /**
     *
     * @param
     * @return
     */
    List<QcStudyInfoReturn> getStudySample();

    /**
     * 随机抽取固定数量
     *
     * @param
     * @return
     */
    List<QcStudyInfoReturn> getStudySampleRand(@Param("sampleStudy") SampleStudy sampleStudy);

    /**
     * 每审核医生抽样
     *
     * @param sampleStudy
     * @return
     */
    List<QcStudyInfoReturn> getStudySampleByCheckDoctor(@Param("sampleStudy") SampleStudy sampleStudy);

    /**
     * 每报告医生抽样
     *
     * @param sampleStudy
     * @return
     */
    List<QcStudyInfoReturn> getStudySampleByReportDoctor(@Param("sampleStudy") SampleStudy sampleStudy);

    /**
     * 每技师抽样
     *
     * @param sampleStudy
     * @return
     */
    List<QcStudyInfoReturn> getStudySampleByArtificer(@Param("sampleStudy") SampleStudy sampleStudy);

    /**
     * 每检查设备抽样
     *
     * @param sampleStudy
     * @return
     */
    List<QcStudyInfoReturn> getStudySampleByDeviceName(@Param("sampleStudy") SampleStudy sampleStudy);

    /**
     * 查询报告工作量
     *
     * @param queryReportWorkload
     * @return
     */
    List<ReportWorkloadBean> getReportWorkload(@Param("queryReportWorkload") QueryReportWorkload queryReportWorkload);

    /**
     * 查询审核工作量
     *
     * @param queryCheckWorkload
     * @return
     */
    List<CheckWorkloadBean> getCheckWorkload(@Param("queryCheckWorkload") QueryCheckWorkload queryCheckWorkload);

    /**
     * 查询技师工作量
     *
     * @param queryArtificerWorkload
     * @return
     */
    List<ArtificerWorkloadBean> getArtificerWorkload(@Param("queryArtificerWorkload") QueryArtificerWorkload queryArtificerWorkload);

    /**
     * 查询科室总工作量
     *
     * @param queryWorkloadAll
     * @return
     */
    List<String> getWorkloadAll(@Param("queryWorkloadAll") QueryWorkloadAll queryWorkloadAll);





}
