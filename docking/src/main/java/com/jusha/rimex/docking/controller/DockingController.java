package com.jusha.rimex.docking.controller;



import com.jusha.rimex.docking.beans.req.*;
import com.jusha.rimex.docking.beans.resp.StudyInfoReturn;
import com.jusha.rimex.docking.beans.resp.WorkloadReturn;
import com.jusha.rimex.docking.common.beans.BaseController;
import com.jusha.rimex.docking.common.beans.ResultBean;
import com.jusha.rimex.docking.common.defines.ServiceDefine;
import com.jusha.rimex.docking.services.DockingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @title: DockingController
 * @description 对接服务控制器
 * @date 2022/2/14
 */

@Api("对接服务控制器")
@RestController
public class DockingController extends BaseController {

    @Autowired
    DockingService dockingService;

    /**
     * 根据唯一检查号获取检查啊信息(邢台市第一医院)
     *
     * @param req
     * @return
     */
    @ApiOperation("根据检查号获取检查信息")
    @ApiImplicitParam(name = "req", value = "检查号", required = true)
    @RequestMapping(value = ServiceDefine.VERIFY + "/get/studyInfo", method =RequestMethod.GET)
    public ResultBean<List<StudyInfoReturn>> getStudyInfo(@RequestParam("keyword") @NotNull String keyword){
        List<StudyInfoReturn> studyInfoReturns = dockingService.getStudyInfo(keyword);
        return ResultBean.success(studyInfoReturns);
    }

    /**
     * 根据检查号查询Acc
     *
     * @param studyId
     * @return
     */
    @ApiOperation("根据检查号查询Acc")
    @RequestMapping(value = ServiceDefine.OPEN + "/get/acc/by/study/id", method = RequestMethod.GET)
    public ResultBean getAccByStudyId(@RequestParam("studyId") @NotNull  String studyId) {
        return dockingService.getAccByStudyId(studyId);
    }

    /**
     * 根据检查号获取检查信息
     *
     * @param studyId
     * @return
     */
    @ApiOperation("根据检查号获取检查信息")
    @RequestMapping(value = ServiceDefine.FORBID + "/get/studyInfo/by/id", method = RequestMethod.GET)
    public ResultBean<StudyInfoReturn> getStudyInfoById(@RequestParam("studyId") @NotNull String studyId){
        StudyInfoReturn studyInfoByStudyId = dockingService.getStudyInfoByStudyId(studyId);
        return ResultBean.success(studyInfoByStudyId);
    }

    /**
     * 根据检查号获取影像文件路径
     *
     * @param reqid
     * @return
     */
    @ApiOperation("根据唯一检查号获取影像路径信息(邢台市第一医院)")
    @ApiImplicitParam(name = "reqid", value = "检查唯一号", required = true)
    @RequestMapping(value = ServiceDefine.VERIFY + "/get/imagepath/xtdiyy", method = RequestMethod.GET)
    public ResultBean getImagePathXtdiyy(@RequestParam("reqid") @NotNull String reqid){
        return dockingService.getImagePathXtdiyy(reqid);
    }

    /**
     * 获取studyUId
     *
     *
     */
    @ApiOperation("获取studyUId")
    @ApiImplicitParam(name = "studyId", value = "检查唯一号", required = true)
    @RequestMapping(value = ServiceDefine.VERIFY + "/get/studyuid", method = RequestMethod.GET)
    public ResultBean getStudyUId(@RequestParam("studyId") @NotNull String studyId){
        return dockingService.getStudyUId(studyId);
    }

    /**
     * 筛选
     *
     * @param studySearchPage
     * @return
     */
    @ApiOperation("筛选")
    @RequestMapping(value = ServiceDefine.VERIFY + "/study/search/page", method = RequestMethod.POST)
    public ResultBean searchStudyPage(@RequestBody @Validated StudySearchPage studySearchPage) {
        return dockingService.searchStudyPage(studySearchPage, getUserId());
    }

    /**
     * 查询检查详细信息
     *
     * @param studyNumber
     * @return
     */
    @ApiOperation("查询检查详细信息（未质控）")
    @RequestMapping(value = ServiceDefine.VERIFY + "/study/details/get/by/studyNumber", method = RequestMethod.GET)
    public ResultBean getStudyDetails(@RequestParam("studyNumber") String studyNumber) {
        return dockingService.getStudyDetails(studyNumber);
    }

    /**
     * 查询检查详细信息（服务内部调用）
     *
     * @param studyNumber
     * @return
     */
    @ApiOperation("查询检查详细信息（服务内部调用）")
    @RequestMapping(value = ServiceDefine.FORBID + "/study/details/get/by/studyNumber/forbid", method = RequestMethod.GET)
    public ResultBean getStudyForbid(@RequestParam("studyNumber") String studyNumber) {
        return dockingService.getStudyForbid(studyNumber);
    }

    /**
     * 抽样
     *
     * @param sampleStudy
     * @return
     */
    @RequestMapping(value = ServiceDefine.VERIFY + "/study/search/sample", method = RequestMethod.POST)
    public ResultBean getStudySample(@RequestBody @Validated SampleStudy sampleStudy) {

        return dockingService.getStudySample(sampleStudy, getUserId());
    }

    /**
     * 查询报告工作量
     * @param queryReportWorkload
     * @return
     */
    @ApiOperation("查询报告工作量")
    @RequestMapping(value = ServiceDefine.VERIFY + "/analysis/report/workload", method = RequestMethod.POST)
    public ResultBean getReportWorkload(@RequestBody @Validated QueryReportWorkload queryReportWorkload) {
        return dockingService.getReportWorkload(queryReportWorkload);
    }

    /**
     * 查询审核工作量
     * @param queryCheckWorkload
     * @return
     */
    @ApiOperation("查询审核工作量")
    @RequestMapping(value = ServiceDefine.VERIFY + "/analysis/check/workload", method = RequestMethod.POST)
    public ResultBean getCheckWorkload(@RequestBody @Validated QueryCheckWorkload queryCheckWorkload) {
        return dockingService.getCheckWorkload(queryCheckWorkload);
    }

    /**
     * 查询技师工作量
     * @param queryArtificerWorkload
     * @return
     */
    @ApiOperation("查询技师工作量")
    @RequestMapping(value = ServiceDefine.VERIFY + "/analysis/artificer/workload", method = RequestMethod.POST)
    public ResultBean getArtificerWorkload(@RequestBody @Validated QueryArtificerWorkload queryArtificerWorkload) {
        return dockingService.getArtificerWorkload(queryArtificerWorkload);
    }

    /**
     * 查询科室总工作量
     *
     * @param queryWorkloadAll
     * @return
     */
    @ApiOperation("查询科室总工作量")
    @RequestMapping(value = ServiceDefine.VERIFY + "/analysis/displayboard/workload/all", method = RequestMethod.POST)
    public ResultBean getWorkloadAll(@RequestBody @Validated QueryWorkloadAll queryWorkloadAll) {
        return dockingService.getWorkloadAll(queryWorkloadAll);
    }

    /**
     * 晨会看板统计工作量
     *
     * @param workloadReq
     * @return
     */
    @ApiOperation("晨会看板统计工作量")
    @RequestMapping(value = ServiceDefine.VERIFY + "/board/workload/report", method = RequestMethod.POST)
    public ResultBean<List<WorkloadReturn>> getReportWorkload(@RequestBody @Validated WorkloadReq workloadReq){
        List<WorkloadReturn> reportWorkload = dockingService.getReportWorkload(workloadReq);
        return ResultBean.success(reportWorkload);
    }




}
