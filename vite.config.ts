import path from 'path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import Inspect from 'vite-plugin-inspect'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { viteMockServe } from 'vite-plugin-mock'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

export default defineConfig(({ command, mode }) => {
  // 检查process.cwd()路径下.env.development.local、.env.development、.env.local、.env这四个环境文件
  loadEnv(mode, process.cwd())
  return {
    base: '/case-library/',
    resolve: {
      alias: {
        '@/': `${path.resolve(__dirname, 'src')}/`,
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/styles/element/index.scss" as *; ',
        },
      },
    },
    plugins: [
      vue(),
      [createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), './src/assets/icons/svg')],
        // 指定symbolId格式
        symbolId: 'icon-[name]',
      })],
      // https://github.com/antfu/unplugin-auto-import
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'vue-i18n',
          'vue/macros',
          '@vueuse/head',
          '@vueuse/core',
        ],
        dts: 'src/auto-imports.d.ts',
        dirs: [
          'src/hooks',
          'src/modules',
          'src/store',
          'src/utils',
        ],
        vueTemplate: true,
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
      }),

      // https://github.com/antfu/unplugin-vue-components
      Components({
        // 自动引入vue组件
        extensions: ['vue'],
        include: [/\.vue$/, /\.vue\?vue/],
        dts: 'src/components.d.ts',
        resolvers: [
          ElementPlusResolver({ importStyle: 'sass' }),
          IconsResolver({ prefix: 'icon' }),
        ],
        dirs: ['src/components', 'src/views/**/components'],
      }),

      // https://github.com/intlify/bundle-tools/tree/main/packages/vite-plugin-vue-i18n
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        include: [path.resolve(__dirname, 'locales/**')],
      }),

      // https://github.com/antfu/vite-plugin-inspect
      Inspect(),
      // https://github.com/antfu/unplugin-icons
      Icons({ autoInstall: true }),

      viteMockServe({
        mockPath: 'mock',
        localEnabled: command === 'serve',
      }),
    ],
    server: {
      proxy: {
        '^/auth-server': {
          target: 'http://**************:9025',
          changeOrigin: true,
          // rewrite: path => path.replace(/^\/auth-server/, ''),
        },
        '^/case-library-api': {
          target: 'http://**************:9020/',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/case-library-api/, ''),
        },
        // '/upload-bucket': {
        //   target: 'http://**************:9000',
        //   changeOrigin: true,
        //   // pathRewrite: {
        //   //   '^/minio': '',
        //   // },
        //   onProxyReq: (proxyReq) => {
        //     proxyReq.setHeader('Host', 'localhost:9000')
        //   },
        // },
      },
    },

    optimizeDeps: {
      include: [
        'vue',
        'vue-i18n',
        'vue-router',
        '@vueuse/core',
        '@vueuse/head',
        '@vueuse/shared',
        'element-plus/es',
        'element-plus/es/components/button/style/css',
        'element-plus/es/components/link/style/css',
        'element-plus/es/components/input/style/css',
        'element-plus/es/components/checkbox/style/css',
        'element-plus/es/components/radio/style/css',
        'element-plus/es/components/switch/style/css',
        'element-plus/es/components/select/style/css',
        'element-plus/es/components/option/style/css',
        'element-plus/es/components/dialog/style/css',
        'element-plus/es/components/checkbox-group/style/index',
        'element-plus/es/components/checkbox/style/index',
        'element-plus/es/components/input-number/style/index',
        'element-plus/es/components/col/style/index',
        'element-plus/es/components/pagination/style/index',
        'element-plus/es/components/tooltip/style/index',
        'element-plus/es/components/select-v2/style/index',
        'element-plus/es/components/divider/style/index',
        'element-plus/es/components/time-select/style/index',
        'element-plus/es/components/drawer/style/index',
        'element-plus/es/components/table/style/index',
        'element-plus/es/components/table-column/style/index',
        'element-plus/es/components/card/style/index',
        'element-plus/es/components/row/style/index',
        'element-plus/es/components/date-picker/style/index',
        'element-plus/es/components/select/style/index',
        'element-plus/es/components/option/style/index',
        'element-plus/es/components/empty/style/index',
        'element-plus/es/components/link/style/index',
        'element-plus/es/components/collapse/style/index',
        'element-plus/es/components/collapse-item/style/index',
        'element-plus/es/components/tag/style/index',
        'element-plus/es/components/rate/style/index',
        'element-plus/es/components/tabs/style/index',
        'element-plus/es/components/tab-pane/style/index',
        'element-plus/es/components/tree/style/index',
        'element-plus/es/components/time-picker/style/index',
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'dayjs',
        'echarts',
      ],
    },
  }
},
)
