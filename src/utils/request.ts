import axios from 'axios'
import { ElMessage } from 'element-plus'
import type { AxiosRequestConfig } from 'axios'

// import { setSysConfig } from './system'
import { ACCESS_TOKEN_KEY } from '@/enums/cacheEnum'
import { Storage } from '@/utils/storage'
import { useUserStore } from '@/store/modules/user'

export interface RequestOptions {
  /** 当前接口权限, 不需要鉴权的接口请忽略， 格式：sys:user:add */
  permCode?: string
  /** 是否直接获取data，而忽略message等 */
  isGetDataDirectly?: boolean
  /** 请求成功是提示信息 */
  successMsg?: string
  /** 请求失败是提示信息 */
  errorMsg?: string
  /** 是否mock数据请求 */
  isMock?: boolean
  // 部分接口需要自定义超时时间
  timeout?: number
  /** 防抖延迟时间(ms)，默认300ms */
  debounceDelay?: number
  /** 是否禁用防抖 */
  disableDebounce?: boolean
  /** 忽略空响应 */
  ignoreEmptyResponse?: boolean
}

const UNKNOWN_ERROR = '未知错误，请重试'
const SERVICE_ERROR = '网络中断，请确认网络连接无误后重试'
// // 是否生产环境
// const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV || '')
// /** 真实请求的路径前缀 */
// const baseApiUrl = IS_PROD ? '/prod-archimedes/' : '/archimedes/'
// /** mock请求路径前缀 */
// const baseMockUrl = '/mock'

const service = axios.create({
  timeout: 6000,
})

// 在请求头里配置token
service.interceptors.request.use(
  async (config) => {
    const token = Storage.get(ACCESS_TOKEN_KEY)
    if (token && config.headers) {
      config.headers.platTag = useUserStore().platTag
      config.headers.Authorization = token
    }

    return config
  },
  (error) => {
    Promise.reject(error)
  },
)

service.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.errorCode && res.errorCode !== 0) {
      if (res.message && res.errorCode !== 401)
        ElMessage.error(res.message || UNKNOWN_ERROR)
      if (res.errorCode === 401)
        useUserStore().logout()
      if (!res.message)
        ElMessage.error(UNKNOWN_ERROR)

      // throw other
      const error = new Error(res.message || UNKNOWN_ERROR) as Error & { code: any }
      error.code = res.code
      return Promise.reject(error)
    }
    else {
      return res
    }
  },
  (error) => {
    // 处理取消错误
    if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
      console.log('Request cancelled by user')
      const cancelError = new Error('Upload cancelled')
      cancelError.name = 'AbortError'
      throw cancelError
    }
    const errMsg = error?.response?.data?.message ?? SERVICE_ERROR
    ElMessage.error(errMsg)
    error.message = errMsg
    return Promise.reject(error)
  },
)

export interface Response<T = any> {
  errorCode: number
  message: string
  data: T
}

export type BaseResponse<T = any> = Promise<Response<T>>
/**
 *
 * @param method - request methods
 * @param url - request url
 * @param data - request data or params
 */
export const request = async <T = any>(
  config: AxiosRequestConfig,
  options: RequestOptions = {},
): Promise<T> => {
  const { successMsg, errorMsg, permCode, isGetDataDirectly = true, timeout, ignoreEmptyResponse = false } = options
  // 如果当前是需要鉴权的接口 并且没有权限的话 则终止请求发起
  if (permCode)
    ElMessage.error('你没有访问该接口的权限，请联系管理员！')

  // 设置超时时间
  if (timeout)
    config.timeout = timeout

  // const fullUrl = baseApiUrl + config.url
  // config.url = baseApiUrl + config.url
  const res = await service.request(config)
  successMsg && ElMessage.success(successMsg)
  errorMsg && ElMessage.error(errorMsg)

  const apiRes = res as any as Response<T>
  if (!apiRes) {
    if (ignoreEmptyResponse) {
      console.warn('Empty response ignored')
      return Promise.resolve({} as T)
    }
    else {
      const error = new Error('Empty response from server') as Error & { code?: any }
      error.code = 500 // 设置一个通用错误码
      return Promise.reject(error)
    }
  }
  return isGetDataDirectly ? res.data : res
}
