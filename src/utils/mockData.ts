
import type { FavoriteCatalogItem } from '@/types/caseLibrary'

export const mockUserTableData = [
  {
    nickName: '张伟',
    userName: 'zhangwei01',
    roles: [{ roleName: '管理员' }],
    createTime: '2024-12-01T08:30:00',
  },
  {
    nickName: '李娜',
    userName: 'lina88',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-01-15T14:22:00',
  },
  {
    nickName: '王强',
    userName: 'wangqiang',
    roles: [{ roleName: '用户' }],
    createTime: '2025-02-05T09:10:00',
  },
  {
    nickName: '赵雷',
    userName: 'zhaolei_admin',
    roles: [{ roleName: '管理员' }, { roleName: '开发者' }],
    createTime: '2025-03-11T17:45:00',
  },
  {
    nickName: '孙媛',
    userName: 'sunyuan23',
    roles: [{ roleName: '用户' }],
    createTime: '2025-04-02T11:05:00',
  },
  {
    nickName: '周杰',
    userName: 'zhoujie',
    roles: [{ roleName: '用户' }, { roleName: '运营' }],
    createTime: '2025-04-25T08:00:00',
  },
  {
    nickName: '吴敏',
    userName: 'wumin09',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-05-09T13:30:00',
  },
  {
    nickName: '郑云',
    userName: 'zhengyun007',
    roles: [{ roleName: '开发者' }],
    createTime: '2025-05-20T19:20:00',
  },
  {
    nickName: '何冰',
    userName: 'hebing',
    roles: [{ roleName: '用户' }],
    createTime: '2025-06-01T07:45:00',
  },
  {
    nickName: '陈晨',
    userName: 'chenchen001',
    roles: [{ roleName: '管理员' }],
    createTime: '2025-06-15T10:10:00',
  },
  {
    nickName: '张伟',
    userName: 'zhangwei01',
    roles: [{ roleName: '管理员' }],
    createTime: '2024-12-01T08:30:00',
  },
  {
    nickName: '李娜',
    userName: 'lina88',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-01-15T14:22:00',
  },
  {
    nickName: '王强',
    userName: 'wangqiang',
    roles: [{ roleName: '用户' }],
    createTime: '2025-02-05T09:10:00',
  },
  {
    nickName: '赵雷',
    userName: 'zhaolei_admin',
    roles: [{ roleName: '管理员' }, { roleName: '开发者' }],
    createTime: '2025-03-11T17:45:00',
  },
  {
    nickName: '孙媛',
    userName: 'sunyuan23',
    roles: [{ roleName: '用户' }],
    createTime: '2025-04-02T11:05:00',
  },
  {
    nickName: '周杰',
    userName: 'zhoujie',
    roles: [{ roleName: '用户' }, { roleName: '运营' }],
    createTime: '2025-04-25T08:00:00',
  },
  {
    nickName: '吴敏',
    userName: 'wumin09',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-05-09T13:30:00',
  },
  {
    nickName: '郑云',
    userName: 'zhengyun007',
    roles: [{ roleName: '开发者' }],
    createTime: '2025-05-20T19:20:00',
  },
  {
    nickName: '何冰',
    userName: 'hebing',
    roles: [{ roleName: '用户' }],
    createTime: '2025-06-01T07:45:00',
  },
  {
    nickName: '陈晨',
    userName: 'chenchen001',
    roles: [{ roleName: '管理员' }],
    createTime: '2025-06-15T10:10:00',
  },
  {
    nickName: '周杰',
    userName: 'zhoujie',
    roles: [{ roleName: '用户' }, { roleName: '运营' }],
    createTime: '2025-04-25T08:00:00',
  },
  {
    nickName: '吴敏',
    userName: 'wumin09',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-05-09T13:30:00',
  },
  {
    nickName: '郑云',
    userName: 'zhengyun007',
    roles: [{ roleName: '开发者' }],
    createTime: '2025-05-20T19:20:00',
  },
  {
    nickName: '何冰',
    userName: 'hebing',
    roles: [{ roleName: '用户' }],
    createTime: '2025-06-01T07:45:00',
  },
  {
    nickName: '陈晨',
    userName: 'chenchen001',
    roles: [{ roleName: '管理员' }],
    createTime: '2025-06-15T10:10:00',
  },
  {
    nickName: '周杰',
    userName: 'zhoujie',
    roles: [{ roleName: '用户' }, { roleName: '运营' }],
    createTime: '2025-04-25T08:00:00',
  },
  {
    nickName: '吴敏',
    userName: 'wumin09',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-05-09T13:30:00',
  },
  {
    nickName: '郑云',
    userName: 'zhengyun007',
    roles: [{ roleName: '开发者' }],
    createTime: '2025-05-20T19:20:00',
  },
  {
    nickName: '何冰',
    userName: 'hebing',
    roles: [{ roleName: '用户' }],
    createTime: '2025-06-01T07:45:00',
  },
  {
    nickName: '陈晨',
    userName: 'chenchen001',
    roles: [{ roleName: '管理员' }],
    createTime: '2025-06-15T10:10:00',
  },
  {
    nickName: '周杰',
    userName: 'zhoujie',
    roles: [{ roleName: '用户' }, { roleName: '运营' }],
    createTime: '2025-04-25T08:00:00',
  },
  {
    nickName: '吴敏',
    userName: 'wumin09',
    roles: [{ roleName: '审核员' }],
    createTime: '2025-05-09T13:30:00',
  },
  {
    nickName: '郑云',
    userName: 'zhengyun007',
    roles: [{ roleName: '开发者' }],
    createTime: '2025-05-20T19:20:00',
  },
  {
    nickName: '何冰',
    userName: 'hebing',
    roles: [{ roleName: '用户' }],
    createTime: '2025-06-01T07:45:00',
  },
  {
    nickName: '陈晨',
    userName: 'chenchen001',
    roles: [{ roleName: '管理员' }],
    createTime: '2025-06-15T10:10:00',
  },
]

interface DiseaseNode {
  id: number
  nickName: string
  createTime: string
  children?: DiseaseNode[]
}

// 随机生成时间字符串
function randomDate(start: Date, end: Date): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString().slice(0, 10)
}

// 生成一个节点
function createNode(idStart: number, level: number, maxLevel: number, siblingsCount: number): DiseaseNode[] {
  const nodes: DiseaseNode[] = []
  for (let i = 0; i < siblingsCount; i++) {
    const id = idStart + i
    const node: DiseaseNode = {
      id,
      nickName: `疾病名称-${level}-${id}`,
      createTime: randomDate(new Date(2023, 0, 1), new Date(2023, 5, 30)),
    }
    if (level < maxLevel) {
      // 每个节点随机生成1~3个子节点
      const childCount = Math.floor(Math.random() * 3) + 1
      node.children = createNode(id * 10, level + 1, maxLevel, childCount)
    }
    nodes.push(node)
  }
  return nodes
}

// 生成100条左右数据，5层嵌套
const mockDiseaseTableData1: DiseaseNode[] = createNode(1, 1, 5, 10)
// 导出
export { mockDiseaseTableData1 }
export const mockDiseaseTableData = [
  {
    id: 1,
    nickName: '呼吸系统疾病',
    createTime: '2024-06-01T08:30:00',
    children: [
      {
        id: 11,
        nickName: '哮喘',
        createTime: '2024-06-02T09:00:00',
        children: [
          {
            id: 111,
            nickName: '过敏性哮喘',
            createTime: '2024-06-03T10:00:00',
            children: [
              {
                id: 1111,
                nickName: '花粉过敏引发哮喘',
                createTime: '2024-06-04T11:00:00',
                children: [
                  {
                    id: 11111,
                    nickName: '季节性花粉哮喘',
                    createTime: '2024-06-05T12:00:00',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 12,
        nickName: '慢性阻塞性肺疾病',
        createTime: '2024-06-06T14:20:00',
      },
    ],
  },
  {
    id: 2,
    nickName: '循环系统疾病',
    createTime: '2024-06-02T11:00:00',
    children: [
      {
        id: 21,
        nickName: '高血压',
        createTime: '2024-06-07T10:15:00',
        children: [
          {
            id: 211,
            nickName: '原发性高血压',
            createTime: '2024-06-08T11:30:00',
            children: [
              {
                id: 2111,
                nickName: '老年性高血压',
                createTime: '2024-06-09T13:00:00',
                children: [
                  {
                    id: 21111,
                    nickName: '单纯收缩期高血压',
                    createTime: '2024-06-10T15:00:00',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 22,
        nickName: '冠心病',
        createTime: '2024-06-08T16:45:00',
      },
    ],
  },
  {
    id: 3,
    nickName: '神经系统疾病',
    createTime: '2024-06-03T10:00:00',
    children: [
      {
        id: 31,
        nickName: '帕金森病',
        createTime: '2024-06-04T12:00:00',
      },
      {
        id: 32,
        nickName: '癫痫',
        createTime: '2024-06-05T13:30:00',
        children: [
          {
            id: 321,
            nickName: '儿童癫痫',
            createTime: '2024-06-06T14:45:00',
            children: [
              {
                id: 3211,
                nickName: '良性儿童癫痫',
                createTime: '2024-06-07T16:00:00',
                children: [
                  {
                    id: 32111,
                    nickName: '睡眠相关良性癫痫',
                    createTime: '2024-06-08T18:00:00',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
]

// #region 病例

// #endregion

/**
   * 默认的模拟收藏目录树结构数据
   */
export const defaultCatalogData: FavoriteCatalogItem[] = [
  {
    catalogId: 1,
    catalogName: '随访病例',
    parentId: 0,
    ancestors: [],
    orderNum: 1,
    children: [
      {
        catalogId: 2,
        catalogName: '头颈部',
        parentId: 1,
        ancestors: [1],
        orderNum: 1,
        children: [
          {
            catalogId: 3,
            catalogName: '脑部',
            parentId: 2,
            ancestors: [1, 2],
            orderNum: 1,
          },
          {
            catalogId: 4,
            catalogName: '颈部',
            parentId: 2,
            ancestors: [1, 2],
            orderNum: 2,
          },
        ],
      },
      {
        catalogId: 5,
        catalogName: '胸部',
        parentId: 1,
        ancestors: [1],
        orderNum: 2,
      },
    ],
  },
  {
    catalogId: 6,
    catalogName: '教学病例',
    parentId: 0,
    ancestors: [],
    orderNum: 2,
    children: [
      {
        catalogId: 7,
        catalogName: '肺结节',
        parentId: 6,
        ancestors: [6],
        orderNum: 1,
      },
      {
        catalogId: 8,
        catalogName: '脑出血',
        parentId: 6,
        ancestors: [6],
        orderNum: 2,
      },
    ],
  },
]

