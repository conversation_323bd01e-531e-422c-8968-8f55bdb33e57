import { useUserStore } from '@/store/modules/user'

// 将用户有权限的菜单列表加工一下，1、将菜单树状结构变成一维数组 2、提取菜单权限中的功能权限列表
export const handleMenuList = (list: any, newMenuList: Array<any>, funcList: Array<any>) => {
  const len = list.length
  for (let i = 0; i < len; i++) {
    const item = list[i]
    item.route = item.path
    if (item.children && item.children.length) {
      handleMenuList(item.children, newMenuList, funcList)
    }
    else {
      newMenuList.push(item)
      funcList.push(...item.meta.keyWords)
    }
  }
  const finalMenuList = JSON.parse(JSON.stringify(newMenuList))
  newMenuList.forEach((item: any) => {
    const parts = item.path.split('/').filter(Boolean)
    for (let i = 1; i < parts.length; i++) {
      const segment = `/${parts.slice(0, i).join('/')}`
      const isExist = finalMenuList.some((item: any) => item.path === segment)
      if (!isExist)
        finalMenuList.push({ name: 'add', path: segment })
    }
  })
  return { newMenuList: finalMenuList, funcList }
}
/**
 * 判断路由是否显示
 * @param route 需要判断的路由对象
 * @param permissionRoutes 权限路由对象
 * @returns 如果路由显示则返回true，否则返回false
 */
function judgeRouteShow(route: any, permissionRoutes: any) {
  const children = route.children
  const routeMeta = route.meta

  // 如果路由有子路由且子路由的meta属性中不包含auth字段，则返回true
  if (children && !children[0].meta.auth)
    return true

  // 获取当前路由的meta属性中的auth字段，如果不存在则获取子路由的meta属性中的auth字段
  const curRouteAuth = routeMeta ? routeMeta.auth : children[0].meta.auth

  // 判断权限路由对象中是否存在与当前路由auth字段相同的路由对象
  return permissionRoutes.some((item: any) => item.path === curRouteAuth)
}

/**
 * 过滤出用户有权限的路由（用于菜单或权限控制）
 * @param allRoutes 所有系统路由（嵌套结构）
 * @param permissionRoutes 用户有权限的路由（扁平结构，含 path 字段）
 * @param parentPath 当前递归的父级路径（初始为 "" 或 "/"）
 * @returns 过滤后的路由数组，只包含有权限的路由项
 */
export const filterRoutes = (
  allRoutes: any[],
  permissionRoutes: any[],
  parentPath = '',
): any[] => {
  // 将权限路由中的 path 取出作为 Set，便于快速判断是否有权限
  const permissionPathSet = new Set(permissionRoutes.map(p => p.path))
  // 最终要返回的过滤后的路由数组
  const result = allRoutes.reduce((acc: any[], route: any) => {
    // 拼接当前 route 的完整路径（例如：/setting/user-manage）
    const fullPath = resolvePath(parentPath, route.path)

    // 拷贝当前路由对象，避免直接修改原数据
    const current: any = { ...route }

    // 递归处理子路由
    if (current.children && current.children.length > 0)
      current.children = filterRoutes(current.children, permissionRoutes, fullPath)

    // 判断当前路由是否有权限
    const hasPermission = permissionPathSet.has(fullPath)

    // 判断是否还有可显示的子路由
    const hasChildren = current.children && current.children.length > 0

    // 公共路由保留
    const isPublic = route.meta?.noAuth || route.meta?.public === true

    // 如果当前路由有权限，或者它的子路由中有一个有权限，就保留
    if (hasPermission || hasChildren || isPublic)
      acc.push(current)

    return acc
  }, [])

  return result
}

/**
 * 拼接路径，避免多重嵌套时路径错误
 * @param parent 父级路径
 * @param child 当前子路由的 path
 * @returns 拼接后的完整路径
 */
function resolvePath(parent: string, child: string): string {
  // 如果子路由为空，返回父路径（如 /dashboard 下的 ""）
  if (!child)
    return parent

  // 如果子路由是绝对路径，直接返回
  if (child.startsWith('/'))
    return child

  // 如果父路径是根路径，直接拼接
  if (parent === '/')
    return `/${child}`

  // 默认拼接父子路径，中间加 /
  return `${parent}/${child}`
}

/**
 * 在系统所有路由中过滤，只保留用户有权限的路由
 * @param allRoutes 系统所有的路由
 * @param permissionRoutes  用户有权限的路由
 */
export const filterRoutes2 = (allRoutes: any, permissionRoutes: any) => {
  try {
    console.log(JSON.stringify(permissionRoutes))
    const filteredRoutes = [] as any
    // 递归遍历每一个动态路由，检查与已配数据库页面是否一致。
    allRoutes.forEach((curRoute: any) => {
      const routesTree = { ...curRoute }
      console.log('routesTree', routesTree)
      console.log('judge', judgeRouteShow(routesTree, permissionRoutes))
      // 判断当前层级（父级）是否有权限，如果有再向子级进行递归
      if (judgeRouteShow(routesTree, permissionRoutes)) {
        // 根据routeTree结构来进行解析，由于利用page插件生成路由，routeTree结构不规则，每一层拥有的属性不同，所以设置下面的判断规则
        // 只有routesTree.children[0]后面的层级才有meta属性，如果还有子集，继续递归
        if (routesTree.meta) {
          // 如果还有子集，继续递归
          if (routesTree.children)
            routesTree.children = filterRoutes(routesTree.children, permissionRoutes)
        }

        // 第一层的递归
        else if (routesTree.children[0].children) {
          routesTree.children[0].children = filterRoutes(routesTree.children[0].children, permissionRoutes)
        }

        filteredRoutes.push(routesTree)
      }
    })
    return filteredRoutes
  }
  catch (err: any) {
    console.log('路由过滤失败', err)
  }
}
// 功能权限判断
export function accessOperation(operation: string): boolean {
  const userStore = useUserStore()
  let operations = []
  operations = userStore.functionList
  // console.log('operations1111:', operations)
  return operations.includes(operation)
}
