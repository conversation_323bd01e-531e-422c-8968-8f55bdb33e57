import COS from 'cos-js-sdk-v5'
import { Md5 } from 'ts-md5'
// import {
//   FileUploadResultReport,
//   getTencentSignatureCos,
//   getTencentSignatureVod,
// } from '@/api/file'
// import { importFileUploadResultReport } from '@/api/resource/case-io'
// import { reportUploadPackResult } from '@/api/resource/upload-case'

/**
 * 计算文件 MD5，用于唯一标识文件（分片计算）
 */
function computeFileMd5(file: File, chunkSize = 1024 * 1024): Promise<string> {
  return new Promise((resolve, reject) => {
    const md5 = new Md5()
    const reader = new FileReader()
    const totalChunks = Math.ceil(file.size / chunkSize)
    let currentChunk = 0

    const readNextChunk = () => {
      const start = currentChunk * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      const blob = file.slice(start, end)
      reader.readAsArrayBuffer(blob)
    }

    reader.onload = (event) => {
      if (event.target?.result)
        md5.appendByteArray(new Uint8Array(event.target.result as ArrayBuffer))

      currentChunk++
      currentChunk < totalChunks ? readNextChunk() : resolve(md5.end() as string)
    }

    reader.onerror = () => reject(new Error('读取文件失败'))

    readNextChunk()
  })
}

/**
 * 上传视频文件到腾讯云 VOD（点播）
 */
// export async function uploadVideoToTencentVod(item: any) {
//   const { file, onProgress } = item
//   onProgress?.({ percent: 0 })

//   const tcVod = new TcVod({ getSignature: getTencentSignatureVod })
//   const uploader = tcVod.upload({ mediaFile: file })

//   uploader.on('media_progress', (info: any) => {
//     onProgress?.({ percent: info.percent * 100 })
//   })

//   const res = await uploader.done()

//   return FileUploadResultReport({
//     fileId: res.fileId,
//     fullUrl: res.video.url,
//     fileName: file.name,
//   })
// }

/**
 * 上传任意文件到腾讯云 COS（对象存储）
 */
async function uploadFileToTencentCos(item: any) {
  const { file, onProgress } = item
  onProgress?.({ percent: 50 })

  const data: any = {}
  return
  // TODO 调用获取签名接口
  //   const data = await getTencentSignatureCos()

  const cos = new COS({
    getAuthorization(_, callback) {
      callback({
        TmpSecretId: data.response.credentials.tmpSecretId,
        TmpSecretKey: data.response.credentials.tmpSecretKey,
        SecurityToken: data.response.credentials.sessionToken,
        StartTime: data.response.startTime,
        ExpiredTime: data.response.expiredTime,
        ScopeLimit: true,
      })
    },
  })

  const md5Hash = await computeFileMd5(file)
  const date = new Date()
  const key = `/${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}/${md5Hash}.${file.name.split('.').pop()}`

  await cos.uploadFile({
    Bucket: data.bucketName,
    Region: data.region,
    Key: key,
    Body: file,
    onProgress(progressData) {
      onProgress?.({ percent: progressData.percent * 100 })
    },
  })

  return {
    fullUrl: data.endpoint + key,
    actualName: key,
  }
}

/**
 * 上传本地文件至对象存储
 */
export async function uploadLocalCaseToCos(item: any) {
  if (!item.file)
    throw new Error('file is empty')

  const uploadCosResult = await uploadFileToTencentCos(item)

  // TODO 调用通知后端接口
  const result = uploadCosResult
  return result
}

