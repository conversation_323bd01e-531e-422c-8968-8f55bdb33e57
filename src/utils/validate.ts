/**
 * 验证手机号是否合法
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validatePhone(rule: any, value: string, callback: Function) {
  const reg = /^1[3-9]\d{9}$/
  if (value === '' || value === undefined || value === null) {
    callback()
    return false
  }
  else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的手机号码'))
      return false
    }
    else {
      callback()
      return true
    }
  }
}

/**
 * 通用验证规范 验证是否为空
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validateIsEmpty(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback(new Error('必填项'))
  else
    callback()
}

/**
 * 判断字符是否为空的方法
 * @param {*} obj
 */
export function checkStringIsEmpty(obj: any) {
  if (typeof obj === 'undefined' || obj == null || obj === '')
    return true

  else
    return false
}

/**
 * 验证邮箱是否合法
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validateEmail(rule: any, value: string, callback: Function) {
  const reg = /^[A-Za-z0-9\u4E00-\u9FA5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  if (value === '' || value === undefined || value === null) {
    callback(new Error('邮箱不允许为空'))
    return false
  }
  else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的邮箱'))
      return false
    }
    else {
      callback()
      return true
    }
  }
}

/**
 * 通用验证规范 验证IP
 */
export function validateIp(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()
  const ipPattern
  = /^(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/

  if (!ipPattern.test(value))
    callback(new Error('请输入合法的 IP 地址'))

  else
    callback()
}

/**
 * 通用验证规范 验证端口
 */
export function validatePort(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()
  const portNumber = Number(value)
  if (
    !/^\d+$/.test(value) // 是否为纯数字
    || portNumber < 1
    || portNumber > 65535 // 端口范围 1~65535
  )
    callback(new Error('请输入 1 到 65535 之间的合法端口号'))

  else
    callback()
}

/**
* 通用验证规范 验证字段约束
* @param {*} rule
* @param {*} value
* @param {*} callback
*/
export function validateCommonField(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()
  if (!/^[a-zA-Z0-9_]*$/ig.test(value))
    callback(new Error('输入内容只支持字母、数字、下划线组合'))
  else
    callback()
}

/**
 * 通用验证规范 验证名称约束
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validateCommonName(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()
  const validate = /^[\u4E00-\u9FA5a-zA-Z0-9_]+$/.test(value)
  if (!validate)
    callback(new Error('输入内容只支持中文、字母、数字、下划线组合'))
  else
    callback()
}

/**
 * 通用验证规范 验证文本约束
 */
export function validateCommonText(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()

  // 检测首尾空白字符
  const validate1 = /^\s|\s$/.test(value)
  if (validate1)
    callback(new Error('首尾不能包含空白符'))

  // 匹配的正则：仅支持中文、大小写字母、数字、下划线“_”、空格、·、#、@、$、&、*、（、）、-、+、，、.、=、{、}、[、]、\、/，不能以空格开头和结尾,不能连续超过两个空格
  const pattern = /^(?!\s)(?!.*\s{2,})[\u4E00-\u9FA5a-zA-Z0-9_ ·#$@&*()\-+,.={}[\]\\/]+(?<!\s)$/
  if (!pattern.test(value))
    callback(new Error('输入内容包含无效字符'))
  else
    callback()
}

/**
 *  在文本约束基础上 允许首尾空格
 * 比如人员介绍、文章等
 */
export function validateCommonTextPermitEmpty(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()

  // 匹配的正则：仅支持中文、大小写字母、数字、下划线“_”、空格、·、#、@、$、&、*、（、）、-、+、，、.、=、{、}、[、]、\、/，不能以空格开头和结尾,不能连续超过两个空格
  // const pattern = /^[\u4E00-\u9FA5\w ·#@$&*()\-+，.={}[\]\\/]*$/
  const pattern = /^(?!\s)(?!.*\s{2,})[\u4E00-\u9FA5a-zA-Z0-9_ ·#$@&*()\-+,.={}[\]\\/]+(?<!\s)$/
  if (!pattern.test(value))
    callback(new Error('输入内容包含无效字符'))
  else
    callback()
}

/**
 * 验证纯数字
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validateNumericField(rule: any, value: string, callback: Function) {
  if (checkStringIsEmpty(value))
    callback()

  if (!/^\d+$/.test(value))
    callback(new Error('输入内容只支持纯数字'))
  else
    callback()
}

/**
 * 验证密码合法性,ASCII码33-126，不能输入空格（空格的ASCII码为32）
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validatePassword(rule: any, value: string, callback: Function) {
  if (value.length < 6 || value.length > 20) {
    callback(new Error('输入字符范围6-20个字符'))
    return false
  }
  if (!/^[\x21-\x7E]+$/.test(value)) {
    callback(new Error('存在系统不支持的特殊字符'))
    return false
  }
  else {
    callback()
    return true
  }
}

// /**
//  * 文本限制
//  * @param {*} rule
//  * @param {*} value
//  * @param {*} callback
//  */
// export function validateText(rule: any, value: string, callback: Function) {
//   const validate = /^(?!\s)(?!.*\s{2,})[\u4E00-\u9FA5a-zA-Z0-9_ ·#$@&*()\-+,.={}[\]\\/]+(?<!\s)$/.test(value)
//   if (!validate)
//     callback(new Error('输入内容仅支持中文、大小写字母、数字、下划线“_”、空格、·、#、@、$、&、*、（、）、-、+、，、.、=、{、}、[、]、\、/，不能以空格开头和结尾,不能连续超过两个空格'))
//   else
//     callback()
// }

