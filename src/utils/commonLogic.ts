import JSEncrypt from 'jsencrypt'
import { dayjs } from 'element-plus'
import { GetPublicKey } from '@/api/auth'
import { CovertDateTypes, RecentDateTypes } from '@/enums/common'
import type { CaseRecord } from '@/types/caseLibrary'
import type { InvalidFile } from '@/types/file'
import type { DictOption } from '@/types/common'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { FollowStatus } from '@/enums/caseLibrary'

/**
 * 根据传入的枚举值获取相应的日期数组范围
 * @param type 日期范围类型
 * @returns 日期数组范围，包含开始日期和结束日期
 */
export function getDateRangeByType(type: number): Date[] {
  const end = new Date()
  const start = new Date()
  start.setHours(0, 0, 0, 0) // 设置开始时间为当天的零点
  end.setHours(23, 59, 59, 999) // 设置结束时间为当天的午夜

  switch (type) {
    case RecentDateTypes.Today:
      break
    case RecentDateTypes.ThreeDays:
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3) // 三天前
      break
    case RecentDateTypes.Week:
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7) // 一周前
      break
    case RecentDateTypes.OneMonth:
      start.setDate(start.getDate() - 30) // 30天前
      break
    case RecentDateTypes.ThreeMonth:
      start.setDate(start.getDate() - 90) // 90天前
      break
    case RecentDateTypes.HalfYear:
      start.setDate(start.getDate() - 180) // 90天前
      break
    case RecentDateTypes.Year:
      start.setFullYear(end.getFullYear() - 1) // 一年前
      break
    case RecentDateTypes.CurrentMonth:
      start.setDate(1) // 本月初
      end.setMonth(end.getMonth() + 1, 0) // 本月末
      break
    default:
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7) // 一周前
      break
  }

  // 返回时间范围数组
  return [start, end]
}

/**
 * 获取日期范围string类型数组
 * @param type 日期范围类型
 * @returns
 */
export function getDateStringRange(type: number): [string, string] {
  const [start, end] = getDateRangeByType(type)
  return [
    dayjs(start).format('YYYY-MM-DD HH:mm:ss'),
    dayjs(end).format('YYYY-MM-DD HH:mm:ss'),
  ]
}

/**
 * 禁用未来日期的方法
 * @param time - Date对象
 * @returns 布尔值，表示该日期是否应该被禁用
 */
export function disabledFutureDate(time: Date): boolean {
  return time.getTime() > Date.now()
}

/**
 * 禁用半年前的日期
 */
export function disabledBeforeHalfYear(time: Date): boolean {
  const now = new Date()
  const limitDate = new Date()
  limitDate.setMonth(now.getMonth() - 6)
  return time.getTime() < limitDate.getTime()
}

/**
 * 导出文件
 * @param blob - Blob 对象
 * @param fileName 文件名称
 */
export function saveAs(blob: any, fileName: string) {
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = fileName
  link.click()
}

/**
 * 获取经过Rsa公钥加密后的值
 */
export const getRsaVal = async (originVal: string) => {
  const rsaKey = await GetPublicKey()
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(rsaKey)
  const val = encrypt.encrypt(originVal) as string
  return val
}

/**
 * 根据参数的情况返回原始值或者 -
 * @param value 原始值
 */
export const getValueOrDefault = <T>(value: T | null | undefined): T | string => {
  return (value !== null && value !== undefined && value !== '') ? value : '--'
}

// 格式化文件大小打印
export function beautifyFileSize(sizeInBytes: number) {
  if (!sizeInBytes)
    return ''
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let unitIndex = 0
  while (sizeInBytes >= 1024 && unitIndex < units.length - 1) {
    sizeInBytes /= 1024
    unitIndex++
  }
  return ` ${Number.parseFloat(sizeInBytes.toFixed(2))} ${units[unitIndex]} `
}

/**
 * 将字符串日期转换为本地格式的日期字符串
 *
 * @param dateString - 要转换的日期字符串（ISO格式或其他 Date 构造函数支持的格式）
 * @param dateType - 日期格式类型，默认使用 'YYYY-MM-DD HH:mm'（可使用枚举 CovertDateTypes）
 * @returns 格式化后的本地日期字符串，如果传入为空则返回 '--'
 */
export const convertToLocaleDateString = (
  dateString: string,
  dateType: string = CovertDateTypes.YYYY_MM_DD_HH_MM,
): string => {
  // 如果日期字符串无效，返回占位符
  if (!dateString)
    return '--'

  // 将传入的日期字符串转换为 Date 对象
  const date = new Date(dateString)

  // 使用 dayjs 格式化日期
  return dayjs(date).format(dateType)
}

/**
 * 通用选项匹配函数，根据 value 获取对应的 label。
 * @param options 选项数组（如 PatientTypeOptions）
 * @param value 当前值（数字或字符串）
 * @returns 匹配到的 label，找不到返回 "-"
 */
export const getOptionLabel = <T extends { value: any; label: string }>(
  options: T[],
  value: any,
): string => {
  return options.find(opt => opt.value === value)?.label ?? ''
} // 根据你的文件结构调整路径

/**
 * 通用选项匹配函数，根据 value 获取对应的 label。
 * @param options 选项数组（如 PatientTypeOptions）
 * @param value 当前值（数字或字符串）
 * @returns 匹配到的 label，找不到返回 "-"
 */
export const getDictOptionLabel = <T extends DictOption>(
  options: T[],
  value: any,
): string => {
  return options.find(opt => opt.dictValue === value)?.dictLabel ?? ''
} // 根据你的文件结构调整路径

/** 生成一个空白的 CaseRecord 对象 */
export function createDefaultCaseRecord(): CaseRecord {
  const caseLibraryStore = useCaseLibraryStore()
  return {
    caseId: undefined,
    caseName: '', // 病例名称
    caseNo: '', // 病例编号
    diseaseId: undefined, // 疾病 ID
    diagnosis: '', // 最终诊断
    patientId: '', // 患者ID
    patientName: '', // 患者姓名
    patientAge: '', // 年龄
    patientSex: '', // 性别
    medicalHistory: '', // 病史
    caseCategory: caseLibraryStore.caseCategoryOptions[0]?.dictValue ?? '', // 病例类别，默认 0
    difficulty: caseLibraryStore.caseDifficultyOptions[0]?.dictValue ?? '', // 难度等级，默认 0
    sign: '', // 征象
    caseAnalysis: '', // 病例分析
    isExport: 0, // 默认未导出
    lmGpId: undefined, // 联盟分组 ID
    sourceType: 0, // 来源类型，默认 0（可按需定义）
    followStatus: FollowStatus.Pending, // 默认待随访
    modifyUsers: '', // 历史修改人
    tags: [], // 默认空标签
    tagInfoList: [], // 默认空标签
    studyInfoList: [], // 检查列表，默认空
    followInfoList: [{
      followType: caseLibraryStore.followTypeOptions[0]?.dictValue ?? '',
      followupResult: '',
    }], // 随访列表，默认空
    qualityMatch: caseLibraryStore.qualityMatchOptions[0]?.dictValue ?? '', // 默认不符合
    positionMatch: caseLibraryStore.positionMatchOptions[0]?.dictValue ?? '', // 默认不符合
    remark: '', // 备注
    selfComplaints: '', // 主诉
  }
}

/**
 * 定义每个文件类别对应的允许后缀、最大大小、最大文件名长度及错误提示
 */
const typeMap: Record<
  string,
  {
    suffixes: string[]
    maxSizeMB: number
    maxNameLength?: number
    errorMsg: string
  }
> = {
  dcm: {
    suffixes: ['dcm'], // DICOM 文件只允许 .dcm 后缀
    maxSizeMB: 100, // 最大 100MB
    maxNameLength: 100, // 最大文件名长度 100 字符
    errorMsg: '单个DICOM 文件不能超过 100MB',
  },
  zip: {
    suffixes: ['zip'], // 压缩包后缀
    maxSizeMB: 2048, // 最大 2GB
    errorMsg: '压缩包大小不能超过 2GB',
  },
  img: {
    suffixes: ['png', 'jpg', 'jpeg'], // 常见图片格式
    maxSizeMB: 10, // 最大 10MB
    errorMsg: '图片大小不能超过 10MB',
  },
  // 这里可以继续添加其他类型...
}

/**
 * 校验文件是否符合指定类别的格式和大小要求
 * @param file 上传的文件对象，必须含 name 和 size 字段
 * @param type 文件类别，例如 "dcm"、"zip"、"img"
 * @returns 返回 InvalidFile 对象表示校验失败，或 false 表示校验通过
 */
export function validateFileByType(file: File, type: string): InvalidFile | false {
  // 获取文件后缀（小写），如果没有后缀则空字符串
  const suffix = file.name.split('.').pop()?.toLowerCase() || ''

  // 计算文件大小，单位 MB，没有则为 0
  const sizeMB = file.size ? file.size / 1024 / 1024 : 0

  // 获取文件所在目录（相对路径），如果有的话
  const fileDir = file.webkitRelativePath || ''

  // 获取文件名，备用
  const fileName = file.name || ''

  // 构造返回的错误对象模板
  const result: InvalidFile = { fileDir, fileName, error: '' }

  // 获取指定类别的配置
  const typeConfig = typeMap[type]

  // 如果类别不存在，返回错误
  if (!typeConfig) {
    result.error = `不支持的文件类型类别: ${type}`
    return result
  }

  // 校验文件后缀是否属于该类别允许的后缀集合
  if (!typeConfig.suffixes.includes(suffix)) {
    result.error = `文件格式错误，请上传以下格式之一：.${typeConfig.suffixes.join('，.')}`
    return result
  }

  // 文件大小不能为 0 字节
  if (sizeMB === 0) {
    result.error = '不能上传空文件'
    return result
  }

  // 文件大小不能超过类别最大限制
  if (sizeMB > typeConfig.maxSizeMB) {
    result.error = typeConfig.errorMsg
    return result
  }

  // 如果配置了最大文件名长度限制，校验文件名长度
  if (typeConfig.maxNameLength && file.name.length > typeConfig.maxNameLength) {
    result.error = `文件名长度超过 ${typeConfig.maxNameLength} 字符`
    return result
  }

  // 通过所有校验，返回 false 表示无错误
  return false
}

/**
 * 格式化患者性别
 * @param row
 * @returns
 */
export function formatSex(patientSex: string) {
  if (patientSex === 'f' || patientSex === 'F')
    return '女'
  if (patientSex === 'm' || patientSex === 'M')
    return '男'

  return patientSex
}

/**
 * 路由排序
 * @param routes 路由列表
 * @returns
 */
export function sortRoutes(routes: any[]) {
  routes.sort((a, b) => {
    const orderA = a.meta?.order ?? 0
    const orderB = b.meta?.order ?? 0
    return orderA - orderB
  })

  // 对 children 递归排序
  routes.forEach((route) => {
    if (Array.isArray(route.children))
      sortRoutes(route.children)
  })

  return routes
}
