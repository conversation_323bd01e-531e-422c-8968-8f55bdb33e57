import { request } from '@/utils/request'
const baseURL = '/auth-server'

/**
 * @description 获取RSA公钥，用于对用户密码进行加密
 */
export function GetPublicKey(): Promise<string> {
  return request<string>({
    baseURL,
    url: '/open/getKeys',
    method: 'get',
  })
}

// 登录
export function Login(data: object): Promise<string> {
  return request<string>({
    baseURL,
    url: '/open/login',
    method: 'post',
    data,
  })
}

// 登出
export function Logout() {
  return request({
    baseURL,
    url: '/open/logout',
    method: 'post',
  })
}

// 从权限系统获取当前用户信息
export function GetCurrentAuthUserInfo() {
  return request({
    baseURL,
    url: '/user/getUserInfo',
    method: 'get',
  })
}

// 获取平台详情
export function GetPlatInfo(params: { platId: string }) {
  return request<any>({
    baseURL,
    url: '/system/plat/query',
    method: 'get',
    params,
  })
}

export function GetUserList(params: {
  platformId: string
  nickName: string
}) {
  return request({
    baseURL,
    url: '/forbid/user/list',
    method: 'get',
    params,
  })
}

// 获取当前用户权限系统上配置的菜单权限列表
export function getMenuList() {
  return request({
    baseURL,
    url: '/menu/getMenuRouters',
    method: 'get',
  })
}

/**
 * @description 登出
 */
export function logoutApi() {
  return request({
    baseURL,
    url: '/open/logout',
    method: 'post',
  })
}

/**
 * @description 更新密码
 */
export function updatePasswordApi(data: object) {
  return request({
    baseURL,
    url: '/user/updatePwd',
    method: 'post',
    data,
  })
}

// 为用户重置密码
export function resetPassword(userId: number, password: string) {
  return request(
    {
      baseURL,
      url: '/forbid/user/updatePassword',
      method: 'post',
      data: { userId, password },
    },
    {
      successMsg: '重置密码成功',
    },
  )
}

// 获取字典
export function getDicType(params: object) {
  return request({
    baseURL,
    url: '/system/dict/data/getDicType',
    method: 'get',
    params,
  })
}
