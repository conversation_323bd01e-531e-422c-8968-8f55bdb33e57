import type { UploadPolicyResp } from '@/types/file'
import { request } from '@/utils/request'

const baseURL = '/case-library-api/caselibrary' // 报告数据接入

// --- File Upload APIs ---
/**
 * 获取PACS图像调阅地址
 * @returns 获取上传地址
 */
export function GetCommonURL() {
  return request({
    baseURL,
    url: '/dept/case/tag/list',
    method: 'get',
  })
}

export function getUploadPolicyAAs(fileName: string): Promise<UploadPolicyResp> {
  return request<UploadPolicyResp>({
    baseURL,
    url: '/file/upload/getPolicy',
    method: 'get',
    params: { fileName },
  })
}

// #region 疾病分类相关

/**
 * 获取疾病树
 */
export function GetDiseaseTreeList(params?: object) {
  return request({
    baseURL,
    url: '/system/disease/tree/list',
    method: 'get',
    params,
  })
}

/**
 * 新增疾病分类
 */
export function AddDisease(data: object) {
  return request({
    baseURL,
    url: '/system/disease/add',
    method: 'post',
    data,
  })
}

/**
 * 修改疾病分类
 */
export function UpdateDisease(data: object) {
  return request({
    baseURL,
    url: '/system/disease/edit',
    method: 'post',
    data,
  })
}

/**
 * 删除疾病分类
 */
export function DelDisease(data: object) {
  return request({
    baseURL,
    url: '/system/disease/remove',
    method: 'post',
    data,
  })
}
// #endregion

// #region 个人目录相关

/**
 * 获取个人目录树
 */
export function GetUserCatalogTreeList(params?: object) {
  return request({
    baseURL,
    url: '/system/userCatalog/tree/list',
    method: 'get',
    params,
  })
}

/**
 * 新增个人目录
 */
export function AddUserCatalog(data: object) {
  return request({
    baseURL,
    url: '/system/userCatalog/add',
    method: 'post',
    data,
  })
}

/**
 * 修改个人目录
 */
export function UpdateUserCatalog(data: object) {
  return request({
    baseURL,
    url: '/system/userCatalog/edit',
    method: 'post',
    data,
  })
}

/**
 * 删除个人目录
 */
export function DelUserCatalog(data: object) {
  return request({
    baseURL,
    url: '/system/userCatalog/remove',
    method: 'post',
    data,
  })
}
// #endregion

// #region 病例库分类增删查改
/**
 * 获取病例库分类
 */
export function GetCaseLibTypeList(params?: object) {
  return request({
    baseURL,
    url: '/system/caseLibType/list/open',
    method: 'get',
    params,
  })
}

/**
 * 新增病例库分类
 */
export function AddCaseLibType(data: object) {
  return request({
    baseURL,
    url: '/system/caseLibType/save',
    method: 'post',
    data,
  })
}

/**
 * 修改病例库分类
 */
export function UpdateCaseLibType(data: object) {
  return request({
    baseURL,
    url: '/system/caseLibType/update',
    method: 'post',
    data,
  })
}

/**
 * 删除病例库分类
 */
export function DelCaseLibType(data: object) {
  return request({
    baseURL,
    url: '/system/caseLibType/delete',
    method: 'post',
    data,
  })
}
// #endregion

// #region 病例搜索

/**
 * 获取病例标签列表
 */
export function GetCaseTagList() {
  return request({
    baseURL,
    url: '/dept/case/tag/list',
    method: 'get',
  })
}
/**
 * 查询病例列表
 */
export function GetDepartmentCase(data: object) {
  return request({
    baseURL,
    url: '/dept/case/page',
    method: 'post',
    data,
  })
}

/**
 * 高级查询
 */
export function AdvanceGetDepartmentCase(data: object) {
  return request({
    baseURL,
    url: '/dept/case/search',
    method: 'post',
    data,
  })
}

/**
 * 查询我创建的病例
 */
export function GetMyCreatedCase(data: object) {
  return request({
    baseURL,
    url: '/dept/case/verify/my/list',
    method: 'post',
    data,
  })
}

/**
 * 查询我审核的病例
 */
export function GetMyReviewedCase(data: object) {
  return request({
    baseURL,
    url: '/dept/case/verify/list',
    method: 'post',
    data,
  })
}

/**
 * 查询个人病例库
 */
export function GetPersonalCase(data: object) {
  return request({
    baseURL,
    url: '/personal/case/page',
    method: 'post',
    data,
  })
}
// #endregion

// #region 疾病概述
/**
 * 查询疾病概述
 */
export function GetDiseaseOverview(params?: object) {
  return request({
    baseURL,
    url: '/system/diseaseOverview/query',
    method: 'get',
    params,
  })
}

/**
 * 病例库分类
 */
export function UpdateDiseaseOverview(data: object) {
  return request({
    baseURL,
    url: '/system/diseaseOverview/edit',
    method: 'post',
    data,
  })
}

// #endregion
// #region 病例导入、新建、删除等
/**
 * 查看自己本地上传的图像
 */
export function getUploadStudyList(params: object) {
  return request({
    baseURL,
    url: '/case/dicom/notRelate',
    method: 'get',
    params,
  })
}

/**
 * 本地影像上传后新建病例
 */
export function addCaseFromUpload(data: object) {
  return request({
    baseURL,
    url: '/case/addCaseFromUpload',
    method: 'post',
    data,
  })
}

/**
 * 获取RIS检查列表（含报告）
 */
export function getRISStudyList(params?: object) {
  return request({
    baseURL,
    url: '/case/risStudyList',
    method: 'get',
    params,
  })
}

/**
 * RIS导入后新建病例
 */
export function addCaseFromRIS(data: object) {
  return request({
    baseURL,
    url: '/case/addCaseFromRIS',
    method: 'post',
    data,
  })
}

/**
 * 从随访库导入病例
 */
export function addCaseFromFollow(data: object) {
  return request({
    baseURL,
    url: '/case/addCaseFromFollow',
    method: 'post',
    data,
  })
}

/**
 * 判断影像是否已关联到其他病例
 */
export function isImageLinkedToOtherCases(data: object) {
  return request({
    baseURL,
    url: '/case/queryCaseExist',
    method: 'post',
    data,
  })
}

/**
 * 科室病例库病例删除
 */
export function delCaseFromDept(data: object) {
  return request({
    baseURL,
    url: '/dept/case/delete',
    method: 'post',
    data,
  })
}

/**
 * 审核病例
 */
export function reviewCase(data: object) {
  return request({
    baseURL,
    url: '/dept/case/verify',
    method: 'post',
    data,
  })
}

// #endregion
