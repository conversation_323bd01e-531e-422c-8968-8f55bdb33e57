
import type { FileUpload, UploadPolicyResp } from '@/types/file'
import { request } from '@/utils/request'

const baseURL = '/case-library-api/caselibrary' // 病例库接口
const pacsURL = '/case-library-api/pacs' // PACS接口
// const baseURL = import.meta.env.VITE_APP_API_KG_MANA_URL

// --- File Upload APIs ---

export function getUploadPolicy(fileName: string): Promise<UploadPolicyResp> {
  return request<UploadPolicyResp>({
    baseURL,
    url: '/file/getPostPolicy',
    method: 'get',
    params: { fileName },
  })
}

export function removeFile(filePath: string): Promise<void> {
  return request<void>({
    baseURL,
    url: '/file/remove',
    method: 'post',
    data: { filePath },
  })
}

// 文件大小阈值：200MB
const FILE_SIZE_THRESHOLD = 200 * 1024 * 1024

// 限速配置
const UPLOAD_SPEED_LIMIT = {
  // 最大上传速率 (bytes/second) - 10MB/s
  maxBytesPerSecond: 10 * 1024 * 1024,
  // 速率检查间隔 (ms)
  checkInterval: 1000,
  // 最大并发上传数
  maxConcurrentUploads: 2,
}

// 当前并发上传计数
let currentUploads = 0

// 限速器类
class UploadSpeedLimiter {
  private startTime = 0
  private uploadedBytes = 0
  private lastCheckTime = 0
  private maxBytesPerSecond: number

  constructor(maxBytesPerSecond: number) {
    this.maxBytesPerSecond = maxBytesPerSecond
    this.startTime = Date.now()
    this.lastCheckTime = this.startTime
  }

  // 检查是否需要限速
  async checkAndLimit(currentBytes: number): Promise<void> {
    this.uploadedBytes = currentBytes
    const now = Date.now()
    const elapsed = (now - this.startTime) / 1000 // 秒

    if (elapsed > 0) {
      const currentSpeed = this.uploadedBytes / elapsed

      // 如果当前速度超过限制，计算需要延迟的时间
      if (currentSpeed > this.maxBytesPerSecond) {
        const expectedTime = this.uploadedBytes / this.maxBytesPerSecond
        const actualTime = elapsed
        const delayTime = (expectedTime - actualTime) * 1000 // 毫秒

        if (delayTime > 0) {
          console.log(
            `Upload speed limiting: ${(currentSpeed / 1024 / 1024).toFixed(
              2,
            )} MB/s, delaying ${delayTime.toFixed(0)}ms`,
          )
          await new Promise(resolve => setTimeout(resolve, delayTime))
        }
      }
    }
  }

  // 获取当前上传速度 (MB/s)
  getCurrentSpeed(): number {
    const elapsed = (Date.now() - this.startTime) / 1000
    return elapsed > 0 ? this.uploadedBytes / elapsed / 1024 / 1024 : 0
  }
}

// 等待并发槽位
async function waitForUploadSlot(): Promise<void> {
  while (currentUploads >= UPLOAD_SPEED_LIMIT.maxConcurrentUploads) {
    console.log(
      `Max concurrent uploads reached (${currentUploads}/${UPLOAD_SPEED_LIMIT.maxConcurrentUploads}), waiting...`,
    )
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  currentUploads++
  console.log(
    `Upload slot acquired (${currentUploads}/${UPLOAD_SPEED_LIMIT.maxConcurrentUploads})`,
  )
}

// 释放并发槽位
function releaseUploadSlot(): void {
  currentUploads = Math.max(0, currentUploads - 1)
  console.log(
    `Upload slot released (${currentUploads}/${UPLOAD_SPEED_LIMIT.maxConcurrentUploads})`,
  )
}

// 流式上传到MinIO（带限速和取消支持）
export function uploadToMinio(
  file: File,
  policy: UploadPolicyResp,
  onProgress?: (progress: number) => void,
  abortSignal?: AbortSignal,
): Promise<FileUpload> {
  if (!policy.postPolicyMinio)
    return Promise.reject(new Error('MinIO policy not available'))

  const { postPolicyMinio } = policy

  // 验证必要的字段
  if (!postPolicyMinio.endpoint || !postPolicyMinio.key || !postPolicyMinio.policy)
    return Promise.reject(new Error('MinIO policy incomplete'))

  console.log('MinIO upload config:', {
    endpoint: postPolicyMinio.endpoint,
    key: postPolicyMinio.key,
    fileName: file.name,
    fileSize: file.size,
  })

  return new Promise((resolve, reject) => {
    // 检查是否已被取消
    if (abortSignal?.aborted) {
      reject(new Error('Upload cancelled'))
      return
    }

    // 使用立即执行的异步函数来处理异步逻辑
    (async () => {
      try {
        // 等待并发槽位
        await waitForUploadSlot()

        // 再次检查是否已被取消
        if (abortSignal?.aborted) {
          releaseUploadSlot()
          throw new Error('Upload cancelled')
        }

        // 创建限速器
        const speedLimiter = new UploadSpeedLimiter(UPLOAD_SPEED_LIMIT.maxBytesPerSecond)

        const formData = new FormData()

        // 按照MinIO要求的顺序添加字段
        formData.append('key', postPolicyMinio.key)
        formData.append('policy', postPolicyMinio.policy)
        formData.append('x-amz-algorithm', postPolicyMinio.xamzAlgorithm)
        formData.append('x-amz-credential', postPolicyMinio.xamzCredential)
        formData.append('x-amz-date', postPolicyMinio.xamzDate)
        formData.append('x-amz-signature', postPolicyMinio.xamzSignature)
        // file字段必须在最后
        formData.append('file', file)

        const minioUrl = `${postPolicyMinio.endpoint}/${postPolicyMinio.bucket}`

        console.log('Starting rate-limited MinIO upload to:', minioUrl)

        await request<any>({
          url: minioUrl,
          method: 'post',
          data: formData,
          timeout: Math.max(10, Math.ceil(file.size / (2 * 1024 * 1024))) * 60 * 1000, // 考虑限速，增加超时时间
          headers: {
            Accept: '*/*',
          },
          signal: abortSignal, // 传递 AbortSignal
          onUploadProgress: (progressEvent: any) => {
            // 检查是否已被取消 - 如果已取消，直接返回，不执行任何操作
            if (abortSignal?.aborted)
              return

            if (progressEvent.lengthComputable) {
              const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
              const currentSpeed = speedLimiter.getCurrentSpeed()

              console.log(
                `Upload progress: ${progress}%, Speed: ${currentSpeed.toFixed(2)} MB/s`,
              )

              if (onProgress)
                onProgress(progress)

              // 异步应用限速，但不阻塞进度回调
              speedLimiter.checkAndLimit(progressEvent.loaded).catch((error) => {
                console.warn('Speed limiting error:', error)
              })
            }
          },
        }, {
          ignoreEmptyResponse: true, // 忽略空响应
        })
        // MinIO上传成功，返回文件信息
        resolve({
          fileName: file.name,
          // filePath: `${minioUrl}/${postPolicyMinio.key}`,
          filePath: `${postPolicyMinio.key}`,
          contentType: file.type,
          size: file.size,
          officeFileName: '',
        })
      }
      catch (error: any) {
        // 特别处理取消错误
        if (error.name === 'AbortError' || error.message === 'Upload cancelled' || abortSignal?.aborted) {
          console.log('MinIO upload cancelled:', file.name)
          reject(new Error('Upload cancelled'))
          return
        }

        console.error('MinIO upload failed:', error)
        reject(new Error(`MinIO upload failed: ${error.message}`))
      }
      finally {
        // 释放并发槽位
        releaseUploadSlot()
      }
    })()
  })
}

// 流式上传到COS（带取消支持）
export function uploadToCOS(
  file: File,
  policy: UploadPolicyResp,
  onProgress?: (progress: number) => void,
  abortSignal?: AbortSignal,
): Promise<FileUpload> {
  if (!policy.policyCOS)
    return Promise.reject(new Error('COS policy not available'))

  const { policyCOS } = policy

  // 验证必要的字段
  if (!policyCOS.endpoint || !policyCOS.key || !policyCOS.response?.credentials)
    return Promise.reject(new Error('COS policy incomplete'))

  console.log('COS upload config:', {
    endpoint: policyCOS.endpoint,
    key: policyCOS.key,
    fileName: file.name,
    fileSize: file.size,
  })

  // 检查是否已被取消
  if (abortSignal?.aborted)
    return Promise.reject(new Error('Upload cancelled'))

  const formData = new FormData()
  const { credentials } = policyCOS.response

  // 添加COS所需的表单字段
  formData.append('key', policyCOS.key)
  formData.append('success_action_status', '200')
  formData.append('x-cos-security-token', credentials.sessionToken)
  formData.append('x-cos-meta-filename', file.name)
  // file字段必须在最后
  formData.append('file', file)

  console.log('Starting COS upload to:', policyCOS.endpoint)

  return request<any>({
    url: policyCOS.endpoint,
    method: 'post',
    data: formData,
    timeout: Math.max(5, Math.ceil(file.size / (10 * 1024 * 1024))) * 60 * 1000, // 动态超时
    signal: abortSignal, // 传递 AbortSignal
    onUploadProgress: onProgress
      ? (progressEvent: any) => {
          // 检查是否已被取消 - 如果已取消，直接返回，不执行任何操作
          if (abortSignal?.aborted)
            return

          if (progressEvent.lengthComputable) {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
            onProgress(progress)
          }
        }
      : undefined,
  }, {
    ignoreEmptyResponse: true, // 忽略空响应
  })
    .then(() => {
      // COS上传成功，返回文件信息
      return {
        fileName: file.name,
        filePath: policyCOS.key,
        contentType: file.type,
        size: file.size,
        officeFileName: '',
      }
    })
    .catch((error) => {
      // 特别处理取消错误
      if (error.name === 'AbortError' || error.message === 'Upload cancelled' || abortSignal?.aborted) {
        console.log('COS upload cancelled:', file.name)
        throw new Error('Upload cancelled')
      }

      console.error('COS upload failed:', error)
      throw new Error(`COS upload failed: ${error.message}`)
    })
}

// 智能上传文件（根据大小和网络类型选择上传方式）
export function smartUploadFile(
  file: File,
  onProgress?: (progress: number) => void,
  abortSignal?: AbortSignal,
): Promise<FileUpload> {
  const fileSizeMB = file.size / (1024 * 1024)
  console.log(`Smart upload: ${file.name} (${fileSizeMB.toFixed(2)}MB), threshold: ${(FILE_SIZE_THRESHOLD / 1024 / 1024).toFixed(0)}MB`)

  // 检查是否已被取消
  if (abortSignal?.aborted)
    return Promise.reject(new Error('Upload cancelled'))

  return getUploadPolicy(file.name)
    .then((policy) => {
      // 再次检查是否已被取消
      if (abortSignal?.aborted)
        return Promise.reject(new Error('Upload cancelled'))

      // 根据netType选择上传方式
      if (policy.serverType === 'LAN' && policy.postPolicyMinio) {
        console.log('Using MinIO upload for LAN environment')
        return uploadToMinio(file, policy, onProgress, abortSignal)
      }
      else if (policy.serverType === 'WAN' && policy.policyCOS) {
        console.log('Using COS upload for WAN environment')
        return uploadToCOS(file, policy, onProgress, abortSignal)
      }
      else {
        console.error('Unsupported network type or missing policy:', {
          serverType: policy.serverType,
          hasMinio: !!policy.postPolicyMinio,
          hasCOS: !!policy.policyCOS,
        })
        return Promise.reject(new Error(`Unsupported network type: ${policy.serverType} or missing upload policy`))
      }
    })
    .catch((error) => {
      // 特别处理取消错误
      if (error.name === 'AbortError' || error.message === 'Upload cancelled' || abortSignal?.aborted) {
        console.log('Policy-based upload cancelled:', file.name)
        throw new Error('Upload cancelled')
      }

      console.error('Policy-based upload failed:', error)
      throw error
    })

  //   if (file.size <= FILE_SIZE_THRESHOLD) {
  //   // 小文件：使用普通上传，需要模拟进度回调
  //   console.log('Using normal upload for small file')

  //   // 为小文件上传创建模拟进度
  //   const simulateProgress = () => {
  //     if (onProgress) {
  //       // 模拟进度更新
  //       let progress = 0
  //       const interval = setInterval(() => {
  //         progress += Math.random() * 30
  //         if (progress >= 95) {
  //           clearInterval(interval)
  //           // 不要设置为100%，等实际完成后再设置
  //           return
  //         }
  //         onProgress(Math.min(progress, 95))
  //       }, 200)

  //       return () => {
  //         clearInterval(interval)
  //         onProgress(100) // 完成时设置为100%
  //       }
  //     }
  //     return () => {}
  //   }

  //   const finishProgress = simulateProgress()

  //   return uploadFile(file, abortSignal)
  //     .then((result) => {
  //       finishProgress()
  //       return result
  //     })
  //     .catch((error) => {
  //       finishProgress()

  //       // 特别处理取消错误
  //       if (error.name === 'AbortError' || error.message === 'Upload cancelled' || abortSignal?.aborted) {
  //         console.log('Normal upload cancelled:', file.name)
  //         throw new Error('Upload cancelled')
  //       }

  //       console.error('Normal upload failed:', error)
  //       throw error
  //     })
  // }
}

// #region 文件上传
/**
 * zip文件上传后上报结果
 */
export function reportZIPUploadResult(data: object) {
  return request({
    baseURL: pacsURL,
    url: '/upload/report',
    method: 'post',
    data,
  })
}
/**
 * dcm文件上传后上报结果
 */
export function reportDCMUploadResult(data: object) {
  return request({
    baseURL: pacsURL,
    url: '/upload/reportDicom',
    method: 'post',
    data,
  })
}
/**
 * 获取文件解析进度
 */
export function getTaskProgress(params: object) {
  return request({
    baseURL: pacsURL,
    url: '/upload/queryStatusTask',
    method: 'get',
    params,
  })
}

// #endregion
