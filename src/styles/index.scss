html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: rgba(233, 236, 239, 1);
  color: #41484F;
  // 设置全局字体为思源黑体
  font-family: 'SourceHanSansCN-Regular';
  font-size: var(--common-font-size);

  /* 设置滚动条的样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* 滚动槽 */
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #bbb;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.25);
  }

  /* 非激活窗口 */
  // ::-webkit-scrollbar-thumb:window-inactive {
  //   background: rgba(0,255,0,0.4);
  // }
}

// 这里设置一些公共样式变量，目前公共变量还不多，后续会逐步补充，足够多时可以抽离出一个variable.scss文件在这里引入
:root {
  --common-padding: 16px;
  --common-font-size: 16px;
  --design-width: 1440px;
  --design-height: 800px;
  --color-primary: #367AC2;
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(13, 148, 136);
  opacity: 0.75;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}



// 搜索页的公共样式
.footer-pagination {
  margin-top: 2rem;
  justify-content: center;
}


@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}
// 公共抽屉样式
.common-drawer{
  .el-drawer__header{
    margin-bottom: 0 ;
  }
}

.el-table {
  border: 1px solid #ebeef5 !important;
  border-bottom: 0 !important;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-range-input {
  font-size: 14px !important;
}

.el-input__wrapper {
  font-size: 14px !important;
}

.cursor-pointer {
  cursor: pointer;
}

.el-popper {
  max-width: 500px;
}

.pagination-common {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: var(--common-padding);
  left: 0;
  right: 0;
}

// 病例详情模态框样式
.case-detail-drawer-model{
  // 模态框宽度与抽屉面板宽度保持一致，这样能解决抽屉面板左侧不能点击的问题
  width: 550px;
  // 设置模态框靠右
  margin-left: auto;
}

