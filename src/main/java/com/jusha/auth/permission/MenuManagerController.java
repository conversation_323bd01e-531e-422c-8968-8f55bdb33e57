package com.jusha.auth.permission;

import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.mybatisplus.entity.SysMenu;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.RouterVo;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysPlatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "菜单路由管理")
@RestController
@RequestMapping("/forbid/menu")
public class MenuManagerController extends BaseController {

    @Autowired
    private ISysMenuService iSysmenuService;

    @Autowired
    private ISysPlatService iSysPlatService;

    @Autowired
    private RedisCache redisCache;

    @Value("${token.expireTime}")
    private int expireTime;


    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation("路由信息")
    @GetMapping("/getMenuRouters")
    public ResultBean getMenuRouters() {
        List<RouterVo> routerVos;
        String platId = getPlatId();
        long userId = getLoginUser().getUserId();
        if (redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + platId) == null) {
            List<SysMenu> menus = iSysmenuService.selectMenuTreeByUserId(userId, Long.parseLong(platId));
            routerVos = iSysmenuService.buildMenus(menus, userId);
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + platId, routerVos, expireTime, TimeUnit.MINUTES);
        } else {
            routerVos = redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + platId);
            if (routerVos.isEmpty()) {
                List<SysMenu> menus = iSysmenuService.selectMenuTreeByUserId(userId, Long.parseLong(platId));
                routerVos = iSysmenuService.buildMenus(menus, userId);
            }
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + platId, routerVos, expireTime, TimeUnit.MINUTES);
        }
        return ResultBean.success(routerVos);
    }

    /**
     * 加载对应角色菜单列表树
     */
    @ApiOperation("对应角色菜单列表树")
    @GetMapping(value = "/menuTreeselect")
    public ResultBean roleMenuTreeselect(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        List<SysMenu> menus = iSysmenuService.selectAllMenuList(roleId);
        HashMap map = new HashMap();
        map.put("checkedKeys", iSysmenuService.selectMenuListByRoleId(roleId));
        map.put("menus", iSysmenuService.buildMenuTreeSelect(menus));
        return ResultBean.success(map);
    }

    @ApiOperation("根据角色ID获取对应角色菜单ID及父ID")
    @GetMapping("/menuIdsByRoleId/list")
    public ResultBean menuIdsByRoleId(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        List<Long> menuIds = iSysmenuService.selectMenuListAndParentIdByRoleId(roleId);
        return ResultBean.success(menuIds);
    }

    @ApiOperation("获取所有接口权限列表")
    @GetMapping("/getPermission")
    public ResultBean getPermission() {
        String platId = getPlatId();
        SysUser user = getLoginUser().getSysUser();
        Set<String> interfacePaths = iSysmenuService.selectIterfacePathsByUserId(user, Long.parseLong(platId));
        return ResultBean.success(interfacePaths);
    }

    @ApiOperation("根据菜单名称获取菜单列表")
    @GetMapping("/getMenuByName")
    public ResultBean getMenuListByName(@RequestParam("menuName") String menuName) {
        String platId = getPlatId();
        List<SysMenu> menuList = iSysmenuService.getMenuListByName(menuName, Long.parseLong(platId));
        return ResultBean.success(menuList);
    }

    @ApiOperation("根据菜单id获取当前菜单详情")
    @GetMapping("/getMenuById")
    public ResultBean getMenuById(@RequestParam("menuId") Long menuId) {
        SysMenu menu = iSysmenuService.selectMenuById(menuId);
        return ResultBean.success(menu);
    }

    @ApiOperation("添加菜单")
    @PostMapping("/add")
    public ResultBean add(@RequestBody SysMenu menu) {
        if (!iSysmenuService.checkMenuNameUnique(menu)) {
            return error(MessageUtils.message("menu.already.exist.add"));
        }
        //如果一个菜单加到菜单下了，提示不可以，因为目录的下级才能是菜单
        if (!iSysmenuService.checkMenuParent(menu)) {
            return error(MessageUtils.message("menu.parent.not.menu"));
        }
        return ResultBean.success(iSysmenuService.addMenu(menu));
    }

    @ApiOperation("修改菜单")
    @PostMapping("/edit")
    public ResultBean edit(@RequestBody SysMenu menu) {
        if (!iSysmenuService.checkMenuNameUnique(menu)) {
            return error(MessageUtils.message("menu.already.exist.edit"));
        }
        else if (menu.getMenuId().equals(menu.getParentId())) {
            return error(MessageUtils.message("menu.parent.myself.edit"));
        }
        return resultBean(iSysmenuService.updateMenu(menu));
    }

    @ApiOperation("删除菜单")
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long menuId) {
        //获取其子菜单ID集合
        List<Long> childIds = iSysmenuService.selectChildIdListByMenuId(menuId);
        if (iSysmenuService.hasChildByMenuId(menuId)) {
            //将子菜单去掉
            iSysmenuService.deleteMenuByIds(childIds);
        }
        if (iSysmenuService.checkMenuExistRole(menuId)) {
            //将角色绑定的所有菜单去掉
            childIds.add(menuId);
            iSysmenuService.deleteRoleMenuByMenuIds(childIds);
        }
        return resultBean(iSysmenuService.deleteMenuById(menuId));
    }

}
