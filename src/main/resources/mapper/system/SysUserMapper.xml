<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.auth.system.mapper.ISysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="phoneNumber"  column="phone_number" />
		<result property="workNumber"   column="work_number"  />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="RoleResult" type="SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="status"       column="role_status"    />
		<result property="platId"       column="plat_id"        />
		<result property="roleType"     column="role_type"      />
    </resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.user_name, u.nick_name, u.phone_number,u.work_number, u.password,
		u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time,
        r.role_id, r.role_name,r.plat_id,r.status as role_status,r.role_type
        from sys_user u
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
	
	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.user_name, u.nick_name, u.phone_number, u.work_number,u.status, u.create_time
	    from sys_user u
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phoneNumber != null and phoneNumber != ''">
			AND u.phone_number like concat('%', #{phoneNumber}, '%')
		</if>
		<if test="workNumber != null and workNumber != ''">
			AND u.work_number like concat('%', #{workNumber}, '%')
		</if>
	</select>
	
	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.user_name, u.nick_name, u.phone_number,u.work_number, u.status, u.create_time
	    from sys_user u
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phoneNumber != null and phoneNumber != ''">
			AND u.phone_number like concat('%', #{phoneNumber}, '%')
		</if>
		<if test="workNumber != null and workNumber != ''">
			AND u.work_number like concat('%', #{workNumber}, '%')
		</if>
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>

	<select id="selectUserByPhoneNumber" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.phone_number = #{phoneNumber} and u.del_flag = '0'
	</select>

	<select id="selectUserByWorkNumber" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.work_number = #{workNumber} and u.del_flag = '0'
	</select>

	<select id="selectUserByUserNamePhoneWork" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where (u.user_name = #{userName} or u.phone_number = #{userName} or u.work_number = #{userName})
		and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
</mapper> 