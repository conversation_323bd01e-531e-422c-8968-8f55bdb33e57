<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.auth.system.mapper.ISysGroupMapper">

	<resultMap type="SysGroup" id="SysGroupResult">
		<id     property="groupId"     column="group_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="groupName"   column="group_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>

    <select id="selectGroupListByRoleId" resultType="Long">
		select d.group_id
		from sys_group d
            left join sys_role_group rd on d.group_id = rd.group_id
        where rd.role_id = #{roleId} and d.plat_id=#{platId}
		and d.group_id not in (select d.parent_id from sys_group d inner join sys_role_group rd
		on d.group_id = rd.group_id
		and rd.role_id = #{roleId}
		and d.plat_id=#{platId})
		and d.del_flag = '0'
		order by d.parent_id, d.order_num
	</select>
	
	<select id="selectChildrenGroupById" parameterType="Long" resultMap="SysGroupResult">
		select * from sys_group where find_in_set(#{groupId}, ancestors)
	</select>
	
	<select id="selectNormalChildrenGroupById" parameterType="Long" resultType="int">
		select count(*) from sys_group where status = 0 and del_flag = '0' and find_in_set(#{groupId}, ancestors)
	</select>
	
	<update id="updateGroupChildren" parameterType="java.util.List">
	    update sys_group set ancestors =
	    <foreach collection="groups" item="item" index="index"
	        separator=" " open="case group_id" close="end">
	        when #{item.groupId} then #{item.ancestors}
	    </foreach>
	    where group_id in
	    <foreach collection="groups" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.groupId}
	    </foreach>
	</update>
</mapper> 