#错误消息
not.null= user's name or password must be entered
user.not.exists= The user does not exist
user.password.not.match= User does not exist/password is incorrect
user.has.been.stopped= The user has been deactivated
user.password.retry.limit.exceed= Password entry error {0} times, account locked {1} minutes
user.password.delete= Sorry, your account has been deleted
user.blocked= The user has been blocked. Please contact the administrator
login.blocked= Unfortunately, the IP address has been placed on the blacklist
user.logout.success= Exit successfully
length.not.valid= The length must be between {min} and {max} characters
user.username.not.valid= It must consist of 2 to 20 Chinese characters, letters, numbers or underlines, and must begin with a non-number character
user.password.not.valid= 5-50 characters
user.email.not.valid= The email format is incorrect
user.mobile.phone.number.not.valid= The phone number format is incorrect
user.login.success= logged in successfully
user.register.success= registered successfully
user.notfound= 请重新登录
user.forcelogout= You were forcibly logged out by the administrator. Please log in again
user.unknown.error= Unknown error, please log in again
user.login.again.error = Invalid login
plat.already.exist.add = Failure to add a platform that already exists
platTag.already.exist.add = Failure to add a Platform ID that already exists
plat.already.exist.edit = Failure to modify a name that already exists
platTag.already.exist.edit = Failure to modify a Platform ID that already exists

interface.already.exist.add = Failure to add a Interface path that already exists
interfacePath.already.exist.add = Failure to add a Interface that path already exists
interface.already.exist.edit = The modification failed because the interface already exists
interfacePath.already.exist.edit = The modification failed because the interface path already exists
interfacePath.already.menu.remove = The interface has been bound to the menu {0} and cannot be deleted
plat.group.child.delete = The platform has subsets, it cannot be deleted
plat.menu.child.delete = The platform has menu, it cannot be deleted
plat.role.child.delete = The platform has roles, it cannot be deleted
plat.myself.delete = The default platform does not allow deletion
common.getlock.time.out = Lock wait timeout
common.repeat.request = Repetition of requests

group.already.exist.add = failure of add up, because the group already exists
group.already.exist.edit = The modification failed because the group already exists
group.parent.self.edit = The modification of the group failed, becuase the superior group cannot be itself
group.child.not.stop.edit = The modification failed because the group contains subgroups that are not disabled
group.stop.message = The grouping is deactivated and new additions are not allowed
group.child.exist.delete = There are subgroups below, and they cannot be deleted
group.not.exist = The group does not exist

menu.already.exist.add = Failure to add a menu that already exists
menu.parent.not.menu = Failure to add a menu. The previous  menu can only be a part of the directory
menu.already.exist.edit = Failure to add a menu whose name already exists
menu.parent.myself.edit = The menu modification failed, because the superior menu cannot select itself
menu.child.delete = It cannot be deleted because it has submenu
menu.role.delete = The menu has been assigned and cannot be deleted

role.already.exist.add = Failure to add a new role, because its name already exists
role.already.exist.edit = Failure to modify the role, because its name already exists
role.already.allot.delete = The role has been assigned, which cannot be deleted
role.already.allot.edit = The role has been assigned and cannot be modified to another platform
role.edit.exception = Failed to modify the character. Please contact the administrator
role.do.not.exist = The role does not exist

user.can.not.null = Account name cannot be empty
password.can.not.null = Password name cannot be empty
nickName.can.not.null = The name of the person cannot be empty
user.already.exist.add = Failure to add a new user because its account name already exists
user.phone.already.exist.add = Failure to add a new user because the cellphone number already exists
user.worknumber.already.exist.add = Failure to add a new user,because the employee number already exists
user.already.exist.edit = Failure to modify a  user because its account name already exists
user.phone.already.exist.edit = Failure to modify a user because the cellphone number already exists
user.worknumber.already.exist.edit = Failure to modify a user because the employee number already exists
user.worknumber.phone.both.not.exist = The mobile phone number and the employee number cannot be empty at the same time
user.myself.delete = The current user cannot be  deleted
user.edit.exception = Please contact the administrator, for the personal information is abnormally modified

user.dowith.admin.user = Operation under the name of a super administrator  is not allowed
user.dowith.admin.role = Operating as a super administrator is not allowed
no.permission.visit.user = No access to user data
no.permission.visit.role = No access to role data
no.permission.visit.group = No access to grouping data

set.permission.first = Please set permissions first for the logged-in user
interface.permission.set = The interface permission under the annotation of @HasPermissions does not match. Please set the function permission for the logged-in user
get.userInfo.exception =  Abnormal user information acquisition
get.userId.exception = Obtaining user ID is abnormal
get.user.exception = Abnormal user account acquisition

dict.key.name.exist.add = Failure to add a parameterm whose key name already exists
dict.key.type.exist.add = Dictionary creation failed, because the dictionary type already exists
dict.key.name.exist.edit = Failure to modify parameter, because the arameter key name already exists
dict.key.type.exist.edit = Dictionary modification failed, because the dictionary type already exists

##权限
no.permission = You do not have permission to access the data. Please contact the administrator to add permission[{0}]
no.create.permission = You do not have permission to create data. Please contact the administrator to add permission[{0}]
no.update.permission = You do not have permission to modify data. Please contact the administrator to add permission[{0}]
no.delete.permission = You do not have permission to delete data. Please contact the administrator to add permission[{0}]
no.export.permission = You do not have permission to export data. Please contact the administrator to add permission[{0}]
no.view.permission = You do not have permission to check up the data. Please contact the administrator to add permission[{0}]

inner.parameter.delete = Built-in parameters cannot be deleted
parameter.allot.delete = The parameter has been assigned and cannot be deleted
import.success.having.error  = The file was imported successfully, but there was an error
import.teplate.file = Please import the template file

index.not.null = Line: The sequence number cannot be empty.
index.not.inorder = Line: The sequence number is not sorted in order.
index.not.legal = Line: The sequence number is illegal.
plate.not.null = Line: The name of the platform to which it belongs cannot be empty.
plate.not.exist = Line: The platform does not exist.
role.not.null = Line: The name of the character it belongs to cannot be empty.
role.not.exist = Line: The character does not exist.
userName.not.null = Line: Account name cannot be empty.
userName.too.long = Line: Account name is too long.
userName.already.exist = Action: The account name already exists.
userName.with.illegal = Line: The account name contains special characters.
personName.not.null = Person: The name of the person cannot be empty.
personName.too.long = Line: Name of the person is too long.
personName.with.illegal = Line: The name of the person contains special characters.
phone.workNumber.not.null = Line: The mobile phone number and the employee number cannot be empty at the same time.
phone.not.right = Line: The phone number format is incorrect.
workNumber.not.right = Line: Employee number format is incorrect.
phone.already.exist = Line: The phone number already exists.
workNumber.already.exist = Line: The employee number already exists.
unkonwn.error.ignore = Line: Unknown error, ignore the import
errorMessage.happen = Abnormalities occur:
all.import.success = The row data has been imported successfully!
total.upload.line = Total uploaded lines:
already.upload.line = ，Imported lines:
upload.error.line = ，Error lines:

this.plate.not.exist = The platform does not exist!
take.plate.parameters = Please carry the parameters of your platform
operate.fail = Operation failed
operate.success = Operation succeeded

newPassword.not.null = The new password cannot be empty
oldpassword.not.null = The old password cannot be empty
newPassword.oldpassword.cannot.same = The new password cannot be the same as the old one
password.not.null = The password cannot be empty
password.is.wrong = wrong password
oldPassword.is.wrong = Password change failed, old password is wrong
edit.password.exception = Abnormal password change, please contact the administrator

login.ip.first = Use SMS verification code for the first login
login.ip.change = The system detected a change in your network environment. Please log in using the SMS verification code
msg.code.expire = The captcha is incorrect or has expired. Please try again
msg.code.repeat = The captcha has been sent. Please do not send it again
msg.code.toomuch = Requesting for the verification code too frequently. Please try again after 10 minutes
msg.code.runoff = The number of SMS messages sent by this phone number on that day has been used up. Please try again tomorrow
msg.code.noPhoneNumber = The phone number has not yet been registered
userId.not.exist = The user does not exist
userId.too.much = Login credentials are not unique. Please contact the administrator for confirmation
verify.code.first = Please enter the graphic captcha first