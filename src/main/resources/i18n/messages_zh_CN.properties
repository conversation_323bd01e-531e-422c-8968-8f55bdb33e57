#错误消息
not.null=用户名或密码必须填写
user.not.exists=该用户不存在
user.password.not.match=用户不存在/密码错误
user.has.been.stopped=用户被停用
user.password.retry.limit.exceed=密码输入错误{0}次，帐户锁定{1}分钟
user.password.delete=对不起，您的账号已被删除
user.blocked=用户已封禁，请联系管理员
login.blocked=很遗憾，访问IP已被列入系统黑名单
user.logout.success=退出成功
length.not.valid=长度必须在{min}到{max}个字符之间
user.username.not.valid=* 2到20个汉字、字母、数字或下划线组成，且必须以非数字开头
user.password.not.valid=* 5-50个字符
user.email.not.valid=邮箱格式错误
user.mobile.phone.number.not.valid=手机号格式错误
user.login.success=登录成功
user.register.success=注册成功
user.notfound=请重新登录
user.forcelogout=管理员强制退出，请重新登录
user.unknown.error=未知错误，请重新登录
user.login.again.error = 无效登录
plat.already.exist.add = 新增失败，该平台已存在
platTag.already.exist.add = 新增失败，平台标识已存在
plat.already.exist.edit = 修改失败，该名称已存在
platTag.already.exist.edit = 修改失败，平台标识已存在

interface.already.exist.add = 新增失败，该接口已存在
interfacePath.already.exist.add = 新增失败，该接口路径已存在
interface.already.exist.edit = 修改失败，该接口已存在
interfacePath.already.exist.edit = 修改失败，该接口路径已存在
interfacePath.already.menu.remove = 接口已和菜单{0}绑定，不允许删除
plat.group.child.delete = 平台下有分组,不允许删除
plat.menu.child.delete = 平台下有菜单,不允许删除
plat.role.child.delete = 平台下有角色,不允许删除
plat.myself.delete = 默认平台不允许删除
common.getlock.time.out = 获取锁超时
common.repeat.request = 重复请求

group.already.exist.add = 新增失败，该分组已存在
group.already.exist.edit = 修改失败，该分组已存在
group.parent.self.edit =修改分組失败，上级分組不能是自己
group.child.not.stop.edit = 修改失败，该分组包含未停用的子分组,无法停用
group.stop.message = 分组停用，不允许新增
group.child.exist.delete = 存在下级分组,不允许删除
group.not.exist = 不存在该分组

menu.already.exist.add = 新增菜单失败，该菜单已存在
menu.parent.not.menu = 新增菜单失败，菜单的上级只能是目录
menu.already.exist.edit = 修改菜单失败，菜单名称已存在
menu.parent.myself.edit = 修改菜单失败，上级菜单不能选择自己
menu.child.delete = 存在子菜单,不允许删除
menu.role.delete = 菜单已分配,不允许删除

role.already.exist.add = 新增角色失败，角色名称已存在
role.already.exist.edit = 修改角色失败，角色名称已存在
role.already.allot.delete = 该角色已经被分配，不能删除
role.already.allot.edit = 该角色已经被分配，不能修改所属平台
role.edit.exception = 修改角色失败，请联系管理员
role.do.not.exist = 不存在该角色

user.can.not.null = 用户名不可以为空
password.can.not.null = 密码不可以为空
nickName.can.not.null = 用户姓名不可以为空
user.already.exist.add = 新增用户失败，账户名已存在
user.phone.already.exist.add = 新增用户失败，手机号码已存在
user.worknumber.already.exist.add = 新增用户失败，工号已存在
user.already.exist.edit = 修改用户失败，账户名已存在
user.phone.already.exist.edit = 修改用户失败，手机号码已存在
user.worknumber.already.exist.edit = 修改用户失败，工号已存在
user.worknumber.phone.both.not.exist = 手机号和工号不可以同时为空
user.myself.delete = 当前用户不能删除
user.edit.exception = 修改个人信息异常，请联系管理员

user.dowith.admin.user = 不允许操作超级管理员用户
user.dowith.admin.role = 不允许操作超级管理员角色
no.permission.visit.user = 没有权限访问用户数据
no.permission.visit.role =没有权限访问角色数据
no.permission.visit.group =没有权限访问分组数据

set.permission.first = 请先给登录用户设置权限
interface.permission.set =  @HasPermissions注解下的接口权限不匹配，请给登录用户设置该功能权限
get.userInfo.exception =  获取用户信息异常
get.userId.exception = 获取用户ID异常
get.user.exception = 获取用户账户异常

dict.key.name.exist.add = 新增参失败，参数键名已存在
dict.key.type.exist.add = 新增字典失败，字典类型已存在
dict.key.name.exist.edit = 修改参数失败，参数键名已存在
dict.key.type.exist.edit = 修改字典败，字典类型已存在

##权限
no.permission=您没有数据的权限，请联系管理员添加权限 [{0}]
no.create.permission=您没有创建数据的权限，请联系管理员添加权限 [{0}]
no.update.permission=您没有修改数据的权限，请联系管理员添加权限 [{0}]
no.delete.permission=您没有删除数据的权限，请联系管理员添加权限 [{0}]
no.export.permission=您没有导出数据的权限，请联系管理员添加权限 [{0}]
no.view.permission=您没有查看数据的权限，请联系管理员添加权限 [{0}]

inner.parameter.delete = 内置参数不能删除
parameter.allot.delete = 该参数已分配,不能删除

import.success.having.error  = 文件导入成功，但有错误。
import.teplate.file =  请导入模板文件

index.not.null = 行：序号不能为空。
index.not.inorder = 行：序号未按顺序排序。
index.not.legal = 行：序号不合法。
plate.not.null = 行：所属平台名称不能为空。
plate.not.exist = 行：所属平台不存在。
role.not.null = 行：所属角色名称不能为空。
role.not.exist = 行：所属角色不存在。
userName.not.null = 行：账户名称不能为空。
userName.too.long = 行：账户名称过长。
userName.already.exist = 行：账户名称已存在。
userName.with.illegal = 行：账户名称包含特殊字符。
personName.not.null = 行：人员姓名不能为空。
personName.too.long = 行：人员姓名过长。
personName.with.illegal = 行：人员姓名包含特殊字符。
phone.workNumber.not.null = 行：手机号码和工号不能同时为空。
phone.not.right = 行：手机号码格式错误。
workNumber.not.right = 行：工号格式错误。
phone.already.exist = 行：手机号码已存在。
workNumber.already.exist = 行：工号已存在。
unkonwn.error.ignore = 行：未知错误，忽略导入
errorMessage.happen = 发生异常：
all.import.success = 行数据全部导入成功！
total.upload.line = 总上传行数：
already.upload.line = ，已导入行数：
upload.error.line = ，错误行数：

this.plate.not.exist = 该平台不存在！
take.plate.parameters = 请携带所属平台参数
operate.fail = 操作失败
operate.success = 操作成功

newPassword.not.null = 新密码不能为空
oldpassword.not.null = 旧密码不能为空
newPassword.oldpassword.cannot.same = 新密码不能与旧密码相同
password.not.null = 密码不能为空
password.is.wrong = 密码错误
oldPassword.is.wrong = 修改密码失败，旧密码错误
edit.password.exception = 修改密码异常，请联系管理员

login.ip.first=初次登录请使用短信验证码
login.ip.change=系统检测到您的网络环境变更，请使用短信验证码登录
msg.code.expire=验证码有误或已过期，请重试
msg.code.repeat=验证码已发送，请勿重复发送
msg.code.toomuch=发送验证码过于频繁，请10分钟以后再试
msg.code.runoff=该手机号当日发送短信次数已用完，请明日再试
msg.code.noPhoneNumber=该手机号尚未注册
userId.not.exist = 该用户不存在
userId.too.much = 登录凭证不唯一，请联系管理员确认
verify.code.first = 请先输入图形验证码