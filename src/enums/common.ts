import { getDateRangeByType } from '@/utils/commonLogic'
// 记录状态类型枚举
export const RecentDateTypes = {
  Week: 1, // 最近一周
  OneMonth: 2, // 近一个月
  HalfYear: 3, // 近半年
  Year: 4, // 近一年
  ThreeMonth: 5, // 近三个月
  CurrentMonth: 6, // 本月
  Today: 7, // 当天
  ThreeDays: 8, // 最近三天
}

// 表单中日期选择器快捷选项
export const shortcuts = [
  {
    text: '近一周',
    value: () => {
      return getDateRangeByType(RecentDateTypes.Week)
    },
  },
  {
    text: '近一个月',
    value: () => {
      return getDateRangeByType(RecentDateTypes.OneMonth)
    },
  },
  {
    text: '近半年',
    value: () => {
      return getDateRangeByType(RecentDateTypes.HalfYear)
    },
  },
]

/**
 * 消息类型枚举
 */
export enum MessageType {
  /**
   * 心跳
   * 代表客户端与服务器之间的心跳检测，用于保持连接
   */
  Heartbeat = 10,

  /**
   * 推送任务
   * 服务器向客户端推送新的任务
   */
  PushTask = 11,

  /**
   * 登出
   * 通知客户端需要登出或已登出
   */
  Logout = 12,
}

/**
 * 转换日期格式枚举
 */
export const CovertDateTypes = {
  YYYY_MM_DD_HH_MM: 'YYYY/MM/DD HH:mm',
  YYYY_MM_DD: 'YYYY/MM/DD',
  YYYY_MM_DD_HH_MM_SS: 'YYYY/MM/DD HH:mm:ss',
  YYYY_MM_DD_HH_MM_SS2: 'YYYY_MM_DD_HH_mm_ss',
  YYYYMMDD_HHmmss: 'YYYY-MM-DD_HH-mm-ss',
}

/**
 * 公共分页选项
 */
export const pageSizeOptions = [5, 10, 20, 50, 100]

/**
 * 表单模式枚举
 */
export enum CaseFormMode {
  ADD = 'add',
  EDIT = 'edit',
  VIEW = 'view',
  DELETE = 'delete',
}

/**
 * 字典类型枚举
 */
export enum DictTypeEnum {
  CASE_TYPE = 'caseCategory', // 病例类型
  CASE_DIFFICULTY = 'caseDifficulty', // 病例难度

  FOLLOW_TYPE = 'followType', // 病例随访定性
  POSITION_MATCH = 'positionMatch', // 病例随访定位判断（符合 / 不符合）
  QUALITY_MATCH = 'qualityMatch', // 病例随访类型判断（符合 / 不符合）
  DEVICE_TYPE = 'deviceType', // 检查类型
  PATIENT_TYPE = 'patientType', // 患者就诊类型
}
