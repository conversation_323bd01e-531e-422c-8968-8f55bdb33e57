/** 就诊类型 */
export enum PatientType {
  Outpatient = '1', // 门诊
  Inpatient = '2', // 住院
  PhysicalExam = '3', // 体检
}

/** 就诊类型选项（用于 el-select） */
export const PatientTypeOptions = [
  { label: '门诊', value: PatientType.Outpatient },
  { label: '住院', value: PatientType.Inpatient },
  { label: '体检', value: PatientType.PhysicalExam },
]

/** 检查状态 */
export enum StudyState {
  Registered = 0, // 已登记
  Examined = 1, // 已检查
  Reported = 2, // 已报告
  Checked = 3, // 已审核
}

/** 检查状态选项 */
export const StudyStateOptions = [
  { label: '已登记', value: StudyState.Registered },
  { label: '已检查', value: StudyState.Examined },
  { label: '已报告', value: StudyState.Reported },
  { label: '已审核', value: StudyState.Checked },
]

/** 通用是否枚举（0 否，1 是） */
export enum YesOrNo {
  No = '0', // 否
  Yes = '1', // 是
}

/** 是否选项 */
export const YesOrNoOptions = [
  { label: '否', value: YesOrNo.No },
  { label: '是', value: YesOrNo.Yes },
]

/** 随访状态 */
export enum FollowStatus {
  Pending = '0', // 待随访
  Completed = '1', // 已随访
  All = '2', // 全部
}

/** 随访状态选项 */
export const FollowStatusOptions = [
  { label: '待随访', value: FollowStatus.Pending },
  { label: '已随访', value: FollowStatus.Completed },
]

/** 随访类型 */
export enum FollowType {
  Pathology = '1', // 病理
  Surgery = '2', // 手术
  Ultrasound = '3', // 超声
  Clinical = '4', // 临床
}

/** 随访类型选项 */
export const FollowTypeOptions = [
  { label: '病理', value: FollowType.Pathology },
  { label: '手术', value: FollowType.Surgery },
  { label: '超声', value: FollowType.Ultrasound },
  { label: '临床', value: FollowType.Clinical },
]

/** 匹配结果（用于 定性/定位 匹配） */
export enum MatchResult {
  Unmatched = '0', // 不符合
  Matched = '1', // 符合
}

/** 匹配结果选项 */
export const MatchResultOptions = [
  { label: '不符合', value: MatchResult.Unmatched },
  { label: '符合', value: MatchResult.Matched },
]

/** 病例难度等级 */
export enum CaseDifficulty {
  Easy = 1, // 简单
  Medium = 2, // 中等
  Hard = 3, // 困难
}

/** 病例难度等级选项（用于 el-select） */
export const CaseDifficultyOptions = [
  { label: '简单', value: CaseDifficulty.Easy },
  { label: '中等', value: CaseDifficulty.Medium },
  { label: '困难', value: CaseDifficulty.Hard },
]

/** 病例类别 */
export enum CaseCategory {
  Typical = 1, // 典型
  Atypical = 2, // 非典型
}

/** 病例类别选项（用于 el-select） */
export const CaseCategoryOptions = [
  { label: '典型', value: CaseCategory.Typical },
  { label: '非典型', value: CaseCategory.Atypical },
]

// 病例库类型枚举，用typeid表示
export enum CaseLibraryType {
  Teaching = 1, // 教学病例库
  FollowUp = -1, // 随访病例库
  MyCreated = 2, // 我创建的病例
  MyReviewed = 3, // 我审核的病例
  Custom = 4, // 自定义病例库
  Personal = 5, // 自定义病例库
}

/** 审核状态 */
export enum ReviewStatus {
  Pending = '0', // 审核中
  Approved = '1', // 通过
  Rejected = '2', // 拒绝
}

/** 审核状态选项（用于 el-select） */
export const ReviewStatusOptions = [
  { label: '审核中', value: ReviewStatus.Pending },
  { label: '通过', value: ReviewStatus.Approved },
  { label: '驳回', value: ReviewStatus.Rejected },
]

// 病例库大类
export enum SystemCaseLibraryType {
  Personal = 1, // 个人病例库
  Department = 2, // 科室病例库
}

/**
 * 病例来源类型
 * 表示新建病例时的来源渠道。
 */
export enum AddCaseSourceType {
  /** 本地影像上传 */
  LocalUpload = 1,

  /** RIS 系统导入 */
  RISImport = 2,

  /** 随访库导入 */
  FollowupLibrary = 3,
}
