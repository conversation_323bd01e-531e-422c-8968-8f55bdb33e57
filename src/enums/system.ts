import { CaseLibraryType } from './caseLibrary'

// 表格每行定义
export interface ColumnConfig {
  label: string
  value: string
  state: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  slot?: boolean
  isFromFirstStudy?: boolean // 是否从第一个study中取值
  showInTypes: number[] // 在哪些病例库类型下显示
}

// 默认表格列
export const defaultAllColumns: ColumnConfig[] = [
  {
    label: '姓名',
    value: 'patientName',
    state: true,
    width: '120',
    align: 'left',
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '性别',
    value: 'patientSex',
    state: true,
    width: '60',
    align: 'center',
    slot: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '年龄',
    value: 'patientAge',
    state: true,
    width: '80',
    align: 'center',
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '检查日期',
    value: 'studyTime',
    state: true,
    width: '180',
    align: 'center',
    isFromFirstStudy: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '就诊类型',
    value: 'patientType',
    state: false,
    width: '100',
    align: 'center',
    slot: true,
    isFromFirstStudy: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '检查类型',
    value: 'modalities',
    state: true,
    width: '100',
    align: 'center',
    slot: true,
    isFromFirstStudy: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '疾病名称',
    value: 'diseaseName',
    state: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '随访类型',
    value: 'followType',
    state: true,
    width: '100',
    align: 'center',
    slot: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '随访状态',
    value: 'followStatus',
    state: true,
    width: '100',
    align: 'center',
    slot: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
  {
    label: '审核状态',
    value: 'auditStatus',
    state: true,
    width: '100',
    align: 'center',
    slot: true,
    showInTypes: [CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed],
  },
  {
    label: '标签',
    value: 'tags',
    state: false,
    slot: true,
    showInTypes: [CaseLibraryType.Teaching, CaseLibraryType.FollowUp, CaseLibraryType.Custom, CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed, CaseLibraryType.Personal],
  },
]
