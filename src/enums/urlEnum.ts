// 是否生产环境
// const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV || '')
// 文件上传路径
export const fileUploadPath = '/api/filelib/file/upload'
// pacs上传路径
export const pacsUrl = 'https://10.2.253.10:8000/viewer?StudyInstanceUIDs='
// 微服务名-pacs部分，包括pacs配置、操作医院pacs、影像文件上传等
export const microServicePacs = '/pacs'
// 微服务名-资源管理部分，包括文件上传、经典病例、课件资源、资料分类
export const microServiceResource = '/riemanRes'
// 微服务名-权限管理部分，包括分组管理、用户管理、角色管理、菜单路由管理、登录登出等
export const microServiceAuth = '/auth-server'

