import './styles/index.scss'
import 'element-plus/theme-chalk/display.css' // 引入基于断点的隐藏类
import 'element-plus/dist/index.css'
import './theme/style/common.scss' // 公共css
import App from './App.vue'
import router from './router'
import store from './store'
import { useUserStore } from './store/modules/user'
import { setupRouterGuard } from '@/router/router-guards'
import { accessOperation } from '@/utils/permission'
import 'virtual:svg-icons-register'
// import './tailwind.css';

// 创建 Vue 应用实例
const app = createApp(App)

// 启动应用初始化逻辑
bootstrap()

/**
 * 应用启动初始化函数（必须是异步）
 * 用于配置插件、状态管理、动态路由、权限、挂载应用等
 */
async function bootstrap() {
  // 安装国际化插件
  installI18n(app)

  // 安装页面 <head> 管理插件（例如动态标题、meta 标签等）
  installHead(app)

  // 注册 Vuex 状态管理
  app.use(store)

  // 初始化动态路由（如基于用户权限生成菜单）
  // 即使失败也不影响主流程，所以使用 try/catch 包裹
  try {
    await useUserStore().initDynamicRoute()
  }
  catch (e) {
    console.error('初始化动态路由失败', e)
  }

  // 注册 Vue Router
  app.use(router)

  // 设置路由守卫（如鉴权、跳转拦截等）
  setupRouterGuard(router)

  // 挂载全局方法（如权限操作控制）
  app.config.globalProperties.$accessOperation = accessOperation

  // 挂载应用到页面
  app.mount('#app')
}
