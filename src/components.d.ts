// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AdvancedSearchDialog: typeof import('./components/case-search-list/components/AdvancedSearchDialog.vue')['default']
    CaseDetailDrawer: typeof import('./components/case-detail-drawer/index.vue')['default']
    CaseFormPanel: typeof import('./components/case-detail-drawer/components/CaseFormPanel.vue')['default']
    CaseImportFromFollowup: typeof import('./components/case-search-list/components/CaseImportFromFollowup.vue')['default']
    CaseImportFromLocal: typeof import('./components/case-search-list/components/CaseImportFromLocal.vue')['default']
    CaseImportFromRis: typeof import('./components/case-search-list/components/CaseImportFromRis.vue')['default']
    CaseSearchList: typeof import('./components/case-search-list/index.vue')['default']
    CaseTableSet: typeof import('./components/case-table-set/index.vue')['default']
    CaseViewPanel: typeof import('./components/case-detail-drawer/components/CaseViewPanel.vue')['default']
    CommonDialog: typeof import('./components/common-dialog/index.vue')['default']
    ContextMenu: typeof import('./components/context-menu/index.vue')['default']
    CustomCategoryTree: typeof import('./components/custom-category-tree/index.vue')['default']
    DiseaseCategoryDialog: typeof import('./components/disease-category-dialog/index.vue')['default']
    DiseaseCategoryTree: typeof import('./components/disease-category-tree/index.vue')['default']
    DiseaseOverview: typeof import('./views/department-case-library/teaching-case-library/components/DiseaseOverview.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FavoriteCatalogSelector: typeof import('./components/case-search-list/components/FavoriteCatalogSelector.vue')['default']
    FileUpload: typeof import('./components/file-upload/index.vue')['default']
    MenuContent: typeof import('./components/context-menu/menu-content.vue')['default']
    RichText: typeof import('./components/rich-text/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StudyContent: typeof import('./components/case-detail-drawer/components/StudyContent.vue')['default']
    SvgIcon: typeof import('./components/svg-icon/index.vue')['default']
    UploadPackage: typeof import('./components/case-search-list/components/UploadPackage.vue')['default']
  }
}
