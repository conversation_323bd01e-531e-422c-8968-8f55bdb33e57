export interface SearchParams {
  pageNum: number
  pageSize: number
  [key: string]: any
}

export interface PageData<T> {
  page: number
  total: number
  list: T[]
}

export interface optionItem {
  label: string
  value: number
}

// 公共弹出框的各个参数
export interface LayerInterface {
  show: boolean // 弹出框开启还是关闭
  title: string // 弹出框标题
  showButton?: boolean // 是否显示确认 取消按钮，默认显示
  width?: string // 弹出框宽度，默认30%
  showClose?: boolean // 是否显示右上角关闭按钮，默认显示
  [propName: string]: any // 其他扩展属性
}

// 标准后台回复接口
export interface IResponse {
  state: boolean
  message: string
  errorCode: number
  data: any
}
// 字典项
export interface DictOption {
  dictLabel: string
  dictValue: string
  dictType: string
}

