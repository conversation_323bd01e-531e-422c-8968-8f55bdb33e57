
/** 病例数据定义 */
export interface CaseRecord {
  caseId?: string
  caseName?: string // 病例名称
  caseNo?: string // 病例编号
  caseType?: string // 病例类型
  diseaseId?: number // 疾病ID
  diagnosis?: string // 最终诊断（疾病名称）
  patientId: string // 患者ID
  patientName: string // 患者姓名
  patientBirthDate?: string // 患者出生日期
  patientAge: string // 年龄
  patientSex: string // 性别
  medicalHistory: string // 病史
  caseCategory: string // 病例类别
  selfComplaints?: string // 主诉
  difficulty: string // 难度等级（字典）
  sign?: string // 征象
  caseAnalysis: string // 病例分析
  isExport?: number // 是否已导出（0 否，1 是）
  lmGpId?: number // 联盟分组ID
  sourceType?: number // 来源类型
  followStatus: string // 随访状态(0 待随访 1 已随访)
  modifyUsers?: string // 历史修改人
  tags: string[] // 标签内容列表
  tagInfoList: Tag[] // 标签实体列表
  studyInfoList: Study[] // 病例检查列表（一对多）
  followInfoList: Followup[] // 随访列表
  qualityMatch: string // 定性匹配（0 不符合 1 符合）
  positionMatch: string // 定位匹配（0 不符合 1 符合）
  remark?: string // 备注
  auditInfo?: AuditInfo // 审核信息列表
}

// 检查信息（study）
export interface Study {
  studyUid: string // 检查UID
  accessNumber: string // 检查流水号/影像号
  studyTime: string // 检查时间
  studyNo: string // RIS中的检查号
  patientId: string // 患者ID
  patientName: string // 姓名
  patientSex: string // 性别
  patientBirthDate: string // 出生日期
  patientAge: string // 年龄
  patientType: string // 就诊类型（0 门诊 1 住院 2体检）
  patientTypeLabel?: number // 就诊类型标签
  visitDate: string // 就诊时间
  outPatientNo: string // 门诊号
  inPatientNo: string // 住院号
  physicalSign?: string // 症状/体征
  clinicalDiagnosis?: string // 临床诊断
  studyItemName: string // 检查项目名称
  partName: string // 检查部位
  deviceType: string // 设备
  studyState?: number // 检查状态（0 已登记 1 已检查 2 已报告 3已审核）
  deviceName?: string // 设备名称
  medicalHistory?: string // 病史
  selfReportedSymptom?: string // 患者主诉
  reportDescribe: string // 影像学表现
  reportDiagnose: string // 影像学诊断
  isPositive?: number // 阳性/阴性
  registerTime: string // 登记时间
  reportTime?: string // 报告时间
  reporter: string // 报告医生
  checker: string // 审核医生
  checkTime?: string // 审核时间
  applyNumber?: string // 申请号
  applyDepartment?: string // 申请科室
  applyDoctor?: string // 申请医生
  artificer?: string // 技师
  isPublic?: number // 是否公有（0 否，1 是）
  isOnline?: number // 是否在线导入（0 否，1 是）
  modalities?: string[] // 检查类型
}

// 随访信息
export interface Followup {
  followType: string // 随访类型（0 病理 1 手术 2 超声 3 临床）
  followupResult: string // 随访结果
}

// 标签信息
export interface Tag {
  tagId: number // 标签ID
  tagName: string // 标签名称
}

// 病例审核信息
export interface AuditInfo {
  auditId: number // 审核ID
  auditType?: string // 审核类型：RIS导入，本地导入，随访转其他，个人转科室
  auditComment?: string // 审核意见
  auditedBy: string // 审核人
  auditedTime: string // 审核时间
  caseId: number // 病例ID
  caseTypeId: number // 病例类型ID
  status?: string // 审核状态
}

/**
   * 收藏目录的每一个节点结构
   */
export interface FavoriteCatalogItem {
  catalogId: number // 目录 ID
  catalogName: string // 目录名称
  parentId: number // 父级 ID
  ancestors: number[] // 所有祖先 ID 列表
  orderNum: number // 排序字段
  children?: FavoriteCatalogItem[] // 子节点列表（可选）
}

/**
 * 疾病概述实体
 */
export interface OverviewData {
  diseaseId: number // 疾病ID
  overview: string // 概述
  pathology: string // 病理表现
  clinical: string // 临床表现
  imaging: string // 影像学表现
  diagnosis: string // 诊断要点
  differential: string // 鉴别诊断
  keyframe: string[] // 关键帧
}

/**
 * 病例库项
 */
export interface CaseLibraryItem {
  address: string // 英文路径名或标识
  audit: string // 审核状态（例如 '1' 表示已审核）
  caseTypeId: number // 病例类型 ID（通常是后端分配的长整型 ID）
  caseTypeName: string // 病例类型名称（如“英文病例库”）
}

