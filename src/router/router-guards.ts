import NProgress from 'nprogress' // progress bar
import type { Router } from 'vue-router'
// import { allRoutes } from './routes/index'
import { useUserStore } from '@/store/modules/user'
// import { filterRoutes } from '@/utils/permission'

NProgress.configure({ showSpinner: false }) // NProgress Configuration
const whiteList = ['/login', '/404'] // 不重定向白名单

// 创建所有的路由守卫
export function setupRouterGuard(router: Router) {
  createGlobalGuards(router)
  // 这里后续有需要的话还可以加其他更细化的守卫
}

// 创建全局路由守卫，主要是：页面的登录鉴权
export function createGlobalGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    const hasToken = userStore.isLogin()
    const toPath = to.path

    if (hasToken) {
      if (toPath === '/login') {
        next({ path: '/' }) // 若存在token,访问login重定向为 /（主页）
        NProgress.done()
      }
      else {
        await userStore.initDynamicRoute()
        userStore.updateSelectCaseTypeId(to.meta)
        next()
        // 如果需要权限控制，取消以下注释
        // const hasInfo = userStore.menuList.length
        // if (hasInfo) {
        //   const isAllow = userStore.menuList.some(menuItem => menuItem.path === toPath) // 判断to.path是否在用户拥有的菜单权限列表中
        //   if (!isAllow && !['/', '/404'].includes(toPath))
        //     next('/404')
        //   else
        //     next()
        // }
        // else {
        //   await userStore.getRouteInfo()
        //   const isAllow = userStore.menuList.some(menuItem => menuItem.path === toPath) // 判断to.path是否在用户拥有的菜单权限列表中
        //   // 递归匹配得到每个角色的路由菜单
        //   router.options.routes = filterRoutes([...allRoutes], userStore.menuList)
        //   // 三种情况不拦截路由，1、获取用户信息失败，放过让用户继续刷新页面；2、正常有权限的路由页面；3、根路由或者404页面
        //   if (router.options.routes.length === 2 || isAllow || ['/', '/404'].includes(toPath))
        //     next()
        //   else
        //     next('/404')
        // }
      }
      // 导出一个函数，用于过滤路由
    }
    else {
    // 打印所有路由
      if (whiteList.includes(toPath)) {
        // 定义一个空数组，用于存放过滤后的路由
        next() // 白名单中直接进入
      }
      else {
      // 复制当前路由
        next(`/login?redirect=${toPath}`)
        NProgress.done()
      }
    }
  })

  router.afterEach(() => {
    NProgress.done()
  })

  router.onError((error) => {
    console.log(error, '路由错误')
  })
}
