import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layouts/default.vue'

const pageRouter: RouteRecordRaw = {
  path: '/setting',
  component: Layout,
  redirect: '/user-manage',
  meta: {
    order: 3,
    icon: 'export',
    title: '系统管理',
  },
  children: [
    {
      path: 'user-manage', // 加了 / 就变成绝对路径,不能加/
      component: () => import('@/views/setting/user-manage/index.vue'),
      meta: {
        title: '用户管理',
      },
    },
    {
      path: 'case-library-manage',
      component: () => import('@/views/setting/case-library-manage/index.vue'),
      meta: {
        title: '病例库管理',
      },
    },
    {
      path: 'disease-manage',
      component: () => import('@/views/setting/disease-manage/index.vue'),
      meta: {
        title: '疾病管理',
      },
    },
  ],
}

export default pageRouter
