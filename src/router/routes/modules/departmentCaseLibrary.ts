import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layouts/default.vue'

const pageRouter: RouteRecordRaw = {
  path: '/department',
  name: 'DepartmentCaseLibrary',
  component: Layout,
  redirect: '/department/teaching-case-library', // 默认跳转路由
  meta: {
    order: 1,
    icon: 'export',
    title: '科室病例库',
  },
  children: [
    {
      path: 'teaching-case-library', // 加了 / 就变成绝对路径,不能加/
      component: () => import('@/views/department-case-library/teaching-case-library/index.vue'),
      meta: {
        title: '教学病例库',
        caseTypeId: 1,
        order: 1,
      },
    },
    {
      path: 'followup-case-library',
      component: () => import('@/views/department-case-library/followup-case-library/index.vue'),
      meta: {
        title: '随访病例库',
        caseTypeId: -1,
        order: 2,
      },
    },
    {
      path: 'my-created-case',
      component: () => import('@/views/department-case-library/my-created-case/index.vue'),
      meta: {
        title: '我导入的病例',
        order: 20,
      },
    },
    {
      path: 'my-reviewed-case',
      component: () => import('@/views/department-case-library/my-reviewed-case/index.vue'),
      meta: {
        title: '我审核的病例',
        order: 21,
      },
    },
    // {
    //   path: 'custom-case-library',
    //   component: () => import('@/views/department-case-library/custom-case-library/index.vue'),
    //   meta: {
    //     title: '自定义病例库',
    //   },
    // },

  ],
}

export default pageRouter
