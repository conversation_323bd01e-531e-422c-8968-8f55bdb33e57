import Layout from '@/layouts/default.vue'
import { sortRoutes } from '@/utils/commonLogic'

// 根路由
export const RootRoute = [{
  path: '/',
  name: 'Root',
  component: Layout,
  redirect: '/department', // 默认跳转路由
  hideMenu: true,
  meta: {
    title: '首页',
    noLayout: true,
  },
}]
// 登录路由
export const LoginRoute = {
  path: '/login',
  component: () => import('@/views/login/index.vue'),
  hideMenu: true,
  meta: {
    title: '登录',
    noLayout: true,
    public: true,
  },
}

// import.meta.glob() 直接引入所有的路由模块 Vite独有的功能
const routeModules = (import.meta as any).glob('./modules/**/*.ts', { eager: true })
const routeModuleList: any[] = []
// 加入到路由集合中
Object.keys(routeModules).forEach((key) => {
  const mod = (routeModules as any)[key].default || {}
  const modList = Array.isArray(mod) ? [...mod] : [mod]
  routeModuleList.push(...modList)
})

// 排序整个模块列表及其 children
sortRoutes(routeModuleList)

export const allRoutes = [
  LoginRoute,
  ...RootRoute,
  ...routeModuleList,
]
