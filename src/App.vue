<script setup lang="ts">
import { onBeforeMount } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { Storage } from '@/utils/storage'
import { ACCESS_TOKEN_KEY } from '@/enums/cacheEnum'

const userStore = useUserStore()

useHead({
  title: '病例库',
  meta: [
    { name: 'description', content: 'Opinionated Vite Starter Template' },
    {
      name: 'theme-color',
      content: '#ffffff',
    },
  ],
})

// onBeforeMount(() => {
//   // 监听浏览器本地存储变化，如果token发生变化，说明原token失效，刷新页面，重置token，
//   // 主要用于一个浏览器多个tab页情况，让多个tab页共享一个token,即一个浏览器内多个tab页的登录用户是同一个用户
//   window.addEventListener('storage', () => {
//     if (userStore.token !== Storage.get(ACCESS_TOKEN_KEY, null))
//       location.reload()
//   })
// })
</script>

<template>
  <RouterView />
</template>

<style scoped>
</style>

