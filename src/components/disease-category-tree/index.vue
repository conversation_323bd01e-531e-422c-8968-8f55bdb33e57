<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import { More } from '@element-plus/icons-vue'
import {
  AddDisease,
  DelDisease,
  GetDiseaseTreeList,
  UpdateDisease,
} from '@/api/case-library'
import { CaseFormMode } from '@/enums/common'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'

// 向父组件发出事件，仅叶子节点选中时才 emit
// 如果是取消选中，emit(null)
const emit = defineEmits(['leafSelected'])

// 搜索框绑定值
const filterText = ref('')

// 树组件引用（用于调用 filter 方法）
const treeRef = ref<InstanceType<typeof ElTree>>()

// 默认展开的节点 ID 列表
const defaultExpandedKeys = ref<number[]>([])

// 当前选中的叶子节点 ID
const currentLeafId = ref<number | null>(null)
const caseLibraryStore = useCaseLibraryStore()
// 树组件接收的数据
const treeData = computed(() => caseLibraryStore.diseaseTreeData)

// el-tree 所需的字段映射配置
const defaultProps = {
  children: 'children',
  label: 'label',
}

// 点击节点时触发的处理逻辑
const handleNodeClick = (data: any) => {
  const isLeaf = !data.children || data.children.length === 0
  if (!isLeaf)
    return // 目录节点不处理任何逻辑

  if (currentLeafId.value === data.id) {
    // 再次点击相同叶子节点 -> 取消选中
    currentLeafId.value = null
    treeRef.value?.setCurrentKey(undefined)
    emit('leafSelected', null)
  }
  else {
    // 点击新叶子节点 -> 选中
    currentLeafId.value = data.id
    treeRef.value?.setCurrentKey(data.id)
    emit('leafSelected', data.id)
  }
}

// 监听搜索关键词变化，实时过滤树节点
watch(filterText, (val) => {
  treeRef.value?.filter(val)
})

// 节点过滤方法：根据 nickName 匹配
const filterNode = (value: string, data: any) => {
  if (!value)
    return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

// 递归收集某个节点及其所有子节点的 id（用于默认展开）
function collectAllIds(node: any): number[] {
  const ids: number[] = [node.id]
  if (node.children && node.children.length > 0)
    for (const child of node.children) ids.push(...collectAllIds(child))

  return ids
}

// 判断是否是叶子节点函数
const isLeafNode = (data: any) => !data.children || data.children.length === 0

// region 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref<CaseFormMode>(CaseFormMode.ADD)
const formRef = ref()
const form = reactive({ id: '', diseaseName: '' })

const rules = {
  diseaseName: [{ required: true, message: '请输入疾病名称', trigger: 'blur' }],
}

// 处理操作按钮点击事件
const handleCommand = (command: string, nodeData: any) => {
  switch (command) {
    case CaseFormMode.ADD:
      dialogMode.value = CaseFormMode.ADD
      form.id = nodeData.id
      form.diseaseName = ''
      dialogVisible.value = true
      break
    case CaseFormMode.EDIT:
      dialogMode.value = CaseFormMode.EDIT
      form.id = nodeData.id
      form.diseaseName = nodeData.label
      dialogVisible.value = true
      break
    case CaseFormMode.DELETE:
      handleDelete(nodeData.id)
      break
  }
}

/**
 * 提交表单
 */
function submitForm() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid)
      return

    if (dialogMode.value === CaseFormMode.ADD) {
      const submitInfo = {
        diseaseName: form.diseaseName,
        parentId: form.id,
      }
      await AddDisease(submitInfo)
      ElMessage.success('新增成功')
    }
    else if (dialogMode.value === CaseFormMode.EDIT) {
      const submitInfo = {
        diseaseName: form.diseaseName,
        diseaseId: form.id,
      }
      await UpdateDisease(submitInfo)
      ElMessage.success('编辑成功')
    }

    initData()

    dialogVisible.value = false
  })
}
const { t } = useI18n()

/**
 * 删除分类
 */
async function handleDelete(diseaseId: number) {
  ElMessageBox.confirm(t('message.common.tooltip.delete'), {
    confirmButtonText: t('message.common.confirm'),
    cancelButtonText: t('message.common.cancel'),
    type: 'warning',
  })
    .then(async () => {
      // TODO 调用删除接口
      await DelDisease({ diseaseId })
      ElMessage.success(t('message.common.delSuccess'))
      initData()
    })
    .catch(() => {})
}

/**
 * 重置表单
 */
function resetForm() {
  formRef.value?.resetFields()
}

// # endregion

/**
 * 初始化数据
 */
async function initData() {
  // 获取疾病树数据
  await caseLibraryStore.GetDiseaseTreeData()
}

onMounted(async () => {
  await initData()
  // 若之前有选中的叶子节点，则展开该目录并选中该叶子节点
  if (treeData.value.length > 0 && treeRef.value) {
    if (caseLibraryStore.selectedDiseaseId) {
      defaultExpandedKeys.value = [caseLibraryStore.selectedDiseaseId]
      treeRef.value.setCurrentKey(caseLibraryStore.selectedDiseaseId)
    }
    // 若之前没有选中的目录，则展开第一级目录
    else { defaultExpandedKeys.value = collectAllIds(treeData.value[0]) }
  }
})
</script>

<template>
  <div class="disease-tree-container">
    <div class="search-container">
      <el-input
        v-model="filterText"
        placeholder="搜索目录名称"
        clearable
        size="small"
        class="search-input"
      />
      <el-button type="primary" size="small" @click="handleCommand(CaseFormMode.ADD, { id: 0 })">
        新增
      </el-button>
    </div>
    <!-- 搜索框 -->

    <!-- 横向滚动只控制树组件 -->
    <div class="tree-scroll-inner">
      <!-- 树组件 -->
      <ElTree
        ref="treeRef"
        :data="treeData"
        :props="defaultProps"
        :filter-node-method="filterNode"
        :highlight-current="true"
        node-key="id"
        class="disease-tree"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      >
        <!-- 自定义树节点内容 -->
        <template #default="{ data }">
          <div
            class="tree-node"
            :class="{
              'leaf-node': isLeafNode(data) && data.id === currentLeafId,
            }"
          >
            <!-- 显示疾病名称 -->
            <span>{{ data.label }}</span>

            <!-- 操作按钮（省略号） -->
            <el-dropdown
              trigger="click"
              @command="(command: any) => handleCommand(command, data)"
            >
              <el-icon class="icon-ellipsis" @click.stop>
                <More />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="CaseFormMode.ADD">
                    新增
                  </el-dropdown-item>
                  <el-dropdown-item :command="CaseFormMode.EDIT">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="CaseFormMode.DELETE">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </ElTree>
    </div>
  </div>
  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogMode === CaseFormMode.ADD ? '新增分类' : '编辑分类'"
    width="400px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      :inline="false"
    >
      <el-form-item label="疾病名称" prop="nickName">
        <el-input v-model="form.diseaseName" placeholder="请输入疾病名称" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="submitForm">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.disease-tree-container {
  width: 100%;
}
.search-container {
  display: flex;
  align-items: center;
  padding: 0 10px;
}
/* 横向滚动条只出现在树这块 */
.tree-scroll-inner {
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
}

.search-input {
  width: 90%;
  /* 上 右 下 左，auto 实现水平居中 */
  margin: 10px auto 8px auto;
  /* 让 margin: auto 生效 */
  display: block;
}

.disease-tree {
  width: fit-content;
  min-width: 100%;
  height: calc(100vh - 120px);
  overflow: auto;
}

/* 每个节点的容器 */
.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 省略号图标样式 */
.icon-ellipsis {
  cursor: pointer;
  padding: 0 6px;
  color: #999;
}

/* 仅叶子节点高亮 */
//TODO 待UI给样式
// .tree-node.leaf-node {

//   // background-color: #f0f9eb;
// }
</style>
