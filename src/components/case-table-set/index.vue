<script lang="ts" setup>
import { cloneDeep } from 'lodash'
import { accessOperation } from '@/utils/permission'
import { defaultAllColumns } from '@/enums/system'
const props = defineProps({
  headerList: {
    type: Array,
    default: () => [],
  },
})
const emit = defineEmits(['saveSet', 'getSet'])
const headerSetList: any = ref([])
// const getTableSet = async () => {
//   const copyHeader = cloneDeep(caseHeaderSetting)
//   // 将默认数据与额外的数据合并
//   copyHeader.push(...props.headerList)

//   headerSetList.value = copyHeader
//   resourceStore.getTableSet().then(() => {
//     headerSetList.value = copyHeader.map((defaultItem: any) => {
//       const findItem = resourceStore.tableSetList.find((saveItem: any) => saveItem.value === defaultItem.value)
//       if (findItem)
//         defaultItem.state = findItem.state

//       return defaultItem
//     })
//     emit('getSet', cloneDeep(headerSetList.value))
//   })
// }

const tableHeaderList = ref(defaultAllColumns)
onBeforeMount(() => {
  // getTableSet()
})
const saveSet = async () => {
  // await resourceStore.saveTableSet({ setData: JSON.stringify(headerSetList.value), type: 1 })
  // getTableSet()
}
</script>

<template>
  <el-popover placement="bottom" width="auto" trigger="click">
    <template #reference>
      <el-button class="case-table-set" type="primary">
        表格设置
      </el-button>
    </template>
    <div style="display:flex;flex-direction:column;">
      <el-checkbox v-for="item in tableHeaderList" :key="item.label" v-model="item.state" :label="item.label" />
    </div>
    <el-button type="primary" style="float: right;" @click="saveSet">
      保存
    </el-button>
  </el-popover>
</template>

<style scoped lang="scss">
.case-table-set {
  float: right;
}
:deep(.el-popper) {
  max-width: 800px !important;
}
</style>
