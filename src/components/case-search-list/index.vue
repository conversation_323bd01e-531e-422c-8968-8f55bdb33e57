<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElLoading, ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { Check, Close, Delete, Star, View } from '@element-plus/icons-vue'
import type { LayerInterface, SearchParams, optionItem } from '@/types/common'
import { validateCommonField, validateCommonName, validateIsEmpty } from '@/utils/validate'
import { createDefaultCaseRecord, getOptionLabel } from '@/utils/commonLogic'
import { CaseFormMode, RecentDateTypes, pageSizeOptions, shortcuts } from '@/enums/common'
import type { CaseRecord, Study } from '@/types/caseLibrary'
import type { AddCaseSourceType } from '@/enums/caseLibrary'
import { CaseLibraryType, FollowStatus, FollowStatusOptions, FollowTypeOptions, ReviewStatus, ReviewStatusOptions } from '@/enums/caseLibrary'
import type { ColumnConfig } from '@/enums/system'
import { defaultAllColumns } from '@/enums/system'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { AdvanceGetDepartmentCase, GetDepartmentCase, GetMyCreatedCase, GetMyReviewedCase, GetPersonalCase, delCaseFromDept, reviewCase } from '@/api/case-library'
import { useUserStore } from '@/store/modules/user'

const props = defineProps({
  followupStatus: {
    type: String,
    default: FollowStatus.Pending, // 👈 随访状态
  },
  caseLibraryType: {
    type: Number,
    default: CaseLibraryType.Teaching, // 👈 默认是教学病例库
  },
})

const { t } = useI18n()
const searchParameter = ref<SearchParams>({
  pageNum: 1,
  pageSize: 10,
  caseTypeId: '', // 病例库类型
  modality: '', // 检查类型
  followStatus: '', // 随访状态
  followType: '', // 随访类型
  keyword: '', // 关键字
  status: '', // 审核状态
  difficulty: '', // 难度
})

const caseLibraryStore = useCaseLibraryStore()
const userStore = useUserStore()
const listTotal = ref(0)
const timeSpan = ref() // 默认空
const defaultTime = ref<[Date, Date]>([
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
])

const tableData = ref<CaseRecord[]>([])

/**
 *  刷新列表
 */
// 防抖搜索处理函数，延迟触发接口调用
const handleSearch = debounce(300, async () => {
  if (timeSpan.value) {
    searchParameter.value.studyStartTime = timeSpan.value[0]
    searchParameter.value.studyEndTime = timeSpan.value[1]
  }
  else {
    searchParameter.value.studyStartTime = ''
    searchParameter.value.studyEndTime = ''
  }
  if (userStore.selectCaseTypeId)
    searchParameter.value.caseTypeId = userStore.selectCaseTypeId

  if (props.caseLibraryType === CaseLibraryType.Personal && caseLibraryStore.selectedCatalogId)
    searchParameter.value.catalogId = caseLibraryStore.selectedCatalogId

  if (props.caseLibraryType === CaseLibraryType.Teaching && caseLibraryStore.selectedDiseaseId)
    searchParameter.value.diseaseId = caseLibraryStore.selectedDiseaseId

  const apiFn = getApiByCaseType(props.caseLibraryType)
  const { list, total } = await apiFn(searchParameter.value)
  if (list) {
    tableData.value = list
    listTotal.value = total
  }
  else {
    tableData.value = []
    listTotal.value = 0
  }
})

/**
 * 根据不同的病例库类型获取不用的调用方法
 * @param type 病例库类型
 */
function getApiByCaseType(type: CaseLibraryType) {
  switch (type) {
    case CaseLibraryType.MyCreated:
      return GetMyCreatedCase
    case CaseLibraryType.MyReviewed:
      return GetMyReviewedCase
    case CaseLibraryType.Personal:
      return GetPersonalCase
    default:
      return GetDepartmentCase
  }
}

// 重置
const handleReset = () => {
  searchParameter.value.nickName = ''
  handleSearch()
}

const searchDialogRef = ref()

const openDialog = () => {
  searchDialogRef.value?.open()
}

/**
 * 根据病例库类型返回对应的默认表格列
 * @param type 病例库类型
 */
const visibleColumns = computed(() =>
  defaultAllColumns.filter((col) => {
    if (!col.state)
      return false

    if (col.showInTypes)
      return col.showInTypes.includes(props.caseLibraryType)

    return true
  }),
)

/** 格式化表格展示数据 */

const formattedTableData = computed(() => {
  const studyFields = defaultAllColumns.filter(c => c.isFromFirstStudy).map(c => c.value)

  return tableData.value.map((caseItem) => {
    const row: Record<string, any> = { ...caseItem }

    const study0 = caseItem.studyInfoList?.[0]

    studyFields.forEach((field) => {
      row[field] = study0?.[field as keyof Study] ?? ''
    })

    return row
  })
})
// #region  病例详情&编辑
// 病例详情抽屉组件
const caseDetailDrawerRef = ref()
// 病例详情抽屉打开状态
const caseDetailDrawerVisible = ref(false)
const caseDetailMode = ref(CaseFormMode.VIEW)
function handleView(row: any) {
  caseDetailMode.value = CaseFormMode.VIEW
  caseDetailDrawerRef.value?.openDrawer(row)
}

/**
 * 审核病例
 * @param row 病例
 */
async function handleReviewCase(row: CaseRecord, status: ReviewStatus) {
  const submitInfo = {
    auditId: row.auditInfo?.auditId,
    caseId: row.caseId,
    status,
  }

  await reviewCase(submitInfo)
  handleSearch()
}

/**
 * 删除病例
 * @param row 病例
 */
async function handleDelete(row: CaseRecord) {
  ElMessageBox.confirm(t('message.common.tooltip.delete'), {
    confirmButtonText: t('message.common.confirm'),
    cancelButtonText: t('message.common.cancel'),
    type: 'warning',
  })
    .then(async () => {
      console.log(row)
      if (!userStore.selectCaseTypeId && !row.auditInfo?.caseTypeId)
        throw new Error('病例库类型为空:caseTypeId')
      const submitInfo = {
        caseId: row.caseId,
        caseTypeId: userStore.selectCaseTypeId ?? row.auditInfo?.caseTypeId,
      }
      await delCaseFromDept(submitInfo)
      ElMessage.success(t('message.common.delSuccess'))
      handleSearch()
    })
    .catch((err) => {
      throw new Error(err)
    })
}
// ris导入抽屉组件
const risImportDrawerRef = ref()

/**
 * 打开RIS导入页面
 */
function openRISImportDrawer() {
  caseDetailDrawerRef.value?.closeDrawer()
  risImportDrawerRef.value.openDrawer()
}

/**
 * 导入后处理
 */
function handleImport(cases: Study[], sourceType?: AddCaseSourceType) {
  if (cases.length === 0)
    throw new Error('没有导入病例')

  const record: CaseRecord = createDefaultCaseRecord()
  record.studyInfoList = cases
  // 从检查里面提取基本信息
  if (cases[0].medicalHistory)
    record.medicalHistory = cases[0].medicalHistory
  if (cases[0].selfReportedSymptom)
    record.selfComplaints = cases[0].selfReportedSymptom
  if (cases[0].reportDescribe)
    record.sign = cases[0].reportDescribe

  caseDetailMode.value = CaseFormMode.ADD
  caseDetailDrawerRef.value?.openDrawer(record, sourceType)
}
// 随访库导入抽屉组件
const followupImportDrawerRef = ref()

/**
 * 打开随访库导入页面
 */
function openFollowupImportDrawer() {
  caseDetailDrawerRef.value?.closeDrawer()
  followupImportDrawerRef.value.openDrawer()
}

// 本地导入抽屉组件
const localImportDrawerRef = ref()

/**
 * 打开本地导入页面
 */
function openLocalImportDrawer() {
  // console.log(getCaseTypeIdFromRoute())
  caseDetailDrawerRef.value?.closeDrawer()
  localImportDrawerRef.value.openDrawer()
}

/**
 * 处理收藏目录选中回调
 */
function handleFavoriteSelect(node: any) {
  console.log('你选中了目录：', node)
}

function beforeUpload() {
  const loading = ElLoading.service({
    lock: true,
    text: '导入中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  setTimeout(() => {
    loading.close()
  }, 1000)
}
// 上传文件成功后的处理
function handleSuccess(response: any) {
  if (response) {
    if (!response.state) {
      ElMessage.error(response.message)
      return
    }
    else {
      ElMessage.success('导入成功')
    }
    console.log(response)
  }
}

// #endregion

// 是否为随访病例库操作
const isFollowLibrary = computed(() => props.caseLibraryType === CaseLibraryType.FollowUp)
//  我创建的 or 我审核的
const isMyCaseLibrary = computed(() =>
  [CaseLibraryType.MyCreated, CaseLibraryType.MyReviewed].includes(props.caseLibraryType),
)

/**
 * 表单提交成功后，刷新表格数据
 */
const refreshTableData = () => {
  handleSearch()
  localImportDrawerRef.value?.handleSearch()
  followupImportDrawerRef.value?.handleSearch()
  risImportDrawerRef.value?.handleSearch()
}

// 是否为我审核的病例库操作
const isMyReviewLibrary = computed(() => props.caseLibraryType === CaseLibraryType.MyReviewed)
// ✅ 判断是否显示“通过/驳回”审核按钮：
// 仅当当前库类型是“我审核的”，且该行状态为“待审核”
const isPendingReview = (row: any) => {
  return isMyReviewLibrary.value && row.auditInfo?.status === ReviewStatus.Pending
}

// 定义在 setup 或 methods 中
function getTagType(status?: string) {
  if (status === ReviewStatus.Pending)
    return 'info'
  if (status === ReviewStatus.Approved)
    return 'success'
  if (status === ReviewStatus.Rejected)
    return 'danger'
  return 'info'
}

watch(() => props.followupStatus, (newVal) => {
  // 监听随访状态变化
  console.log('props.followupStatus', newVal)
})

watch(
  () => caseLibraryStore.selectedDiseaseId,
  (_) => {
    handleSearch()
  },
)

watch(
  () => caseLibraryStore.selectedCatalogId,
  (_) => {
    handleSearch()
  },
)

onMounted(() => {
  handleSearch()
})
</script>

<template>
  <div class="main-content" :class="{ 'view-status': caseDetailDrawerVisible }">
    <el-card class="search-card">
      <el-form ref="searchFormRef" :inline="true">
        <el-form-item v-if="isMyCaseLibrary" label="审核状态">
          <el-select v-model="searchParameter.status" style="width: 120px" placeholder="请选择">
            <el-option
              v-for="option in ReviewStatusOptions" :key="option.value" :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查日期">
          <ElDatePicker
            v-model="timeSpan" value-format=DateUtil.DEFAULT_PATTERN :default-time="defaultTime"
            style="width: 300px;" type="daterange" range-separator="至" :shortcuts="shortcuts" start-placeholder="请选择日期"
            end-placeholder="请选择日期" :editable="false" @change="handleSearch"
          />
        </el-form-item>

        <el-form-item label="关键字">
          <el-input
            v-model.trim="searchParameter.keyword" style="width: 300px;" maxlength="50"
            placeholder="患者姓名、影像号、住院号、门诊号等" @input="handleSearch"
          />
        </el-form-item>
        <!-- 检查类型 -->
        <el-form-item label="检查类型">
          <el-select v-model="searchParameter.modality" placeholder="请选择" clearable style="width: 120px" @change="handleSearch">
            <el-option v-for="item in caseLibraryStore.deviceTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictLabel" />
          </el-select>
        </el-form-item>

        <!-- 病例难度 -->
        <el-form-item v-if="caseLibraryType === CaseLibraryType.Teaching" label="病例难度">
          <el-select v-model="searchParameter.difficulty" placeholder="请选择" clearable style="width: 120px">
            <el-option v-for="item in caseLibraryStore.caseDifficultyOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
          </el-select>
        </el-form-item>

        <!-- 随访类型 -->
        <el-form-item v-if="!isMyCaseLibrary" label="随访类型">
          <el-select v-model="searchParameter.followType" placeholder="请选择" clearable style="width: 120px">
            <el-option v-for="item in caseLibraryStore.followTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
          </el-select>
        </el-form-item>
        <div v-if="!isMyCaseLibrary" style="float: right;">
          <el-button type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button type="primary" @click="openDialog">
            高级检索
          </el-button>
        </div>
      </el-form>
    </el-card>
    <el-card class="data-card">
      <div v-if="!isMyCaseLibrary" class="option-buttons">
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button type="primary">
              导入病例
            </el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="openRISImportDrawer">
                RIS导入
              </el-dropdown-item>
              <el-dropdown-item v-if="!isFollowLibrary" @click="openFollowupImportDrawer">
                随访库导入
              </el-dropdown-item>
              <el-dropdown-item @click="openLocalImportDrawer">
                本地导入
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" style="margin-left: 10px;">
          导出病例
        </el-button>
        <FileUpload
          v-if="caseLibraryType === CaseLibraryType.FollowUp" class="import-button" button-text="病理导入"
          :limit-num="1000" accept=".xls,.xlsx" :upload-data="{ fileType: 'file' }" :show-file-list="false"
          :return-suc-all-res="true" :multiple="false" @before-upload="beforeUpload" @handle-success="handleSuccess"
        />
        <case-table-set />
      </div>
      <div class="data-table">
        <el-table
          :data="formattedTableData" stripe height="100%"
          :header-cell-style="{ background: '#eef0ef', color: '#7d827f' }" @row-click="handleView"
        >
          <!-- 多选框列 -->
          <el-table-column type="selection" width="50" />
          <!-- 动态列 -->
          <el-table-column
            v-for="col in visibleColumns" :key="col.value" :label="col.label" :prop="col.value"
            :width="col.width" :align="col.align || 'left'" :min-width="!col.width ? '120' : undefined"
          >
            <!-- 自定义插槽列 -->
            <template v-if="col.slot" #default="scope">
              <!-- 标签字段（数组） -->
              <template v-if="col.value === 'tags'">
                <el-tag v-for="tag in scope.row.tags" :key="tag.tagId" size="small" class="mr-1">
                  {{ tag.tagName }}
                </el-tag>
              </template>
              <!-- 检查类型 -->
              <template v-else-if="col.value === 'modalities'">
                {{ scope.row.modalities ? scope.row.modalities.join(',') : '' }}
              </template>
              <!-- 患者性别 -->
              <template v-else-if="col.value === 'patientSex'">
                {{ formatSex(scope.row.patientSex) }}
              </template>
              <!-- 就诊类型 -->
              <template v-else-if="col.value === 'patientType'">
                {{ getDictOptionLabel(caseLibraryStore.patientTypeOptions, scope.row.patientType) }}
              </template>
              <!-- 随访类型 -->
              <template v-else-if="col.value === 'followType'">
                <el-tag v-for="(type, index) in scope.row.followInfoList" :key="index" size="small" class="mr-1">
                  {{ getDictOptionLabel(caseLibraryStore.followTypeOptions, type.followType) }}
                </el-tag>
              </template>
              <!-- 随访状态显示为 Tag -->
              <template v-else-if="col.value === 'followStatus'">
                <el-tag
                  v-if="scope.row.followStatus"
                  :type="scope.row.followStatus === FollowStatus.Completed ? 'success' : 'info'" size="small"
                >
                  {{ getOptionLabel(FollowStatusOptions, scope.row.followStatus) }}
                </el-tag>
              </template>

              <!-- 审核状态 -->
              <!-- 审核状态字段 -->
              <template v-else-if="col.value === 'auditStatus'">
                <el-tag
                  v-if="scope.row.auditInfo"
                  :type="getTagType(scope.row.auditInfo?.status)"
                  size="small"
                >
                  {{ getOptionLabel(ReviewStatusOptions, scope.row.auditInfo?.status) }}
                </el-tag>
              </template>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column label="操作" width="140" align="center">
            <template #default="scope">
              <div class="operator-button" style="display: flex; gap: 12px; justify-content: center;">
                <!-- 使用收藏目录选择组件 -->
                <FavoriteCatalogSelector v-if="!isMyCaseLibrary" @select="handleFavoriteSelect" />

                <!-- 编辑 -->
                <el-tooltip effect="light" content="查看详情" placement="top">
                  <el-icon @click.stop="handleView(scope.row)">
                    <View />
                  </el-icon>
                </el-tooltip>
                <!-- ✅ 审核通过按钮：仅在“我审核的病例”中，且状态为待审核（Pending）时显示 -->
                <el-tooltip
                  v-if="isPendingReview(scope.row)" effect="light" content="通过"
                  placement="top"
                >
                  <el-icon @click.stop="handleReviewCase(scope.row, ReviewStatus.Approved)">
                    <Check />
                  </el-icon>
                </el-tooltip>
                <!-- ❌ 审核驳回按钮：仅在“我审核的病例”中，且状态为待审核（Pending）时显示 -->
                <el-tooltip
                  v-if="isPendingReview(scope.row)" effect="light" content="驳回"
                  placement="top"
                >
                  <el-icon @click.stop="handleReviewCase(scope.row, ReviewStatus.Rejected)">
                    <Close />
                  </el-icon>
                </el-tooltip>
                <!-- 🗑️ 删除按钮：我审核的病例 不能出现 -->
                <el-tooltip
                  v-if="caseLibraryType !== CaseLibraryType.MyReviewed" effect="light" content="删除"
                  placement="top"
                >
                  <el-icon @click.stop="handleDelete(scope.row)">
                    <Delete />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        class="footer-pagination" :page-size="searchParameter.pageSize" :page-sizes="pageSizeOptions"
        background layout="total, sizes, prev, pager, next" :total="listTotal"
        @current-change="searchParameter.pageNum = $event; handleSearch();"
        @size-change="searchParameter.pageSize = $event; handleSearch();"
      />
    </el-card>
  </div>
  <AdvancedSearchDialog ref="searchDialogRef" />
  <!-- 弹出详情组件 -->
  <CaseDetailDrawer
    ref="caseDetailDrawerRef" v-model:drawer-visible="caseDetailDrawerVisible" :mode="caseDetailMode"
    :case-library-type="caseLibraryType" @submit-success="refreshTableData"
  />
  <CaseImportFromRis ref="risImportDrawerRef" @import-case="handleImport" @submit-success="refreshTableData" />
  <CaseImportFromFollowup ref="followupImportDrawerRef" @import-case="handleImport" @submit-success="refreshTableData" />
  <CaseImportFromLocal ref="localImportDrawerRef" @import-case="handleImport" />
</template>

<style scoped lang="scss">
:deep(.el-select__tags) {
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
}

.el-icon {
  cursor: pointer;
}

.view-status {
  width: calc(100% - 550px);
}

.import-button {
  display: inline-block;
}
</style>
