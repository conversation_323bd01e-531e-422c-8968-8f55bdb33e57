<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { Plus } from '@element-plus/icons-vue'
import type { CaseRecord, Study } from '@/types/caseLibrary'
import type { SearchParams } from '@/types/common'
import { pageSizeOptions, shortcuts } from '@/enums/common'
import { GetDepartmentCase, addCaseFromFollow, addCaseFromRIS } from '@/api/case-library'
import { defaultAllColumns } from '@/enums/system'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { CaseLibraryType, FollowStatus, FollowStatusOptions } from '@/enums/caseLibrary'
import { useUserStore } from '@/store/modules/user'

const emit = defineEmits(['importCase', 'submitSuccess'])
const searchParameter = ref<SearchParams>({
  pageNum: 1,
  pageSize: 10,
  keyword: '', // 关键字
})
const listTotal = ref(0)
const timeSpan = ref() // 默认空
const defaultTime = ref<[Date, Date]>([
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
])

/**
 *  刷新列表
 */
// 防抖搜索处理函数，延迟触发接口调用
const handleSearch = debounce(300, async () => {
  if (timeSpan.value) {
    searchParameter.value.studyStartTime = timeSpan.value[0]
    searchParameter.value.studyEndTime = timeSpan.value[1]
  }
  else {
    searchParameter.value.studyStartTime = ''
    searchParameter.value.studyEndTime = ''
  }
  searchParameter.value.caseTypeId = -1

  const { list, total } = await GetDepartmentCase(searchParameter.value)
  if (list) {
    tableData.value = list
    listTotal.value = total
  }
})

// 控制抽屉显示
const visible = ref(false)

// 加载状态
const loading = ref(false)

// 表格数据列表
const tableData = ref<CaseRecord[]>([])

// 被勾选的记录
const selected = ref<CaseRecord[]>([])

/**
 * 当用户选择病例时触发
 */
function handleSelectionChange(val: CaseRecord[]) {
  selected.value = val
}

const formattedTableData = computed(() => {
  const studyFields = defaultAllColumns.filter(c => c.isFromFirstStudy).map(c => c.value)

  return tableData.value.map((caseItem: any) => {
    const row: Record<string, any> = { ...caseItem }

    const study0 = caseItem.studyInfoList?.[0]

    studyFields.forEach((field) => {
      row[field] = study0?.[field as keyof Study] ?? ''
    })

    return row
  })
})

/**
 * 导入操作（这里为模拟）
 */
function confirmImport(row: CaseRecord | null) {
  if (row)
    selected.value = [row]

  handleImportCase(useCaseLibraryStore().selectedDiseaseId)
}
const userStore = useUserStore()
/**
 * 快速导入RIS病例，仅选择疾病分类即可
 */
async function handleImportCase(diseaseId: number | null) {
  if (!diseaseId && userStore.selectCaseTypeId === CaseLibraryType.Teaching) {
    dialogVisible.value = true
    return
  }
  if (selected.value.length === 0)
    throw new Error('请选择要导入的病例')

  const submitInfo = {
    caseId: selected.value[0].caseId,
    diseaseId,
    caseTypeId: userStore.selectCaseTypeId,
  }
  await addCaseFromFollow(submitInfo)
  emit('submitSuccess')
  ElMessage.success('导入成功')
}

// 控制疾病分类选择框显示或关闭
const dialogVisible = ref(false)

/**
 * 打开抽屉
 */
function openDrawer() {
  visible.value = true
  handleSearch()
}

// 将 openDrawer 方法暴露给父组件使用
defineExpose({
  openDrawer,
  handleSearch,
})
</script>

<template>
  <div class="common-drawer">
    <!-- RIS导入弹窗 -->
    <el-drawer v-model="visible" title="从随访库导入" size="60%" destroy-on-close>
      <div class="main-content">
        <el-card class="search-card" shadow="never">
          <!-- 搜索栏 -->
          <el-form ref="searchFormRef" :inline="true">
            <el-form-item label="检查日期">
              <ElDatePicker
                v-model="timeSpan" value-format=DateUtil.DEFAULT_PATTERN
                :default-time="defaultTime" style="width: 300px;" type="daterange"
                range-separator="至" :shortcuts="shortcuts" start-placeholder="请选择日期" end-placeholder="请选择日期"
                :editable="false"
                @change="handleSearch"
              />
            </el-form-item>

            <el-form-item label="关键字">
              <el-input
                v-model.trim="searchParameter.keyword" style="width: 300px;" maxlength="50" placeholder="患者姓名、影像号、住院号、门诊号等"
                @input="handleSearch"
              />
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="data-card" shadow="never">
          <div class="data-table">
            <el-table
              v-loading="loading"
              :data="formattedTableData"
              stripe
              height="100%"
              :header-cell-style="{ background: '#eef0ef', color: '#7d827f' }"
              @selection-change="handleSelectionChange"
            >
              <!-- 多选框列 -->
              <!-- <el-table-column type="selection" width="50" /> -->
              <el-table-column prop="patientName" label="姓名" />
              <el-table-column prop="patientAge" label="年龄" />

              <el-table-column prop="patientSex" label="性别">
                <template #default="{ row }">
                  {{ formatSex(row.patientSex) }}
                </template>
              </el-table-column>
              <el-table-column prop="studyNo" label="检查号" />

              <el-table-column prop="modalities" label="检查类型">
                <template #default="{ row }">
                  {{ row.modalities ? row.modalities.join(',') : '' }}
                </template>
              </el-table-column>
              <el-table-column prop="studyTime" label="检查时间" width="160" />
              <el-table-column prop="followType" label="随访类型">
                <template #default="{ row }">
                  <el-tag v-for="(type, index) in row.followInfoList" :key="index" size="small" class="mr-1">
                    {{ getDictOptionLabel(useCaseLibraryStore().followTypeOptions, type.followType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="followType" label="随访状态">
                <template #default="{ row }">
                  <el-tag :type="row.followStatus === FollowStatus.Completed ? 'success' : 'info'" size="small">
                    {{ getOptionLabel(FollowStatusOptions, row.followStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <!-- 操作 -->
              <el-table-column label="操作" width="140" align="center">
                <template #default="scope">
                  <div class="operator-button" style="display: flex; gap: 12px; justify-content: center;">
                    <el-tooltip effect="light" content="导入病例" placement="top">
                      <el-icon @click="confirmImport(scope.row)">
                        <Plus />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <el-pagination
            class="footer-pagination" :page-size="searchParameter.pageSize" :page-sizes="pageSizeOptions"
            background layout="total, sizes, prev, pager, next" :total="listTotal"
            @current-change="searchParameter.pageNum = $event; handleSearch();"
            @size-change="searchParameter.pageSize = $event; handleSearch();"
          />
        </el-card>
      </div>
      <!-- 底部操作按钮 -->
      <!-- <template #footer>
        <div class="drawer-footer">
          <el-button @click="visible = false">
            取消
          </el-button>
          <el-button type="primary" :disabled="!selected.length" @click="confirmImport">
            批量导入
          </el-button>
        </div>
      </template> -->
    </el-drawer>
  </div>
  <disease-category-dialog v-model="dialogVisible" @submit="handleImportCase" />
</template>

<style scoped>
.el-card{
  margin-bottom: 0px;
  border: 0px;
}

/* 底部操作按钮区域 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  gap: 10px;
}
</style>
