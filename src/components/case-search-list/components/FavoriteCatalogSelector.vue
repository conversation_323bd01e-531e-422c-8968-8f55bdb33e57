<script setup lang="ts">
import { computed, ref } from 'vue'
import { Star } from '@element-plus/icons-vue'
import type { FavoriteCatalogItem } from '@/types/caseLibrary'
import { defaultCatalogData } from '@/utils/mockData'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'

/**
   * Props: 允许传入自定义数据（可选）
   */
const props = defineProps<{
  data?: FavoriteCatalogItem[]
}>()

/**
   * Emits: 选中叶子节点时触发事件
   */
const emit = defineEmits<{
  (e: 'select', node: FavoriteCatalogItem): void
}>()

// 控制 Popover 的显隐
const visible = ref(false)

/**
   * 实际展示的数据来源：
   * 如果父组件传入 data 则使用传入的；
   * 否则使用默认模拟数据
   */
const treeData = computed(() => useCaseLibraryStore().customCategoryTreeData)

const referenceEl = ref<HTMLElement>()

/**
   * 点击树节点的处理逻辑：
   * - 仅当节点是叶子节点（无子节点）时才允许选中
   * - 发出 select 事件，并关闭 Popover
   */
function handleNodeClick(node: FavoriteCatalogItem) {
  if (!node.children || node.children.length === 0) {
    emit('select', node)
    visible.value = false
  }
}
</script>

<template>
  <!-- 弹出层组件，点击图标后显示收藏目录树 -->
  <el-popover
    :virtual-ref="referenceEl"
    virtual-triggering
    width="300"
    trigger="click"
    placement="bottom-start"
  >
    <!-- 弹出内容：收藏目录树 -->
    <el-tree
      :data="treeData"
      node-key="catalogId"
      default-expand-all
      highlight-current
      @node-click="handleNodeClick"
    />
  </el-popover>
  <!-- ⭐ 实体触发器元素，绑定到 Popover -->
  <el-tooltip effect="light" content="收藏目录" placement="top">
    <el-icon
      ref="referenceEl"
      style="cursor: pointer"
      @click.stop="visible = true"
    >
      <Star />
    </el-icon>
  </el-tooltip>
</template>
