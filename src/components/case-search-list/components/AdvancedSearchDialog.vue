<script setup lang="ts">
import { ref } from 'vue'
import type { LayerInterface } from '@/types/common'

// interface SearchForm {
//   patientName: string
//   imageId: string
//   inpatientId: string
//   outpatientId: string
//   examItems: string[]
//   tags: string[]
//   dateRange: [string, string] | []
// }

const visible = ref(false)

const form = ref({
  patientName: '',
  imageId: '',
  inpatientId: '',
  outpatientId: '',
  examItems: [],
  tags: [],
  dateRange: [],
  // 新增字段
  examType: '',
  caseDifficulty: '',
  gender: '',
  followUpType: '',
})

// 模拟选项，可根据实际接口替换
const examItemOptions = [
  { label: '头部CT', value: 'head-ct' },
  { label: '胸部X光', value: 'chest-xray' },
  { label: '腹部MRI', value: 'abdomen-mri' },
]

const tagOptions = [
  { label: '重点病例', value: 'important' },
  { label: '教学案例', value: 'teaching' },
  { label: '疑难病例', value: 'difficult' },
]

const handleSearch = () => {
  console.log('高级检索表单数据:', form.value)
  // emit 或调用 API 执行搜索
  dialogInfo.show = false
}

const handleClose = () => {
  dialogInfo.show = false
}

const dialogInfo = reactive<LayerInterface>({
  show: false,
  title: '高级检索',
})

// 可暴露给父组件调用
defineExpose({
  open: () => { dialogInfo.show = true },
})
</script>

<template>
  <common-dialog ref="dialogDom" :dialog-info="dialogInfo" @confirm="handleSearch" @close="handleClose">
    <el-form
      :model="form"
      label-width="100px"
      label-position="right"
      style="margin-right: 5rem"
    >
      <el-form-item label="姓名">
        <el-input v-model="form.patientName" clearable />
      </el-form-item>

      <el-form-item label="性别">
        <el-select v-model="form.gender" clearable placeholder="请选择性别">
          <el-option label="男" value="男" />
          <el-option label="女" value="女" />
        </el-select>
      </el-form-item>

      <el-form-item label="影像号">
        <el-input v-model="form.imageId" clearable />
      </el-form-item>

      <el-form-item label="住院号">
        <el-input v-model="form.inpatientId" clearable />
      </el-form-item>

      <el-form-item label="门诊号">
        <el-input v-model="form.outpatientId" clearable />
      </el-form-item>

      <el-form-item label="检查类型">
        <el-select v-model="form.examType" clearable placeholder="请选择检查类型">
          <el-option label="CT" value="CT" />
          <el-option label="MRI" value="MRI" />
          <el-option label="X光" value="X光" />
          <el-option label="超声" value="超声" />
        </el-select>
      </el-form-item>

      <el-form-item label="检查项目">
        <el-select v-model="form.examItems" multiple clearable placeholder="请选择检查项目">
          <el-option
            v-for="item in examItemOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="检查日期">
        <el-date-picker
          v-model="form.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          unlink-panels
          clearable
        />
      </el-form-item>

      <el-form-item label="病例难度">
        <el-select v-model="form.caseDifficulty" clearable placeholder="请选择病例难度">
          <el-option label="简单" value="简单" />
          <el-option label="一般" value="一般" />
          <el-option label="困难" value="困难" />
        </el-select>
      </el-form-item>

      <el-form-item label="随访类型">
        <el-select v-model="form.followUpType" clearable placeholder="请选择随访类型">
          <el-option label="病理" value="病理" />
          <el-option label="手术" value="手术" />
          <el-option label="超声" value="超声" />
        </el-select>
      </el-form-item>

      <el-form-item label="标签">
        <el-select v-model="form.tags" multiple clearable placeholder="请选择标签">
          <el-option
            v-for="tag in tagOptions"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

  <style scoped lang='scss'>
  .el-form-item {
    margin-bottom: 16px;
  }
  </style>
