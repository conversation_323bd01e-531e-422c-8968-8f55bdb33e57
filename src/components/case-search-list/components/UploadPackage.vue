<script setup lang="ts">
import { ElMessage, type UploadFile, type UploadFiles } from 'element-plus'
import { DocumentAdd } from '@element-plus/icons-vue'
import { Storage } from '@/utils/storage'
import { uploadLocalCaseToCos } from '@/utils/tencent-upload'
import { ACCESS_TOKEN_KEY } from '@/enums/cacheEnum'

const emit = defineEmits(['onSuccess', 'handleUpload'])

const headerObj = {
  Authorization: Storage.get(ACCESS_TOKEN_KEY, null),
  platId: Storage.get('SYS_CONFIG')?.platId || 0,
}

const beforeUploadPack = async (
  uploadFile: UploadFile,
  uploadFiles: UploadFiles,
) => {
  const fileSuffix = uploadFile.name.substring(
    uploadFile.name.lastIndexOf('.') + 1,
  ).toLowerCase()
  if (fileSuffix !== 'zip') {
    ElMessage({
      message: '请选择zip格式的压缩包',
      type: 'error',
    })
    return false
  }
  const isLt = uploadFile.size ? uploadFile.size / 1024 / 1024 : 0
  if (isLt === 0) {
    ElMessage({
      message: '压缩包大小不能为0字节',
      type: 'error',
    })
    return false
  }
  else if (isLt > 2048) {
    ElMessage({
      message: '压缩包大小不能超过2GB',
      type: 'error',
    })
    return false
  }
}
function handlePackUploadSuccess(response: any) {
  emit('onSuccess', response)
}

function handleUpload(response: any) {
  emit('handleUpload', response)
}
function handleExceed() {
  ElMessage({
    message: '单次上传文件个数不能超过1',
    type: 'error',
  })
}
</script>

<template>
  <el-upload
    ref="uploadPack" style="margin-left: 10px" :limit="1" :before-upload="beforeUploadPack" :on-exceed="handleExceed"
    :multiple="true" accept=".zip" :on-success="handlePackUploadSuccess" :headers="headerObj" :http-request="handleUpload" :show-file-list="false"
  >
    <template #trigger>
      <el-button type="primary" :icon="DocumentAdd">
        上传压缩包
      </el-button>
    </template>
  </el-upload>
</template>

<style lang="scss" scoped>
.upload-progress {
    display: flex;
    flex-direction: column;
}
</style>
