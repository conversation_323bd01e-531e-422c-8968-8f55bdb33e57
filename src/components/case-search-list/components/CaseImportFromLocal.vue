<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { UploadFile } from 'element-plus'
import { ElLoading, ElMenuItem, ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { Close, DocumentAdd, FolderAdd, Plus } from '@element-plus/icons-vue'
import type { Study } from '@/types/caseLibrary'
import type { SearchParams } from '@/types/common'
import { pageSizeOptions, shortcuts } from '@/enums/common'
import { uploadLocalCaseToCos } from '@/utils/tencent-upload'
import { useFileStore } from '@/store/modules/file'
import type { FileUpload, InvalidFile } from '@/types/file'
import { formatSex, validateFileByType } from '@/utils/commonLogic'
import { getTaskProgress, reportDCMUploadResult, reportZIPUploadResult } from '@/api/file'
import { AddCaseSourceType, SystemCaseLibraryType } from '@/enums/caseLibrary'
import { getUploadStudyList, isImageLinkedToOtherCases } from '@/api/case-library'

const emit = defineEmits(['importCase'])

enum ParseState {
  Off = 'off', // 未开始上传
  Active = 'active', // 上传中
  Success = 'success', // 上传完成
  Failed = 'failed', // 上传失败
}

// 文件解析状态
enum FileParseStatus {
  Active = '1', // 进行中
  Success = '2', // 解析成功
  Failed = '3', // 解析失败
}
// 上传文件类型
enum FileType {
  ZIP = 'zip',
  DICOM = 'dicom',
}

// 控制抽屉显示
const visible = ref(false)

// 表格数据列表
const tableData = ref<Study[]>([])

// 被勾选的记录
const selected = ref<Study[]>([])

const searchParameter = ref<SearchParams>({
  pageNum: 1,
  pageSize: 10,
})
const listTotal = ref(0)

/**
 * 当用户选择病例时触发
 */
function handleSelectionChange(val: Study[]) {
  selected.value = val
}

/**
 * 导入操作（这里为模拟）
 */
async function confirmImport(row: Study | null) {
  let studyInfoList = []
  if (row)
    selected.value = [row]

  studyInfoList = selected.value.map((item) => {
    return {
      patientId: item.patientId,
      studyUid: item.studyUid,
    }
  })

  if (studyInfoList.length === 0) {
    ElMessage({
      message: '请选择要导入的病例',
      type: 'error',
    })
  }

  const res = await isImageLinkedToOtherCases({ studyInfoList, targetLibrary: SystemCaseLibraryType.Department })
  if (res && res.length > 0) {
    ElMessageBox.confirm('该影像已经关联其他影像，是否继续导入', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        emit('importCase', selected.value, AddCaseSourceType.LocalUpload)
      })
      .catch(() => {})
  }
  else { emit('importCase', selected.value, AddCaseSourceType.LocalUpload) }
}

/**
 * 打开抽屉
 */
function openDrawer() {
  visible.value = true
  getUploadInput()
  handleSearch()
}
/**
 * 获取自己上传的图像
 */
async function handleSearch() {
  const res = await getUploadStudyList(searchParameter.value)
  tableData.value = res.list
  listTotal.value = res.total
}
// #region  文件上传相关逻辑

const invalidFiles = ref<InvalidFile[]>([]) // 验证失败的文件
const validFiles = ref<UploadFile[]>([]) // 验证通过的文件
const allSelectedFiles = ref<UploadFile[]>([]) // 所有需要上传的文件
const notionText = ref<string>('')// 解析提示语
const uploadProgress = ref(0)// 上传进度百分比
const parseState = ref<string>(ParseState.Off)// off: 未开始上传，active：上传中，success：上传完成，failed：上传失败
const stateMap = {
  off: { type: 'info', closable: true },
  active: { type: 'warning', closable: false },
  success: { type: 'success', closable: true },
  failed: { type: 'error', closable: true },
}

const upload = ref()
const uploadInputEle = ref()
const fileStore = useFileStore()

/**
 * 获取上传input元素
 */
const getUploadInput = () => {
  nextTick(() => {
    uploadInputEle.value = upload.value.$el.querySelector('input[type="file"]')
  })
}
/**
 * 设置文件/文件夹选择模式
 * @param isFolder  是否是文件夹
 */
function setUploadMode(isFolder: boolean) {
  if (!uploadInputEle.value)
    return
  if (isFolder)
    uploadInputEle.value.setAttribute('webkitdirectory', '')
  else
    uploadInputEle.value.removeAttribute('webkitdirectory')
}

function handleExceed() {
  ElMessage({
    message: '单次上传文件个数不能超过1000',
    type: 'error',
  })
}

/**
 * 压缩包文件上传
 */
const handleZIPChange = async (file: UploadFile, fileList: UploadFile[]) => {
  console.log('压缩包上传')
  notionText.value = '文件校验中...'
  parseState.value = ParseState.Active
  const validationResult = validateFileByType(file.raw as File, 'zip')
  if (validationResult) {
    notionText.value = '无有效文件，请检查文件格式'
    parseState.value = ParseState.Failed
    ElMessage.warning(`${validationResult.fileName}: ${validationResult.error}`)
    return false
  }
  await uploadFiles(fileList, FileType.ZIP)
}

// 上传文件过程中
async function handleDCMChange(file: UploadFile) {
  notionText.value = '文件校验中...'
  parseState.value = ParseState.Active
  if (file.status === 'ready')
    allSelectedFiles.value.push(file)
  const uploadInstance = uploadInputEle.value
  if (!uploadInstance || !uploadInstance.files) {
    console.error('上传文件实例不存在')
    parseState.value = ParseState.Failed
    return
  }
  const totalUpload = uploadInstance.files.length
  const validateRes = validateFileByType(file.raw as File, 'dcm')
  if (validateRes)
    invalidFiles.value.push(validateRes)
  else
    validFiles.value.push(file)

  // 判断文件列表长度，如果等于选择的文件列表长度，就开始上传有效文件（自定义上传、批量或者单文件，都可）
  if (allSelectedFiles.value.length === totalUpload) {
    if (!validFiles.value.length) {
      notionText.value = '无有效文件，请检查文件格式'
      parseState.value = ParseState.Failed

      if (invalidFiles.value.length > 0) {
        const invalidMessages = invalidFiles.value.map(f => `${f.fileName}: ${f.error}`)
        if (invalidMessages.length > 0) {
          ElMessage.warning({
            message: invalidMessages.join('<br>'),
            dangerouslyUseHTMLString: true,
          })
        }
      }
      resetUploadStatus()
      return
    }
    await uploadFiles(validFiles.value, FileType.DICOM)
  }
}

/**
 * 上传文件
 */
async function uploadFiles(files: UploadFile[], fileType: string) {
  notionText.value = '文件上传中...'
  uploadProgress.value = 0
  const { successFileList } = await fileStore.uploadFilesWithProgress(files, (process) => {
    uploadProgress.value = process
  })
  handleUploadResult(successFileList, fileType)
}

/**
 * 取消上传
 */
const onCancel = () => {
  fileStore.cancelAllUploads()
  parseState.value = ParseState.Off
  notionText.value = '上传已取消'
}

/**
 * 处理上传的结果
 * @param successRes 成功的文件
 */
async function handleUploadResult(successRes: FileUpload[], fileType: string) {
  if (parseState.value === ParseState.Off)
    return

  if (!successRes.length) {
    notionText.value = '无正确解析结果，请检查文件格式'
    parseState.value = ParseState.Failed
    resetUploadStatus()
  }
  else {
    notionText.value = '上传成功，文件正在解析中...'
    // parseState.value = ParseState.Success
    const fileIdList = successRes.map(item => item.filePath)
    // 上报文件上传结果
    let taskId = ''
    if (fileType === FileType.DICOM)
      taskId = await reportDCMUploadResult({ fileIdList })
    else
      taskId = await reportZIPUploadResult({ fileIdList })
    handleFileParse(taskId)
  }
}

/**
 * 处理文件解析的结果（带超时机制）
 * @param taskId 后台任务 ID
 */
function handleFileParse(taskId: string) {
  const maxTimeout = 60 * 1000 // 最长等待 60 秒
  const startTime = Date.now()

  const timer = setInterval(async () => {
    try {
      const data = await getTaskProgress({ taskId }) // data 为状态字符串 '1' | '2' | '3'
      switch (data.status) {
        case FileParseStatus.Success:
          clearInterval(timer)
          notionText.value = '文件解析完成'
          parseState.value = ParseState.Success
          resetUploadStatus()
          handleSearch()
          return

        case FileParseStatus.Failed:
          clearInterval(timer)
          notionText.value = '文件解析失败'
          parseState.value = ParseState.Failed
          resetUploadStatus()
          return

        case FileParseStatus.Active:
          // 解析中，继续轮询
          break

        default:
          clearInterval(timer)
          notionText.value = `解析状态异常（status=${data}）`
          parseState.value = ParseState.Failed
          resetUploadStatus()
          return
      }

      // 超时判断
      if (Date.now() - startTime > maxTimeout) {
        clearInterval(timer)
        notionText.value = '解析超时，请稍后重试'
        parseState.value = ParseState.Failed
        resetUploadStatus()
        return
      }
    }
    catch (e) {
      clearInterval(timer)
      notionText.value = '查询解析进度失败'
      parseState.value = ParseState.Failed
      resetUploadStatus()
    }
  }, 1000)
}

/**
 * 重置上传状态
 */
function resetUploadStatus() {
  allSelectedFiles.value = []
  invalidFiles.value = []
  validFiles.value = []
}

// #endregion

// 将 openDrawer 方法暴露给父组件使用
defineExpose({
  openDrawer,
  handleSearch,
})
</script>

<template>
  <div class="common-drawer">
    <el-drawer v-model="visible" title="本地上传病例" size="60%" destroy-on-close>
      <div class="main-content">
        <el-card class="search-card" shadow="never">
          <div>
            <div class="upload-container" style="display: flex;">
              <el-upload
                ref="upload"
                :limit="1000"
                :auto-upload="false"
                :on-exceed="handleExceed"
                :multiple="true" accept=".dcm" :show-file-list="false"
                :on-change="handleDCMChange"
                name="uploadDCMFolder"
              >
                <template #trigger>
                  <el-button type="primary" :disabled="parseState === ParseState.Active" :icon="DocumentAdd" @click="setUploadMode(false)">
                    上传文件
                  </el-button>
                  <el-button type="primary" :disabled="parseState === ParseState.Active" :icon="FolderAdd" @click="setUploadMode(true)">
                    上传文件夹
                  </el-button>
                </template>
              </el-upload>
              <el-upload
                ref="uploadPack" style="margin-left: 10px;" :auto-upload="false" :on-change="handleZIPChange"
                :multiple="false" accept=".zip" :show-file-list="false"
              >
                <el-button type="primary" :disabled="parseState === ParseState.Active" :icon="DocumentAdd">
                  上传压缩包
                </el-button>
              </el-upload>
            </div>
          </div>
          <div v-if="notionText" class="upload-notion">
            <div v-if="parseState === ParseState.Active" class="progress-panel">
              <el-progress :percentage="uploadProgress" />
              <el-icon style="cursor: pointer;" @click="onCancel()">
                <Close />
              </el-icon>
            </div>
            <el-alert
              v-if="parseState !== ParseState.Off"
              :title="notionText" :type="stateMap[parseState as keyof typeof stateMap].type"
              :closable="stateMap[parseState as keyof typeof stateMap].closable"
              @close="parseState = ParseState.Off"
            />
          </div>
        </el-card>

        <el-card class="data-card" shadow="never">
          <div class="data-table">
            <el-table
              :data="tableData"
              stripe
              height="100%"
              :header-cell-style="{ background: '#eef0ef', color: '#7d827f' }"
              @selection-change="handleSelectionChange"
            >
              <!-- 多选框列 -->
              <el-table-column type="selection" width="50" />
              <el-table-column prop="accessNumber" label="检查流水号" />
              <el-table-column prop="patientName" label="姓名" />
              <el-table-column prop="patientAge" label="年龄" />

              <el-table-column prop="patientSex" label="性别">
                <template #default="{ row }">
                  {{ formatSex(row.patientSex) }}
                </template>
              </el-table-column>
              <el-table-column prop="studyNo" label="检查号" />
              <el-table-column prop="modalities" label="检查类型" />
              <el-table-column prop="studyTime" label="检查时间" width="160" />
              <!-- 操作 -->
              <el-table-column label="操作" width="140" align="center">
                <template #default="scope">
                  <div class="operator-button" style="display: flex; gap: 12px; justify-content: center;">
                    <el-tooltip effect="light" content="新建病例" placement="top">
                      <el-icon style="cursor: pointer;" @click="confirmImport(scope.row)">
                        <Plus />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            class="footer-pagination" :page-size="searchParameter.pageSize" :page-sizes="pageSizeOptions"
            background layout="total, sizes, prev, pager, next" :total="listTotal"
            @current-change="searchParameter.pageNum = $event; handleSearch();"
            @size-change="searchParameter.pageSize = $event; handleSearch();"
          />
        </el-card>
      </div>
      <!-- 底部操作按钮 -->
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="visible = false">
            取消
          </el-button>
          <el-button type="primary" :disabled="!selected.length" @click="confirmImport(null)">
            新建病例
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-upload-list) {
  max-height: 100px; /* 设置最大高度 */
  overflow-y: auto;  /* 超出滚动 */
  overflow-x: hidden;
  .el-upload-list__item{
    width: 99%;
  }
}
.el-card{
  margin-bottom: 0px;
  border: 0px;
}

/* 底部操作按钮区域 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  gap: 10px;
}

.upload-notion{
  margin-top: 10px;
  .progress-panel{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .el-progress{
    width: 100%;
    padding:8px 0;
  }

}
</style>
