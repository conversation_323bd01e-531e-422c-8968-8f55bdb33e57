<script lang="ts" setup>
import type { LayerInterface } from '@/types/common'

const props = defineProps({
  dialogInfo: {
    type: Object as () => LayerInterface,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true,
      }
    },
    required: true,
  },
})

const emit = defineEmits(['confirm', 'cancelConfirm'])
const internalInfo = props.dialogInfo
// 确认提交事件
const confirm = () => {
  emit('confirm')
}

// 关闭弹窗
const closeDialog = () => {
  emit('cancelConfirm')
  internalInfo.show = false
}

defineExpose({ closeDialog })
</script>

<template>
  <div class="dialogPanel">
    <!-- 优化： width不传时默认宽度为30%   showButton不传时默认开启 -->
    <el-dialog
      v-model="internalInfo.show" :title="internalInfo.title" :width="typeof internalInfo.width === 'undefined' ? '30%' : internalInfo.width"
      :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close :show-close="dialogInfo.showClose"
    >
      <slot />
      <template v-if="internalInfo.showButton || typeof internalInfo.showButton === 'undefined'" #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">
            {{ $t('message.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="confirm">
            {{ $t('message.common.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  // padding-bottom: 12px;
  // padding-top: 12px;

  .el-dialog__headerbtn {
    top: 5px;
    font-size: 25px;
  }
}

:deep(.el-dialog__body) {
  padding-top: 10px;
  // border-top: 1px solid rgba(204, 204, 204, 1);
}
</style>
