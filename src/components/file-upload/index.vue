<script lang="ts" setup>
import {
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import type {
  UploadFile,
  UploadFiles,
  UploadProgressEvent,
  UploadProps,
  UploadUserFile,
} from 'element-plus'

import {
  Edit,
  Plus,
} from '@element-plus/icons-vue'
import { watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { beautifyFileSize } from '@/utils/commonLogic'

const props = defineProps({
  // 按钮名称
  buttonText: {
    type: String,
    default: '添加文件',
  },
  // 请求 URL
  actionPath: {
    type: String,
    default: '/rqcAlgorithm/rule/import',
  },
  // 是否显示已上传文件列表
  showFileList: {
    type: Boolean,
    default: true,
  },
  // 允许上传的文件格式
  accept: {
    type: String,
    default: '.mp4,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg',
  },
  // 上传成功时返回所有response
  returnSucAllRes: {
    type: Boolean,
    default: false,
  },
  // 允许上传文件的最大数量
  limitNum: {
    type: Number,
    default: 10,
  },
  // 是否支持多选文件
  multiple: {
    type: Boolean,
    default: true,
  },
  // 是否在移除文件时自动调用接口删除，少数场景需要删除时不立刻移除后续自己处理
  needAutoRemoveFile: {
    type: Boolean,
    default: true,
  },
  // 上传时附带的额外参数
  uploadData: {
    type: Object,
    default: () => { return { fileType: 'Related' } },
  },
  // 文件列表的类型 'text' | 'picture' | 'picture-card'
  listType: {
    type: String as any,
    default: 'text',
  },
  // 提示语
  tipText: {
    type: String,
    default: '',
  },
  // 移除文件是否需要防误触
  preventTouch: {
    type: Boolean,
    default: false,
  },
  // 对于用户头像等单文件上传时，只允许上传一个文件
  onlyOneFile: {
    type: Boolean,
    default: true,
  },
  // http-request 是一个自定义的上传函数，一旦你传入这个属性，el-upload 就不会再自动发送请求，而是完全交由你提供的函数来处理上传
  httpRequest: {
    type: Function,
    default: undefined,
  },
})

const emit = defineEmits(['handleSuccess', 'handleRemove', 'beforeUpload', 'handlePreview', 'handleChange', 'handleProgress', 'uploadFileList'])
const uploadRef = ref()

// 测试人员要求加删除前的防误触，加在before-remove钩子里，但是上传不符合要求文件时，会触发before-remove钩子，然后会弹出防误触，因此设置一个标志位isAllow判断是用户主动删除，还是上传了不符合要求的文件
const isAllow = ref(true)
interface resData {
  id: number
}
interface res {
  data: resData
}

const fileList = ref<UploadUserFile[]>([])
watch(fileList, () => {
  emit('uploadFileList', fileList.value)
}, { immediate: true })
defineExpose({
  fileList,
})

const headerObj = {
  Authorization: useUserStore().token,
  platTag: useUserStore().platTag,
}

// 文件上传前做一些校验
const beforeUpload: UploadProps['beforeUpload'] = (
  uploadFile: any,
) => {
  isAllow.value = true

  const fileSuffix = uploadFile.name.substring(
    uploadFile.name.lastIndexOf('.') + 1,
  ).toLowerCase()

  if (!props.accept.includes(fileSuffix)) {
    ElMessage({
      message:
        `上传文件只能是 ${props.accept}格式`,
      type: 'warning',
    })
    isAllow.value = false
    return false
  }
  if (uploadFile.size === 0) {
    ElMessage({
      message: '不能上传空文件',
      type: 'error',
    })
    isAllow.value = false
    return false
  }
  const isLt = uploadFile.size ? uploadFile.size / 1024 / 1024 : 0
  if (fileSuffix === 'png' || fileSuffix === 'jpg' || fileSuffix === 'jpeg') {
    if (isLt > 5) {
      ElMessage({
        message: 'png、jpg、jpeg文件大小不能超过5MB',
        type: 'error',
      })
      isAllow.value = false
      return false
    }
  }
  else if (fileSuffix === 'mp4') {
    const VideoLimitSize = 500

    if (isLt > VideoLimitSize) {
      ElMessage({
        message: `mp4文件大小不能超过${beautifyFileSize(VideoLimitSize * 1024 * 1024)}`,
        type: 'error',
      })
      isAllow.value = false
      return false
    }
  }
  else if (fileSuffix === 'pdf') {
    const FileLimitSize = 120

    if (isLt > FileLimitSize) {
      ElMessage({
        message: `文件大小不能超过${beautifyFileSize(FileLimitSize * 1024 * 1024)}`,
        type: 'error',
      })
      isAllow.value = false
      return false
    }
  }
  else if (fileSuffix === 'xls' || fileSuffix === 'xlsx') {
    const FileLimitSize = 3

    if (isLt > FileLimitSize) {
      ElMessage({
        message: `文件大小不能超过${beautifyFileSize(FileLimitSize * 1024 * 1024)}`,
        type: 'error',
      })
      isAllow.value = false
      return false
    }
  }
  else {
    if (isLt > 120) {
      ElMessage({
        message: '文件大小不能超过120MB',
        type: 'error',
      })
      isAllow.value = false
      return false
    }
  }

  emit('beforeUpload', uploadFile)
}
function handleExceed() {
  ElMessage.warning(`上传文件的最大数量不能超过${props.limitNum}个`)
}
function handleSuccess(response: res) {
  if (fileList.value.length > 1 && props.listType === 'picture-card' && props.onlyOneFile)
    fileList.value = [fileList.value[1]]
  const emitData = props.returnSucAllRes ? response : response.data.id
  emit('handleSuccess', emitData)
}

function handleError(response: Error) {
  ElMessage.error('导入失败')
  console.error(response)
}

function handleRemove(uploadFile: UploadFile) {
  const fileResponse: any = uploadFile.response
  emit('handleRemove', (fileResponse as res)?.data?.id)
}
const handBeforeRemove: UploadProps['beforeRemove'] = (uploadFile, _) => {
  if (!props.preventTouch)
    return true
  if (isAllow.value) {
    isAllow.value = true
    return ElMessageBox.confirm(
      `是否确认删除文件“${uploadFile.name}” ?`,
      { type: 'warning' },
    ).then(
      () => true,
      () => false,
    )
  }
  else {
    isAllow.value = true
    return true
  }
}
function handlePreview(file: UploadFile) {
  emit('handlePreview', file)
}
function handleChange(uploadFile: UploadFile, uploadFiles: UploadFiles) {
  emit('handleChange', uploadFile, uploadFiles)
}

function handleProgress(evt: UploadProgressEvent, uploadFile: UploadFile, uploadFiles: UploadFiles) {
  emit('handleProgress', evt.percent)
}
</script>

<template>
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    class="upload-common"
    :action="actionPath"
    :headers="headerObj"
    :data="uploadData"
    :auto-upload="true"
    :limit="limitNum"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-exceed="handleExceed"
    :on-remove="handleRemove"
    :on-preview="handlePreview"
    :on-change="handleChange"
    :on-progress="handleProgress"
    :before-remove="handBeforeRemove"
    :multiple="multiple"
    :show-file-list="showFileList"
    :accept="accept"
    :list-type="listType"
    :http-request="httpRequest"
  >
    <template #trigger>
      <div v-if="listType === 'text' && buttonText">
        <el-button type="primary" style="margin-left: 10px;">
          {{ buttonText }}
        </el-button>
      </div>
      <el-icon v-if="listType === 'picture-card'">
        <Plus v-if="fileList.length === 0 || !onlyOneFile" />
        <Edit v-else />
      </el-icon>
      <slot />
    </template>
    <template v-if="tipText" #tip>
      <div class="el-upload__tip">
        {{ tipText }}
      </div>
    </template>
  </el-upload>
</template>

<style lang="scss" scoped>
:deep(.el-progress__text) {
  right: -20px;
}
</style>
