<script>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { onBeforeUnmount, onMounted, ref, shallowRef } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { ElMessage } from 'element-plus'
// import { createEditor } from '@wangeditor/editor'
import { ACCESS_TOKEN_KEY } from '@/enums/cacheEnum'
import { Storage } from '@/utils/storage'
import { fileUploadPath } from '@/enums/urlEnum'

export default {
  components: { Editor, Toolbar },
  props: { content: String, maxLength: { type: Number, default: 1000 }, placeholder: String },
  emits: ['updateHtml', 'updatePlainText'],
  setup(props, { emit }) {
    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef()
    // 内容 HTML
    const valueHtml = ref('')
    const imgCount = ref(0)
    const toolbarConfig = {
      toolbarKeys: [
        'headerSelect',
        'bold', 'italic', 'through',
        'color', 'clearStyle',
        'justifyLeft', 'justifyRight',
        'insertLink', 'uploadImage',
      ],
      // mode: 'default',
      // excludeKeys: ['insertTable', 'code', 'codeBlock', 'group-video', 'insertImage', 'fullScreen', 'emotion', 'blockquote', 'underline'],
    }
    // 模拟 ajax 异步获取内容
    onMounted(() => {
      // 查询所有工具栏key
      // const editor = createEditor({})
      // console.log(editor.getAllMenuKeys())
      // setTimeout(() => {
      // }, 1500)
    })
    const editorConfig = {
      placeholder: props.placeholder,
      MENU_CONF: {},
      hoverbarKeys: {
        image: {
          // 清空 image 元素的 hoverbar
          menuKeys: [
            'imageWidth30',
            'imageWidth50',
            'imageWidth100',
            'deleteImage',
          ],
        },
      },
    }
    props.maxLength > 0 && (editorConfig.maxLength = props.maxLength)

    editorConfig.MENU_CONF.uploadImage = {
      // 上传图片的配置
      server: fileUploadPath,
      // 单个文件的最大体积限制
      maxFileSize: 50 * 1024 * 1024, // 50M
      // 最多可上传几个文件，默认为 100
      // maxNumberOfFiles: 1,
      fieldName: 'file',
      allowedFileTypes: ['.jpg', '.png', '.jpeg'],
      headers: {
        Authorization: Storage.get(ACCESS_TOKEN_KEY, null),
      },
      meta: {
        fileType: 'RichText',
      },
      onError(file, err, res) {
        // ElMessage.error(err)
      },
      onBeforeUpload(file) { // TS 语法
        const fileSuffix = file[Object.keys(file)[0]].name.substring(file[Object.keys(file)[0]].name.lastIndexOf('.') + 1).toLowerCase()
        const fileSize = file[Object.keys(file)[0]].size / 1024 / 1024
        const whiteList = [
          'png',
          'jpg',
          'jpeg',
        ]
        if (!whiteList.includes(fileSuffix)) {
          ElMessage({
            message:
              '上传的图片只能是png、jpg、jpeg格式',
            type: 'error',
          })
          file = {}
          return file
        }
        if (imgCount.value >= 10) {
          ElMessage({
            message:
              '图片最多上传10张',
            type: 'error',
          })
          file = {}
          return file
        }
        if (fileSize > 10) {
          ElMessage({
            message:
              '图片大小不能超过10MB',
            type: 'error',
          })
          file = {}
          return file
        }
        return file
      },
      customInsert(res, insertFn) {
        const url = res.data.fullUrl
        const alt = res.data.originName
        const href = res.data.fileUrl
        const node = { type: 'image', src: url, style: { width: '100%' }, children: [{ text: '' }], alt, href }
        editorRef.value.insertNode(node)
      },
    }

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
      valueHtml.value = ''
      const editor = editorRef.value
      if (editor == null)
        return
      editor.destroy()
    })
    // wangeditor在内容没有p标签包裹时会疯狂报错且不能编辑，因此加个保护措施
    if (props.content) {
      const content = props.content
      // 检查 content 是否以 <p> 标签开头并以 </p> 标签结尾，允许带有属性
      const pTagPattern = /^<p\b[^>]*>.*<\/p>$/
      if (!pTagPattern.test(content)) {
        // 如果没有 <p> 标签，添加 <p> 标签包裹内容
        valueHtml.value = `<p>${content}</p>`
      }
      else {
        // 如果已经有 <p> 标签，直接赋值
        valueHtml.value = content
      }
    }
    // valueHtml.value = props.content
    const text = ref('')
    const handleCreated = (editor) => {
      imgCount.value = editor.getElemsByType('image').length
      editorRef.value = editor // 记录 editor 实例，重要！
      text.value = editor.getText()
    }
    const handleChange = (editor) => {
      // imgCount.value = editor.getElemsByType('image').length
      text.value = editor.getHtml()
      const pureTxt = editor.getText()
      emit('updateHtml', text.value)
      emit('updatePlainText', pureTxt)
    }

    const insertHtml = (text) => {
      const editor = editorRef.value
      const originHtml = editor.getHtml()
      editor.setHtml(`${originHtml}${text}`)
    }

    return {
      editorRef,
      valueHtml,
      mode: 'default', // 或 'simple'
      toolbarConfig,
      editorConfig,
      handleCreated,
      handleChange,
      text,
      insertHtml,
    }
  },
}
</script>

<template>
  <div style="border: 1px solid #ccc; width: 100%">
    <slot />
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :default-config="toolbarConfig" :mode="mode" />
    <Editor
      v-model="valueHtml" style="height: 300px; overflow-y: hidden" :default-config="editorConfig" :mode="mode"
      placeholder="补充病例流行病学、影像学表现、鉴别诊断等（选填）" @on-created="handleCreated" @on-change="handleChange"
    />
  </div>
</template>
