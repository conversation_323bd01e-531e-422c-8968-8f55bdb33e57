<script setup lang="ts">
import { computed, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'

// 父组件通过 v-model 控制弹窗的显示
const props = defineProps<{
  modelValue: boolean
}>()

// 自定义事件：关闭弹窗、提交选中值
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', diseaseId: string): void
}>()

// ==============================
// 弹窗显隐（v-model 双向绑定）
// ==============================
const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 表单引用
const formRef = ref<FormInstance>()

// 表单模型
const formInfo = ref<{ diseaseId: string }>({
  diseaseId: '',
})

// 表单校验规则
const rules = {
  diseaseId: [{ required: true, message: '请选择疾病分类', trigger: 'blur' }],
}

// 获取疾病树数据
const diseaseData = computed(() => useCaseLibraryStore().diseaseTreeData)

// el-tree-select 的字段映射配置
const treeSelectProps = {
  value: 'id',
  label: 'label',
  children: 'children',
}

// 提交表单并传递选中的疾病 ID 给父组件
function submitForm() {
  formRef.value?.validate((valid) => {
    if (!valid)
      return
    emit('submit', formInfo.value.diseaseId) // 通知父组件选中的 diseaseId
    emit('update:modelValue', false) // 关闭弹窗
  })
}
/**
 * 重置表单
 */
function resetForm() {
  formRef.value?.resetFields()
}

// 关闭弹窗
function closeDialog() {
  emit('update:modelValue', false)
}
</script>

<template>
  <!-- 弹窗组件 -->
  <el-dialog
    v-model="dialogVisible"
    title="补充疾病分类"
    width="400px"
    @close="resetForm"
  >
    <!-- 表单 -->
    <el-form
      ref="formRef"
      :model="formInfo"
      :rules="rules"
      label-width="90px"
    >
      <!-- 疾病分类字段 -->
      <el-form-item label="疾病分类" prop="diseaseId">
        <el-tree-select
          v-model="formInfo.diseaseId"
          :data="diseaseData"
          :props="treeSelectProps"
          placeholder="请选择疾病分类(必填)"
          style="width: 100%"
          clearable
          filterable
        />
      </el-form-item>
    </el-form>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <el-button @click="closeDialog">
        取消
      </el-button>
      <el-button type="primary" @click="submitForm">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
