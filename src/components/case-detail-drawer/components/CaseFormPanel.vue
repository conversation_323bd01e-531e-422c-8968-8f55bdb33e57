<script setup lang="ts">
import { reactive, ref } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash'
import { ElMessage } from 'element-plus'
import { AddCaseSourceType, CaseLibraryType, FollowType, SystemCaseLibraryType } from '@/enums/caseLibrary'

import type { CaseRecord, Study, Tag } from '@/types/caseLibrary'
import { CaseFormMode } from '@/enums/common'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { GetCaseTagList, addCaseFromRIS, addCaseFromUpload } from '@/api/case-library'
import { useUserStore } from '@/store/modules/user'
const props = defineProps<{
  caseData: CaseRecord
  mode: CaseFormMode
  addSourceType?: AddCaseSourceType | null
  caseLibraryType: number
}>()

const emit = defineEmits(['submitSuccess'])

/** 表单校验规则 */
const rules = computed(() => {
  const baseRules: Record<string, any[]> = {
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    age: [{ type: 'number', required: true, message: '请输入年龄', trigger: 'blur' }],
  }

  if (props.caseLibraryType === CaseLibraryType.Teaching)
    baseRules.diseaseId = [{ required: true, message: '请选择疾病分类', trigger: 'blur' }]

  return baseRules
})

// 是否为教学病例库操作
const isTeachingLibrary = computed(() => props.caseLibraryType === CaseLibraryType.Teaching)

// 是否为随访病例库操作
const isFollowLibrary = computed(() => props.caseLibraryType === CaseLibraryType.FollowUp)

const caseLibraryStore = useCaseLibraryStore()

// 级联选项数据
const diseaseData = computed(() => caseLibraryStore.diseaseTreeData)

// 配置项
const treeSelectProps = {
  value: 'id',
  label: 'label',
  children: 'children',
}
// 创建本地副本，避免直接修改 prop
// const editForm = ref<any>(cloneDeep(props.mode === CaseFormMode.ADD ? defaultForm : props.caseData))
const editForm = ref()

/** el-form 表单实例 */
const formRef = ref()
/*
 * 表单提交保存数据
 */
function submitForm() {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (props.mode === CaseFormMode.ADD) {
        if (!props.addSourceType)
          throw new Error('addSourceType is null')

        if (props.addSourceType === AddCaseSourceType.LocalUpload) {
          const studyInfoList = editForm.value.studyInfoList.map((item: Study) => {
            return {
              patientId: item.patientId,
              studyUid: item.studyUid,
            }
          })
          const submitInfo = {
            studyInfoList,
            supplement: editForm.value,
            targetLibrary: SystemCaseLibraryType.Department,
            diseaseId: editForm.value?.diseaseId,
            targetId: useUserStore().selectCaseTypeId,
          }
          await addCaseFromUpload(submitInfo)
        }
        else if (props.addSourceType === AddCaseSourceType.RISImport) {
          const submitInfo = {
            risStudyList: editForm.value.studyInfoList,
            targetLibrary: SystemCaseLibraryType.Department,
            diseaseId: editForm.value?.diseaseId,
            targetId: useUserStore().selectCaseTypeId,
          }
          await addCaseFromRIS(submitInfo)
        }
      }
      else {
        // TODO: 更新病例数据
      }

      emit('submitSuccess')
      ElMessage.success('保存成功')
    }
  })
}

/**
 * 更新病例数据
 * @param newVal 新的病例数据
 */
function handleUpdateCaseData(newVal: CaseRecord) {
  editForm.value = cloneDeep(newVal)
  if (props.mode === CaseFormMode.ADD)
    editForm.value.diseaseId = caseLibraryStore.selectedDiseaseId
  else
    editForm.value.tags = newVal.tagInfoList.map((item: Tag) => item.tagName)
}

const keyWordsList = ref<Tag[]>([])

async function getKeyWords() {
  try {
    keyWordsList.value = await GetCaseTagList()
  }
  catch (e) {
    keyWordsList.value = []
  }
}

// 如果外部 props.editForm 发生变化，重新覆盖 localForm
watch(
  () => props.caseData,
  (newVal) => {
    handleUpdateCaseData(newVal)
  },
  { deep: true, immediate: true },
)

// 可暴露给父组件调用
defineExpose({
  submitForm,
})
</script>

<template>
  <!-- 编辑模式：el-form 编辑内容 -->
  <el-form ref="formRef" :model="editForm" :rules="rules" label-width="100px" label-position="top">
    <!-- 疾病名称 -->
    <el-form-item v-if="isTeachingLibrary" label="疾病分类" prop="diseaseId">
      <!-- <el-input v-model="editForm.diagnosis" /> -->
      <el-tree-select
        v-model="editForm.diseaseId"
        :data="diseaseData"
        :props="treeSelectProps"
        placeholder="请选择疾病分类(必填)"
        style="width: 100%"
        clearable
        filterable
      />
    </el-form-item>

    <!-- 病史 -->
    <el-form-item label="简要病史" prop="medicalHistory">
      <el-input v-model="editForm.medicalHistory" type="textarea" autosize :rows="1" placeholder="请输入简要病史（选填）" />
    </el-form-item>
    <!-- 主诉 -->
    <el-form-item label="主诉" prop="selfComplaints">
      <el-input v-model="editForm.selfComplaints" type="textarea" autosize :rows="1" placeholder="请输入主诉（选填）" />
    </el-form-item>
    <!-- 关键征象 -->
    <el-form-item label="关键征象" prop="sign">
      <el-input v-model="editForm.sign" type="textarea" :rows="1" autosize placeholder="请输入关键征象（选填）" />
    </el-form-item>
    <!-- 病例类型 -->
    <el-form-item v-if="!isFollowLibrary" label="病例类型:" prop="caseCategory" label-position="left">
      <el-radio-group v-model="editForm.caseCategory">
        <el-radio v-for="(type) in caseLibraryStore.caseCategoryOptions" :key="type.dictValue" :value="type.dictValue">
          {{ type.dictLabel }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- 病例难度 -->
    <el-form-item v-if="!isFollowLibrary" label="病例难度:" prop="difficulty" label-position="left">
      <el-radio-group v-model="editForm.difficulty">
        <el-radio v-for="(type) in caseLibraryStore.caseDifficultyOptions" :key="type.dictValue" :value="type.dictValue">
          {{ type.dictLabel }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 标签 -->
    <el-form-item label="标签" prop="tags">
      <el-select
        v-model="editForm.tags"
        multiple
        filterable
        allow-create
        :reserve-keyword="false"
        default-first-option
        placeholder="请选择或输入关键词（选填）"
        @focus="getKeyWords"
      >
        <el-option
          v-for="item in keyWordsList"
          :key="item.tagId"
          :label="item.tagName"
          :value="item.tagName"
        />
      </el-select>
    </el-form-item>

    <!-- 随访结果 -->
    <el-form-item>
      <!-- 自定义标题 + 新增按钮 -->
      <template #label>
        <div style="display: flex; justify-content: space-between;">
          <span>随访结果</span>
          <el-button
            type="primary"
            size="small"
            style="margin-left: 10px;"
            @click="editForm.followInfoList.push({ followType: FollowType.Pathology, followupResult: '' })"
          >
            新增随访
          </el-button>
        </div>
      </template>
      <div v-for="(item, index) in editForm.followInfoList" :key="index" class="followup-entry" style="margin-bottom: 10px;width: 100%;">
        <el-row align="middle">
          <el-col :span="16">
            <el-radio-group v-model="item.followType">
              <el-radio v-for="(type) in caseLibraryStore.followTypeOptions" :key="type.dictValue" :value="type.dictValue">
                {{ type.dictLabel }}
              </el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="2" />
          <el-col :span="6" style="text-align: left;">
            <el-button
              v-if="editForm.followInfoList.length > 1"
              :icon="Delete"
              type="danger"
              link
              @click="editForm.followInfoList.splice(index, 1)"
            />
          </el-col>

          <el-col :span="24" style="margin-top: 5px;">
            <RichText :content="item.followupResult" :max-length="5000" placeholder="补充手术、病理等结果（选填）" @update-html="item.followupResult = $event" />
          </el-col>
        </el-row>
      </div>
    </el-form-item>

    <!-- 定位 -->
    <el-form-item label="定位:" label-width="60px" label-position="left">
      <el-radio-group v-model="editForm.qualityMatch">
        <el-radio v-for="(type) in caseLibraryStore.qualityMatchOptions" :key="type.dictValue" :value="type.dictValue">
          {{ type.dictLabel }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="定性:" label-width="60px" label-position="left">
      <el-radio-group v-model="editForm.positionMatch">
        <el-radio v-for="(type) in caseLibraryStore.positionMatchOptions" :key="type.dictValue" :value="type.dictValue">
          {{ type.dictLabel }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 病例分析 -->
    <el-form-item label="病例分析" prop="caseAnalysis">
      <!-- <el-input v-model="editForm.caseAnalysis" type="textarea" :rows="3" /> -->
      <el-input v-show="false" v-model="editForm.caseAnalysis" />
      <RichText :content="editForm.caseAnalysis" :max-length="5000" placeholder="补充病例流行病学、影像学表现、鉴别诊断等（选填）" @update-html="editForm.caseAnalysis = $event" />
    </el-form-item>

    <!-- 备注 -->
    <el-form-item label="备注" prop="remark">
      <el-input v-model="editForm.remark" type="textarea" autosize :rows="1" placeholder="请输入备注（选填）" />
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
:deep(.el-cascader-node)
{
  max-width: 100px;
}
</style>
