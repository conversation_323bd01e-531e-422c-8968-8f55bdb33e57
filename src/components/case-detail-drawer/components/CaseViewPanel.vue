<script setup lang="ts">
import { PatientTypeOptions } from '@/enums/caseLibrary'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { getDictOptionLabel } from '@/utils/commonLogic'

defineProps<{
  caseData: any
}>()

const caseLibraryStore = useCaseLibraryStore()
// 当前折叠项
const activeNames = ref(['1', '2', '3', '4', '5', '6'])
</script>

<template>
  <!-- 查看模式：段落展示 -->
  <div class="drawer-content">
    <!-- 非编辑模式：折叠面板展示 -->
    <el-collapse v-model="activeNames">
      <el-collapse-item title="患者信息" name="1">
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>姓名：</strong>{{ caseData?.patientName }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>性别：</strong>{{ formatSex(caseData?.patientSex) }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>年龄：</strong>{{ caseData?.patientAge }}</p>
          </el-col>
          <el-col
            v-if="caseData?.studyInfoList?.[0]?.patientType !== undefined && caseData.studyInfoList.length > 0"
            :span="12"
          >
            <p><strong>就诊类型：</strong>{{ getOptionLabel(PatientTypeOptions, caseData?.studyInfoList[0].patientType) }}</p>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item title="检查信息" name="2">
        <!-- 多个检查：使用 el-tabs -->
        <template v-if="caseData?.studyInfoList && caseData?.studyInfoList?.length > 1">
          <el-tabs>
            <el-tab-pane
              v-for="(studyItem, index) in caseData?.studyInfoList"
              :key="index"
              :label="`检查 ${index + 1}`"
            >
              <StudyContent :study="studyItem" />
            </el-tab-pane>
          </el-tabs>
        </template>

        <!-- 只有一个检查：直接展示 -->
        <template v-else-if="caseData?.studyInfoList?.length === 1">
          <StudyContent :study="caseData.studyInfoList[0]" />
        </template>
        <!-- 没有检查：显示提示 -->
        <template v-else>
          <p style="color: #999;">
            暂无检查信息
          </p>
        </template>
      </el-collapse-item>
      <el-collapse-item title="病例信息" name="3">
        <el-row :gutter="20">
          <el-col :span="24">
            <p><strong>疾病名称：</strong>{{ caseData?.diseaseName }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>病例类型：</strong>{{ getDictOptionLabel(caseLibraryStore.caseCategoryOptions, caseData?.caseCategory) }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>病例难度：</strong>{{ getDictOptionLabel(caseLibraryStore.caseDifficultyOptions, caseData?.difficulty) }}</p>
          </el-col>
          <el-col :span="24">
            <div v-if="caseData?.tagInfoList?.length">
              <p><strong>标签：</strong></p>
              <el-tag v-for="tag in caseData.tagInfoList" :key="tag.tagId" size="small" class="mr-1" style="margin-right: 5px;">
                {{ tag.tagName }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item title="病史信息" name="4">
        {{ caseData?.medicalHistory }}
      </el-collapse-item>
      <el-collapse-item title="随访结果" name="5">
        <template v-if="caseData?.followInfoList">
          <el-tabs>
            <el-tab-pane
              v-for="(followItem, index) in caseData?.followInfoList"
              :key="index"
              :label="getDictOptionLabel(caseLibraryStore.followTypeOptions, followItem.followType)"
            >
              <div v-html="followItem.followupResult" />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-collapse-item>
      <el-collapse-item title="病例分析" name="6">
        <div v-html="caseData?.caseAnalysis" />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
