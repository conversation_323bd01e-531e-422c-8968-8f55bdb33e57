<!-- StudyContent.vue -->
<script setup lang="ts">
import type { Study } from '@/types/caseLibrary'

defineProps<{ study: Study }>()

function handleOpenPacs() {
  console.log('查阅影像')
}
</script>

<template>
  <div class="study-content">
    <el-row :gutter="20">
      <el-col :span="12">
        <p><strong>检查号：</strong>{{ study.studyNo }}</p>
      </el-col>
      <el-col :span="12">
        <p><strong>检查日期：</strong>{{ study.studyTime }}</p>
      </el-col>
      <el-col v-if="study.modalities" :span="12">
        <p><strong>检查类型：</strong>{{ study.modalities.join(',') }}</p>
      </el-col>
      <el-col v-if="study.studyItemName" :span="12">
        <p><strong>检查项目：</strong>{{ study.studyItemName }}</p>
      </el-col>
      <el-col v-if="study.partName" :span="12">
        <p><strong>检查部位：</strong>{{ study.partName }}</p>
      </el-col>
      <el-col v-if="study.reportTime" :span="12">
        <p><strong>报告时间：</strong>{{ study.reportTime }}</p>
      </el-col>
      <el-col v-if="study.reporter" :span="12">
        <p><strong>报告医生：</strong>{{ study.reporter }}</p>
      </el-col>
      <el-col v-if="study.checker" :span="12">
        <p><strong>审核医生：</strong>{{ study.checker }}</p>
      </el-col>
      <el-col v-if="study.reportDescribe" :span="24">
        <p><strong>影像学表现：</strong>{{ study.reportDescribe }}</p>
      </el-col>
      <el-col v-if="study.reportDiagnose" :span="24">
        <p><strong>影像学诊断：</strong>{{ study.reportDiagnose }}</p>
      </el-col>
      <el-col :span="24">
        <el-button size="small" type="primary" style="float: right;" @click="handleOpenPacs">
          查阅影像
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>
