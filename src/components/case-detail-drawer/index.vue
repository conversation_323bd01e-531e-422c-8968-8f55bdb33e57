<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { CaseRecord } from '@/types/caseLibrary'
import { getOptionLabel } from '@/utils/commonLogic'
import { CaseFormMode } from '@/enums/common'
import { type AddCaseSourceType, CaseLibraryType } from '@/enums/caseLibrary'

/** 接收父组件传入的动态标题 */
const props = withDefaults(defineProps<{
  drawerVisible: boolean
  mode?: CaseFormMode
  caseLibraryType: number
}>(), {
  mode: CaseFormMode.VIEW,
  caseLibraryType: CaseLibraryType.Teaching,
})

const emit = defineEmits(['update:drawerVisible', 'submitSuccess'])
const drawerVisible = computed({
  get() {
    return props.drawerVisible
  },
  set(visible: boolean) {
    emit('update:drawerVisible', visible)
  },
})

const currentMode = ref(props.mode)
const addSourceType = ref<AddCaseSourceType | null>(null)

/** 当前病例数据（原始数据） */
const caseData = ref<CaseRecord>()
/**
 * 取消编辑并重置表单
 */
function cancelEdit() {
  if (currentMode.value === CaseFormMode.ADD)
    drawerVisible.value = false
  else
    currentMode.value = CaseFormMode.VIEW

  emit('submitSuccess')
}

/**
 * 开始编辑
 */
const handleEdit = () => {
  currentMode.value = CaseFormMode.EDIT
}

/**
 * 对外暴露方法：打开抽屉并传入数据
 */
function openDrawer(data: CaseRecord, sourceType?: AddCaseSourceType) {
  // 如果抽屉已打开，点击新行也更新内容
  caseData.value = data
  addSourceType.value = sourceType ?? null
  if (!drawerVisible.value) {
    drawerVisible.value = true
    currentMode.value = props.mode
  }
}

/**
 * 对外暴露方法：关闭抽屉
 */
function closeDrawer() {
  if (drawerVisible.value)
    drawerVisible.value = false
}

// 编辑面板
const editPanel = ref()

/**
 * 保存 新增或编辑病例
 */
const submitForm = () => {
  editPanel?.value.submitForm()
}

/**
 * 抽屉标题
 */
const drawerTitle = computed(() => {
  if (props.mode === CaseFormMode.VIEW) {
    currentMode.value = CaseFormMode.VIEW
    return '查看病例'
  }
  else if (props.mode === CaseFormMode.ADD) {
    currentMode.value = CaseFormMode.ADD
    return '新增病例'
  }
  else {
    currentMode.value = CaseFormMode.EDIT
    return '编辑病例'
  }
})

/** 暴露方法给父组件使用 */
defineExpose({ openDrawer, closeDrawer })
</script>

<template>
  <div class="common-drawer">
    <!-- 抽屉组件 -->
    <el-drawer
      v-model="drawerVisible" size="550px" direction="rtl" :title="drawerTitle"
      :modal="currentMode === CaseFormMode.ADD"
      :modal-class="currentMode === CaseFormMode.ADD ? '' : 'case-detail-drawer-model'"
    >
      <!-- 滚动内容区域 -->
      <div class="drawer-body">
        <CaseViewPanel
          v-if="currentMode === CaseFormMode.VIEW" ref="viewPanel" :case-data="caseData"
          :case-library-type="caseLibraryType"
        />
        <CaseFormPanel
          v-else ref="editPanel" :mode="currentMode" :add-source-type="addSourceType" :case-library-type="caseLibraryType"
          :case-data="caseData" @submit-success="cancelEdit"
        />
      </div>
      <!-- 底部按钮（插入到 drawer 的 footer 区域） -->
      <template #header>
        <!-- 我审核的病例 页面仅提供查看病例功能，不提供病例编辑功能 -->
        <div v-if="caseLibraryType !== CaseLibraryType.MyReviewed" class="drawer-footer-buttons">
          <el-button type="primary" @click="handleEdit">
            查看影像
          </el-button>
          <el-button type="primary" @click="handleEdit">
            查看360
          </el-button>
          <el-button v-if="currentMode === CaseFormMode.VIEW" type="primary" @click="handleEdit">
            病例编辑
          </el-button>

          <template v-else>
            <el-button type="primary" @click="submitForm">
              {{ currentMode === CaseFormMode.ADD ? '新建' : '保存' }}
            </el-button>
            <el-button @click="cancelEdit">
              取消
            </el-button>
          </template>
          <el-button type="primary" @click="handleEdit">
            添加随访
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">

</style>
