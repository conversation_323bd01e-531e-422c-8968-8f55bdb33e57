<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import { More } from '@element-plus/icons-vue'
import { defaultCatalogData } from '@/utils/mockData'
import { AddUserCatalog, DelUserCatalog, UpdateUserCatalog } from '@/api/case-library'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { CaseFormMode } from '@/enums/common'

// 向父组件发出事件，仅叶子节点选中时才 emit
// 如果是取消选中，emit(null)
const emit = defineEmits(['leafSelected'])

// 树组件引用（用于调用 filter 方法）
const treeRef = ref<InstanceType<typeof ElTree>>()

// 默认展开的节点 ID 列表
const defaultExpandedKeys = ref<number[]>([])

const caseLibraryStore = useCaseLibraryStore()

// 树组件接收的数据
const treeData = computed(() => caseLibraryStore.customCategoryTreeData)

// 点击节点时触发的处理逻辑
const handleNodeClick = (data: any) => {
  const isLeaf = !data.children || data.children.length === 0
  if (!isLeaf)
    return // 目录节点不处理任何逻辑
  emit('leafSelected', data.id)
}

// 递归收集某个节点及其所有子节点的 id（用于默认展开）
function collectAllIds(node: any): number[] {
  const ids: number[] = [node.id]
  if (node.children && node.children.length > 0) {
    for (const child of node.children)
      ids.push(...collectAllIds(child))
  }
  return ids
}

// 查找第一个叶子节点
function findFirstLeafNode(data: any[]): any | null {
  for (const node of data) {
    if (!node.children || node.children.length === 0) {
      return node
    }
    else {
      const result = findFirstLeafNode(node.children)
      if (result)
        return result
    }
  }
  return null
}

// #region 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref<CaseFormMode>(CaseFormMode.ADD)
const formRef = ref()
const form = reactive({ id: '', catalogName: '' })
const rules = {
  catalogName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
}

// 处理操作按钮点击事件
const handleCommand = (command: string, nodeData: any) => {
  switch (command) {
    case CaseFormMode.ADD:
      dialogMode.value = CaseFormMode.ADD
      form.id = nodeData.id
      form.catalogName = ''
      dialogVisible.value = true
      break
    case CaseFormMode.EDIT:
      dialogMode.value = CaseFormMode.EDIT
      form.id = nodeData.id
      form.catalogName = nodeData.catalogName
      dialogVisible.value = true
      break
    case CaseFormMode.DELETE:
      handleDelete(nodeData.id)
      break
  }
}
const { t } = useI18n()
/**
 * 删除分类
 */
async function handleDelete(catalogId: number) {
  ElMessageBox.confirm(t('message.common.tooltip.delete'), {
    confirmButtonText: t('message.common.confirm'),
    cancelButtonText: t('message.common.cancel'),
    type: 'warning',
  })
    .then(async () => {
      // TODO 调用删除接口
      await DelUserCatalog({ catalogId })
      ElMessage.success(t('message.common.delSuccess'))
      caseLibraryStore.getCustomCategoryTreeData()
    })
    .catch(() => {})
}

/**
 * 提交表单
 */
function submitForm() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid)
      return

    if (dialogMode.value === CaseFormMode.ADD) {
      const submitInfo = {
        catalogName: form.catalogName,
        parentId: form.id,
      }
      await AddUserCatalog(submitInfo)
      ElMessage.success('新增成功')
    }
    else if (dialogMode.value === CaseFormMode.EDIT) {
      const submitInfo = {
        catalogName: form.catalogName,
        catalogId: form.id,
      }
      await UpdateUserCatalog(submitInfo)
      ElMessage.success('编辑成功')
    }

    caseLibraryStore.getCustomCategoryTreeData()
    dialogVisible.value = false
  })
}

/**
 * 重置表单
 */
function resetForm() {
  formRef.value?.resetFields()
}

// 搜索框绑定值
const filterText = ref('')

// 监听搜索关键词变化，实时过滤树节点
watch(filterText, (val) => {
  treeRef.value?.filter(val)
})

// 节点过滤方法：根据 nickName 匹配
const filterNode = (value: string, data: any) => {
  if (!value)
    return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

// #endregion

onMounted(async () => {
  await caseLibraryStore.getCustomCategoryTreeData()

  if (treeData.value.length > 0 && treeRef.value) {
    // 若之前有选中的叶子节点，则展开该目录并选中该叶子节点
    if (caseLibraryStore.selectedCatalogId) {
      defaultExpandedKeys.value = [caseLibraryStore.selectedCatalogId]
      treeRef.value.setCurrentKey(caseLibraryStore.selectedCatalogId)
    }
    else {
      // 若之前没有选中的目录，则展开第一级目录并选中第一个叶子节点
      defaultExpandedKeys.value = collectAllIds(treeData.value[0])
      const firstLeaf = findFirstLeafNode(treeData.value)
      if (firstLeaf) {
        treeRef.value.setCurrentKey(firstLeaf.id)
        handleNodeClick(firstLeaf)
      }
    }
  }
})
</script>

<template>
  <div class="tree-container">
    <div class="search-container">
      <el-input
        v-model="filterText"
        placeholder="搜索疾病名称"
        clearable
        size="small"
        class="search-input"
      />
      <el-button type="primary" size="small" @click="handleCommand(CaseFormMode.ADD, { id: 0 })">
        新增
      </el-button>
    </div>
    <!-- 横向滚动只控制树组件 -->
    <div class="tree-scroll-inner">
      <!-- 树组件 -->
      <ElTree
        ref="treeRef" :data="treeData"
        :highlight-current="true"
        :filter-node-method="filterNode"
        node-key="id" class="disease-tree"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      >
        <!-- 自定义树节点内容 -->
        <template #default="{ data }">
          <div
            class="tree-node"
          >
            <!-- 显示疾病名称 -->
            <span>{{ data.label }}</span>

            <!-- 操作按钮（省略号） -->
            <el-dropdown trigger="click" @command="(command: any) => handleCommand(command, data)">
              <el-icon class="icon-ellipsis" @click.stop>
                <More />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="CaseFormMode.ADD">
                    新增
                  </el-dropdown-item>
                  <el-dropdown-item :command="CaseFormMode.EDIT">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="CaseFormMode.DELETE">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </ElTree>
    </div>
  </div>
  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogMode === CaseFormMode.ADD ? '新增分类' : '编辑分类'"
    width="400px"
    @close="resetForm"
  >
    <el-form
      ref="formRef" :model="form" :rules="rules"
      label-width="90px" :inline="false"
    >
      <el-form-item label="分类名称" prop="catalogName">
        <el-input v-model="form.catalogName" placeholder="请输入分类名称" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="submitForm">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.tree-container {
  width: 100%;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 0 10px;
}

/* 横向滚动条只出现在树这块 */
.tree-scroll-inner {
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
}

.search-input {
  width: 90%;
  /* 上 右 下 左，auto 实现水平居中 */
  margin: 10px auto 8px auto;
  /* 让 margin: auto 生效 */
  display: block;
}

.disease-tree {
  width: fit-content;
  min-width: 100%;
  height: calc(100vh - 120px);
  overflow: auto;
}

/* 每个节点的容器 */
.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 省略号图标样式 */
.icon-ellipsis {
  cursor: pointer;
  padding: 0 6px;
  color: #999;
}

/* 仅叶子节点高亮 */
  //TODO 待UI给样式
// .tree-node.leaf-node {

//   // background-color: #f0f9eb;
// }
</style>
