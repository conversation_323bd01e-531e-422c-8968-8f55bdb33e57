@import "./transition.scss";
@import "../index.scss";
@import "@/assets/styles/main.scss";

.layout-container {
  background-color: var(--system-container-main-background);
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  margin: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  &-form {
    display: flex;
    justify-content: space-between;
    padding: 15px 15px 0;

    &-handle {
      display: flex;
      justify-content: flex-start;

      .export-excel-btn {
        margin-left: 15px;
      }
    }

    &-search {
      display: flex;
      justify-content: flex-end;

      .search-btn {
        margin-left: 15px;
      }
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }

  &-table {
    flex: 1;
    height: 100%;
    padding: 15px;
    overflow: auto;
  }
}

.flex-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.center {
  justify-content: center;
  align-items: center;
  text-align: center;
}

a {
  text-decoration: none;
}

/** element-plus **/
.el-icon {
  text-align: center;
}

/** 用于提示信息 **/
.my-tip {
  background-color: #f1f1f1;
  padding: 5px 10px;
  text-align: left;
  border-radius: 4px;
}

.system-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(144, 147, 153, 0.3);
  }

  &:hover {
    &::-webkit-scrollbar {
      display: block;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
  }
}

// 修改button的圆角及默认长度
// .el-button {
//   // border-radius: 0px !important;
//   // min-width: 120px;
//   // min-height: 36px;
// }

.el-tag .el-icon {
  font-size: 1rem !important;
}

// 适用于表格内的的操作按钮
.tableBtn .el-button {
  min-width: 0px;
}

.el-table {
  font-size: 16px !important;
}

//待竖条的标题
.title-prefix {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: rgba(67, 70, 74, 1);

  &::before {
    content: "";
    width: 4px;
    height: 18px;
    display: block;
    background: rgba(54, 122, 194, 1);
    display: inline-block;
    margin-right: 20px;
  }
}

// 选择框最小宽度
// .el-select {
//   min-width: 15rem;
// }

// 表格操作栏的公共样式
.operator-button {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;

  div {
    margin: 0 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: var(--system-word-lighter-color);
  }

  .primary-type {
    color: var(--system-primary-color);
  }
}

// 功能性标签，如添加标签、更多标签等
.operate-tag {
  display: flex;
  align-items: center;
  cursor: pointer;
  div {
    cursor: pointer;
  }
}

// 主面板公共样式
.main-content {
  box-sizing: border-box;
  height: 100%;
  padding: 18px;

  display: flex;
  flex-direction: column;

  // 搜索框card样式
  .search-card {
    margin-bottom: var(--common-padding);

    .el-card__body {
      padding-bottom: 0px;
    }
  }

  .data-card {
    flex: 1;

    .el-card__body {
      height: 100%;
    }

    .data-table {
      height: calc(100% - 150px);
    }

    .option-buttons {
      margin-bottom: 1rem;
    }
  }
}

// 解决鼠标悬浮在el-dropdown时，会有黑色边框问题
.el-dropdown-link:focus {
  outline: none;
}

/* 单行文本省略号样式 */
.ellipsis-single {
  overflow: hidden; /* 隐藏超出容器的内容 */
  text-overflow: ellipsis; /* 超出部分用省略号表示 */
  white-space: nowrap; /* 不换行，强制单行显示 */
}

/* 多行文本省略号样式，可通过 CSS 变量 --line 设置行数，默认2行 */
.ellipsis-multi {
  display: -webkit-box; /* 创建多行盒子模型，兼容 WebKit 浏览器 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  -webkit-box-orient: vertical; /* 垂直排列盒子子项 */
  -webkit-line-clamp: var(--line, 2); /* 限制显示的最大行数，默认 2 行 */
  line-clamp: var(--line, 2); /* 标准语法，部分浏览器支持 */
  white-space: normal; /* 允许文本换行 */
}
