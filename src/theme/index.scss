:root {
  // 主题色
  --system-primary-color: rgb(31, 115, 98); // 可做背景色和文本色，用做背景色时，需要和--system-primary-text-color配合使用，避免文件颜色和主题色冲突
  --system-primary-text-color: #1f7362; // 主题色作为背景色时使用
  --system-word-color: rgb(125, 130, 127); // 正常的文字颜色
  --system-word-lighter-color: rgb(162, 176, 172); // 淡一点的文字颜色，一般用在提示，如"请输入科室名称

  // logo颜色相关
  --system-logo-color: #f1f1f1;
  --system-logo-background: #263445;

  // 菜单颜色相关
  // --system-menu-text-color: rgb(125,130,127,1);
  --system-menu-text-color: #606266;
  --system-menu-background: rgb(31, 115, 98,0.1);
  --system-menu-children-background: rgba(255, 255, 255, 1);
  --system-menu-submenu-active-color: #367AC2;
  --system-menu-hover-background: rgba(233, 236, 239, 1);

  // header区域
  --system-header-background: #fff;
  --system-header-text-color: #303133;
  --system-header-breadcrumb-text-color: #606266;
  --system-header-item-hover-color: rgba(0, 0, 0, .06);
  --system-header-border-color: #d8dce5;
  --system-header-tab-background: #fff;

  // contaier区域，父框架
  --system-container-background: rgba(233, 236, 239, 1);
  --system-container-main-background: #fff;

  // 页面区域, 这一块是你在自己写的文件中使用主题，核心需要关注的地方
  --system-page-background: #fff; // 主背景
  --system-page-color: #303133; // 主要的文本颜色
  --system-page-tip-color: rgba(0, 0, 0, 0.45); // 协助展示的文本颜色
  --system-page-border-color: #ebeef5; // 通用的边框配置色，便于主题扩展

  // element主题色修改
  --el-color-primary: var(--system-primary-color);

  // element默认字号修改
  --el-font-size-base:var(--common-font-size) !important;
  // --el-select-font-size: 14px;
}

.el-checkbox {
  --el-checkbox-font-size: 16px !important;
}

// 进度条颜色修改为主题色
body #nprogress .bar {
  background-color: var(--system-primary-color);
}

body #nprogress .peg {
  box-shadow: 0 0 10px var(--system-primary-color), 0 0 5px var(--system-primary-color);
}

body #nprogress .spinner-icon {
  border-top-color: var(--system-primary-color);
  border-left-color: var(--system-primary-color);
}

// 设置div默认鼠标形状
div {
  cursor: default;
}

@import './modules/dark.scss';
