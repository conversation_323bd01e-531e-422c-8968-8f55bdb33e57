import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import type { UploadFile } from 'element-plus'
import {
  smartUploadFile, // 使用新的智能上传函数
} from '@/api/file'
import type { FileUpload } from '@/types/file'

export const useFileStore = defineStore('file', () => {
  // 上传配置和状态
  const uploadConfig = ref({
    speedLimit: 10, // MB/s
    maxConcurrent: 2,
    enabled: true,
  })
  const uploadStats = ref({
    totalFiles: 0,
    completedFiles: 0,
    failedFiles: 0,
    currentSpeed: 0,
    estimatedTime: 0,
    totalBytes: 0,
    uploadedBytes: 0,
    startTime: 0,
  })

  // 单个文件上传状态追踪
  const fileUploadStats = ref<Map<string, {
    startTime: number
    uploadedBytes: number
    totalBytes: number
    lastUpdateTime: number
  }>>(new Map())

  // 上传控制相关
  const uploadControllers = ref<Map<string, AbortController>>(new Map())
  const isCancellingUploads = ref(false)

  async function uploadDocument(file: File, onProgress?: (progress: number) => void) {
    const fileId = `${file.name}-${file.size}-${Date.now()}`

    // 创建 AbortController 用于取消上传
    const controller = new AbortController()
    uploadControllers.value.set(fileId, controller)

    try {
      // 显示文件大小信息（便于调试）
      // const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2)
      // console.log(`Uploading file: ${file.name}, size: ${fileSizeMB}MB`)

      // 检查是否已被取消
      if (controller.signal.aborted)
        throw new Error('Upload cancelled')

      // 更新上传统计
      uploadStats.value.totalFiles++
      uploadStats.value.totalBytes += file.size

      // 如果是第一个文件，记录开始时间
      if (uploadStats.value.totalFiles === 1)
        uploadStats.value.startTime = Date.now()

      // 初始化文件上传统计
      fileUploadStats.value.set(fileId, {
        startTime: Date.now(),
        uploadedBytes: 0,
        totalBytes: file.size,
        lastUpdateTime: Date.now(),
      })

      // 1. 使用智能上传（根据文件大小和网络环境选择上传方式）
      const uploadResult = await smartUploadFile(
        file,
        (progress) => {
          // 检查是否已被取消 - 如果已取消，直接返回，不执行任何操作
          if (controller.signal.aborted)
            return

          // 更新单个文件进度
          const fileStat = fileUploadStats.value.get(fileId)
          if (fileStat) {
            const now = Date.now()
            const currentBytes = Math.round((progress / 100) * file.size)

            fileStat.uploadedBytes = currentBytes
            fileStat.lastUpdateTime = now

            // 计算当前文件速度
            const elapsed = (now - fileStat.startTime) / 1000 // 秒
            let currentSpeed = 0
            if (elapsed > 0)
              currentSpeed = (currentBytes / elapsed) / (1024 * 1024) // MB/s

            // 更新全局上传统计
            updateGlobalUploadStats(currentSpeed)

            console.log(`File ${file.name} progress: ${progress}%, speed: ${currentSpeed.toFixed(2)} MB/s`)
          }

          // 传递进度回调
          if (onProgress)
            onProgress(progress)
        },
        controller.signal, // 传递 AbortSignal
      )

      // 检查是否已被取消
      if (controller.signal.aborted)
        throw new Error('Upload cancelled')

      // 验证上传结果
      if (!uploadResult || !uploadResult.fileName || !uploadResult.filePath) {
        console.error('Invalid upload result:', uploadResult)
        throw new Error('Upload result is invalid')
      }

      // 更新成功统计
      uploadStats.value.completedFiles++
      uploadStats.value.uploadedBytes += file.size

      // 清理文件统计和控制器
      fileUploadStats.value.delete(fileId)
      uploadControllers.value.delete(fileId)

      console.log(`Upload completed: ${file.name}`)
      return uploadResult
    }
    catch (err: any) {
      console.error('Upload document error:', err)

      // 如果是取消操作，不当作错误处理
      if (err.name === 'AbortError' || err.message === 'Upload cancelled') {
        console.log(`Upload cancelled: ${file.name}`)
        // 清理文件统计和控制器
        fileUploadStats.value.delete(fileId)
        uploadControllers.value.delete(fileId)
        return null
      }

      // 更新失败统计
      uploadStats.value.failedFiles++

      // 清理文件统计和控制器
      fileUploadStats.value.delete(fileId)
      uploadControllers.value.delete(fileId)

      // 增强错误信息
      if (err.message?.includes('Unsupported network type'))
        err.message = `网络环境不支持或配置错误: ${err.message}`
      else if (err.message?.includes('policy'))
        err.message = `上传策略获取失败: ${err.message}`
      else if (err.message?.includes('Upload result is invalid'))
        err.message = `上传结果无效，请重试: ${err.message}`

      throw err
    }
  }

  // 取消单个文件上传
  function cancelFileUpload(fileId: string) {
    const controller = uploadControllers.value.get(fileId)
    if (controller) {
      controller.abort()
      console.log(`Cancelled upload for file: ${fileId}`)
    }
  }

  // 取消所有上传
  function cancelAllUploads() {
    isCancellingUploads.value = true

    // 取消所有进行中的上传
    uploadControllers.value.forEach((controller, fileId) => {
      controller.abort()
      console.log(`Cancelled upload for file: ${fileId}`)
    })

    // 清理所有状态
    uploadControllers.value.clear()
    fileUploadStats.value.clear()

    // 重置上传统计
    resetUploadStats()

    isCancellingUploads.value = false
    console.log('All uploads cancelled')
  }

  // 检查是否有进行中的上传
  const hasActiveUploads = computed(() => {
    return uploadControllers.value.size > 0
  })

  // 更新全局上传统计
  function updateGlobalUploadStats(_currentFileSpeed: number) {
    const now = Date.now()

    // 计算所有文件的平均速度
    let totalSpeed = 0
    let activeFiles = 0

    fileUploadStats.value.forEach((stat) => {
      const fileElapsed = (now - stat.startTime) / 1000
      if (fileElapsed > 0 && stat.uploadedBytes > 0) {
        const fileSpeed = (stat.uploadedBytes / fileElapsed) / (1024 * 1024)
        totalSpeed += fileSpeed
        activeFiles++
      }
    })

    // 更新当前速度（所有活跃文件的速度之和）
    uploadStats.value.currentSpeed = totalSpeed

    // 计算预计剩余时间
    const remainingBytes = uploadStats.value.totalBytes - uploadStats.value.uploadedBytes
    if (totalSpeed > 0)
      uploadStats.value.estimatedTime = Math.ceil(remainingBytes / (totalSpeed * 1024 * 1024))
    else
      uploadStats.value.estimatedTime = 0

    console.log(`Global upload stats - Speed: ${totalSpeed.toFixed(2)} MB/s, Active files: ${activeFiles}`)
  }

  // 重置上传统计
  function resetUploadStats() {
    uploadStats.value = {
      totalFiles: 0,
      completedFiles: 0,
      failedFiles: 0,
      currentSpeed: 0,
      estimatedTime: 0,
      totalBytes: 0,
      uploadedBytes: 0,
      startTime: 0,
    }
    fileUploadStats.value.clear()
    uploadControllers.value.clear()
    isCancellingUploads.value = false
  }

  // 更新上传配置
  function updateUploadConfig(config: Partial<typeof uploadConfig.value>) {
    uploadConfig.value = { ...uploadConfig.value, ...config }
  }

  // 通用上传方法
  async function uploadFilesWithProgress(files: UploadFile[], onProgress?: (progress: number) => void) {
    const uploadQueue: Promise<any>[] = []
    const successFileList: any[] = []
    const cancelFileList: any[] = []
    const failedFileList: any[] = []

    const total = files.length
    let completed = 0

    files.forEach((item, index) => {
      if (!item.raw) {
        failedFileList.push({ fileInfo: item })
        return
      }

      uploadQueue[index] = new Promise((resolve, reject) => {
        uploadDocument(item.raw!, (progress: number) => {
        // 单文件时显示实时进度，多文件时按完成比例显示
          if (total === 1)
            onProgress?.(Math.trunc(progress))
        }).then((fileInfo: FileUpload | null) => {
          successFileList.push(fileInfo)
          resolve(true)
        }).catch((err) => {
          if (err.name === 'AbortError' || err.message?.includes('Upload cancelled')) {
            console.log(`上传已取消: ${item.raw!.name}`)
            cancelFileList.push({ fileInfo: item })
            resolve(true)
          }
          else {
            failedFileList.push({ fileInfo: item })
            reject(err)
          }
        }).finally(() => {
          completed++
          if (total > 1)
            onProgress?.(Math.trunc((completed / total) * 100))
        })
      })
    })

    await Promise.allSettled(uploadQueue)
    return { successFileList, failedFileList }
  }

  return {
    uploadConfig,
    uploadStats,
    isCancellingUploads,
    hasActiveUploads,

    uploadDocument,
    uploadFilesWithProgress,
    resetUploadStats,
    updateUploadConfig,
    cancelFileUpload,
    cancelAllUploads,
  }
})
