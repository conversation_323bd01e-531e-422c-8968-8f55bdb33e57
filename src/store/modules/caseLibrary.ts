import { defineStore } from 'pinia'
import { AddUserCatalog, GetDiseaseTreeList, GetUserCatalogTreeList } from '@/api/case-library'
import { getDicType } from '@/api/auth'
import { DictTypeEnum } from '@/enums/common'
import type { DictOption } from '@/types/common'

export const useCaseLibraryStore = defineStore('caseLibrary', () => {
  // #region 疾病树数据
  // 当前选中的疾病Id
  const selectedDiseaseId = ref<number | null>(null)
  // 疾病树数据
  const diseaseTreeData = ref()

  // 获取疾病树数据
  async function GetDiseaseTreeData() {
    diseaseTreeData.value = await GetDiseaseTreeList()
  }
  // #endregion

  // #region 自定义分类树数据
  const customCategoryTreeData = ref()
  // 当前选中的收藏目录Id
  const selectedCatalogId = ref<number | null>(null)
  /**
   * 获取自定义分类树数据
   */
  async function getCustomCategoryTreeData() {
    const FOLLOWUP_NAME = '随访病例'
    customCategoryTreeData.value = await GetUserCatalogTreeList()

    // 检查第一层是否已存在“随访病例”
    const hasFollowup = customCategoryTreeData.value.some((node: any) => node.label === FOLLOWUP_NAME)

    if (!hasFollowup) {
      const submitInfo = {
        catalogName: FOLLOWUP_NAME,
        parentId: 0,
      }

      await AddUserCatalog(submitInfo)

      // 重新加载目录树数据，确保 UI 更新
      customCategoryTreeData.value = await GetUserCatalogTreeList()
    }
  }

  // #endregion

  // #region 初始化字典

  const caseCategoryOptions = ref<DictOption[]>([]) // 病例类型
  const caseDifficultyOptions = ref<DictOption[]>([]) // 病例难度
  const followTypeOptions = ref<DictOption[]>([]) // 病例随访类型
  const positionMatchOptions = ref<DictOption[]>([]) // 病例随访定位判断
  const qualityMatchOptions = ref<DictOption[]>([]) // 病例随访类型判断
  const deviceTypeOptions = ref<DictOption[]>([]) // 检查类型
  const patientTypeOptions = ref<DictOption[]>([]) // 患者就诊类型

  /**
 * 初始化字典
 */
  async function initDict() {
    caseCategoryOptions.value = await getDicType({ dictType: DictTypeEnum.CASE_TYPE })
    caseDifficultyOptions.value = await getDicType({ dictType: DictTypeEnum.CASE_DIFFICULTY })
    followTypeOptions.value = await getDicType({ dictType: DictTypeEnum.FOLLOW_TYPE })
    positionMatchOptions.value = await getDicType({ dictType: DictTypeEnum.POSITION_MATCH })
    qualityMatchOptions.value = await getDicType({ dictType: DictTypeEnum.QUALITY_MATCH })
    deviceTypeOptions.value = await getDicType({ dictType: DictTypeEnum.DEVICE_TYPE })
    patientTypeOptions.value = await getDicType({ dictType: DictTypeEnum.PATIENT_TYPE })
  }

  // #endregion

  return {
    customCategoryTreeData,
    selectedDiseaseId,
    selectedCatalogId,
    diseaseTreeData,
    caseCategoryOptions,
    caseDifficultyOptions,
    followTypeOptions,
    positionMatchOptions,
    qualityMatchOptions,
    deviceTypeOptions,
    patientTypeOptions,

    getCustomCategoryTreeData,
    GetDiseaseTreeData,
    initDict,
  }
})
