import { defineStore } from 'pinia'
import JSEncrypt from 'jsencrypt'
import { ElMessage } from 'element-plus'
import type { RouteMeta, RouteRecordRaw } from 'vue-router'
import {
  GetCurrentAuthUserInfo,
  GetPublicKey,
  Login,
  Logout,
  getMenuList,
  updatePasswordApi,
} from '@/api/auth'
import { ACCESS_TOKEN_KEY } from '@/enums/cacheEnum'
import { Storage } from '@/utils/storage'
import { router } from '@/router'
import { handleMenuList } from '@/utils/permission'
import { GetCaseLibTypeList } from '@/api/case-library'
import type { CaseLibraryItem } from '@/types/caseLibrary'
import { sortRoutes } from '@/utils/commonLogic'

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const platTag = 'case_library'
  const userInfo = ref()
  const userName = computed(() => userInfo.value?.nickName ?? '--')
  const menuList = ref<any[]>([])
  const functionList = ref<any[]>([])
  const roleList = ref([])
  const selectCaseTypeId = ref<number | null>() // 当前选择的病例库类型id

  /**
   * 清空token及用户信息
   */
  function resetToken() {
    token.value = ''
    userInfo.value = {}
    menuList.value = []
    functionList.value = []
    Storage.remove(ACCESS_TOKEN_KEY)
  }

  /**
   * 登录成功保存token
   * @param newToken token
   */
  function setToken(newToken: string) {
    token.value = newToken ?? ''
    const ex = 10 * 60 * 60
    Storage.set(ACCESS_TOKEN_KEY, token.value, ex)
  }
  /**
   * 登录
   */
  async function login(params: object) {
    const token = await Login(params)
    setToken(token)
    await getUserInfo()
    return token
  }

  /** 登出 */
  async function logout(isNeedNotify = true) {
    try {
      if (isNeedNotify) {
        ElMessage.error({
          message: '登录状态已失效或已过期，请重新登录',
          duration: 0,
          showClose: true,
          grouping: true,
        })
      }

      await Logout()
      // ElMessage.error({ message: '登录状态已失效或已过期，请重新登录', duration: 0, showClose: true, grouping: true })
      // await Logout()
    }
    catch (e) {
    }
    finally {
      resetToken()
      router.replace('/login')
    }
  }

  /** 判断是否已登录 */
  function isLogin() {
    // 首先判断token在不在
    if (token.value)
      return true

    // 然后判断之前是否登陆过
    const t = Storage.get(ACCESS_TOKEN_KEY)
    if (!t) {
      console.log('[isLogin] token is not exist in storage')
      return false
    }

    token.value = t
    // 判断userInfo是否存在
    if (!userInfo.value)
      getUserInfo()

    return true
  }
  /** 获取用户信息 */
  async function getUserInfo() {
    try {
      const res = await GetCurrentAuthUserInfo()
      userInfo.value = res || {}
      roleList.value = res.roles || []
      return userInfo.value
    }
    catch (error) {
      return Promise.reject(error)
    }
  }
  /** 获取用户权限息 */
  async function getRouteInfo() {
    try {
      const res = await getMenuList()
      const { newMenuList = [], funcList = [] } = handleMenuList(
        res || [],
        [],
        [],
      )
      menuList.value = newMenuList
      functionList.value = funcList
      return menuList.value
    }
    catch (error) {
      return Promise.reject(error)
    }
  }

  /**
   * 修改密码
   * @param params 新密码和旧密码
   * @returns
   */
  async function updatePassword(params: any) {
    const rsaKey = await GetPublicKey()
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(rsaKey)
    params.newPassword = encrypt.encrypt(params.newPassword) as string
    params.oldPassword = encrypt.encrypt(params.oldPassword) as string
    return new Promise<any>((resolve, reject) => {
      updatePasswordApi(params)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  }

  // 更改头像
  function updateAvatar(params: any) {
    return new Promise<any>((resolve, reject) => {
      updateAvatar(params)
        .then((res) => {
          getUserInfo()
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const isInitDynamicRoute = ref(false)
  /**
   * 初始化动态路由
   */
  async function initDynamicRoute() {
    if (isInitDynamicRoute.value)
      return
    const res = await GetCaseLibTypeList()
    if (res && res.length > 0) {
      res.forEach((item: CaseLibraryItem) => {
        addCustomCaseLibrary(item)
      })
    }

    isInitDynamicRoute.value = true
  }

  /** title: string, path: string
   * 添加自定义病例库
   */
  function addCustomCaseLibrary(caseLibraryItem: CaseLibraryItem) {
    // 检查是否已存在该 path
    const department = router.options.routes.find(
      route => route.name === 'DepartmentCaseLibrary',
    )
    if (!department || !department.children)
      return

    const exists = department.children.some(child => child.path === caseLibraryItem.address)
    if (exists)
      return // 已存在，不重复添加

    // 构造新的子路由
    const newRoute: RouteRecordRaw = {
      path: caseLibraryItem.address,
      component: () =>
        import('@/views/department-case-library/custom-case-library/index.vue'),
      meta: { title: caseLibraryItem.caseTypeName, caseTypeId: caseLibraryItem.caseTypeId, order: 10 },
    }

    // 添加到菜单源
    department.children.push(newRoute)
    // 路由排序
    sortRoutes(department.children)
    // 添加到实际 router 中
    router.addRoute('DepartmentCaseLibrary', newRoute)
  }

  /**
   * 更新选择的病例库类型id
   */
  function updateSelectCaseTypeId(meta: RouteMeta) {
    const caseTypeId = typeof meta.caseTypeId === 'number' ? meta.caseTypeId : null

    selectCaseTypeId.value = caseTypeId
  }

  return {
    userName,
    token,
    platTag,
    userInfo,
    menuList,
    functionList,
    roleList,
    selectCaseTypeId,

    resetToken,
    setToken,
    login,
    logout,
    isLogin,
    getUserInfo,
    getRouteInfo,
    updatePassword,
    updateAvatar,

    initDynamicRoute,
    addCustomCaseLibrary,
    updateSelectCaseTypeId,
  }
})
