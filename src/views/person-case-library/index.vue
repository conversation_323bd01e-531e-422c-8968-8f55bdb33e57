<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { CaseLibraryType } from '@/enums/caseLibrary'

const caseLibraryStore = useCaseLibraryStore()
// 处理子组件发出的事件
function handleLeafSelected(id: number | null) {
  console.log(`Selected leaf ID: ${id}`)
  caseLibraryStore.selectedCatalogId = id
}
</script>

<template>
  <el-container>
    <el-aside width="300px">
      <div class="tree-scroll-wrapper">
        <custom-category-tree @leaf-selected="handleLeafSelected" />
      </div>
    </el-aside>
    <el-main>
      <case-search-list :case-library-type="CaseLibraryType.Personal" />
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.el-container {
  height: 100%;

  .el-aside {
    background-color: #fff;

    /* 不再对 el-aside 设置 overflow */
    .tree-scroll-wrapper {
      width: 100%;
      overflow-x: hidden;
      /* 让 disease-tree 自己控制横向滚动 */
    }
  }

  .el-main {
    padding: 0;
    .el-tabs{
      box-sizing: border-box;
      :deep(.el-tabs__content){
        padding: 0px;
        height: calc(100% - 40px) ;
      }
    }

  }
}

:deep(.el-select__tags) {
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
}
</style>
