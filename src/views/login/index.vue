<script lang="ts" setup>
import { Hide, Lock, User, View } from '@element-plus/icons-vue'
import { onBeforeMount, onMounted, reactive, ref } from 'vue'

import { ElMessage, type FormInstance } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import storage from '@/utils/storage'
import defaultBgImg from '@/assets/images/bg1.png'
import defaultLogoImg from '@/assets/images/login_jusha.png'
import defaultSystemLogo from '@/assets/icons/system-logo-inner.png'
import { getRsaVal } from '@/utils/commonLogic'
import { validateCommonField, validatePassword } from '@/utils/validate'
import router from '@/router'

const systemName = ref('')

interface FormState {
  username: string
  password: string
  remember: boolean
}
interface UserPwdState {
  arr: Array<FormState>
}
const formState = reactive<FormState>({
  username: '',
  password: '',
  remember: false,
})
const rules = {
  username: [{ required: true, validator: validateIsEmpty, trigger: 'change' },
    { validator: validateC<PERSON><PERSON><PERSON>ield, trigger: 'blur' }],
  password: [{ required: true, validator: validateIsEmpty, trigger: 'change' },
    { validator: validatePassword, trigger: 'blur' }],
}
// 已记住的用户名密码列表
const userPwdState = reactive<UserPwdState>({
  arr: [
  ],
})

const userStore = useUserStore()
const backImage = ref(defaultBgImg)
const jushaLogoImg = ref(defaultLogoImg)
const systemLogo = ref(defaultSystemLogo)

// 记住密码
// 逻辑：如果勾选了“记住密码”，先把密码加密，然后组合成一个对象数组（这样可以保存多人的密码）存储到localstorage
//       如果没勾选“记住密码”，并且当前localstorage中存有用户名密码，则从当前localstorage中存有的该用户名删去
const savePwd = (password: string) => {
  const storedUserPwd = storage.get('userPwd') || []
  const base64Password = window.btoa(password)
  if (formState.remember) {
    const userPwd = {
      username: formState.username,
      password: base64Password,
    }
    const hasCurUser = storedUserPwd?.find(
      (item: any) => item.username === formState.username,
    )
    // 是否已存储了当前用户，无则存储，有则更新（用户可能改密码，需要更新）
    if (hasCurUser) {
      const newShouldStore = storedUserPwd.map((item: any) => {
        if (item.username === formState.username)
          item.password = base64Password

        return item
      })
      storage.set('userPwd', newShouldStore)
    }
    else {
      storage.set('userPwd', [...storedUserPwd, userPwd])
    }
  }
  else if (storedUserPwd) {
    const curUserPwd = storedUserPwd.filter(
      (item: any) => item.username !== formState.username,
    )
    storage.set('userPwd', curUserPwd)
  }
}
// 获取已记住的用户名密码列表
const getPwd = () => {
  const storedUserPwd = storage.get('userPwd')
  userPwdState.arr = storedUserPwd || []
}
const setSavedPwd = (userPwd: FormState) => {
  formState.username = userPwd.username
  formState.password = window.atob(userPwd.password) // base64解码
  formState.remember = true // 使用记住密码中的账号登录时，记住密码为“已勾选”状态
}
onMounted(async () => {
  getPwd()
})

onBeforeMount(() => {
  // getBgAndLogo()
})

const ruleFormRef = ref<FormInstance>()
const onClickLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl)
    return
  await formEl.validate(async (valid) => {
    ElMessage.closeAll()
    // 密码长度大于20说明是自动填充的md5加密密码，放过校验，手动输入的密码不会超过20
    if (formState.password.length > 20 || valid) {
      const password = await getRsaVal(formState.password)
      // const password = formState.password
      const loginParams = {
        username: formState.username,
        password,
      }

      await userStore.login(loginParams)
      savePwd(formState.password)
      router.replace('/')
    }
    else {
      console.log('密码错误', formState.password)
    }
  })
}
// 增加enter快捷键登录
window.addEventListener('keydown', (event) => {
  if (event.code === 'Enter' || event.code === 'NumpadEnter')
    onClickLogin(ruleFormRef.value)
}, true)
const showPassword = ref(false)
</script>

<template>
  <div
    class="login-wrap"
    :style="`background:url(${backImage}) no-repeat;background-size:cover;  background-size: 100% 100%;`"
  >
    <!-- <div class="left">
      <img class="system-logo" :class="{ 'from-set': settingStore.pageInfo.relativeHospitalPicUrl }" :src="systemLogo">
      <img class="jusha-logo" :src="jushaLogoImg">
    </div> -->
    <div class="top-box">
      <img class="system-logo" :src="systemLogo || defaultSystemLogo">
      <div class="product-name">
        <span> {{ systemName || $t("pages.layout.header.productName") }} </span>
      </div>
    </div>
    <div class="right-box">
      <div class="welcome-name">
        {{ $t("pages.login.welcome") }}
      </div>
      <div style="padding: 0 15%">
        <el-form ref="ruleFormRef" :model="formState" :rules="rules" label-width="0" size="large">
          <el-form-item prop="username" class="input-item">
            <div v-if="userPwdState.arr?.length === 0" class="input-wrapper">
              <!-- el-input用来占位不过不展示，通过这种写法来使得rules生效 -->
              <el-input v-model.trim="formState.username" style="display: none;" />
              <input v-model="formState.username" class="input-text" maxlength="20" placeholder="请输入账号">
              <el-icon class="inner-icon icon-left" color="#A4A9B0" :size="32">
                <User />
              </el-icon>
            </div>
            <el-dropdown v-if="userPwdState.arr?.length > 0" style="width: 100%">
              <div style="width: 100%" class="el-dropdown-link input-wrapper">
                <!-- el-input用来占位不过不展示，通过这种写法来使得rules生效 -->
                <el-input v-model.trim="formState.username" style="display: none;" />
                <input v-model="formState.username" class="input-text" maxlength="20" placeholder="请输入账户名称">
                <el-icon class="inner-icon icon-left" color="#A4A9B0" :size="32">
                  <User />
                </el-icon>
              </div>
              <template #dropdown>
                <el-scrollbar max-height="192px">
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="item in userPwdState.arr" :key="item.username" @click="setSavedPwd(item)">
                      {{ item.username }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-scrollbar>
              </template>
            </el-dropdown>
          </el-form-item>
          <el-form-item prop="password" class="input-item">
            <div class="input-wrapper">
              <!-- el-input用来占位不过不展示，通过这种写法来使得rules生效 -->
              <el-input v-model.trim="formState.password" style="display: none;" />
              <input
                v-model="formState.password" class="input-text" :type="showPassword ? 'text' : 'password'"
                maxlength="20" placeholder="请输入密码"
              >
              <el-icon class="inner-icon icon-left" color="#A4A9B0" :size="32">
                <Lock />
              </el-icon>
              <el-icon
                v-show="formState.password && formState.password.length <= 20 && showPassword"
                class="inner-icon icon-right cursor-pointer" color="#A4A9B0" :size="26" @click="showPassword = false"
              >
                <View />
              </el-icon>
              <el-icon
                v-show="formState.password && formState.password.length <= 20 && !showPassword"
                class="inner-icon icon-right cursor-pointer" color="#A4A9B0" :size="26" @click="showPassword = true"
              >
                <Hide />
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item prop="remember">
            <el-checkbox v-model="formState.remember" label="记住密码" size="large" />
          </el-form-item>
          <el-form-item prop="userPwdConfirm">
            <el-button type="primary" size="large" class="login-button" @click="onClickLogin(ruleFormRef)">
              {{ $t("pages.login.log_in") }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="jusha-logo">
          <img :src="jushaLogoImg">
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 重写el-input样式
:deep(.el-input__prefix-inner) {
  font-size: 28px;
}

:deep(.el-input .el-input__icon) {
  font-size: 25px;
}

:deep(.el-checkbox.el-checkbox--large) {
  color: rgba(164, 169, 176, 1);
  margin-left: 15px;
}

.login-wrap {
  display: flex;
  justify-content: end;
  align-items: center;
  height: 100%;
  position: relative;

  .top-box {
    position: absolute;
    left: 0;
    top: 0;
    height: 60px;
    display: flex;
    align-items: center;

    .system-logo {
      width: 32px;
      margin-left: 20px;
    }

    .product-name {
      margin-left: 10px;

      span {
        font-size: 22px;
        font-weight: 400;
        line-height: 30px;
        color: var(--system-primary-text-color);
        text-align: left;
        vertical-align: top;

        font-family: 'Kingsoft_Cloud_Font';
      }
    }
  }

  .right-box {
    width: 405px;
    height: 470px;
    border-radius: 2px;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    padding-top: 55px;
    margin-right: 15%;

    .welcome-name {
      text-align: left;
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 50px;
      padding-left: 15%;
    }

    .login-button {
      width: 100%;
      border-radius: 4px !important;
    }

    .input-wrapper {
      position: relative;
      width: 100%;

      .input-text {
        // width: 100%;
        height: 44px;
        padding-left: 60px;
        border-width: 0 0 1px 0;
        border-color: #A4A9B0;
        font-size: 18px;

        &:focus {
          outline: none;
          border-color: var(--system-primary-color)
        }

        &::input-placeholder {
          color: #A4A9B0;
        }

        &::-webkit-input-placeholder {
          color: #A4A9B0;
          font-size: 18px;
        }
      }

      .inner-icon {
        position: absolute;
        bottom: 8px;

        &.icon-left {
          left: 8px;
        }

        &.icon-right {
          right: 5px;
        }
      }
    }

    .el-dropdown-link:focus {
      outline: none;
    }

    .input-item {
      &.is-error .input-text {
        border-color: red !important;
      }
    }

    .jusha-logo {
      display: flex;
      justify-content: center;

      img {
        width: 114px;
      }
    }
  }

}
</style>
