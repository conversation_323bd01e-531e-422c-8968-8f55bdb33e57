<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { CaseLibraryType, FollowStatus } from '@/enums/caseLibrary'
// const tabs = [
//   { label: '待随访', status: FollowStatus.Pending },
//   { label: '已随访', status: FollowStatus.Completed },
//   { label: '全部', status: FollowStatus.All },
// ]

const tabs = [
  { label: '待随访', status: FollowStatus.Pending, name: 'todo' },
  { label: '已随访', status: FollowStatus.Completed, name: 'done' },
  { label: '全部', status: FollowStatus.All, name: 'all' },
]
// 根据选中 tab 找出对应的 status
const currentStatus = computed(() => {
  return tabs.find(tab => tab.name === activeName.value)?.status || FollowStatus.Pending
})

// 当前选中的 tab 名称
const activeName = ref('todo')
</script>

<template>
  <el-container>
    <el-header>
      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane
          v-for="tab in tabs"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        />
      </el-tabs>
    </el-header>
    <el-main>
      <case-search-list :followup-status="currentStatus" :case-library-type="CaseLibraryType.FollowUp" />
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.el-container {
  height: 100%;
  .el-header{
    padding: 0px;
    height: 40px;
    .el-tabs{
      box-sizing: border-box;
      :deep(.el-tabs__content){
        padding: 0px;
        height: calc(100% - 40px) ;
      }
    }
  }
  .el-main {
    padding: 0;
  }
}
</style>
