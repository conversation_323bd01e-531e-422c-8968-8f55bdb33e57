<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { CaseLibraryType } from '@/enums/caseLibrary'
</script>

<template>
  <el-container>
    <el-main>
      <case-search-list :case-library-type="CaseLibraryType.Custom" />
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.el-container {
  height: 100%;
  .el-main {
    padding: 0;
  }
}
</style>
