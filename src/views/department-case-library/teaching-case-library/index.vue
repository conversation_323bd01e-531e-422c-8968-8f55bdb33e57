<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { DArrowLeft, DArrowRight, Expand, Fold } from '@element-plus/icons-vue'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { CaseLibraryType } from '@/enums/caseLibrary'

const caseLibraryStore = useCaseLibraryStore()
const asideCollapsed = ref(false)
// 处理子组件发出的事件
function handleLeafSelected(id: number | null) {
  console.log(`Selected leaf ID: ${id}`)
  caseLibraryStore.selectedDiseaseId = id
}
</script>

<template>
  <el-container>
    <!-- 左侧栏 -->
    <el-aside :width="asideCollapsed ? '0px' : '300px'" class="aside-wrapper">
      <div v-show="!asideCollapsed" class="tree-scroll-wrapper">
        <disease-category-tree @leaf-selected="handleLeafSelected" />
      </div>
    </el-aside>

    <!-- 中间按钮 -->
    <!-- <div class="collapse-toggle">
      <el-tooltip :content="asideCollapsed ? '展开分类' : '收起分类'" placement="right">
        <el-icon style="cursor: pointer;" @click="asideCollapsed = !asideCollapsed">
          <component :is="asideCollapsed ? DArrowRight : DArrowLeft" />
        </el-icon>
      </el-tooltip>
    </div> -->
    <el-main>
      <template v-if="caseLibraryStore.selectedDiseaseId != null">
        <el-tabs type="border-card">
          <el-tab-pane label="病例列表">
            <case-search-list :case-library-type="CaseLibraryType.Teaching" />
          </el-tab-pane>
          <el-tab-pane label="疾病概述">
            <DiseaseOverview />
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-else>
        <case-search-list :case-library-type="CaseLibraryType.Teaching" />
      </template>
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.el-tabs{
  height: 100%;
  .el-tab-pane{
    height: 100%;
  }
}
.el-container {
  height: 100%;

  .el-aside {
    background-color: #fff;

    /* 不再对 el-aside 设置 overflow */
    .tree-scroll-wrapper {
      width: 100%;
      overflow-x: hidden;
      /* 让 disease-tree 自己控制横向滚动 */
    }
  }

  .el-main {
    padding: 0;
    .el-tabs{
      box-sizing: border-box;
      :deep(.el-tabs__content){
        padding: 0px;
        height: calc(100% - 40px) ;
      }
    }

  }
}

/* 左侧区域动画过渡 */
.aside-wrapper {
  transition: width 0.3s ease;
  overflow: hidden;
}

/* 中间按钮区域 */
.collapse-toggle {
  width: 16px;
  // background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  // border-left: 1px solid #dcdfe6;
  // border-right: 1px solid #dcdfe6;
  transition: background-color 0.2s;
}

// .collapse-toggle:hover {
//   background-color: #e0e0e0;
// }
</style>
