<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  FormInstance,
  FormRules,
  UploadFile,
  UploadProps,
  UploadRequestOptions,
  UploadUserFile,
} from 'element-plus'

import { Plus } from '@element-plus/icons-vue'
import { GetDiseaseOverview, UpdateDiseaseOverview } from '@/api/case-library'
import type { OverviewData } from '@/types/caseLibrary'
import { validateFileByType } from '@/utils/commonLogic'
import { useFileStore } from '@/store/modules/file'
import type { FileUpload } from '@/types/file'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'

// const props = defineProps<{
//   diseaseId: number | null
// }>()
// 当前折叠项
const activeNames = ref([
  'overview',
  'pathology',
  'clinical',
  'imaging',
  'keyframes',
  'diagnosisPoints',
  'differentialDiagnosis',
])
const editing = ref(false)
const formRef = ref<FormInstance | null>(null)
const caseLibraryStore = useCaseLibraryStore()
// 原始数据
const overviewData = ref<OverviewData>({
  diseaseId: 0,
  overview: '',
  pathology: '',
  clinical: '',
  imaging: '',
  diagnosis: '',
  differential: '',
  keyframe: [],
})

/**
 * 重置
 */
const handleReset = () => {
  overviewData.value = {
    diseaseId: 0,
    overview: '',
    pathology: '',
    clinical: '',
    imaging: '',
    diagnosis: '',
    differential: '',
    keyframe: [],
  }
  fileList.value = []
}

// 编辑副本
// const editData = reactive({ ...overviewData })

// 表单规则（可根据需要自定义更严格规则）
const formRules: FormRules = {
  // overview: [{ required: true, message: '请输入概述', trigger: 'blur' }],
  // pathology: [{ required: true, message: '请输入病理表现', trigger: 'blur' }],
  // clinical: [{ required: true, message: '请输入临床表现', trigger: 'blur' }],
  // imaging: [{ required: true, message: '请输入影像学表现', trigger: 'blur' }],
  // keyframe: [{ required: true, message: '请输入关键帧', trigger: 'blur' }],
  // diagnosis: [{ required: true, message: '请输入诊断要点', trigger: 'blur' }],
  // differential: [
  //   { required: true, message: '请输入鉴别诊断', trigger: 'blur' },
  // ],
}

// 编辑控制
const enableEdit = () => {
  // Object.assign(editData, overviewData)
  editing.value = true
}

const cancelEdit = () => {
  editing.value = false
}

// 保存前校验
const handleSubmit = () => {
  if (!formRef.value)
    return
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      overviewData.value.keyframe = fileList.value
        .map(file => file.filePath)
        .filter((url): url is string => !!url) // 过滤掉 undefined 或 null

      await UpdateDiseaseOverview(overviewData.value)
      editing.value = false
      handleSearch()
    }
  })
}

/**
 * 查询方法
 */
const handleSearch = async () => {
  handleReset()
  if (caseLibraryStore.selectedDiseaseId) {
    const res = await GetDiseaseOverview({ diseaseId: caseLibraryStore.selectedDiseaseId })
    if (res.overview) {
      overviewData.value = res
      fileList.value = overviewData.value.keyframe.map((path, index) => {
        return {
          name: `关键帧${index}`,
          url: path,
          filePath: path,
        }
      })
    }

    overviewData.value.diseaseId = caseLibraryStore.selectedDiseaseId
  }
}

// #region 关键帧上传
const fileList = ref<(UploadUserFile & { filePath?: string })[]>([])

/**
 * 自定义上传方法
 */
const handleUpload = async (options: any) => {
  const { file, onProgress } = options
  const uploadResult: FileUpload | null = await useFileStore().uploadDocument(
    file,
    (process) => {
      onProgress?.({ percent: process })
    },
  )
  if (uploadResult) {
    const target = fileList.value.find(f => f.uid === (file as any).uid)
    if (target)
      target.filePath = uploadResult.filePath
  }
}

/**
 * 文件有效性校验
 * @param file 文件
 */
const beforeUpload: UploadProps['beforeUpload'] = (file: File) => {
  const validationResult = validateFileByType(file, 'img')
  if (validationResult) {
    ElMessage.warning(`${validationResult.error}`)
    return false
  }
  return true
}
/**
 * 文件数量超出限制
 */
function handleExceed() {
  ElMessage.warning('上传文件的最大数量不能超过10个')
}
const previewVisible = ref(false)
const previewSrc = ref('')

/**
 * 缩略图放大预览
 * @param uploadFile 文件
 */
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  previewSrc.value = uploadFile.url!
  previewVisible.value = true
}

// #endregion
watch(
  () => caseLibraryStore.selectedDiseaseId,
  (newVal) => {
    handleSearch()
  },
  { immediate: true },
)
</script>

<template>
  <el-card class="disease-overviewData" shadow="never">
    <!-- 编辑/保存按钮 -->
    <template #header>
      <div class="header-toolbar">
        <el-button v-if="!editing" type="primary" @click="enableEdit">
          编辑
        </el-button>
        <div v-else>
          <el-button type="primary" @click="handleSubmit">
            保存
          </el-button>
          <el-button @click="cancelEdit">
            取消
          </el-button>
        </div>
      </div>
    </template>

    <!-- 编辑模式：表单平铺展示 -->
    <el-form
      v-if="editing"
      ref="formRef"
      :model="overviewData"
      :rules="formRules"
      label-width="100px"
      class="edit-form"
      label-position="top"
    >
      <el-form-item label="概述" prop="overview">
        <el-input v-model="overviewData.overview" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="病理表现" prop="pathology">
        <el-input v-model="overviewData.pathology" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="临床表现" prop="clinicalManifestation">
        <el-input v-model="overviewData.clinical" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="影像学表现" prop="imaging">
        <el-input v-model="overviewData.imaging" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="关键帧" prop="keyframe">
        <el-upload
          v-model:file-list="fileList"
          list-type="picture-card"
          :before-upload="beforeUpload"
          :limit="10"
          :multiple="true"
          :http-request="handleUpload"
          :on-preview="handlePictureCardPreview"
          :on-exceed="handleExceed"
          accept=".png,.jpeg,.jpg"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item label="诊断要点" prop="diagnosis">
        <el-input v-model="overviewData.diagnosis" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="鉴别诊断" prop="differential">
        <el-input
          v-model="overviewData.differential"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>

    <!-- 非编辑模式：折叠面板展示 -->
    <el-collapse v-else v-model="activeNames">
      <el-collapse-item title="概述" name="overview">
        {{ overviewData.overview }}
      </el-collapse-item>
      <el-collapse-item title="病理表现" name="pathology">
        {{ overviewData.pathology }}
      </el-collapse-item>
      <el-collapse-item title="临床表现" name="clinical">
        {{ overviewData.clinical }}
      </el-collapse-item>
      <el-collapse-item title="影像学表现" name="imaging">
        {{ overviewData.imaging }}
      </el-collapse-item>
      <el-collapse-item title="关键帧" name="keyframes">
        <div class="keyframe-list">
          <el-image
            v-for="(item, index) in overviewData.keyframe"
            :key="index"
            :src="item"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="overviewData.keyframe"
            :initial-index="index"
            fit="cover"
          />
        </div>
      </el-collapse-item>
      <el-collapse-item title="诊断要点" name="diagnosisPoints">
        {{ overviewData.diagnosis }}
      </el-collapse-item>
      <el-collapse-item title="鉴别诊断" name="differentialDiagnosis">
        {{ overviewData.differential }}
      </el-collapse-item>
    </el-collapse>
  </el-card>
  <el-dialog v-model="previewVisible">
    <img :src="previewSrc" alt="预览图">
  </el-dialog>
</template>

<style scoped lang="scss">
.disease-overviewData {
  padding: 16px;
  height: 100%;
  border: none;
  :deep(.el-card__header) {
    border: none;
    padding-bottom: 0px;
    padding-top: 10px;
  }
  :deep(.el-card__body) {
    height: calc(100% - 150px);
    overflow: auto;
  }
}
img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.keyframe-list {
  display: flex;
  .el-image {
    width: 200px;
    margin-right: 10px;
  }
}

// .header-toolbar {
//   position: absolute;
//   top: 16px;
//   right: 16px;
//   z-index: 1;
// }
</style>
