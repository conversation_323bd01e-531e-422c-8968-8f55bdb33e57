<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { Delete, Edit, User } from '@element-plus/icons-vue'
import { validateCommonField, validateCommonName, validateIsEmpty } from '@/utils/validate'
import { convertToLocaleDateString } from '@/utils/commonLogic'
import { CaseFormMode, pageSizeOptions } from '@/enums/common'
import { AddCaseLibType, AddDisease, DelCaseLibType, GetCaseLibTypeList, UpdateCaseLibType } from '@/api/case-library'
import { YesOrNo, YesOrNoOptions } from '@/enums/caseLibrary'
import { useUserStore } from '@/store/modules/user'
import router from '@/router'

const { t } = useI18n()

const dialogVisible = ref(false)
const dialogMode = ref<CaseFormMode>(CaseFormMode.ADD)
const formRef = ref()
// 表单数据
const formInfo = ref({
  caseTypeId: '',
  caseTypeName: '',
  audit: YesOrNo.No,
})

// 表格接收的数据
const tableData = ref()

// 表单验证规则
const rules = {
  caseTypeName: [
    { required: true, validator: validateIsEmpty, trigger: 'blur' },
    { validator: validateCommonName, trigger: 'blur' },
    { min: 1, max: 10, message: `${t('message.common.verifyInputRange')}1~10`, trigger: 'blur' },
  ],
}

// 处理操作按钮点击事件
const handleCommand = (command: string, nodeData?: any) => {
  switch (command) {
    case CaseFormMode.ADD:
      dialogMode.value = CaseFormMode.ADD
      formInfo.value.caseTypeId = ''
      formInfo.value.caseTypeName = ''
      formInfo.value.audit = YesOrNo.No
      dialogVisible.value = true
      break
    case CaseFormMode.EDIT:
      dialogMode.value = CaseFormMode.EDIT
      formInfo.value.caseTypeId = nodeData.caseTypeId
      formInfo.value.caseTypeName = nodeData.caseTypeName
      formInfo.value.audit = nodeData.audit
      dialogVisible.value = true
      break
    case CaseFormMode.DELETE:
      handleDelete(nodeData.id)
      break
  }
}

/**
 * 提交表单
 */
function submitForm() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid)
      return

    if (dialogMode.value === CaseFormMode.ADD) {
      const submitInfo = {
        caseTypeName: formInfo.value.caseTypeName,
        audit: formInfo.value.audit,
      }
      await AddCaseLibType(submitInfo)
    }
    else if (dialogMode.value === CaseFormMode.EDIT) {
      const submitInfo = {
        caseTypeName: formInfo.value.caseTypeName,
        caseTypeId: formInfo.value.caseTypeId,
        audit: formInfo.value.audit,
      }
      await UpdateCaseLibType(submitInfo)
    }
    dialogVisible.value = false
    handleSearch()
    // window.location.reload()
  })
}

// 删除
const handleDelete = (caseTypeId: number) => {
  ElMessageBox.confirm(
    t('message.common.tooltip.delete'),
    {
      confirmButtonText: t('message.common.confirm'),
      cancelButtonText: t('message.common.cancel'),
      type: 'warning',
    },
  ).then(async () => {
    await DelCaseLibType({ caseTypeId })
    handleSearch()
    // window.location.reload()
    // ElMessageBox.confirm(
    //   '操作成功，菜单栏需要刷新后才能生效,是否立即刷新？',
    //   '提示',
    //   {
    //     confirmButtonText: '立即刷新',
    //     cancelButtonText: '稍后再说',
    //     type: 'warning',
    //   },
    // ).then(() => {

    // }).finally(() => {

    // })
  }).catch(() => { })
}

/**
 *  刷新列表
 */
// 防抖搜索处理函数，延迟触发接口调用
const handleSearch = debounce(300, async () => {
  tableData.value = await GetCaseLibTypeList()
})

/**
 * 重置表单
 */
function resetForm() {
  formRef.value?.resetFields()
}

onMounted(() => {
  handleSearch()
})
</script>

<template>
  <div class="main-content">
    <el-card class="data-card">
      <div class="option-buttons">
        <el-button type="primary" :icon="User" @click="handleCommand(CaseFormMode.ADD)">
          新增病例库
        </el-button>
      </div>
      <div class="data-table">
        <el-table
          :data="tableData" :header-cell-style="{ background: '#eef0ef', color: '#7d827f' }" stripe
          height="100%"
        >
          <el-table-column type="index" label="序号" width="100" align="left">
            <template #default="scope">
              <span style="color: var(--system-word-lighter-color);"> {{ (scope.$index + 1).toString().padStart(3, '0')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="病例库名称" align="center" prop="caseTypeName" />
          <el-table-column label="病例库地址" align="center" prop="address" />
          <el-table-column label="是否需要审核" align="center" prop="audit">
            <template #default="scope">
              <el-tag :type="scope.row.audit === YesOrNo.Yes ? 'success' : 'info'" size="small">
                {{ getOptionLabel(YesOrNoOptions, scope.row.audit) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" align="center" prop="createTime">
            <template #default="scope">
              <span>{{ convertToLocaleDateString(scope.row.createTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="340" fixed="right" align="center">
            <template #default="scope">
              <div class="operator-button">
                <el-tooltip effect="light" content="编辑病例库" placement="top">
                  <div class="primary-type">
                    <el-icon @click="handleCommand(CaseFormMode.EDIT, scope.row)">
                      <Edit />
                    </el-icon>
                  </div>
                </el-tooltip>
                <el-tooltip effect="light" content="删除病例库" placement="top">
                  <div>
                    <el-icon @click="handleDelete(scope.row.caseTypeId)">
                      <Delete />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogMode === CaseFormMode.ADD ? '新增分类' : '编辑分类'"
    width="400px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formInfo"
      :rules="rules"
      label-width="120px"
      :inline="false"
    >
      <el-form-item label="病例库名称" prop="caseTypeName">
        <el-input v-model="formInfo.caseTypeName" placeholder="请输入病例库名称" />
      </el-form-item>
      <el-form-item label="是否需要审核" prop="audit">
        <el-select v-model="formInfo.audit" placeholder="请选择">
          <el-option v-for="item in YesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="submitForm">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-select__tags) {
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
}
</style>
