<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { Delete, DocumentAdd, Edit, Refresh, Search, User } from '@element-plus/icons-vue'
import type { LayerInterface, SearchParams, optionItem } from '@/types/common'
import { validateCommonField, validateCommonName, validateIsEmpty } from '@/utils/validate'
import { convertToLocaleDateString } from '@/utils/commonLogic'
import { useUserStore } from '@/store/modules/user'
import { pageSizeOptions } from '@/enums/common'
import { mockDiseaseTableData } from '@/utils/mockData'

const { t } = useI18n()
const userStore = useUserStore()
const searchParameter = ref<SearchParams>({
  pageNum: 1,
  pageSize: 10,
  nickName: '',
})
const listTotal = ref(0)

onMounted(() => {
  handleSearch()
})
const ruleForm = ref<FormInstance>()
const dialogInfo = reactive<LayerInterface>({
  show: false,
  title: '新增疾病',
})

// 表单数据
const formInfo = ref({
  userID: '',
  userName: '',
  userNumber: '',
  roleId: '',
})

// 用户所能分配的角色列表
const roleTypeOptions = ref()

// 表单验证规则
const rules = {
  userName: [
    { required: true, validator: validateIsEmpty, trigger: 'blur' },
    { validator: validateCommonName, trigger: 'blur' },
    { min: 1, max: 10, message: `${t('message.common.verifyInputRange')}1~10`, trigger: 'blur' },
  ],
  userNumber: [
    { required: true, validator: validateIsEmpty, trigger: 'blur' },
    { validator: validateCommonField, trigger: 'blur' },
    { min: 2, max: 20, message: `${t('message.common.verifyInputRange')}2~20`, trigger: 'blur' },
  ],
  roleId: [
    { required: true, validator: validateIsEmpty, trigger: 'blur' },
  ],
}

// 提交表单
const submit = () => {
  if (ruleForm.value) {
    ruleForm.value.validate(async (valid: boolean) => {
      if (valid) {
        const commitInfo = {
          userName: formInfo.value.userNumber,
          nickName: formInfo.value.userName,
          roleIds: [formInfo.value.roleId],
        }
        // 新增
        if (!formInfo.value.userID) {
          // TODO 调用新增接口
          dialogInfo.show = false
          ElMessage.success(t('message.common.addSuccess'))
        }
        else {
          // TODO 调用修改接口

          dialogInfo.show = false
          ElMessage.success(t('message.common.editSuccess'))
        }
      }
    })
  }
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    t('message.common.tooltip.delete'),
    {
      confirmButtonText: t('message.common.confirm'),
      cancelButtonText: t('message.common.cancel'),
      type: 'warning',
    },
  ).then(() => {
    // TODO 调用删除接口

    ElMessage.success(t('message.common.delSuccess'))
  }).catch(() => { })
}

/**
 *  刷新列表
 */
// 防抖搜索处理函数，延迟触发接口调用
const handleSearch = debounce(300, () => {
  // TODO: 调用刷新列表的接口逻辑
})

// 重置
const handleReset = () => {
  searchParameter.value.nickName = ''
  handleSearch()
}

// 新增
const addDialog = () => {
  formInfo.value.userID = ''
  formInfo.value.userName = ''
  formInfo.value.userNumber = ''
  formInfo.value.roleId = ''
  dialogInfo.title = '新增目录'
  dialogInfo.show = true
}

// 编辑
const editDialog = (row: any) => {
  formInfo.value.userID = row.userId
  formInfo.value.userName = row.nickName
  formInfo.value.userNumber = row.userName
  formInfo.value.roleId = row.roles?.[0]?.roleId || ''
  dialogInfo.title = '编辑目录'
  dialogInfo.show = true
}

const extractRoleNames = (roles: any): string => roles.length ? roles.map((role: any) => role.roleName).join('/') : '-'
</script>

<template>
  <div class="main-content">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form ref="searchFormRef" :inline="true">
        <el-form-item label="疾病名称">
          <el-input v-model.trim="searchParameter.nickName" maxlength="10" placeholder="请输入疾病名称" @input="handleSearch" />
        </el-form-item>
        <div style="float: right;">
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ t('message.common.search') }}
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button :icon="Refresh" @click="handleReset">
              {{ t('message.common.reset') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
    <el-card class="data-card">
      <div class="option-buttons">
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button type="primary" :icon="User">
              新增
            </el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="addDialog">
                新增目录
              </el-dropdown-item>
              <el-dropdown-item @click="addDialog">
                新增疾病
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="data-table">
        <el-table
          :data="mockDiseaseTableData"
          row-key="id"
          :header-cell-style="{ background: '#eef0ef', color: '#7d827f' }"
          stripe
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          height="100%"
        >
          <el-table-column label="疾病名称" align="left" prop="nickName" />

          <el-table-column label="创建时间" align="left" prop="createTime">
            <template #default="scope">
              <span>{{ convertToLocaleDateString(scope.row.createTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="340" fixed="right" align="center">
            <template #default="scope">
              <div class="operator-button">
                <el-tooltip effect="light" content="新增疾病" placement="top">
                  <div class="primary-type">
                    <el-icon @click="editDialog(scope.row)">
                      <DocumentAdd />
                    </el-icon>
                  </div>
                </el-tooltip>

                <el-tooltip effect="light" content="编辑疾病" placement="top">
                  <div class="primary-type">
                    <el-icon @click="editDialog(scope.row)">
                      <Edit />
                    </el-icon>
                  </div>
                </el-tooltip>

                <el-tooltip effect="light" content="删除疾病" placement="top">
                  <div>
                    <el-icon @click="handleDelete(scope.row)">
                      <Delete />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        class="footer-pagination" :page-size="searchParameter.pageSize" :page-sizes="pageSizeOptions"
        background layout="total, sizes, prev, pager, next" :total="listTotal"
        @current-change="searchParameter.pageNum = $event; handleSearch();"
        @size-change="searchParameter.pageSize = $event; handleSearch();"
      />
    </el-card>
  </div>

  <common-dialog ref="dialogDom" :dialog-info="dialogInfo" @confirm="submit">
    <el-form
      ref="ruleForm" :model="formInfo" :rules="rules" label-width="120px" label-position="top"
      style="margin-right: 10rem"
    >
      <el-form-item label="目录名称" prop="userName">
        <el-input
          v-model.trim="formInfo.userName" placeholder="请输入目录名称"
          maxlength="10"
        />
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<style scoped lang="scss">
:deep(.el-select__tags) {
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
}
</style>
