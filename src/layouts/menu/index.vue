<script lang="ts" setup>
import { onBeforeMount } from 'vue'
import MenuItem from './MenuItem.vue'
import router from '@/router'

const appStore = useAppStore()
const isCollapse = computed(() => appStore.isCollapse)
const expandOneMenu = computed(() => appStore.expandOneMenu)
// const allRoutes = computed(() => useUserStore().allRoutes)
const allRoutes = computed(() => useRouter().options.routes)
const route = useRoute()
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu)
    return meta.activeMenu as string
  return path
})
</script>

<template>
  <!-- router表示启动路由模式，启用该模式会在激活导航时以 index 作为 path 进行路由跳转 -->
  <!-- menu-trigger表示子菜单需要通过鼠标点击打开，不用hover的原因是el-menu-item在设置为横向菜单栏时存在可以被focus，导致残留样式，属于UI框架bug -->
  <!-- 设置菜单栏的鼠标悬浮背景色可通过重新定义主题色修改：  --el-menu-hover-bg-color -->
  <!-- 横向TODO: 添加以下内容 mode="horizontal" :close-on-click-outside="true"  -->
  <el-menu
    class="layout-menu"
    router
    menu-trigger="click"
    :unique-opened="true"
    :hide-timeout="400"
    text-color="var(--system-word-color)"
    active-text-color="var(--system-primary-text-color)"
    :default-active="activeMenu"
  >
    <MenuItem v-for="(menu, key) in allRoutes" :key="key" :menu="menu" />
  </el-menu>
</template>

<style lang="scss" scoped>
.layout-menu {
  // 横向TODO: 注释以下内容
  height: 100%;
  // 横向TODO: 取消注释以下内容
  // height: 48px;

  // 菜单项通用样式
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    min-width: 120px;

    &.is-active {
      font-weight: 700 !important;
    }
  }
  // 父菜单高亮样式
  :deep(.el-sub-menu) {
    &.is-active {
      > .el-sub-menu__title,
      > .el-sub-menu__title i {
        font-weight: 700 !important;
        color: var(--system-primary-text-color) !important;
      }
    }
  }
}

// 菜单栏为向时设置选中的菜单栏文字加粗
// :deep(.el-menu) {
//   border-bottom: solid 0px;
//   .el-menu-item,
//   .el-sub-menu__title {
//     min-width: 120px;
//     &.is-active {
//       font-weight: 700 !important;
//       // font-family: 'Source Han Sans CN' !important;
//       background-color: transparent !important;
//     }

//     &:hover {
//       background-color: var(--system-menu-background) !important;
//     }
//   }

//   .el-sub-menu {
//     &.is-active {
//       > .el-sub-menu__title,
//       > .el-sub-menu__title i {
//         font-weight: 700 !important;
//         color: red !important;
//       }
//     }
//   }
// }

// .layout-menu {
//   width: 100%;
//   height: 100%;
//   border: none;

//   &.collapse {
//     margin-left: 0px;
//   }

//   :deep() {
//     .el-menu-item,
//     .el-sub-menu {
//       background-color: var(--system-menu-background) !important;
//     }

//     .el-menu-item i,
//     .el-menu-item-group__title,
//     .el-sub-menu__title i {
//       color: var(--system-menu-text-color);
//     }

//     .el-menu-item,
//     .el-sub-menu__title {
//       &.is-active {
//         background-color: var(--system-primary-color) !important;
//         color: var(--system-primary-text-color) !important;
//         font-weight: 700;

//         i {
//           color: var(--system-primary-text-color) !important;
//         }

//         &:hover {
//           background-color: var(--system-primary-color) !important;
//           color: var(--system-primary-text-color) !important;
//         }
//       }

//       &:hover {
//         background-color: var(--system-menu-hover-background) !important;
//       }
//     }

//     .el-sub-menu {
//       &.is-active {
//         > .el-sub-menu__title,
//         > .el-sub-menu__title i {
//           color: var(--system-menu-submenu-active-color) !important;
//         }
//       }

//       .el-menu-item {
//         background-color: var(--system-menu-children-background) !important;

//         &.is-active {
//           background-color: var(--system-primary-color) !important;
//           color: var(--system-primary-text-color) !important;

//           &:hover {
//             background-color: var(--system-primary-color) !important;
//             color: var(--system-primary-text-color) !important;
//           }
//         }

//         &:hover {
//           background-color: var(--system-menu-hover-background) !important;
//         }
//       }

//       .el-sub-menu {
//         .el-sub-menu__title {
//           background-color: var(--system-menu-children-background) !important;

//           &:hover {
//             background-color: var(--system-menu-hover-background) !important;
//           }
//         }
//       }
//     }
//   }
// }
</style>
