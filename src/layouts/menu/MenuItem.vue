<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useUserStore } from '@/store/modules/user'
const props = defineProps({
  menu: {
    type: Object,
    required: true,
  },
  basePath: {
    type: String,
    default: '',
  },
})
const menu = props.menu
const icon = computed(() => {
  if (menu.meta && menu.meta.icon)
    return menu.meta.icon
  else if (menu.children && menu.children.length > 0 && menu.children[0].meta)
    return menu.children[0].meta.icon
  else
    return ''
})

// 返回展示的菜单标题
const title = computed(() => {
  if (menu.meta && menu.meta.title)
    return menu.meta.title
  else if (menu.children && menu.children.length > 0 && menu.children[0].meta)
    return menu.children[0].meta.title
  else return ''
})
// 判断当前路由页面是否展示在菜单中
const isHidden = computed(() => {
  if (menu.hideMenu)
    return true

  if (menu.children && menu.children.length > 0)
    return menu.children.every((item: any) => item.hideMenu)
})
// todo: 优化if结构
const showMenuType = computed(() => { // 0: 无子菜单， 1：有1个子菜单， 2：存在多个子菜单，需要显示上下级子菜单
  if (menu.children && menu.children.length > 1)
    return 2
  else if (menu.children && menu.children.length === 1)
    return 1
  else
    return 0
})
// todo: 优化多层if
const pathResolve = computed(() => {
  let path = menu.path
  const char = '/'
  // if (showMenuType.value === 1 && menu.children[0].children && menu.children[0].children.length === 1)
  //   path = path + char + menu.children[0].children[0].path
  // else path = props.basePath ? props.basePath + char + path : path
  path = props.basePath ? props.basePath + char + path : path
  return path
})
// 生成菜单对应的类名
const genTitleClass = computed(() => {
  if (!menu.meta)
    return 'main-title-bold-1'
  if (menu.meta && menu.meta.level)
    return `main-title-bold-${menu.meta.level}`
  else
    return 'main-title-bold-default'
})
</script>

<template>
  <template v-if="!isHidden && menu.path !== '/:all(.*)*'">
    <!-- 只有一个子项，且该子项没有 path，说明可以把菜单显示成一级菜单（el-menu-item），而不是必须展开成二级菜单（el-sub-menu） -->
    <el-sub-menu v-if="showMenuType === 2 || (showMenuType === 1 && menu.children && menu.children.length > 0 && menu.children[0].path !== '')" :index="pathResolve">
      <template #title>
        <!-- 横向TODO: 注释以下代码，隐藏图标 -->
        <SvgIcon v-if="icon" :name="icon" />
        <span :class="genTitleClass">
          {{ title }}
        </span>
      </template>
      <menu-item v-for="(item, key) in menu.children" :key="key" :menu="item" :base-path="pathResolve" />
    </el-sub-menu>
    <el-menu-item v-else :index="pathResolve">
      <template #title>
        <!-- 一级菜单需要展示图标 -->
        <SvgIcon v-if="icon" :name="icon" />
        <span :class="genTitleClass">
          {{ title }}
        </span>
      </template>
    </el-menu-item>
  </template>
</template>

<style lang="scss" scoped>
.svg-icon{
  font-size: 20px;
}
.title-text {
  margin-left: 14px;
}
.main-title-bold-1 {
  font-size: 16px;
  // font-weight: 400;
  // font-weight: bold;
}

.main-title-bold-2,
.main-title-bold-default {
  font-size: 16px;
  margin-left: 16px;
}

.el-menu-item [class^=el-icon] {
  margin-right: 0 !important;
}
</style>
