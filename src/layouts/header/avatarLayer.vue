<script setup lang="ts">
import { ElMessage, type FormInstance, type UploadFile, type UploadFiles, type UploadUserFile } from 'element-plus'
import { ref } from 'vue'
import { useUserStore } from '@/store/modules/user'
import type { LayerInterface } from '@/types/common'

const ruleForm = ref<FormInstance>()
const userStore = useUserStore()
const dialogInfo = reactive<LayerInterface>({
  show: false,
  showButton: true,
  title: '个人头像设置',
  width: '30%',
  showClose: false,
})
// 表单数据
const formData = reactive({
  headPic: '',
  // headPicId: '',
})
// 判断是否正在上传
const isUploading = ref(false)
const fileList = ref<UploadUserFile[]>([])
function submit() {
  if (ruleForm.value) {
    ruleForm.value.validate((valid) => {
      if (valid) {
        // if (!formData.headPic) {
        //   ElMessage.error('请上传头像')
        //   return
        // }
        if (isUploading.value) {
          ElMessage({
            message: '正在上传文件，请稍后再试保存',
            type: 'error',
          })
          return
        }

        userStore.updateAvatar({
          userId: userStore.userInfo.userId,
          userHead: formData.headPic,
        }).then((res: any) => {
          ElMessage({
            type: 'success',
            message: '修改成功',
          })
          dialogInfo.show = false
        })
      }
      else {
        return false
      }
    })
  }
}

// 上传文件成功后的处理
const handleSuccess = (
  response: any,
) => {
  if (response.data)
    formData.headPic = response.data.url
}

// 移动文件后的处理
const handleRemove = () => {
  formData.headPic = ''
  // formData.headPicId = ''
}

const handleChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  // status为ready说明尚未上传成功
  isUploading.value = uploadFile.status === 'ready'
}

const previewVisible = ref(false)
const previewUrl = ref('')
const handlePreview = (uploadFile: UploadFile) => {
  previewVisible.value = true
  previewUrl.value = uploadFile.url!
}

// 取消新增和编辑分类
const cancelSubmit = () => {
  // 添加时若选择取消，则需要删除已经上传的资源
  // if (formData.headPicId)
  //   resourceStore.deleteFile(Number(formData.headPicId))

  dialogInfo.show = false
}

// 清空formData数据
function clearFormData() {
  formData.headPic = ''
  // formData.headPicId = ''
  fileList.value.splice(0, fileList.value.length)
}

function show(data: any) {
  clearFormData()
  if (data.value && data.value.userHead) {
    formData.headPic = data.value.userHead
    fileList.value.push({ name: '头像', url: formData.headPic })
  }

  dialogInfo.show = true
}

defineExpose({ show })
</script>

<template>
   <common-dialog ref="dialogDom" :dialog-info="dialogInfo" @confirm="submit" @cancel-confirm="cancelSubmit">
    <el-form ref="ruleForm" :model="formData">
      <el-form-item label="头像图片：">
        <!-- <FileUpload
          :uploaded-list="fileList"
          accept=".png,.jpg,.jpeg"
          :upload-data="{ fileType: 'archimedes-picture' }"
          list-type="picture-card"
          :return-suc-all-res="true"
          :multiple="false"
          :limit-num="2"
          tip-text="上传文件只能是png、jpg、jpeg格式，大小不能超过5MB"
          @handle-success="handleSuccess"
          @handle-remove="handleRemove"
          @handle-preview="handlePreview"
          @handle-change="handleChange"
        /> -->
      </el-form-item>
    </el-form>
  </common-dialog>
  <el-dialog v-model="previewVisible" width="30%">
    <div class="img-preview">
      <img w-full :src="previewUrl" alt="预览">
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.img-preview {
  display: flex;
  justify-content: center;

  img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
}
</style>
