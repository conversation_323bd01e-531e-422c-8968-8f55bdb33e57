<script setup lang="ts">
import { ElMessage, type FormInstance } from 'element-plus'
import { ref } from 'vue'
import { validatePassword } from '../../utils/validate'
import { useUserStore } from '@/store/modules/user'
import type { LayerInterface } from '@/types/common'

const props = defineProps({
  dialogInfo: {
    type: Object as () => LayerInterface,
    required: true,
  },
})

const { t } = useI18n()
const dialogDom = ref()
const ruleForm = ref<FormInstance>()
const userStore = useUserStore()
const form = ref({
  userOldPwd: '',
  userPwd: '',
  userPwdConfirm: '',
})
function validateNewPassword(rule: any, value: string, callback: any) {
  if (value !== form.value.userPwd) {
    callback(t('message.login.verify_confirm_pwd'))
    return false
  }
  else {
    callback()
    return true
  }
}

const rules = {
  userOldPwd: [
    { required: true, trigger: 'blur', message: '必填项' },
    { trigger: 'blur', validator: validatePassword },
  ],
  userPwd: [
    { required: true, trigger: 'blur', message: '必填项' },
    { trigger: 'blur', validator: validatePassword },
  ],
  userPwdConfirm: [
    { required: true, trigger: 'blur', message: '必填项' },
    { trigger: 'blur', validator: validateNewPassword },
  ],
}

// 提交密码
function submit() {
  if (ruleForm.value) {
    ruleForm.value.validate(async (valid) => {
      if (valid) {
        const params = {
          newPassword: form.value.userPwd,
          oldPassword: form.value.userOldPwd,
        }
        // 联调开放
        userStore.updatePassword(params).then((res: any) => {
          ElMessage({
            type: 'success',
            message: t('message.login.res_update_success'),
          })
          // layerDom.value && layerDom.value.close() // 关闭修改密码弹窗
          setTimeout(() => {
            userStore.logout() // 自动登出，让用户在登录页面用新密码重新登录
          }, 1000)
        })
      }
      else {
        return false
      }
    })
  }
}
</script>

<template>
  <common-dialog ref="dialogDom" :dialog-info="dialogInfo" @confirm="submit">
    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      style="margin-right: 30px"
    >
      <el-form-item :label="$t('message.login.old_pwd')" prop="userOldPwd">
        <el-input
          v-model.trim="form.userOldPwd"
          :placeholder="$t('message.common.please_input')"
          maxlength="20"
          show-password
        />
      </el-form-item>
      <el-form-item :label="$t('message.login.new_pwd')" prop="userPwd">
        <el-input
          v-model.trim="form.userPwd"
          :placeholder="$t('message.common.please_input')"
          maxlength="20"
          show-password
        />
      </el-form-item>
      <el-form-item
        :label="$t('message.login.confirm_new_pwd')"
        prop="userPwdConfirm"
      >
        <el-input
          v-model.trim="form.userPwdConfirm"
          :placeholder="$t('message.common.please_input')"
          maxlength="20"
          show-password
        />
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<style lang="scss" scoped></style>
