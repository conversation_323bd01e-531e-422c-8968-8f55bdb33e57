<script lang="ts" setup>
import { ElMessageBox } from 'element-plus'
import {
  ArrowDown,
} from '@element-plus/icons-vue'
import Menu from '../menu/index.vue'
import PasswordLayer from './passwordLayer.vue'
import AvatarLayer from './avatarLayer.vue'
import { useUserStore } from '@/store/modules/user'
import defaultAvatar from '@/assets/images/default-avatar.png'
import defaultSystemLogo from '@/assets/icons/system-logo-inner.png'
import type { LayerInterface } from '@/types/common'

const systemName = ref('')
const { t } = useI18n()
const userStore = useUserStore()
const dialogInfo = reactive<LayerInterface>({
  show: false,
  showButton: true,
  title: '修改密码',
  width: '30%',
})
const userInfo = computed(() => userStore.userInfo)
const curUser = computed(() => userInfo.value?.nickName || 'admin')
const loginOut = () => {
  ElMessageBox.confirm('登出后需重新登录进入系统，您确定要退出登录吗？', {
    confirmButtonText: t('message.common.confirm'),
    cancelButtonText: t('message.common.cancel'),
    type: 'warning',
  })
    .then(() => {
      userStore.logout(false)
    })
    .catch(() => {})
}

const showPasswordLayer = () => {
  dialogInfo.show = true
}

const avatarDialog = ref()

const showAvatarLayer = () => {
  avatarDialog.value.show(userInfo)
}
const systemLogo = ref('')
</script>

<template>
  <header>
    <div class="left-box">
      <img class="system-logo" :src="systemLogo || defaultSystemLogo">
      <div class="product-name">
        <span> {{ systemName || $t("pages.layout.header.productName") }} </span>
      </div>
    </div>
    <!-- 横向TODO: 取消注释以下内容 -->
    <!-- <div class="nav-box">
        <Menu />
    </div> -->
    <div class="menu-right-box">
      <!-- 用户信息 -->
      <div class="user-info">
        <el-dropdown>
          <span
            class="el-dropdown-link"
            style="display: flex; align-items: center"
          >
            <!-- <el-avatar
              :src="defaultAvatar"
              class="avatar"
              :size="32"
            /> -->
            <el-avatar class="avatar"> {{ curUser.slice(-2) }} </el-avatar>
            <span style="margin-right: 6px">
              {{ useUserStore().userName }}</span>
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="showPasswordLayer">
                {{ $t("message.system.change_password") }}
              </el-dropdown-item>
              <!-- <el-dropdown-item @click="showAvatarLayer">
                {{ $t("message.system.change_avatar") }}
              </el-dropdown-item> -->
              <el-dropdown-item @click="loginOut">
                {{ $t("message.system.login_out") }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <PasswordLayer v-if="dialogInfo.show" :dialog-info="dialogInfo" />
      <AvatarLayer ref="avatarDialog" />
    </div>
  </header>
</template>

<style lang="scss" scoped>
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--system-header-background);
  padding-right: 22px;
  min-height: 48px;
}

.left-box {
  flex: 0.4;
  display: flex;
  align-items: center;
  .system-logo {
    width: 32px;
    height: 32px;
    margin-left: 20px;
  }

  .product-name {
    margin-left: 10px;

    span {
      font-size: 22px;
      font-weight: 400;
      line-height: 30px;
      color: var(--system-primary-text-color);
      text-align: left;
      vertical-align: top;

      font-family: "Kingsoft_Cloud_Font";
    }
  }
}

.nav-box {
  flex: 0.5;
  height: 48px;
}

.menu-right-box {
  flex: 0.1;
  height: 100%;
  display: flex;
  justify-content: right;
  align-items: center;

  .user-info {
    margin-left: 10px;

    .el-dropdown-link {
      // color: var(--system-header-breadcrumb-text-color);
      color: var(--system-primary-text-color);

      .avatar {
        margin-right: 8px;
      }
      // 禁用浏览器自动赋值的focus-visible，该属性会使得下拉框有一圈黑色边框
      &:focus {
        outline: none;
      }
    }
  }
}
</style>
