<script lang="ts" setup>
import { useEventListener } from '@vueuse/core'
import Menu from './menu/index.vue'
import Header from './header/index.vue'
import { useFileStore } from '@/store/modules/file'
import { useCaseLibraryStore } from '@/store/modules/caseLibrary'
import { GetCaseLibTypeList } from '@/api/case-library'
const appStore = useAppStore()
const isCollapse = computed(() => appStore.isCollapse)
// 页面宽度变化监听后执行的方法
const resizeHandler = () => {
  if (document.body.clientWidth <= 1000 && !isCollapse.value)
    appStore.isCollapse = true
  else if (document.body.clientWidth > 1000 && isCollapse.value)
    appStore.isCollapse = false
}
/**
 * 初始化公共数据
 */
async function initCommonData() {
  // 初始化个人收藏目录分类
  useCaseLibraryStore().getCustomCategoryTreeData()
  useCaseLibraryStore().initDict()
}

// 初始化调用
resizeHandler()
// beforeMount
onBeforeMount(() => {
  // 监听页面变化
  useEventListener('resize', resizeHandler)
  initCommonData()
})

const route = useRoute()
</script>

<template>
  <div v-if="route?.meta.noLayout" style="height: 100vh">
    <router-view v-slot="{ Component, route: innerRoute }">
      <!-- 去除自定义过度动画 -->
      <!-- :name="route.meta.transition || 'fade-transform'" -->
      <component :is="Component" :key="innerRoute.fullPath" />
    </router-view>
  </div>
  <el-container v-else style="height: 100vh">
    <el-header>
      <Header />
    </el-header>
    <el-container>
      <!-- 横向TODO: 注释以下内容 - -->
      <el-aside :width="isCollapse ? '60px' : '216px'" :class="isCollapse ? 'hide-aside' : 'show-side'">
        <Menu />
      </el-aside>

      <el-main>
        <router-view v-slot="{ Component, route: innerRoute }">
          <!-- 去除自定义过度动画 -->
          <!-- :name="route.meta.transition || 'fade-transform'" -->
          <component :is="Component" :key="innerRoute.fullPath" />
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<style lang="scss" scoped>
.el-header {
  padding-left: 0;
  padding-right: 0;
  height: 48px;
}

.el-aside {
  display: flex;
  flex-direction: column;
  transition: 0.2s;
  overflow-x: hidden;
  transition: 0.3s;
  border-right: 1px solid #fff;
  border-left: 1px solid #fff;

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.el-main {
  background-color: var(--system-container-background);
  height: calc(100vh - 48px);
  padding: 0;
  overflow-x: hidden;
}

.el-main-box {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

@media screen and (max-width: 1000px) {
  .el-aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;

    &.hide-aside {
      left: -250px;
    }
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
