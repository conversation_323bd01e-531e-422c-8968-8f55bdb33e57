<script lang="ts" setup>
const appStore = useAppStore()
const isCollapse = computed(() => appStore.isCollapse)
</script>

<template>
  <div class="logo-container">
    <!-- <img src="@/assets/logo.png" alt=""> -->
    <h1 v-if="!isCollapse">
      {{ $t('message.system.title') }}
    </h1>
  </div>
</template>

<style lang="scss" scoped>
.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: var(--system-logo-background);

  h1 {
    font-size: 18px;
    white-space: nowrap;
    color: var(--system-logo-color);
  }
}
</style>
