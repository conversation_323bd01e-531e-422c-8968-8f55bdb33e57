# 巨鲨影像中心教学平台
## 依赖安装
npm i --need-lock
## 本地运行
npm run dev
## 构建打包
npm run build

<font color=gray size =2>最近更新时间：2025-06-27</font>
**Vue 3 + TypeScript + Vite**  

## 注意事项
- `pnpm install` 有问题的话在根目录把 `pnpm-lock.yaml` 这类有 `lock` 的文件删一下再试一试。
- 最好先自己 `google` 一下报错信息或检查 `本地环境`。

## Recommended IDE Setup

先安装 `node.js`，我的版本是 `v16.16.0`。
推荐插件已经写在配置里了，点一下编译器弹出的 `安装` 就行；**务必**安装。


## Features

### 右键菜单

#### 使用方法

`右键菜单类型`
``` ts 
export interface ContextmenuItem {
  text?: string // 主要内容
  subText?: string // 副内容，当有子组件时不显示
  divider?: boolean // 没有内容，只是一个分割线
  disable?: boolean // 是否禁用
  hide?: boolean // 是否显示
  children?: ContextmenuItem[] // 子组件
  handler?: (el: HTMLElement) => void // 触发事件
}
```

`右键菜单内容`
```ts
const menu = () => {
  return [
    {
      text: 'test',
      handler: () => console.log('test'),
    },
  ]
}
```
`template标签`
``` html
<div v-contextmenu="menu">
</div>
```
#### 更新计划
- 增加选中状态，多选状态

### 图标库

#### 使用方法
在 `.vue` 文件 `<template>` 中直接使用，以 `carbon` 图标集的 `accessibility` 图标为例。
``` html
<icon-carbon-accessibility style="color: red; font-size: 36px;" />
```
其中 `icon` 为统一前缀，`carbon` 表示当前使用的图标集，`accessibility` 表示图标名称。
图标样式为较为常规的写法，不一一赘述。

另外的，如
``` html
<icon-mdi-account />
```
表示 `mdi(Material Design Icons)` 图标集的 `account` 图标。



#### 注意事项
- 安装 `antfu.iconify` 插件，以在编译器中显示图标。
- 图标库包含的内容请上 <a>https://icon-sets.iconify.design/</a> 查看。
- `mdi` 和 `carbon` 为较为常用的图标集，但是风格偏商务；也可以选用 `icon-park` 图标集。
- 不推荐引入两个以上图标集，可能会造成风格不一致。

#### 公共组件使用案例
`FileUpload`

``` typescript
 
function beforeUpload() {
  const loading = ElLoading.service({
    lock: true,
    text: '导入中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  setTimeout(() => {
    loading.close()
  }, 1000)
}
// 上传文件成功后的处理
function handleSuccess(response: any) {
  if (response) {
    if (!response.state) {
      ElMessage.error(response.message)
      return
    }
    else {
      ElMessage.success('导入成功')
    }
  }
}

 <FileUpload
    class="import-button"
    button-text="导入"
    :limit-num="1000"
    accept=".xls,.xlsx"
    :upload-data="{ fileType: 'file' }"
    :action-path="actionPath"
    :show-file-list="false"
    :return-suc-all-res="true"
    :multiple="false"
    @before-upload="beforeUpload"
    @handle-success="handleSuccess"
  />
```

├─.vscode
├─dist  //构建后的产物
│  └─assets
├─public  // 公共资源 如favicon.ico
├─src
│  ├─locales  // 国际化
│  ├─api  // 封装后台接口请求
│  ├─assets // 静态资源，包括icon和image
│  │  ├─icons
│  │  └─images
│  ├─components   // 公共业务组件，可提供给各业务模块使用
│  │  └─context-menu
│  ├─enums  // 常量集合
│  ├─hooks
│  ├─layouts  // 主页布局
│  │  ├─footer
│  │  ├─header
│  │  │  └─components
│  │  │      ├─fullscreen
│  │  │      ├─search
│  │  │      │  └─components
│  │  │      └─setting
│  │  ├─logo
│  │  ├─menu
│  │  ├─routerView
│  │  └─tabs
│  ├─router // 路由
│  │  └─modules
│  ├─store  // 全局状态管理
│  │  └─modules
│  ├─styles   // 公共样式
│  ├─utils  // 抽离出的公共方法
│  └─views  // 业务页面，下面划分业务模块
│      ├─login
│      └─problems
│          └─components
├─test  // 前端单测
