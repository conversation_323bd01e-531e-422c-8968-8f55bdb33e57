08:50:06.163 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:50:08.364 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0
08:50:08.523 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 69 ms to scan 1 urls, producing 3 keys and 6 values 
08:50:08.579 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
08:50:08.591 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
08:50:08.854 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 258 ms to scan 213 urls, producing 0 keys and 0 values 
08:50:08.862 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
08:50:08.873 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
08:50:08.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
08:50:09.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 234 ms to scan 213 urls, producing 0 keys and 0 values 
08:50:09.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
08:50:09.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$319/390034407
08:50:09.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$320/1134894336
08:50:09.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
08:50:09.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
08:50:09.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
08:50:16.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753059024759_************_62497
08:50:16.247 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Notify connected event to listeners.
08:50:16.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
08:50:16.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040832f6-7c51-4ddd-8e84-a6aee6adf198_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$328/2088246338
08:50:16.773 [main] INFO  c.j.i.IMServerApp - [logStartupProfileInfo,632] - No active profile set, falling back to 1 default profile: "default"
08:50:22.463 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.4
08:50:25.732 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for localhost/127.0.0.1:6379
08:50:26.162 [redisson-netty-2-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for localhost/127.0.0.1:6379
08:50:27.788 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9898"]
08:50:27.793 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:50:27.794 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
08:50:28.268 [main] INFO  o.a.c.c.C.[.[.[/im-server] - [log,173] - Initializing Spring embedded WebApplicationContext
08:50:28.633 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:24:27.020 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
11:24:29.624 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
11:24:29.625 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
11:24:29.631 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
11:24:29.838 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:24:29.838 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:24:29.865 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
11:24:30.817 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
11:24:30.824 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0
11:24:30.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:24:30.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/905940937
11:24:30.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1605834811
11:24:30.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:24:30.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:24:30.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
11:24:38.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753068277827_************_35092
11:24:38.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Notify connected event to listeners.
11:24:38.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:38.082 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Connected,notify listen context...
11:24:38.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/163160871
11:24:38.167 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
11:24:38.216 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
11:24:38.277 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
11:24:42.285 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
11:24:43.169 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
11:24:45.390 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
11:24:45.390 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
11:24:45.390 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
11:24:45.461 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
11:24:45.462 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
11:24:45.462 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
11:24:45.714 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
11:24:45.714 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
11:24:45.715 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
11:24:45.765 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
11:24:45.766 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
11:24:45.766 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
11:24:45.823 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
11:24:45.823 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
11:24:45.823 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
11:24:45.871 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
11:24:45.872 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
11:24:45.872 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
11:24:45.942 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
11:24:45.942 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
11:24:45.942 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
11:24:46.389 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:24:47.882 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:24:48.478 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
11:24:48.478 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
11:24:48.478 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
11:24:48.533 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
11:24:48.533 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
11:24:48.533 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
11:24:48.891 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
11:24:48.891 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
11:24:48.892 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
11:24:48.975 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
11:24:48.975 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
11:24:48.975 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
11:24:50.035 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
11:24:50.043 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
11:24:50.085 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
11:24:50.110 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
11:24:53.652 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
11:24:53.652 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
11:24:53.653 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
11:24:53.694 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:24:53.695 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:24:53.741 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 0ae42f5e-81f5-4330-8c67-0dd3114825d0
11:24:53.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] RpcClient init label, labels = {module=naming, source=sdk}
11:24:53.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:24:53.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:24:53.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:24:53.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
11:24:53.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Success to connect to server [*************:8848] on start up, connectionId = 1753068294094_************_35295
11:24:53.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:53.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Notify connected event to listeners.
11:24:53.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/163160871
11:24:53.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
11:24:53.899 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
11:24:53.954 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
11:24:53.983 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
11:24:54.049 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
11:24:54.090 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
11:24:54.120 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
11:24:54.291 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
11:24:54.487 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Receive server push request, request = NotifySubscriberRequest, requestId = 4
11:24:54.488 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Ack server push request, request = NotifySubscriberRequest, requestId = 4
11:24:55.484 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
11:24:55.493 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
11:24:55.857 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
11:24:55.935 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
11:24:56.043 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Receive server push request, request = NotifySubscriberRequest, requestId = 5
11:24:56.055 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
11:24:56.055 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
11:24:56.056 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:24:56.075 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
11:24:56.522 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
11:24:56.523 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
11:24:56.524 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
11:24:56.527 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
11:24:56.529 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
11:24:56.544 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
11:24:56.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
11:24:56.558 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
11:24:56.560 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
11:24:56.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
11:24:56.563 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
11:24:56.563 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
11:24:56.567 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
11:24:56.569 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
11:24:56.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
11:24:56.592 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
11:24:56.593 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
11:24:56.596 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
11:24:56.597 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
11:24:56.603 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
11:24:56.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
11:24:56.607 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
11:24:56.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
11:24:56.609 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
11:24:56.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
11:24:56.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
11:24:56.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
11:24:56.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
11:24:56.614 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
11:24:56.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
11:24:56.617 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
11:24:56.619 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
11:24:56.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
11:24:56.624 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
11:24:56.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
11:24:56.631 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
11:24:56.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
11:24:56.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
11:24:56.636 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
11:24:56.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
11:24:56.640 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
11:24:56.641 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
11:24:56.642 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
11:24:56.645 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
11:24:56.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
11:24:56.648 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
11:24:56.649 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
11:24:56.649 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
11:24:56.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
11:24:56.653 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
11:24:56.653 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
11:24:56.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
11:24:56.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
11:24:56.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
11:24:56.656 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
11:24:56.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
11:24:56.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
11:24:56.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
11:24:56.663 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
11:24:56.664 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
11:24:56.668 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
11:24:56.669 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
11:24:56.672 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
11:24:56.673 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
11:24:56.674 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
11:24:56.675 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
11:24:56.675 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
11:24:56.679 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
11:24:56.680 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
11:24:56.680 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
11:24:56.684 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
11:24:56.685 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
11:24:56.686 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
11:24:56.687 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
11:24:56.687 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
11:24:56.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
11:24:56.693 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
11:24:56.693 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
11:24:56.694 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
11:24:56.694 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
11:24:56.695 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
11:24:56.695 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
11:24:56.696 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
11:24:56.696 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
11:24:56.698 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
11:24:56.699 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
11:24:56.699 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
11:24:56.701 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
11:24:56.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
11:24:56.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
11:24:56.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
11:24:56.705 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
11:24:56.705 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
11:24:56.707 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
11:24:56.708 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
11:24:56.709 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
11:24:56.711 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
11:24:56.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
11:24:56.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
11:24:56.714 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
11:24:56.715 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
11:24:56.716 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
11:24:56.720 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
11:24:56.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
11:24:56.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
11:24:56.722 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
11:24:56.722 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
11:24:56.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
11:24:56.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
11:24:56.724 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
11:24:56.727 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
11:24:56.728 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
11:24:56.728 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
11:24:56.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
11:24:56.731 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
11:24:56.736 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
11:24:56.737 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
11:24:56.737 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
11:24:56.738 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
11:24:56.738 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
11:24:56.739 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
11:24:56.739 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
11:24:56.740 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
11:24:56.741 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
11:24:56.741 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
11:24:56.742 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
11:24:56.742 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
11:24:56.798 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 32.805 seconds (JVM running for 41.371)
11:24:56.808 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
11:24:56.809 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
11:24:56.811 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
11:24:56.814 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
11:24:56.814 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
11:47:16.575 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:47:16.908 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276565347430469
11:47:17.674 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276565347430469
11:48:55.649 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276565752381509
11:48:55.677 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276565752381509
11:48:55.929 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
11:48:56.353 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Error][用户不存在/密码错误]
11:49:04.889 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276565790244933
11:49:04.920 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276565790244933
11:49:04.988 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
11:49:05.112 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
11:49:05.834 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
11:49:55.232 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276565996425285
11:49:55.262 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276565996425285
11:49:55.304 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
11:49:55.416 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
11:49:55.537 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
11:53:58.692 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276566993637445
11:53:58.757 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276566993637445
11:53:58.946 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
11:53:59.052 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
11:54:36.199 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276567147278405
11:54:36.241 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276567147278405
11:54:36.340 [XNIO-1 task-2] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
11:54:36.523 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
11:54:39.503 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/monitor/logininfor/list
11:54:42.766 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
11:54:47.593 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
11:54:47.667 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/user/list
11:54:47.669 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
11:54:52.430 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276567213764677
11:54:52.470 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276567213764677
11:54:52.499 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========31043
11:54:52.657 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Success][登录成功]
11:55:02.495 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Logout][退出成功]
11:55:05.827 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276567268634693
11:55:05.869 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276567268634693
11:55:05.887 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========31043
11:55:06.015 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Success][登录成功]
13:36:12.395 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276592117256261
13:36:12.432 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276592117256261
13:36:12.463 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
13:36:12.647 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[admin][Success][登录成功]
14:28:39.254 [XNIO-1 task-4] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
14:28:39.320 [XNIO-1 task-4] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
14:29:28.868 [XNIO-1 task-4] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
14:29:44.742 [XNIO-1 task-4] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
14:29:44.806 [XNIO-1 task-4] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
14:35:31.794 [XNIO-1 task-4] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
14:35:40.130 [XNIO-1 task-4] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276606730739781
14:35:40.154 [XNIO-1 task-4] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276606730739781
14:35:40.227 [XNIO-1 task-4] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
14:35:40.347 [XNIO-1 task-4] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
14:35:45.455 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
14:35:45.513 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:06:33.834 [nacos-grpc-client-executor-*************-2658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 54
15:06:33.834 [nacos-grpc-client-executor-*************-2653] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Receive server push request, request = ClientDetectionRequest, requestId = 53
15:06:33.841 [nacos-grpc-client-executor-*************-2658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 54
15:06:33.841 [nacos-grpc-client-executor-*************-2653] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Ack server push request, request = ClientDetectionRequest, requestId = 53
15:10:03.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Server healthy check fail, currentConnection = 1753068277827_************_35092
15:10:03.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:10:41.596 [nacos-grpc-client-executor-*************-2697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 62
15:10:41.596 [nacos-grpc-client-executor-*************-2694] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Receive server push request, request = ClientDetectionRequest, requestId = 61
15:10:41.596 [nacos-grpc-client-executor-*************-2697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 62
15:10:41.596 [nacos-grpc-client-executor-*************-2694] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Ack server push request, request = ClientDetectionRequest, requestId = 61
15:10:41.598 [nacos-grpc-client-executor-*************-2697] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753068277827_************_35092]Ignore complete event,isRunning:false,isAbandon=false
15:11:52.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Server healthy check fail, currentConnection = 1753068294094_************_35295
15:11:52.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:11:52.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Success to connect a server [*************:8848], connectionId = 1753081911647_************_28007
15:11:52.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Abandon prev connection, server is *************:8848, connectionId is 1753068294094_************_35295
15:11:52.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753068294094_************_35295
15:11:52.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:11:52.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Notify disconnected event to listeners
15:11:52.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Notify connected event to listeners.
15:11:52.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:11:52.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Success to connect a server [*************:8848], connectionId = 1753081911821_************_28012
15:11:52.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753068277827_************_35092
15:11:52.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753068277827_************_35092
15:11:52.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Notify disconnected event to listeners
15:11:52.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] DisConnected,clear listen context...
15:11:52.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Notify connected event to listeners.
15:11:52.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [6a52a538-bea4-4193-b031-ef52c07b6f7b_config-0] Connected,notify listen context...
15:11:52.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Success to connect a server [*************:8848], connectionId = 1753081911962_************_28013
15:11:52.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Abandon prev connection, server is *************:8848, connectionId is 1753081911647_************_28007
15:11:52.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753081911647_************_28007
15:11:52.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Notify disconnected event to listeners
15:11:52.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Notify connected event to listeners.
15:11:52.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:11:54.485 [com.alibaba.nacos.client.naming.updater.1] INFO  c.a.n.client.naming - [isChangedServiceInfo,240] - removed ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:11:54.485 [com.alibaba.nacos.client.naming.updater.1] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:11:55.109 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
15:11:55.115 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
15:11:55.698 [nacos-grpc-client-executor-*************-2706] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Receive server push request, request = NotifySubscriberRequest, requestId = 65
15:11:55.698 [nacos-grpc-client-executor-*************-2706] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:11:55.699 [nacos-grpc-client-executor-*************-2706] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:11:55.712 [nacos-grpc-client-executor-*************-2706] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ae42f5e-81f5-4330-8c67-0dd3114825d0] Ack server push request, request = NotifySubscriberRequest, requestId = 65
15:13:23.892 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:13:25.007 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:13:25.008 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:13:25.012 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:13:25.154 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:13:25.154 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:13:25.173 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:13:25.814 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:13:25.819 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0
15:13:25.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:13:25.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/4015102
15:13:25.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1957530885
15:13:25.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:13:25.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:13:25.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:13:31.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753082010857_************_28451
15:13:31.334 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Notify connected event to listeners.
15:13:31.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:13:31.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Connected,notify listen context...
15:13:31.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1002400446
15:13:31.423 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:13:31.465 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:13:31.520 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:13:34.933 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:13:35.873 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:13:37.983 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:13:37.983 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:13:37.984 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:13:38.052 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:13:38.052 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:13:38.052 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:13:38.295 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:13:38.295 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:13:38.296 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:13:38.349 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:13:38.349 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:13:38.349 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:13:38.401 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:13:38.401 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:13:38.401 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:13:38.444 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:13:38.444 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:13:38.444 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:13:38.498 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:13:38.499 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:13:38.499 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:13:38.831 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:13:40.052 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:13:40.525 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:13:40.526 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:13:40.526 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:13:40.585 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:13:40.586 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:13:40.586 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:13:40.967 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:13:40.967 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:13:40.967 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:13:41.039 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:13:41.039 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:13:41.039 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:13:41.952 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:13:41.958 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:13:41.992 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:13:42.011 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:13:44.201 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:13:44.201 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:13:44.201 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:13:44.227 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:13:44.227 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:13:44.249 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 079e007c-2c0d-452d-bb03-fddecc515896
15:13:44.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] RpcClient init label, labels = {module=naming, source=sdk}
15:13:44.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:13:44.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:13:44.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:13:44.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:13:44.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Success to connect to server [*************:8848] on start up, connectionId = 1753082025117_************_28549
15:13:44.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:13:44.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1002400446
15:13:44.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Notify connected event to listeners.
15:13:44.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:13:44.510 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:13:44.549 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:13:44.576 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:13:44.611 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:13:44.645 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:13:44.666 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:13:44.818 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:13:45.156 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Receive server push request, request = NotifySubscriberRequest, requestId = 70
15:13:45.156 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Ack server push request, request = NotifySubscriberRequest, requestId = 70
15:13:45.788 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:13:45.796 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:13:46.136 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:13:46.230 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:13:46.351 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:13:46.367 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Receive server push request, request = NotifySubscriberRequest, requestId = 73
15:13:46.373 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:13:46.373 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:13:46.374 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Ack server push request, request = NotifySubscriberRequest, requestId = 73
15:13:46.928 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:13:46.929 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:13:46.931 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:13:46.933 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:13:46.935 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:13:46.949 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:13:46.962 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:13:46.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:13:46.965 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:13:46.966 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:13:46.967 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:13:46.968 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:13:46.974 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:13:46.976 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:13:46.981 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:13:46.997 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:13:46.998 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:13:47.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:13:47.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:13:47.008 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:13:47.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:13:47.012 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:13:47.013 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:13:47.013 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:13:47.014 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:13:47.015 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:13:47.015 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:13:47.017 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:13:47.019 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:13:47.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:13:47.022 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:13:47.024 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:13:47.026 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:13:47.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:13:47.029 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:13:47.031 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:13:47.032 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:13:47.032 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:13:47.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:13:47.036 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:13:47.040 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:13:47.041 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:13:47.042 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:13:47.045 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:13:47.046 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:13:47.049 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:13:47.050 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:13:47.051 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:13:47.054 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:13:47.055 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:13:47.056 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:13:47.057 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:13:47.058 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:13:47.059 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:13:47.059 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:13:47.061 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:13:47.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:13:47.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:13:47.066 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:13:47.067 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:13:47.070 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:13:47.071 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:13:47.074 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:13:47.075 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:13:47.076 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:13:47.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:13:47.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:13:47.081 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:13:47.082 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:13:47.083 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:13:47.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:13:47.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:13:47.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:13:47.091 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:13:47.092 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:13:47.097 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:13:47.099 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:13:47.100 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:13:47.100 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:13:47.101 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:13:47.101 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:13:47.102 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:13:47.103 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:13:47.103 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:13:47.105 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:13:47.106 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:13:47.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:13:47.108 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:13:47.111 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:13:47.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:13:47.113 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:13:47.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:13:47.115 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:13:47.117 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:13:47.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:13:47.120 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:13:47.121 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:13:47.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:13:47.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:13:47.124 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:13:47.125 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:13:47.126 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:13:47.131 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:13:47.132 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:13:47.132 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:13:47.133 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:13:47.133 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:13:47.134 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:13:47.134 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:13:47.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:13:47.139 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:13:47.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:13:47.142 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:13:47.143 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:13:47.144 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:13:47.153 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:13:47.154 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:13:47.154 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:13:47.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:13:47.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:13:47.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:13:47.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:13:47.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:13:47.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:13:47.160 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:13:47.160 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:13:47.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:13:47.231 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 24.789 seconds (JVM running for 28.366)
15:13:47.256 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:13:47.257 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:13:47.260 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:13:47.261 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:13:47.262 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:13:52.113 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:14:32.968 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:15:42.427 [nacos-grpc-client-executor-*************-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Receive server push request, request = ClientDetectionRequest, requestId = 75
15:15:42.427 [nacos-grpc-client-executor-*************-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [079e007c-2c0d-452d-bb03-fddecc515896] Ack server push request, request = ClientDetectionRequest, requestId = 75
15:15:42.427 [nacos-grpc-client-executor-*************-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 74
15:15:42.428 [nacos-grpc-client-executor-*************-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 74
15:15:42.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Server healthy check fail, currentConnection = 1753082010857_************_28451
15:15:42.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:15:42.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Success to connect a server [*************:8848], connectionId = 1753082142424_************_28928
15:15:42.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753082010857_************_28451
15:15:42.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753082010857_************_28451
15:15:42.595 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:15:42.595 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Notify disconnected event to listeners
15:15:42.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] DisConnected,clear listen context...
15:15:42.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Notify connected event to listeners.
15:15:42.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Connected,notify listen context...
15:15:42.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Success to connect a server [*************:8848], connectionId = 1753082142564_************_28930
15:15:42.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753082142424_************_28928
15:15:42.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753082142424_************_28928
15:15:42.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Notify disconnected event to listeners
15:15:42.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] DisConnected,clear listen context...
15:15:42.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Notify connected event to listeners.
15:15:42.732 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [9e8ba5d6-5007-4dba-91db-cb171ec4fa74_config-0] Connected,notify listen context...
15:19:47.028 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:19:47.048 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
15:19:47.053 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:19:47.053 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:19:47.053 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:19:47.054 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:19:47.054 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:19:47.054 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:19:47.054 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:20:02.427 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:20:03.629 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:20:03.630 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:20:03.637 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:20:03.853 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:20:03.853 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:20:03.882 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:20:04.445 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:20:04.451 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0
15:20:04.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:20:04.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1571278562
15:20:04.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/2021854618
15:20:04.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:20:04.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:20:04.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:20:07.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753082408008_************_29845
15:20:07.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Notify connected event to listeners.
15:20:07.818 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Connected,notify listen context...
15:20:07.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:07.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1100004490
15:20:07.902 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:20:07.939 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:20:07.998 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:20:11.742 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:20:12.477 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:20:14.544 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:20:14.544 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:20:14.545 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:20:14.611 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:20:14.611 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:20:14.611 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:20:14.846 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:20:14.847 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:20:14.847 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:20:14.905 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:20:14.905 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:20:14.906 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:20:14.980 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:20:14.980 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:20:14.981 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:20:15.042 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:20:15.042 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:20:15.042 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:20:15.104 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:20:15.105 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:20:15.105 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:20:15.486 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:20:17.179 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:20:17.726 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:20:17.726 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:20:17.727 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:20:17.786 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:20:17.786 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:20:17.786 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:20:18.227 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:20:18.227 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:20:18.228 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:20:18.303 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:20:18.303 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:20:18.303 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:20:19.306 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:20:19.313 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:20:19.389 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:20:19.413 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:20:22.030 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:20:22.031 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:20:22.031 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:20:22.061 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:20:22.061 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:20:22.088 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 67fa8ee1-718f-4602-aaab-938c1fcba633
15:20:22.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] RpcClient init label, labels = {module=naming, source=sdk}
15:20:22.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:20:22.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:20:22.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:20:22.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:20:22.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Success to connect to server [*************:8848] on start up, connectionId = 1753082421942_************_29919
15:20:22.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:22.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Notify connected event to listeners.
15:20:22.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1100004490
15:20:22.220 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:20:22.226 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:20:22.273 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:20:22.300 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:20:22.336 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:20:22.368 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:20:22.389 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:20:22.572 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:20:22.886 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Receive server push request, request = NotifySubscriberRequest, requestId = 79
15:20:22.886 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Ack server push request, request = NotifySubscriberRequest, requestId = 79
15:20:29.172 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:20:29.181 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:20:29.625 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:20:29.695 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:20:29.789 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Receive server push request, request = NotifySubscriberRequest, requestId = 81
15:20:29.797 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
15:20:29.798 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
15:20:29.798 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Ack server push request, request = NotifySubscriberRequest, requestId = 81
15:20:29.836 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:20:30.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:20:30.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:20:30.289 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:20:30.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:20:30.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:20:30.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:20:30.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:20:30.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:20:30.327 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:20:30.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:20:30.330 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:20:30.331 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:20:30.335 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:20:30.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:20:30.343 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:20:30.361 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:20:30.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:20:30.364 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:20:30.365 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:20:30.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:20:30.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:20:30.376 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:20:30.377 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:20:30.377 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:20:30.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:20:30.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:20:30.379 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:20:30.381 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:20:30.383 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:20:30.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:20:30.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:20:30.389 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:20:30.392 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:20:30.393 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:20:30.394 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:20:30.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:20:30.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:20:30.399 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:20:30.402 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:20:30.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:20:30.407 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:20:30.408 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:20:30.409 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:20:30.411 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:20:30.412 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:20:30.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:20:30.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:20:30.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:20:30.420 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:20:30.421 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:20:30.422 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:20:30.423 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:20:30.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:20:30.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:20:30.425 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:20:30.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:20:30.427 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:20:30.431 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:20:30.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:20:30.433 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:20:30.436 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:20:30.437 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:20:30.440 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:20:30.441 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:20:30.442 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:20:30.443 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:20:30.443 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:20:30.447 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:20:30.448 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:20:30.448 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:20:30.452 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:20:30.453 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:20:30.453 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:20:30.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:20:30.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:20:30.459 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:20:30.460 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:20:30.461 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:20:30.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:20:30.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:20:30.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:20:30.463 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:20:30.463 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:20:30.464 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:20:30.465 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:20:30.466 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:20:30.466 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:20:30.468 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:20:30.470 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:20:30.471 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:20:30.471 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:20:30.473 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:20:30.474 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:20:30.477 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:20:30.477 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:20:30.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:20:30.481 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:20:30.482 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:20:30.483 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:20:30.483 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:20:30.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:20:30.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:20:30.490 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:20:30.491 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:20:30.491 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:20:30.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:20:30.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:20:30.493 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:20:30.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:20:30.496 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:20:30.499 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:20:30.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:20:30.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:20:30.501 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:20:30.502 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:20:30.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:20:30.509 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:20:30.509 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:20:30.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:20:30.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:20:30.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:20:30.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:20:30.513 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:20:30.515 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:20:30.515 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:20:30.516 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:20:30.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:20:30.578 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 29.634 seconds (JVM running for 33.029)
15:20:30.589 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:20:30.589 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:20:30.593 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:20:30.596 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:20:30.596 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:21:42.948 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:30:05.814 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:30:55.716 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:48:55.368 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:49:05.138 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/remove
15:49:12.556 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
15:49:13.031 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
15:49:18.775 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
15:49:21.035 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/query
15:49:22.950 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/groupTree/query
15:49:29.440 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/authUser/allocatedList
15:49:37.908 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
15:49:38.003 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:50:04.339 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:50:10.900 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
15:50:10.953 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
15:50:13.751 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/query
15:50:19.109 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/edit
15:50:19.268 [XNIO-1 task-2] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
15:50:21.333 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/query
15:50:24.323 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/edit
15:50:24.390 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/role/list
15:50:26.145 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
15:50:26.188 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:50:29.659 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/remove
15:50:29.720 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:50:49.870 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:51:35.177 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:52:59.664 [nacos-grpc-client-executor-*************-397] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Receive server push request, request = ClientDetectionRequest, requestId = 104
15:52:59.664 [nacos-grpc-client-executor-*************-397] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67fa8ee1-718f-4602-aaab-938c1fcba633] Ack server push request, request = ClientDetectionRequest, requestId = 104
15:52:59.666 [nacos-grpc-client-executor-*************-398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 103
15:52:59.666 [nacos-grpc-client-executor-*************-398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2af44e7-fc0a-4984-850f-a77c63a592a1_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 103
15:57:22.773 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:57:22.790 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
15:57:22.797 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:57:22.798 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:57:38.895 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:57:40.202 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:57:40.203 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:57:40.205 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:57:40.347 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:57:40.347 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:57:40.391 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:57:41.125 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:57:41.131 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of b6813be0-7759-4387-abd7-388101a17d8a_config-0
15:57:41.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:57:41.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/233343686
15:57:41.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1539995236
15:57:41.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:57:41.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:57:41.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:57:45.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753084664785_************_36918
15:57:45.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Notify connected event to listeners.
15:57:45.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:57:45.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Connected,notify listen context...
15:57:45.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6813be0-7759-4387-abd7-388101a17d8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1195909598
15:57:45.306 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:57:45.361 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:57:45.433 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:57:48.888 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:57:49.718 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:57:51.650 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:57:51.650 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:57:51.651 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:57:51.717 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:57:51.717 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:57:51.717 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:57:51.948 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:57:51.949 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:57:51.949 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:57:52.001 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:57:52.001 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:57:52.001 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:57:52.053 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:57:52.053 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:57:52.053 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:57:52.098 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:57:52.098 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:57:52.098 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:57:52.156 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:57:52.157 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:57:52.157 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:57:52.493 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:57:53.725 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:57:54.123 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:57:54.123 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:57:54.123 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:57:54.177 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:57:54.177 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:57:54.178 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:57:54.476 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:57:54.476 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:57:54.476 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:57:54.555 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:57:54.555 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:57:54.555 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:57:55.310 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:57:55.318 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:57:55.356 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:57:55.371 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:57:59.027 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:57:59.028 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:57:59.028 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:57:59.068 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:57:59.068 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:57:59.099 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 3d8f9729-f271-48a8-b73f-fd0f814d68a5
15:57:59.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] RpcClient init label, labels = {module=naming, source=sdk}
15:57:59.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:57:59.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:57:59.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:57:59.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:57:59.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Success to connect to server [*************:8848] on start up, connectionId = 1753084680070_************_36993
15:57:59.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:57:59.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1195909598
15:57:59.247 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Notify connected event to listeners.
15:57:59.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:57:59.254 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:57:59.306 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:57:59.342 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:57:59.386 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:57:59.429 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:57:59.454 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:57:59.648 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:57:59.845 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Receive server push request, request = NotifySubscriberRequest, requestId = 107
15:57:59.846 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Ack server push request, request = NotifySubscriberRequest, requestId = 107
15:58:00.709 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:58:00.718 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:58:01.069 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:58:01.140 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:58:01.257 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:58:01.348 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Receive server push request, request = NotifySubscriberRequest, requestId = 109
15:58:01.354 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
15:58:01.355 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
15:58:01.356 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d8f9729-f271-48a8-b73f-fd0f814d68a5] Ack server push request, request = NotifySubscriberRequest, requestId = 109
15:58:01.725 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:58:01.726 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:58:01.727 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:58:01.729 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:58:01.731 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:58:01.751 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:58:01.765 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:58:01.766 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:58:01.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:58:01.771 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:58:01.774 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:58:01.774 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:58:01.780 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:58:01.781 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:58:01.787 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:58:01.806 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:58:01.807 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:58:01.810 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:58:01.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:58:01.817 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:58:01.820 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:58:01.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:58:01.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:58:01.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:58:01.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:58:01.823 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:58:01.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:58:01.827 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:58:01.828 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:58:01.830 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:58:01.831 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:58:01.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:58:01.838 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:58:01.840 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:58:01.841 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:58:01.844 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:58:01.845 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:58:01.846 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:58:01.849 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:58:01.850 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:58:01.854 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:58:01.854 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:58:01.855 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:58:01.858 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:58:01.859 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:58:01.863 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:58:01.864 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:58:01.864 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:58:01.868 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:58:01.869 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:58:01.869 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:58:01.870 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:58:01.871 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:58:01.871 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:58:01.872 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:58:01.873 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:58:01.874 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:58:01.878 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:58:01.879 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:58:01.880 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:58:01.883 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:58:01.884 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:58:01.887 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:58:01.888 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:58:01.890 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:58:01.890 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:58:01.891 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:58:01.895 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:58:01.896 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:58:01.896 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:58:01.900 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:58:01.901 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:58:01.902 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:58:01.902 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:58:01.903 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:58:01.908 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:58:01.910 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:58:01.911 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:58:01.911 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:58:01.912 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:58:01.912 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:58:01.912 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:58:01.913 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:58:01.913 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:58:01.915 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:58:01.915 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:58:01.916 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:58:01.917 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:58:01.919 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:58:01.920 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:58:01.920 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:58:01.922 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:58:01.923 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:58:01.925 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:58:01.925 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:58:01.927 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:58:01.928 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:58:01.929 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:58:01.930 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:58:01.930 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:58:01.932 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:58:01.932 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:58:01.937 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:58:01.938 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:58:01.938 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:58:01.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:58:01.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:58:01.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:58:01.940 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:58:01.941 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:58:01.944 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:58:01.945 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:58:01.946 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:58:01.947 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:58:01.947 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:58:01.952 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:58:01.953 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:58:01.953 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:58:01.954 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:58:01.955 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:58:01.955 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:58:01.956 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:58:01.956 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:58:01.957 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:58:01.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:58:01.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:58:01.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:58:02.016 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 24.901 seconds (JVM running for 27.865)
15:58:02.026 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:58:02.027 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:58:02.029 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:58:02.031 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:58:02.031 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:58:48.032 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:59:28.502 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
16:36:14.879 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
16:36:14.893 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
16:36:14.906 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:36:14.907 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
16:36:14.907 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
16:36:14.907 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
16:36:14.908 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
16:36:14.908 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
16:36:14.908 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
16:36:24.413 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
16:36:25.707 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
16:36:25.707 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
16:36:25.713 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
16:36:25.902 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:36:25.903 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:36:25.965 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
16:36:26.836 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
16:36:26.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 0988ff79-ec56-455d-86be-72fabd7a10e3_config-0
16:36:26.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:36:26.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/26418585
16:36:26.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1788432625
16:36:26.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:36:26.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:36:26.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
16:36:31.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753086991878_************_43362
16:36:31.620 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Notify connected event to listeners.
16:36:31.621 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Connected,notify listen context...
16:36:31.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:36:31.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0988ff79-ec56-455d-86be-72fabd7a10e3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/803149788
16:36:31.711 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
16:36:31.748 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
16:36:31.804 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
16:36:35.271 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
16:36:36.111 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
16:36:38.242 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
16:36:38.243 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
16:36:38.243 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
16:36:38.330 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
16:36:38.330 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
16:36:38.330 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
16:36:38.658 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
16:36:38.659 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
16:36:38.659 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
16:36:38.714 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
16:36:38.715 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
16:36:38.715 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
16:36:38.777 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
16:36:38.777 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
16:36:38.777 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
16:36:38.825 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
16:36:38.825 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
16:36:38.825 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
16:36:38.895 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
16:36:38.895 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
16:36:38.895 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
16:36:39.224 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:36:40.411 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:36:40.864 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
16:36:40.864 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
16:36:40.864 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
16:36:40.920 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
16:36:40.921 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
16:36:40.921 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
16:36:41.238 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
16:36:41.238 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
16:36:41.238 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
16:36:41.311 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
16:36:41.311 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
16:36:41.311 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
16:36:42.227 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
16:36:42.237 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
16:36:42.359 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
16:36:42.450 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
16:36:45.816 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
16:36:45.816 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
16:36:45.816 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
16:36:45.842 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:36:45.843 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:36:45.865 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of a4587260-40ef-4c9a-a67d-dfcd4848c346
16:36:45.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] RpcClient init label, labels = {module=naming, source=sdk}
16:36:45.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:36:45.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:36:45.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:36:45.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
16:36:46.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Success to connect to server [*************:8848] on start up, connectionId = 1753087005511_************_43446
16:36:46.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:36:46.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/803149788
16:36:46.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Notify connected event to listeners.
16:36:46.003 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
16:36:46.009 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
16:36:46.044 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
16:36:46.085 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
16:36:46.345 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
16:36:46.375 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
16:36:46.394 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
16:36:46.685 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Receive server push request, request = NotifySubscriberRequest, requestId = 114
16:36:46.685 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Ack server push request, request = NotifySubscriberRequest, requestId = 114
16:36:47.484 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
16:36:51.523 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
16:36:51.533 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
16:36:51.957 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
16:36:52.027 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
16:36:52.110 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Receive server push request, request = NotifySubscriberRequest, requestId = 116
16:36:52.117 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
16:36:52.118 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
16:36:52.120 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4587260-40ef-4c9a-a67d-dfcd4848c346] Ack server push request, request = NotifySubscriberRequest, requestId = 116
16:36:52.155 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
16:36:52.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
16:36:52.583 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
16:36:52.584 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
16:36:52.586 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
16:36:52.588 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
16:36:52.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
16:36:52.615 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
16:36:52.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
16:36:52.618 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
16:36:52.619 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
16:36:52.621 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
16:36:52.622 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
16:36:52.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
16:36:52.628 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
16:36:52.634 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
16:36:52.650 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
16:36:52.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
16:36:52.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
16:36:52.656 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
16:36:52.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
16:36:52.666 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
16:36:52.667 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
16:36:52.668 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
16:36:52.669 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
16:36:52.670 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
16:36:52.671 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
16:36:52.672 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
16:36:52.677 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
16:36:52.679 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
16:36:52.681 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
16:36:52.682 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
16:36:52.685 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
16:36:52.688 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
16:36:52.689 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
16:36:52.690 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
16:36:52.693 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
16:36:52.694 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
16:36:52.694 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
16:36:52.697 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
16:36:52.698 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
16:36:52.701 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
16:36:52.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
16:36:52.704 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
16:36:52.708 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
16:36:52.709 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
16:36:52.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
16:36:52.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
16:36:52.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
16:36:52.716 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
16:36:52.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
16:36:52.718 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
16:36:52.719 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
16:36:52.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
16:36:52.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
16:36:52.722 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
16:36:52.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
16:36:52.724 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
16:36:52.727 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
16:36:52.729 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
16:36:52.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
16:36:52.732 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
16:36:52.733 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
16:36:52.737 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
16:36:52.738 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
16:36:52.739 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
16:36:52.739 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
16:36:52.740 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
16:36:52.743 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
16:36:52.744 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
16:36:52.745 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
16:36:52.748 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
16:36:52.749 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
16:36:52.749 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
16:36:52.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
16:36:52.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
16:36:52.755 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
16:36:52.757 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
16:36:52.758 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
16:36:52.758 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
16:36:52.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
16:36:52.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
16:36:52.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
16:36:52.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
16:36:52.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
16:36:52.762 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
16:36:52.762 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
16:36:52.763 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
16:36:52.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
16:36:52.766 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
16:36:52.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
16:36:52.768 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
16:36:52.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
16:36:52.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
16:36:52.773 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
16:36:52.773 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
16:36:52.775 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
16:36:52.776 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
16:36:52.777 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
16:36:52.778 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
16:36:52.779 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
16:36:52.780 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
16:36:52.781 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
16:36:52.785 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
16:36:52.786 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
16:36:52.786 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
16:36:52.787 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
16:36:52.787 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
16:36:52.788 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
16:36:52.788 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
16:36:52.790 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
16:36:52.793 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
16:36:52.793 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
16:36:52.794 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
16:36:52.795 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
16:36:52.796 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
16:36:52.802 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
16:36:52.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
16:36:52.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
16:36:52.804 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
16:36:52.805 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
16:36:52.805 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
16:36:52.806 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
16:36:52.806 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
16:36:52.808 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
16:36:52.808 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
16:36:52.809 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
16:36:52.810 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
16:36:52.864 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 30.671 seconds (JVM running for 33.778)
16:36:52.875 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
16:36:52.875 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
16:36:52.878 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
16:36:52.886 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
16:36:52.886 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
16:38:37.712 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:38:53.271 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
16:39:31.271 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
17:10:53.202 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
17:12:26.639 [XNIO-1 task-1] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
20:38:30.441 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Logout][退出成功]
20:38:31.783 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276695906943045
20:38:32.827 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276695906943045
20:38:32.857 [XNIO-1 task-2] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========31043
20:38:33.000 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Success][登录成功]
20:44:55.534 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Logout][退出成功]
20:44:56.575 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 276697483550789
20:44:56.602 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  276697483550789
20:44:56.620 [XNIO-1 task-2] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========31043
20:44:56.716 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[31043][Success][登录成功]
