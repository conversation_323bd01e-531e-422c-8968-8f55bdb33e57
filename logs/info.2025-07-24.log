08:42:39.118 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
08:42:41.265 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
08:42:41.265 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
08:42:41.271 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
08:42:41.548 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
08:42:41.548 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
08:42:41.638 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
08:42:42.852 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
08:42:42.860 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 8d638fa4-f921-4c62-8e08-2e864e46673e_config-0
08:42:42.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
08:42:42.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/859617558
08:42:42.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/103394942
08:42:42.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
08:42:42.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
08:42:42.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
08:42:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
08:42:54.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753317778239_************_44407
08:42:54.879 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify connected event to listeners.
08:42:54.880 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Connected,notify listen context...
08:42:54.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
08:42:54.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$278/2107873140
08:42:55.103 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
08:42:55.223 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
08:42:55.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Success to connect a server [*************:8848], connectionId = 1753317778960_************_44409
08:42:55.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753317778239_************_44407
08:42:55.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753317778239_************_44407
08:42:55.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify disconnected event to listeners
08:42:55.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] DisConnected,clear listen context...
08:42:55.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify connected event to listeners.
08:42:55.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Connected,notify listen context...
08:42:56.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
08:42:56.323 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Success to connect a server [*************:8848], connectionId = 1753317779707_************_44416
08:42:56.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753317778960_************_44409
08:42:56.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753317778960_************_44409
08:42:56.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify disconnected event to listeners
08:42:56.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] DisConnected,clear listen context...
08:42:56.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify connected event to listeners.
08:42:56.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Connected,notify listen context...
08:42:57.536 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
08:42:57.779 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
08:43:07.298 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
08:43:08.753 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
08:43:11.713 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:43:11.713 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:43:11.713 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:43:11.809 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:43:11.809 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:43:11.810 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:43:12.091 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:43:12.091 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:43:12.092 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:43:12.158 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:43:12.158 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:43:12.159 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:43:12.235 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:43:12.235 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:43:12.235 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:43:12.302 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:43:12.303 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:43:12.303 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:43:12.395 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:43:12.395 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:43:12.396 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:43:12.987 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:43:18.842 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:43:20.803 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:43:20.803 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:43:20.803 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:43:20.875 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:43:20.875 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:43:20.875 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:43:21.793 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:43:21.793 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:43:21.793 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:43:21.886 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:43:21.887 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:43:21.887 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:43:26.213 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
08:43:26.271 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
08:43:26.632 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
08:43:26.898 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
08:43:30.703 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
08:43:30.703 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
08:43:30.706 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
08:43:30.769 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
08:43:30.769 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
08:43:30.811 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of cad7a69a-2a26-4e90-8ef5-9438c3f37139
08:43:30.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] RpcClient init label, labels = {module=naming, source=sdk}
08:43:30.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
08:43:30.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
08:43:30.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
08:43:30.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
08:43:30.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Success to connect to server [*************:8848] on start up, connectionId = 1753317814427_************_44720
08:43:30.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
08:43:30.946 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify connected event to listeners.
08:43:30.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$278/2107873140
08:43:30.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
08:43:30.954 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
08:43:31.289 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
08:43:31.317 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
08:43:31.391 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
08:43:31.431 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
08:43:31.458 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
08:43:31.675 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
08:43:31.940 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Receive server push request, request = NotifySubscriberRequest, requestId = 1
08:43:31.941 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Ack server push request, request = NotifySubscriberRequest, requestId = 1
08:43:38.636 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
08:43:38.648 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
08:43:39.035 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
08:43:39.109 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
08:43:39.250 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
08:43:39.267 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Receive server push request, request = NotifySubscriberRequest, requestId = 3
08:43:39.277 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:43:39.277 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:43:39.279 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Ack server push request, request = NotifySubscriberRequest, requestId = 3
08:43:39.763 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
08:43:39.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
08:43:39.765 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
08:43:39.768 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
08:43:39.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
08:43:39.785 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
08:43:39.797 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
08:43:39.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
08:43:39.800 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
08:43:39.801 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
08:43:39.802 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
08:43:39.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
08:43:39.807 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
08:43:39.809 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
08:43:39.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
08:43:39.836 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
08:43:39.837 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
08:43:39.841 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
08:43:39.843 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
08:43:39.850 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
08:43:39.854 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
08:43:39.864 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
08:43:39.865 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
08:43:39.867 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
08:43:39.867 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
08:43:39.868 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
08:43:39.869 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
08:43:39.870 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
08:43:39.873 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
08:43:39.875 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
08:43:39.876 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
08:43:39.880 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
08:43:39.883 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
08:43:39.884 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
08:43:39.887 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
08:43:39.890 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
08:43:39.891 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
08:43:39.892 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
08:43:39.896 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
08:43:39.897 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
08:43:39.900 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
08:43:39.902 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
08:43:39.903 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
08:43:39.906 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
08:43:39.907 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
08:43:39.909 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
08:43:39.910 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
08:43:39.910 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
08:43:39.914 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
08:43:39.915 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
08:43:39.916 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
08:43:39.917 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
08:43:39.917 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
08:43:39.918 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
08:43:39.919 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
08:43:39.920 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
08:43:39.920 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
08:43:39.925 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
08:43:39.926 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
08:43:39.927 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
08:43:39.930 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
08:43:39.931 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
08:43:39.935 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
08:43:39.936 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
08:43:39.937 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
08:43:39.938 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
08:43:39.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
08:43:39.943 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
08:43:39.944 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
08:43:39.945 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
08:43:39.950 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
08:43:39.951 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
08:43:39.951 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
08:43:39.952 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
08:43:39.953 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
08:43:39.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
08:43:39.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
08:43:39.960 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
08:43:39.961 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
08:43:39.962 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
08:43:39.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
08:43:39.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
08:43:39.964 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
08:43:39.964 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
08:43:39.966 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
08:43:39.966 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
08:43:39.967 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
08:43:39.969 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
08:43:39.970 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
08:43:39.971 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
08:43:39.972 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
08:43:39.974 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
08:43:39.974 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
08:43:39.977 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
08:43:39.977 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
08:43:39.979 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
08:43:39.982 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
08:43:39.984 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
08:43:39.985 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
08:43:39.985 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
08:43:39.987 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
08:43:39.987 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
08:43:39.999 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
08:43:40.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
08:43:40.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
08:43:40.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
08:43:40.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
08:43:40.002 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
08:43:40.003 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
08:43:40.005 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
08:43:40.008 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
08:43:40.008 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
08:43:40.009 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
08:43:40.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
08:43:40.012 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
08:43:40.018 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
08:43:40.019 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
08:43:40.019 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
08:43:40.020 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
08:43:40.020 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
08:43:40.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
08:43:40.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
08:43:40.022 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
08:43:40.023 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
08:43:40.024 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
08:43:40.025 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
08:43:40.025 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
08:43:40.091 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 62.759 seconds (JVM running for 66.872)
08:43:40.103 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
08:43:40.104 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
08:43:40.107 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
08:43:40.109 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
08:43:40.109 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
08:44:52.529 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:44:52.841 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 277582203789381
08:44:53.568 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  277582203789381
08:44:53.953 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
08:44:54.212 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
10:09:57.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Server healthy check fail, currentConnection = 1753317814427_************_44720
10:09:57.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:09:58.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Success to connect a server [*************:8848], connectionId = 1753322999849_************_3324
10:09:58.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Abandon prev connection, server is *************:8848, connectionId is 1753317814427_************_44720
10:09:58.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753317814427_************_44720
10:09:58.215 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify disconnected event to listeners
10:09:58.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify connected event to listeners.
10:09:58.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
10:09:59.949 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
10:10:00.016 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
10:10:00.642 [nacos-grpc-client-executor-*************-1046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:10:00.642 [nacos-grpc-client-executor-*************-1046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:36:55.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Server healthy check fail, currentConnection = 1753317779707_************_44416
10:36:55.100 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:36:55.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Success to connect a server [*************:8848], connectionId = 1753324618574_************_12666
10:36:55.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753317779707_************_44416
10:36:55.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753317779707_************_44416
10:36:55.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify disconnected event to listeners
10:36:55.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] DisConnected,clear listen context...
10:36:55.508 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify connected event to listeners.
10:36:55.508 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Connected,notify listen context...
10:49:50.333 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Server healthy check fail, currentConnection = 1753322999849_************_3324
10:49:50.333 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:49:53.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Success to connect a server [*************:8848], connectionId = 1753325394816_************_53827
10:49:53.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Abandon prev connection, server is *************:8848, connectionId is 1753322999849_************_3324
10:49:53.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753322999849_************_3324
10:49:53.846 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify disconnected event to listeners
10:49:54.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Server check success, currentServer is *************:8848 
10:49:54.710 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify connected event to listeners.
10:49:54.710 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
10:50:02.193 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [isChangedServiceInfo,240] - removed ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
10:50:02.194 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
10:50:02.067 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
10:50:02.380 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
10:50:05.529 [nacos-grpc-client-executor-*************-1539] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:50:05.597 [nacos-grpc-client-executor-*************-1539] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
10:50:05.597 [nacos-grpc-client-executor-*************-1539] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
10:50:05.670 [nacos-grpc-client-executor-*************-1539] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:41:45.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Server healthy check fail, currentConnection = 1753325394816_************_53827
13:41:46.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:41:47.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Success to connect a server [*************:8848], connectionId = 1753335707340_************_11623
13:41:47.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Abandon prev connection, server is *************:8848, connectionId is 1753325394816_************_53827
13:41:47.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753325394816_************_53827
13:41:47.726 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify disconnected event to listeners
13:41:47.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify connected event to listeners.
13:41:47.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
13:41:49.239 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
13:41:50.015 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
13:41:51.220 [nacos-grpc-client-executor-*************-3616] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:41:51.220 [nacos-grpc-client-executor-*************-3616] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Ack server push request, request = NotifySubscriberRequest, requestId = 45
17:20:14.500 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Server healthy check fail, currentConnection = 1753324618574_************_12666
17:20:14.809 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:20:16.757 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Success to connect a server [*************:8848], connectionId = 1753348816258_************_41577
17:20:16.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Abandon prev connection, server is *************:8848, connectionId is 1753324618574_************_12666
17:20:16.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753324618574_************_12666
17:20:16.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify disconnected event to listeners
17:20:16.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] DisConnected,clear listen context...
17:20:16.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Notify connected event to listeners.
17:20:16.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8d638fa4-f921-4c62-8e08-2e864e46673e_config-0] Connected,notify listen context...
18:45:49.148 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
18:45:52.054 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
18:45:54.407 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:45:54.588 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
18:45:54.608 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
18:45:54.640 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
18:45:54.640 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
18:45:54.640 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
18:45:54.640 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
18:45:55.016 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
18:45:55.088 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,192] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
18:45:55.337 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,197] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
18:45:55.358 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,527] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
18:45:55.428 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
18:45:55.428 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
18:45:55.428 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,530] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
18:45:55.458 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,453] - Shutdown rpc client, set status to shutdown
18:45:55.480 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,455] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@556fa80e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:45:55.480 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753335707340_************_11623
18:45:55.481 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,129] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6873aa3f[Running, pool size = 2, active threads = 0, queued tasks = 0, completed tasks = 7297]
18:45:55.482 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,267] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@7d53ab72[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 12006]
18:45:55.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cad7a69a-2a26-4e90-8ef5-9438c3f37139] Notify disconnected event to listeners
18:45:55.637 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
18:45:55.733 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [free,99] - [null] CredentialService is freed
18:45:55.872 [com.alibaba.nacos.client.naming.security] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
18:45:55.872 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
18:45:55.873 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,189] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
18:45:58.587 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:45:58.952 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
