14:18:17.973 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
14:18:19.249 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
14:18:19.250 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
14:18:19.252 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
14:18:19.385 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:18:19.385 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:18:19.408 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
14:18:20.137 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
14:18:20.144 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0
14:18:20.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:18:20.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/673129458
14:18:20.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/142261320
14:18:20.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:18:20.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:18:20.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
14:18:24.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753683505237_************_48255
14:18:24.657 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Notify connected event to listeners.
14:18:24.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Connected,notify listen context...
14:18:24.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:24.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987b302c-ec5f-4e4d-8559-ba1b06f6bd61_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/759099777
14:18:24.767 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
14:18:24.887 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
14:18:24.947 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
14:18:28.888 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
14:18:29.633 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
14:18:31.723 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
14:18:31.723 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
14:18:31.724 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
14:18:31.801 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
14:18:31.801 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
14:18:31.801 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
14:18:32.049 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
14:18:32.049 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
14:18:32.050 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
14:18:32.104 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
14:18:32.105 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
14:18:32.105 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
14:18:32.174 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
14:18:32.175 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
14:18:32.175 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
14:18:32.231 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
14:18:32.231 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
14:18:32.231 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
14:18:32.303 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
14:18:32.304 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
14:18:32.304 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
14:18:32.757 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:18:34.428 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:18:35.122 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
14:18:35.122 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
14:18:35.122 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
14:18:35.177 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
14:18:35.177 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
14:18:35.177 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
14:18:35.478 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
14:18:35.479 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
14:18:35.479 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
14:18:35.549 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
14:18:35.549 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
14:18:35.550 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
14:18:36.350 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
14:18:36.356 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
14:18:36.387 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
14:18:36.400 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
14:18:38.580 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
14:18:38.580 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
14:18:38.580 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
14:18:38.619 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:18:38.620 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:18:38.648 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 15efb991-5507-4ce9-b1d4-949300c3b62a
14:18:38.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] RpcClient init label, labels = {module=naming, source=sdk}
14:18:38.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:18:38.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:18:38.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:18:38.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
14:18:38.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Success to connect to server [*************:8848] on start up, connectionId = 1753683518488_************_48317
14:18:38.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:38.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Notify connected event to listeners.
14:18:38.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/759099777
14:18:38.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
14:18:38.796 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
14:18:38.859 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
14:18:38.887 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
14:18:38.940 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
14:18:38.981 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
14:18:39.008 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
14:18:39.167 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
14:18:39.495 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Receive server push request, request = NotifySubscriberRequest, requestId = 1
14:18:39.496 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Ack server push request, request = NotifySubscriberRequest, requestId = 1
14:18:40.247 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
14:18:40.258 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
14:18:40.574 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
14:18:40.714 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
14:18:40.822 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Receive server push request, request = NotifySubscriberRequest, requestId = 2
14:18:40.903 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
14:18:40.903 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
14:18:40.905 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [15efb991-5507-4ce9-b1d4-949300c3b62a] Ack server push request, request = NotifySubscriberRequest, requestId = 2
14:18:41.054 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:18:42.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
14:18:42.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
14:18:42.254 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
14:18:42.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
14:18:42.263 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
14:18:42.380 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
14:18:42.457 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
14:18:42.459 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
14:18:42.467 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
14:18:42.472 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
14:18:42.474 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
14:18:42.480 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
14:18:42.486 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
14:18:42.487 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
14:18:42.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
14:18:42.528 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
14:18:42.529 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
14:18:42.535 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
14:18:42.538 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
14:18:42.546 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
14:18:42.549 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
14:18:42.550 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
14:18:42.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
14:18:42.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
14:18:42.552 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
14:18:42.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
14:18:42.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
14:18:42.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
14:18:42.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
14:18:42.562 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
14:18:42.563 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
14:18:42.566 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
14:18:42.569 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
14:18:42.570 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
14:18:42.572 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
14:18:42.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
14:18:42.576 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
14:18:42.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
14:18:42.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
14:18:42.580 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
14:18:42.583 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
14:18:42.584 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
14:18:42.585 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
14:18:42.589 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
14:18:42.592 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
14:18:42.600 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
14:18:42.601 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
14:18:42.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
14:18:42.605 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
14:18:42.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
14:18:42.607 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
14:18:42.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
14:18:42.609 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
14:18:42.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
14:18:42.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
14:18:42.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
14:18:42.618 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
14:18:42.627 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
14:18:42.633 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
14:18:42.634 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
14:18:42.642 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
14:18:42.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
14:18:42.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
14:18:42.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
14:18:42.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
14:18:42.664 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
14:18:42.667 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
14:18:42.681 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
14:18:42.684 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
14:18:42.687 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
14:18:42.698 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
14:18:42.699 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
14:18:42.700 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
14:18:42.701 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
14:18:42.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
14:18:42.707 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
14:18:42.710 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
14:18:42.710 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
14:18:42.711 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
14:18:42.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
14:18:42.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
14:18:42.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
14:18:42.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
14:18:42.714 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
14:18:42.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
14:18:42.737 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
14:18:42.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
14:18:42.757 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
14:18:42.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
14:18:42.765 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
14:18:42.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
14:18:42.772 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
14:18:42.773 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
14:18:42.776 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
14:18:42.777 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
14:18:42.779 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
14:18:42.782 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
14:18:42.783 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
14:18:42.784 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
14:18:42.785 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
14:18:42.786 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
14:18:42.787 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
14:18:42.795 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
14:18:42.796 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
14:18:42.797 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
14:18:42.797 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
14:18:42.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
14:18:42.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
14:18:42.799 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
14:18:42.802 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
14:18:42.812 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
14:18:42.813 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
14:18:42.814 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
14:18:42.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
14:18:42.816 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
14:18:42.823 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
14:18:42.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
14:18:42.825 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
14:18:42.826 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
14:18:42.827 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
14:18:42.828 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
14:18:42.828 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
14:18:42.828 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
14:18:42.830 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
14:18:42.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
14:18:42.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
14:18:42.833 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
14:18:42.934 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 28.607 seconds (JVM running for 32.289)
14:18:42.950 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
14:18:42.950 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
14:18:42.954 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
14:18:42.955 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
14:18:42.956 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
14:22:16.910 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:22:17.224 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279080702357573
14:22:17.926 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279080702357573
14:22:18.355 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
14:22:18.612 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
14:36:02.374 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279084082667589
14:36:02.402 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279084082667589
14:36:02.473 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
14:36:02.639 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
14:36:07.902 [XNIO-1 task-3] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/plat/list
14:36:07.968 [XNIO-1 task-3] INFO  c.j.a.c.a.HasPermissionsAspect - [doBefore,49] - 当前请求接口路径是/auth-server/system/menu/list
15:12:26.954 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:12:26.969 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
15:12:26.986 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:12:26.987 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:12:34.331 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:12:35.490 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:12:35.491 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:12:35.494 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:12:35.627 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:12:35.627 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:12:35.642 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:12:36.246 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:12:36.252 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0
15:12:36.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:12:36.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1624355359
15:12:36.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1724399560
15:12:36.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:12:36.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:12:36.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:12:39.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753686758816_************_56672
15:12:39.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Notify connected event to listeners.
15:12:39.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:12:39.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Connected,notify listen context...
15:12:39.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc65d0-c5bb-4e77-b513-15d438a5df78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1771190979
15:12:39.370 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:12:39.411 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:12:39.468 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:12:42.894 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:12:43.684 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:12:45.540 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:12:45.541 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:12:45.541 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:12:45.617 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:12:45.617 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:12:45.617 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:12:45.837 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:12:45.837 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:12:45.837 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:12:45.886 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:12:45.886 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:12:45.886 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:12:45.939 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:12:45.939 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:12:45.939 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:12:45.981 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:12:45.982 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:12:45.982 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:12:46.037 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:12:46.037 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:12:46.037 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:12:46.351 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:12:47.497 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:12:47.889 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:12:47.890 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:12:47.890 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:12:47.943 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:12:47.944 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:12:47.944 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:12:48.256 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:12:48.256 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:12:48.257 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:12:48.323 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:12:48.323 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:12:48.323 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:12:49.047 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:12:49.052 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:12:49.083 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:12:49.095 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:12:50.938 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:12:50.939 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:12:50.939 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:12:50.966 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:12:50.966 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:12:50.988 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of a66d04a0-422f-4e8c-b224-8b0a91895cfa
15:12:50.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] RpcClient init label, labels = {module=naming, source=sdk}
15:12:50.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:12:50.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:12:50.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:12:50.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:12:51.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Success to connect to server [*************:8848] on start up, connectionId = 1753686772139_************_56739
15:12:51.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:12:51.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1771190979
15:12:51.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Notify connected event to listeners.
15:12:51.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:12:51.141 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:12:51.177 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:12:51.201 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:12:51.236 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:12:51.268 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:12:51.286 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:12:51.410 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:12:51.696 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Receive server push request, request = NotifySubscriberRequest, requestId = 18
15:12:51.697 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Ack server push request, request = NotifySubscriberRequest, requestId = 18
15:12:52.252 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:12:52.260 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:12:52.515 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:12:52.579 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:12:52.701 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:12:52.815 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Receive server push request, request = NotifySubscriberRequest, requestId = 19
15:12:52.823 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
15:12:52.823 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
15:12:52.825 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a66d04a0-422f-4e8c-b224-8b0a91895cfa] Ack server push request, request = NotifySubscriberRequest, requestId = 19
15:12:53.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:12:53.138 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:12:53.139 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:12:53.141 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:12:53.142 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:12:53.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:12:53.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:12:53.168 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:12:53.170 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:12:53.171 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:12:53.172 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:12:53.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:12:53.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:12:53.178 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:12:53.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:12:53.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:12:53.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:12:53.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:12:53.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:12:53.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:12:53.210 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:12:53.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:12:53.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:12:53.213 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:12:53.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:12:53.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:12:53.215 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:12:53.217 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:12:53.219 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:12:53.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:12:53.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:12:53.223 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:12:53.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:12:53.226 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:12:53.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:12:53.229 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:12:53.230 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:12:53.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:12:53.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:12:53.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:12:53.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:12:53.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:12:53.238 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:12:53.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:12:53.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:12:53.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:12:53.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:12:53.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:12:53.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:12:53.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:12:53.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:12:53.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:12:53.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:12:53.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:12:53.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:12:53.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:12:53.252 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:12:53.255 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:12:53.256 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:12:53.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:12:53.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:12:53.261 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:12:53.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:12:53.265 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:12:53.266 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:12:53.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:12:53.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:12:53.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:12:53.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:12:53.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:12:53.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:12:53.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:12:53.276 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:12:53.276 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:12:53.277 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:12:53.281 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:12:53.282 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:12:53.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:12:53.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:12:53.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:12:53.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:12:53.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:12:53.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:12:53.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:12:53.286 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:12:53.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:12:53.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:12:53.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:12:53.290 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:12:53.290 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:12:53.291 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:12:53.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:12:53.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:12:53.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:12:53.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:12:53.297 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:12:53.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:12:53.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:12:53.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:12:53.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:12:53.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:12:53.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:12:53.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:12:53.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:12:53.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:12:53.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:12:53.308 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:12:53.308 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:12:53.309 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:12:53.310 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:12:53.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:12:53.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:12:53.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:12:53.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:12:53.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:12:53.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:12:53.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:12:53.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:12:53.322 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:12:53.322 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:12:53.323 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:12:53.323 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:12:53.323 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:12:53.324 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:12:53.325 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:12:53.325 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:12:53.326 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:12:53.379 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 20.392 seconds (JVM running for 23.276)
15:12:53.389 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:12:53.390 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:12:53.392 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:12:53.393 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:12:53.393 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:14:54.390 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:14:54.789 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
15:15:01.696 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279093664100421
15:15:02.283 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279093664100421
15:15:02.515 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
15:15:02.671 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
15:16:32.697 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:16:32.712 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
15:16:32.724 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:16:32.725 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:16:32.725 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:16:32.725 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:16:32.726 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:16:32.726 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:16:32.726 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:16:39.815 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:16:40.774 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:16:40.774 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:16:40.777 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:16:40.900 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:16:40.900 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:16:40.941 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:16:41.602 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:16:41.608 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 14b0557e-70e0-4524-bb2f-583657e9b62e_config-0
15:16:41.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:16:41.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/791499503
15:16:41.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/15912555
15:16:41.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:16:41.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:16:41.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:16:45.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753687005488_************_57561
15:16:45.509 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Notify connected event to listeners.
15:16:45.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:16:45.510 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Connected,notify listen context...
15:16:45.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14b0557e-70e0-4524-bb2f-583657e9b62e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/704482919
15:16:45.603 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:16:45.647 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:16:45.704 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:16:49.005 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:16:49.741 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:16:51.565 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:16:51.566 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:16:51.566 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:16:51.643 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:16:51.644 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:16:51.644 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:16:51.871 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:16:51.872 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:16:51.872 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:16:51.926 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:16:51.926 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:16:51.926 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:16:51.977 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:16:51.978 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:16:51.978 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:16:52.027 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:16:52.027 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:16:52.027 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:16:52.087 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:16:52.088 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:16:52.088 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:16:52.415 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:16:53.521 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:16:53.894 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:16:53.894 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:16:53.894 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:16:53.952 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:16:53.952 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:16:53.952 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:16:54.290 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:16:54.291 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:16:54.291 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:16:54.359 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:16:54.360 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:16:54.360 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:16:55.069 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:16:55.075 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:16:55.107 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:16:55.119 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:16:56.962 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:16:56.962 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:16:56.963 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:16:56.989 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:16:56.989 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:16:57.011 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 7ceaa27e-6982-426e-a307-658738575186
15:16:57.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] RpcClient init label, labels = {module=naming, source=sdk}
15:16:57.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:16:57.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:16:57.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:16:57.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:16:57.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Success to connect to server [*************:8848] on start up, connectionId = 1753687016814_************_57602
15:16:57.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:16:57.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/704482919
15:16:57.142 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Notify connected event to listeners.
15:16:57.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:16:57.148 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:16:57.185 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:16:57.213 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:16:57.252 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:16:57.285 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:16:57.308 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:16:57.432 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:16:57.736 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Receive server push request, request = NotifySubscriberRequest, requestId = 23
15:16:57.736 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Ack server push request, request = NotifySubscriberRequest, requestId = 23
15:16:58.299 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:16:58.308 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:16:58.568 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:16:58.632 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:16:58.745 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:16:58.863 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Receive server push request, request = NotifySubscriberRequest, requestId = 24
15:16:58.871 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:16:58.871 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:16:58.873 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ceaa27e-6982-426e-a307-658738575186] Ack server push request, request = NotifySubscriberRequest, requestId = 24
15:16:59.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:16:59.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:16:59.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:16:59.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:16:59.169 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:16:59.185 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:16:59.196 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:16:59.197 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:16:59.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:16:59.200 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:16:59.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:16:59.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:16:59.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:16:59.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:16:59.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:16:59.229 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:16:59.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:16:59.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:16:59.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:16:59.240 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:16:59.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:16:59.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:16:59.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:16:59.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:16:59.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:16:59.245 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:16:59.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:16:59.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:16:59.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:16:59.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:16:59.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:16:59.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:16:59.255 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:16:59.256 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:16:59.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:16:59.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:16:59.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:16:59.261 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:16:59.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:16:59.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:16:59.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:16:59.268 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:16:59.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:16:59.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:16:59.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:16:59.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:16:59.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:16:59.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:16:59.277 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:16:59.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:16:59.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:16:59.279 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:16:59.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:16:59.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:16:59.281 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:16:59.282 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:16:59.282 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:16:59.286 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:16:59.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:16:59.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:16:59.290 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:16:59.291 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:16:59.294 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:16:59.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:16:59.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:16:59.297 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:16:59.297 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:16:59.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:16:59.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:16:59.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:16:59.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:16:59.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:16:59.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:16:59.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:16:59.308 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:16:59.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:16:59.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:16:59.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:16:59.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:16:59.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:16:59.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:16:59.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:16:59.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:16:59.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:16:59.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:16:59.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:16:59.319 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:16:59.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:16:59.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:16:59.322 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:16:59.322 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:16:59.324 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:16:59.324 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:16:59.326 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:16:59.327 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:16:59.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:16:59.330 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:16:59.330 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:16:59.331 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:16:59.331 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:16:59.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:16:59.333 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:16:59.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:16:59.338 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:16:59.338 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:16:59.339 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:16:59.339 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:16:59.340 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:16:59.340 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:16:59.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:16:59.344 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:16:59.344 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:16:59.345 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:16:59.346 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:16:59.346 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:16:59.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:16:59.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:16:59.353 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:16:59.354 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:16:59.354 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:16:59.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:16:59.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:16:59.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:16:59.357 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:16:59.357 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:16:59.358 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:16:59.358 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:16:59.415 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 20.949 seconds (JVM running for 23.825)
15:16:59.425 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:16:59.426 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:16:59.429 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:16:59.430 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:16:59.431 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:17:38.640 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:39.007 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
15:20:54.752 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:20:54.770 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
15:20:54.782 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:20:54.783 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:20:54.783 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:20:54.783 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:20:54.784 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:20:54.784 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:20:54.784 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:21:01.688 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:21:02.761 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:21:02.761 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:21:02.764 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:21:02.861 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:21:02.861 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:21:02.876 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:21:03.557 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:21:03.562 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of ca665456-e127-427e-adf1-81b5a09729c1_config-0
15:21:03.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:03.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/15912555
15:21:03.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/633683828
15:21:03.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:03.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:03.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:21:07.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753687268003_************_58165
15:21:07.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Notify connected event to listeners.
15:21:07.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:07.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Connected,notify listen context...
15:21:07.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1272284318
15:21:07.886 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:21:07.920 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:21:07.976 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:21:11.383 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:21:12.101 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:21:14.111 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:21:14.111 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:21:14.112 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:21:14.176 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:21:14.177 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:21:14.177 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:21:14.416 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:21:14.416 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:21:14.416 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:21:14.472 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:21:14.472 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:21:14.472 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:21:14.525 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:21:14.526 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:21:14.526 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:21:14.570 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:21:14.570 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:21:14.570 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:21:14.627 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:21:14.627 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:21:14.627 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:21:14.958 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:21:15.967 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:21:16.366 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:21:16.366 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:21:16.366 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:21:16.419 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:21:16.420 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:21:16.420 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:21:16.850 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:21:16.850 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:21:16.851 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:21:16.922 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:21:16.923 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:21:16.923 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:21:17.696 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:21:17.702 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:21:17.737 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:21:17.751 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:21:19.688 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:21:19.689 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:21:19.689 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:21:19.715 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:21:19.715 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:21:19.742 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of d9edde82-a67b-4776-8bf3-daede5558388
15:21:19.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] RpcClient init label, labels = {module=naming, source=sdk}
15:21:19.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:21:19.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:21:19.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:21:19.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:21:19.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Success to connect to server [*************:8848] on start up, connectionId = 1753687279371_************_58211
15:21:19.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:19.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Notify connected event to listeners.
15:21:19.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1272284318
15:21:19.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:21:19.911 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:21:19.952 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:21:19.982 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:21:20.028 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:21:20.060 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:21:20.081 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:21:20.227 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:21:20.529 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Receive server push request, request = NotifySubscriberRequest, requestId = 28
15:21:20.530 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Ack server push request, request = NotifySubscriberRequest, requestId = 28
15:21:21.126 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:21:21.140 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:21:21.403 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:21:21.478 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:21:21.592 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:21:21.696 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Receive server push request, request = NotifySubscriberRequest, requestId = 30
15:21:21.705 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
15:21:21.706 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
15:21:21.707 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9edde82-a67b-4776-8bf3-daede5558388] Ack server push request, request = NotifySubscriberRequest, requestId = 30
15:21:22.064 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:21:22.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:21:22.067 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:21:22.070 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:21:22.074 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:21:22.094 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:21:22.108 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:21:22.109 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:21:22.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:21:22.113 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:21:22.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:21:22.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:21:22.121 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:21:22.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:21:22.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:21:22.149 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:21:22.150 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:21:22.153 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:21:22.154 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:21:22.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:21:22.162 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:21:22.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:21:22.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:21:22.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:21:22.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:21:22.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:21:22.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:21:22.168 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:21:22.170 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:21:22.172 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:21:22.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:21:22.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:21:22.179 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:21:22.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:21:22.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:21:22.186 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:21:22.187 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:21:22.188 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:21:22.192 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:21:22.193 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:21:22.196 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:21:22.197 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:21:22.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:21:22.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:21:22.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:21:22.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:21:22.205 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:21:22.205 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:21:22.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:21:22.209 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:21:22.209 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:21:22.210 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:21:22.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:21:22.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:21:22.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:21:22.213 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:21:22.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:21:22.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:21:22.219 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:21:22.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:21:22.223 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:21:22.224 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:21:22.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:21:22.228 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:21:22.229 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:21:22.230 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:21:22.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:21:22.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:21:22.235 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:21:22.235 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:21:22.239 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:21:22.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:21:22.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:21:22.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:21:22.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:21:22.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:21:22.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:21:22.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:21:22.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:21:22.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:21:22.252 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:21:22.252 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:21:22.252 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:21:22.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:21:22.255 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:21:22.255 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:21:22.256 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:21:22.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:21:22.259 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:21:22.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:21:22.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:21:22.262 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:21:22.263 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:21:22.265 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:21:22.266 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:21:22.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:21:22.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:21:22.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:21:22.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:21:22.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:21:22.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:21:22.286 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:21:22.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:21:22.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:21:22.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:21:22.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:21:22.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:21:22.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:21:22.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:21:22.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:21:22.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:21:22.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:21:22.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:21:22.308 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:21:22.309 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:21:22.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:21:22.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:21:22.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:21:22.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:21:22.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:21:22.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:21:22.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:21:22.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:21:22.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:21:22.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:21:22.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:21:22.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:21:22.384 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 22.053 seconds (JVM running for 24.512)
15:21:22.394 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:21:22.395 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:21:22.397 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:21:22.399 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:21:22.399 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:23:05.752 [nacos-grpc-client-executor-*************-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 31
15:23:05.752 [nacos-grpc-client-executor-*************-33] INFO  c.a.n.c.c.i.ClientWorker - [lambda$initRpcClientHandler$0,596] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] [server-push] config changed. dataId=auth-server.yml, group=DEFAULT_GROUP,tenant=pacs-test
15:23:05.752 [nacos-grpc-client-executor-*************-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca665456-e127-427e-adf1-81b5a09729c1_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 31
15:23:05.772 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.c.i.ClientWorker - [refreshContentAndCheck,441] - [fixed-pacs-test-*************_8848] [data-received] dataId=auth-server.yml, group=DEFAULT_GROUP, tenant=pacs-test, md5=90fe09f86a3a28686e503143b4609eb9, content=# 开发环境配置
server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    acc..., type=yaml
15:23:05.773 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.c.i.CacheData - [safeNotifyListener,366] - [fixed-pacs-test-*************_8848] [notify-listener] time cost=0ms in ClientWorker, dataId=auth-server.yml, group=DEFAULT_GROUP, md5=90fe09f86a3a28686e503143b4609eb9, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@77dfac51 
15:23:05.773 [nacos.client.cachedata.internal.notifier] INFO  c.a.n.c.c.i.CacheData - [lambda$safeNotifyListener$1,302] - [fixed-pacs-test-*************_8848] [notify-context] dataId=auth-server.yml, group=DEFAULT_GROUP, md5=90fe09f86a3a28686e503143b4609eb9
15:23:06.516 [nacos.client.cachedata.internal.notifier] INFO  c.a.n.c.c.i.CacheData - [lambda$safeNotifyListener$1,328] - [fixed-pacs-test-*************_8848] [notify-ok] dataId=auth-server.yml, group=DEFAULT_GROUP, md5=90fe09f86a3a28686e503143b4609eb9, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@77dfac51 ,cost=743 millis.
15:26:07.111 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:26:07.151 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:26:07.152 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:26:14.263 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
15:26:15.790 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
15:26:15.791 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
15:26:15.798 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
15:26:15.963 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:26:15.963 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:26:15.998 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
15:26:16.681 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
15:26:16.688 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of b5158a42-7164-4f98-97af-937f7601c531_config-0
15:26:16.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:26:16.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1033425208
15:26:16.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/791499503
15:26:16.830 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:26:16.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:26:16.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:26:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:26:24.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753687584074_************_59020
15:26:24.409 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Notify connected event to listeners.
15:26:24.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Connected,notify listen context...
15:26:24.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:26:24.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5158a42-7164-4f98-97af-937f7601c531_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/836903727
15:26:24.530 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
15:26:24.577 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
15:26:24.641 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
15:26:29.457 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
15:26:30.814 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
15:26:32.951 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:26:32.952 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:26:32.952 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
15:26:33.020 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:26:33.021 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:26:33.021 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
15:26:33.264 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:26:33.264 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:26:33.264 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
15:26:33.322 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:26:33.322 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:26:33.322 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
15:26:33.377 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:26:33.378 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:26:33.378 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
15:26:33.427 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:26:33.428 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:26:33.428 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
15:26:33.490 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:26:33.490 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:26:33.491 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
15:26:33.803 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:26:34.981 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:26:35.432 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:26:35.433 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:26:35.433 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
15:26:35.493 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:26:35.494 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:26:35.494 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
15:26:35.817 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:26:35.817 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:26:35.817 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
15:26:35.890 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:26:35.890 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:26:35.890 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
15:26:36.707 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
15:26:36.713 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
15:26:36.744 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:26:36.757 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
15:26:38.835 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
15:26:38.836 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
15:26:38.836 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
15:26:38.872 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:26:38.872 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:26:38.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of dd8f0bc8-33c9-4b3e-8322-7cee510eddae
15:26:38.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] RpcClient init label, labels = {module=naming, source=sdk}
15:26:38.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:26:38.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:26:38.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:26:38.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
15:26:39.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Success to connect to server [*************:8848] on start up, connectionId = 1753687599676_************_59165
15:26:39.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:26:39.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Notify connected event to listeners.
15:26:39.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/836903727
15:26:39.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
15:26:39.056 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
15:26:39.096 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:26:39.126 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
15:26:39.172 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
15:26:39.208 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
15:26:39.229 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
15:26:39.758 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Receive server push request, request = NotifySubscriberRequest, requestId = 33
15:26:39.758 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Ack server push request, request = NotifySubscriberRequest, requestId = 33
15:26:41.131 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
15:26:45.980 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
15:26:46.018 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
15:26:46.307 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
15:26:46.377 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
15:26:46.504 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
15:26:46.519 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Receive server push request, request = NotifySubscriberRequest, requestId = 34
15:26:46.529 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:26:46.530 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
15:26:46.531 [nacos-grpc-client-executor-*************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd8f0bc8-33c9-4b3e-8322-7cee510eddae] Ack server push request, request = NotifySubscriberRequest, requestId = 34
15:26:46.955 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
15:26:46.957 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
15:26:46.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
15:26:46.960 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
15:26:46.962 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
15:26:46.988 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
15:26:47.009 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
15:26:47.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
15:26:47.014 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
15:26:47.015 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
15:26:47.016 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
15:26:47.017 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
15:26:47.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
15:26:47.023 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
15:26:47.029 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
15:26:47.047 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
15:26:47.048 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
15:26:47.051 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
15:26:47.052 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
15:26:47.058 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
15:26:47.061 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
15:26:47.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
15:26:47.063 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
15:26:47.064 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
15:26:47.064 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
15:26:47.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
15:26:47.066 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
15:26:47.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
15:26:47.070 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
15:26:47.071 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
15:26:47.072 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
15:26:47.075 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
15:26:47.078 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
15:26:47.079 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
15:26:47.080 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
15:26:47.083 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
15:26:47.084 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
15:26:47.084 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
15:26:47.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
15:26:47.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
15:26:47.092 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
15:26:47.093 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
15:26:47.094 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
15:26:47.096 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
15:26:47.097 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
15:26:47.100 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
15:26:47.100 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
15:26:47.101 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
15:26:47.103 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
15:26:47.104 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
15:26:47.105 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
15:26:47.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
15:26:47.108 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
15:26:47.109 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
15:26:47.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
15:26:47.111 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
15:26:47.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
15:26:47.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
15:26:47.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
15:26:47.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
15:26:47.119 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
15:26:47.120 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
15:26:47.125 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
15:26:47.126 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
15:26:47.128 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
15:26:47.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
15:26:47.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
15:26:47.135 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
15:26:47.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
15:26:47.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
15:26:47.141 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
15:26:47.143 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
15:26:47.143 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
15:26:47.145 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
15:26:47.146 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
15:26:47.151 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
15:26:47.153 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
15:26:47.154 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
15:26:47.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
15:26:47.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
15:26:47.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
15:26:47.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
15:26:47.157 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
15:26:47.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
15:26:47.160 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
15:26:47.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
15:26:47.162 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
15:26:47.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
15:26:47.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
15:26:47.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
15:26:47.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
15:26:47.169 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
15:26:47.170 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
15:26:47.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
15:26:47.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
15:26:47.175 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
15:26:47.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
15:26:47.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
15:26:47.179 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
15:26:47.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
15:26:47.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
15:26:47.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
15:26:47.186 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
15:26:47.187 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
15:26:47.187 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
15:26:47.188 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
15:26:47.188 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
15:26:47.189 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
15:26:47.189 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
15:26:47.191 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
15:26:47.194 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
15:26:47.195 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
15:26:47.196 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
15:26:47.197 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
15:26:47.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
15:26:47.205 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
15:26:47.205 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
15:26:47.206 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
15:26:47.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
15:26:47.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
15:26:47.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
15:26:47.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
15:26:47.209 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
15:26:47.210 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
15:26:47.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
15:26:47.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
15:26:47.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
15:26:47.274 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 34.451 seconds (JVM running for 37.623)
15:26:47.289 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
15:26:47.290 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
15:26:47.294 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
15:26:47.296 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
15:26:47.296 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:30:22.268 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:30:22.564 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279097435897925
15:30:23.367 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279097435897925
15:30:23.820 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
15:30:24.027 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
16:45:29.110 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
16:45:43.433 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279115953868869
16:45:43.463 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279115953868869
16:45:43.530 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
16:45:43.656 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
16:45:57.246 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Logout][退出成功]
16:58:43.568 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 279119149293637
16:58:43.602 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  279119149293637
16:58:43.625 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
16:58:43.742 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [************]内网IP[admin][Success][登录成功]
19:58:29.011 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
19:58:30.029 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
19:58:30.816 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:58:31.024 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
19:58:31.024 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
19:58:31.047 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
19:58:31.047 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
19:58:31.047 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
19:58:31.047 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
19:58:31.438 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
19:58:31.438 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,192] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
19:58:31.438 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,197] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
19:58:31.438 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,527] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
19:58:31.439 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
19:58:31.439 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
19:58:31.439 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,530] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
19:58:31.439 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,453] - Shutdown rpc client, set status to shutdown
19:58:31.439 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,455] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67d00201[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:58:31.457 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753687599676_************_59165
19:58:31.628 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,129] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2488dc70[Running, pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 3269]
19:58:31.629 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,267] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@6acc31d7[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 5418]
19:58:31.627 [nacos-grpc-client-executor-*************-3269] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753687599676_************_59165]Ignore complete event,isRunning:false,isAbandon=false
19:58:31.822 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
19:58:31.822 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [free,99] - [null] CredentialService is freed
19:58:31.822 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,189] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
19:58:32.078 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
19:58:32.202 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:58:32.415 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:05:36.916 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
20:05:38.623 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
20:05:38.623 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
20:05:38.628 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
20:05:38.922 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
20:05:38.923 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
20:05:38.959 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
20:05:39.985 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
20:05:39.990 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0
20:05:40.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:05:40.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1757970371
20:05:40.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/197195067
20:05:40.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:05:40.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:05:40.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
20:05:45.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1753704346036_************_39586
20:05:45.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Notify connected event to listeners.
20:05:45.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Connected,notify listen context...
20:05:45.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:05:45.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c41c644-6d00-483e-88c1-a53cf443fb3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/965778714
20:05:45.412 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
20:05:45.487 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
20:05:45.685 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
20:05:51.114 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
20:05:52.683 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
20:05:54.962 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
20:05:54.963 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
20:05:54.963 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
20:05:55.046 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
20:05:55.046 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
20:05:55.046 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
20:05:55.335 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
20:05:55.335 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
20:05:55.335 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
20:05:55.405 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
20:05:55.405 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
20:05:55.405 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
20:05:55.466 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
20:05:55.466 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
20:05:55.467 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
20:05:55.528 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
20:05:55.528 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
20:05:55.528 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
20:05:55.626 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
20:05:55.627 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
20:05:55.627 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
20:05:56.147 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:05:58.437 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:05:59.121 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
20:05:59.121 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
20:05:59.121 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
20:05:59.190 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
20:05:59.191 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
20:05:59.191 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
20:05:59.760 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
20:05:59.760 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
20:05:59.760 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
20:05:59.838 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
20:05:59.838 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
20:05:59.838 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
20:06:00.937 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
20:06:00.943 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
20:06:00.989 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
20:06:01.024 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
20:06:03.710 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
20:06:03.711 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
20:06:03.711 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
20:06:03.746 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
20:06:03.746 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
20:06:03.773 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of ec67c861-a68d-4f22-8624-f6ec5e02ec16
20:06:03.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] RpcClient init label, labels = {module=naming, source=sdk}
20:06:03.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:06:03.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:06:03.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:06:03.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
20:06:03.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Success to connect to server [*************:8848] on start up, connectionId = 1753704365737_************_39735
20:06:03.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:06:03.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/965778714
20:06:03.928 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Notify connected event to listeners.
20:06:03.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
20:06:03.937 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
20:06:04.769 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
20:06:04.830 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
20:06:04.927 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
20:06:04.976 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
20:06:05.003 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
20:06:05.398 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
20:06:05.431 [nacos-grpc-client-executor-*************-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Receive server push request, request = NotifySubscriberRequest, requestId = 43
20:06:05.431 [nacos-grpc-client-executor-*************-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Ack server push request, request = NotifySubscriberRequest, requestId = 43
20:06:07.327 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
20:06:07.345 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
20:06:07.655 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
20:06:07.754 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
20:06:07.879 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Receive server push request, request = NotifySubscriberRequest, requestId = 44
20:06:07.887 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
20:06:07.888 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"*************#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
20:06:07.889 [nacos-grpc-client-executor-*************-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec67c861-a68d-4f22-8624-f6ec5e02ec16] Ack server push request, request = NotifySubscriberRequest, requestId = 44
20:06:07.898 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
20:06:08.425 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
20:06:08.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
20:06:08.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
20:06:08.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
20:06:08.434 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
20:06:08.447 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
20:06:08.460 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
20:06:08.461 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
20:06:08.464 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
20:06:08.465 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
20:06:08.466 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
20:06:08.467 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
20:06:08.472 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
20:06:08.473 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
20:06:08.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
20:06:08.495 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
20:06:08.497 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
20:06:08.499 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
20:06:08.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
20:06:08.506 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
20:06:08.509 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
20:06:08.510 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
20:06:08.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
20:06:08.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
20:06:08.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
20:06:08.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
20:06:08.513 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
20:06:08.514 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
20:06:08.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
20:06:08.520 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
20:06:08.520 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
20:06:08.523 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
20:06:08.527 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
20:06:08.528 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
20:06:08.530 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
20:06:08.532 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
20:06:08.533 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
20:06:08.534 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
20:06:08.536 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
20:06:08.537 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
20:06:08.540 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
20:06:08.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
20:06:08.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
20:06:08.544 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
20:06:08.544 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
20:06:08.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
20:06:08.548 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
20:06:08.549 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
20:06:08.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
20:06:08.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
20:06:08.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
20:06:08.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
20:06:08.555 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
20:06:08.556 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
20:06:08.556 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
20:06:08.558 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
20:06:08.558 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
20:06:08.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
20:06:08.570 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
20:06:08.571 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
20:06:08.573 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
20:06:08.574 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
20:06:08.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
20:06:08.578 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
20:06:08.580 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
20:06:08.581 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
20:06:08.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
20:06:08.586 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
20:06:08.587 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
20:06:08.587 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
20:06:08.592 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
20:06:08.593 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
20:06:08.594 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
20:06:08.594 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
20:06:08.595 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
20:06:08.599 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
20:06:08.601 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
20:06:08.601 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
20:06:08.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
20:06:08.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
20:06:08.603 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
20:06:08.603 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
20:06:08.604 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
20:06:08.604 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
20:06:08.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
20:06:08.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
20:06:08.607 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
20:06:08.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
20:06:08.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
20:06:08.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
20:06:08.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
20:06:08.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
20:06:08.614 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
20:06:08.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
20:06:08.617 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
20:06:08.618 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
20:06:08.620 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
20:06:08.621 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
20:06:08.622 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
20:06:08.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
20:06:08.625 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
20:06:08.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
20:06:08.636 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
20:06:08.636 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
20:06:08.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
20:06:08.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
20:06:08.638 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
20:06:08.638 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
20:06:08.639 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
20:06:08.641 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
20:06:08.643 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
20:06:08.644 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
20:06:08.645 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
20:06:08.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
20:06:08.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
20:06:08.651 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
20:06:08.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
20:06:08.653 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
20:06:08.653 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
20:06:08.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
20:06:08.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
20:06:08.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
20:06:08.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
20:06:08.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
20:06:08.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
20:06:08.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
20:06:08.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
20:06:08.726 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 35.166 seconds (JVM running for 39.563)
20:06:08.740 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
20:06:08.741 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
20:06:08.745 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
20:06:08.747 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
20:06:08.747 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
21:10:42.144 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
21:10:42.334 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:10:42.336 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
21:10:42.336 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
21:10:42.337 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
21:10:42.337 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
21:10:42.337 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
21:10:42.337 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
21:10:42.663 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,192] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,197] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,527] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,530] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
21:10:42.664 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,453] - Shutdown rpc client, set status to shutdown
21:10:42.665 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,455] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2928ec6f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:10:42.665 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753704365737_************_39735
21:10:42.668 [nacos-grpc-client-executor-*************-784] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753704365737_************_39735]Ignore complete event,isRunning:false,isAbandon=false
21:10:42.684 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,129] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a5c465f[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 785]
21:10:42.685 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,267] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@352c2fbc[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 1285]
21:10:42.686 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
21:10:42.686 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [free,99] - [null] CredentialService is freed
21:10:42.686 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,189] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
21:10:42.702 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:10:42.790 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
