03:25:22.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Server healthy check fail, currentConnection = 1754440050566_*************_16424
03:25:22.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Server healthy check fail, currentConnection = 1754440031322_*************_16294
03:25:23.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
03:25:23.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Success to connect a server [**************:8848], connectionId = 1754508326832_*************_9473
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Success to connect a server [**************:8848], connectionId = 1754508326831_*************_9472
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Abandon prev connection, server is **************:8848, connectionId is 1754440050566_*************_16424
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Abandon prev connection, server is **************:8848, connectionId is 1754440031322_*************_16294
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1754440031322_*************_16294
03:25:24.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1754440050566_*************_16424
03:25:24.530 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Notify disconnected event to listeners
03:25:24.505 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Notify disconnected event to listeners
03:25:24.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] DisConnected,clear listen context...
03:25:24.629 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Notify connected event to listeners.
03:25:24.629 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Connected,notify listen context...
03:25:24.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Notify connected event to listeners.
03:25:24.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
03:25:27.660 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
03:25:27.699 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
03:25:28.340 [nacos-grpc-client-executor-**************-13655] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Receive server push request, request = NotifySubscriberRequest, requestId = 1880
03:25:28.341 [nacos-grpc-client-executor-**************-13655] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Ack server push request, request = NotifySubscriberRequest, requestId = 1880
03:25:37.620 [nacos-grpc-client-executor-**************-13657] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Receive server push request, request = NotifySubscriberRequest, requestId = 1896
03:25:37.621 [nacos-grpc-client-executor-**************-13657] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Ack server push request, request = NotifySubscriberRequest, requestId = 1896
08:50:50.989 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 282538192105541
08:50:51.089 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  282538192105541
08:50:52.363 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
08:50:53.215 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[admin][Success][登录成功]
09:19:28.882 [nacos-grpc-client-executor-**************-17869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 1917
09:19:28.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:28.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Server healthy check fail, currentConnection = 1754508326831_*************_9472
09:19:28.901 [nacos-grpc-client-executor-**************-17872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Receive server push request, request = ClientDetectionRequest, requestId = 1921
09:19:28.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:28.967 [nacos-grpc-client-executor-**************-17869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 1917
09:19:28.967 [nacos-grpc-client-executor-**************-17872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Ack server push request, request = ClientDetectionRequest, requestId = 1921
09:19:29.027 [nacos-grpc-client-executor-**************-17869] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754508326831_*************_9472]Ignore complete event,isRunning:false,isAbandon=false
09:19:29.027 [nacos-grpc-client-executor-**************-17872] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754508326832_*************_9473]Ignore complete event,isRunning:false,isAbandon=false
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Success to connect a server [**************:8848], connectionId = 1754529572864_*************_1024
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Success to connect a server [**************:8848], connectionId = 1754529572864_*************_1025
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Abandon prev connection, server is **************:8848, connectionId is 1754508326831_*************_9472
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Abandon prev connection, server is **************:8848, connectionId is 1754508326832_*************_9473
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1754508326831_*************_9472
09:19:29.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1754508326832_*************_9473
09:19:29.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Notify disconnected event to listeners
09:19:29.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Notify disconnected event to listeners
09:19:29.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] DisConnected,clear listen context...
09:19:29.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Notify connected event to listeners.
09:19:29.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Notify connected event to listeners.
09:19:29.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [35b6c12c-f156-409d-9662-4a97260c5246_config-0] Connected,notify listen context...
09:19:29.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
09:19:31.201 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForInstance,72] - Redo instance operation REGISTER for DEFAULT_GROUP@@auth-server
09:19:31.204 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  c.a.n.client.naming - [redoForSubscribe,110] - Redo subscriber operation REGISTER for DEFAULT_GROUP@@auth-server#DEFAULT
09:19:31.783 [nacos-grpc-client-executor-**************-17881] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Receive server push request, request = NotifySubscriberRequest, requestId = 1925
09:19:31.783 [nacos-grpc-client-executor-**************-17881] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f305e579-aee6-4f60-820d-7eb4334040b0] Ack server push request, request = NotifySubscriberRequest, requestId = 1925
19:44:12.478 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 282698761240645
19:44:12.671 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  282698761240645
19:44:13.738 [XNIO-1 task-2] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
19:44:15.329 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[admin][Success][登录成功]
