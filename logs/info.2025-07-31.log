08:44:38.171 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
08:44:40.450 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
08:44:40.450 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
08:44:40.462 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
08:44:40.847 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
08:44:40.848 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
08:44:40.905 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
08:44:42.368 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
08:44:42.437 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0
08:44:42.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
08:44:42.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/15912555
08:44:42.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/633683828
08:44:42.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
08:44:42.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
08:44:42.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
08:44:56.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753922697645_*************_63084
08:44:56.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Notify connected event to listeners.
08:44:56.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
08:44:56.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Connected,notify listen context...
08:44:56.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1272284318
08:44:56.379 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
08:44:56.491 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
08:44:56.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Success to connect a server [**************:8848], connectionId = 1753922698583_*************_63088
08:44:56.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Abandon prev connection, server is **************:8848, connectionId is 1753922697645_*************_63084
08:44:56.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753922697645_*************_63084
08:44:56.653 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Notify disconnected event to listeners
08:44:56.654 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] DisConnected,clear listen context...
08:44:56.654 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Notify connected event to listeners.
08:44:56.654 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [9b89800f-85af-467c-9a7f-4bbac1aabd14_config-0] Connected,notify listen context...
08:44:56.743 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
08:44:57.141 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
08:45:05.916 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
08:45:08.194 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
08:45:10.826 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:45:10.827 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:45:10.827 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
08:45:10.994 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:45:10.994 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:45:10.994 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
08:45:11.510 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:45:11.510 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:45:11.511 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
08:45:11.574 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:45:11.574 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:45:11.574 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
08:45:11.646 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:45:11.647 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:45:11.647 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
08:45:11.722 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:45:11.723 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:45:11.723 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
08:45:11.915 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:45:11.915 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:45:11.915 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
08:45:12.951 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:45:18.342 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:45:19.760 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:45:19.760 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:45:19.760 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
08:45:19.859 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:45:19.859 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:45:19.859 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
08:45:21.268 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:45:21.268 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:45:21.268 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
08:45:21.367 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:45:21.367 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:45:21.367 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
08:45:22.705 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
08:45:22.711 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
08:45:22.816 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
08:45:22.858 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
08:45:26.539 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
08:45:26.539 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
08:45:26.539 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
08:45:26.596 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
08:45:26.596 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
08:45:26.635 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 7cec602e-89b1-464b-8cda-c2f2d796f2a1
08:45:26.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] RpcClient init label, labels = {module=naming, source=sdk}
08:45:26.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
08:45:26.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
08:45:26.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
08:45:26.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
08:45:26.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Success to connect to server [**************:8848] on start up, connectionId = 1753922728739_*************_63268
08:45:26.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
08:45:26.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1272284318
08:45:26.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Notify connected event to listeners.
08:45:26.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
08:45:26.797 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
08:45:26.862 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
08:45:26.889 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
08:45:26.956 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
08:45:26.994 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
08:45:27.018 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
08:45:27.391 [nacos-grpc-client-executor-**************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Receive server push request, request = NotifySubscriberRequest, requestId = 702
08:45:27.392 [nacos-grpc-client-executor-**************-4] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server -> []
08:45:27.392 [nacos-grpc-client-executor-**************-4] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server -> []
08:45:27.394 [nacos-grpc-client-executor-**************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Ack server push request, request = NotifySubscriberRequest, requestId = 702
08:45:28.780 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
08:45:40.916 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] ris-cqc registering service auth-server with instance Instance{instanceId='null', ip='*************', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
08:45:40.942 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server *************:9025 register finished
08:45:41.261 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
08:45:41.381 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
08:45:41.538 [nacos-grpc-client-executor-**************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Receive server push request, request = NotifySubscriberRequest, requestId = 703
08:45:41.551 [nacos-grpc-client-executor-**************-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server -> [{"ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:45:41.551 [nacos-grpc-client-executor-**************-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server -> [{"ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:45:41.553 [nacos-grpc-client-executor-**************-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cec602e-89b1-464b-8cda-c2f2d796f2a1] Ack server push request, request = NotifySubscriberRequest, requestId = 703
08:45:41.575 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
08:45:41.876 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:45:41.876 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"ip":"*************","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
08:45:42.076 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
08:45:42.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
08:45:42.078 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
08:45:42.081 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
08:45:42.083 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
08:45:42.099 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
08:45:42.111 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
08:45:42.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
08:45:42.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
08:45:42.115 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
08:45:42.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
08:45:42.117 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
08:45:42.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
08:45:42.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
08:45:42.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
08:45:42.146 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
08:45:42.148 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
08:45:42.151 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
08:45:42.153 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
08:45:42.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
08:45:42.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
08:45:42.162 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
08:45:42.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
08:45:42.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
08:45:42.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
08:45:42.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
08:45:42.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
08:45:42.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
08:45:42.170 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
08:45:42.171 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
08:45:42.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
08:45:42.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
08:45:42.178 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
08:45:42.179 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
08:45:42.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
08:45:42.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
08:45:42.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
08:45:42.184 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
08:45:42.191 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
08:45:42.193 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
08:45:42.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
08:45:42.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
08:45:42.200 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
08:45:42.203 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
08:45:42.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
08:45:42.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
08:45:42.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
08:45:42.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
08:45:42.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
08:45:42.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
08:45:42.213 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
08:45:42.213 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
08:45:42.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
08:45:42.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
08:45:42.215 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
08:45:42.216 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
08:45:42.217 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
08:45:42.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
08:45:42.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
08:45:42.223 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
08:45:42.226 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
08:45:42.228 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
08:45:42.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
08:45:42.232 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
08:45:42.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
08:45:42.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
08:45:42.235 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
08:45:42.240 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
08:45:42.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
08:45:42.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
08:45:42.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
08:45:42.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
08:45:42.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
08:45:42.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
08:45:42.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
08:45:42.256 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
08:45:42.258 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
08:45:42.258 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
08:45:42.259 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
08:45:42.259 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
08:45:42.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
08:45:42.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
08:45:42.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
08:45:42.261 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
08:45:42.262 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
08:45:42.263 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
08:45:42.263 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
08:45:42.265 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
08:45:42.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
08:45:42.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
08:45:42.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
08:45:42.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
08:45:42.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
08:45:42.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
08:45:42.279 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
08:45:42.281 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
08:45:42.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
08:45:42.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
08:45:42.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
08:45:42.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
08:45:42.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
08:45:42.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
08:45:42.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
08:45:42.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
08:45:42.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
08:45:42.294 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
08:45:42.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
08:45:42.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
08:45:42.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
08:45:42.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
08:45:42.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
08:45:42.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
08:45:42.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
08:45:42.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
08:45:42.304 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
08:45:42.310 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
08:45:42.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
08:45:42.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
08:45:42.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
08:45:42.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
08:45:42.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
08:45:42.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
08:45:42.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
08:45:42.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
08:45:42.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
08:45:42.319 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
08:45:42.319 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
08:45:42.381 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 66.073 seconds (JVM running for 71.709)
08:45:42.393 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
08:45:42.393 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-ris-cqc-**************_8848] [subscribe] auth-server+DEFAULT_GROUP+ris-cqc
08:45:42.396 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-ris-cqc-**************_8848] [add-listener] ok, tenant=ris-cqc, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
08:45:42.399 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-ris-cqc-**************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+ris-cqc
08:45:42.399 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-ris-cqc-**************_8848] [add-listener] ok, tenant=ris-cqc, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
08:49:29.046 [XNIO-1 task-1] INFO  io.undertow.servlet - [log,371] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:49:29.364 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 280060597162053
08:49:30.009 [XNIO-1 task-1] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  280060597162053
08:49:30.440 [XNIO-1 task-1] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
08:49:30.654 [XNIO-1 task-1] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[admin][Success][登录成功]
20:37:43.966 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,75] - 获取redis锁成功--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys 280234654867525
20:37:44.417 [XNIO-1 task-2] INFO  c.j.a.c.a.NoDuplicateAspect - [around,86] - 解redis锁--> auth-server:lockKey:com.jusha.auth.permission.LoginManagerController.getKeys_getKeys  280234654867525
20:37:47.666 [XNIO-1 task-2] INFO  c.j.a.p.LoginManagerController - [login,95] - 用户准备验证码登录，用户名========admin
20:37:48.755 [XNIO-1 task-2] INFO  sys-user - [recordLogininfor,47] - [*************]内网IP[admin][Success][登录成功]
21:18:28.972 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
21:18:30.200 [SpringContextShutdownHook] INFO  io.undertow.servlet - [log,371] - Destroying Spring FrameworkServlet 'dispatcherServlet'
21:18:31.520 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:18:31.552 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
21:18:31.552 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
21:18:31.609 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
21:18:31.609 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
21:18:31.620 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
21:18:31.620 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
21:18:31.980 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
21:18:32.067 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,192] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
21:18:32.120 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,197] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
21:18:32.121 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,527] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
21:18:32.121 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
21:18:32.121 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
21:18:32.121 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,530] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
21:18:32.186 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,453] - Shutdown rpc client, set status to shutdown
21:18:32.199 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,455] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@baa85e8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:18:32.199 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1753922728739_*************_63268
21:18:32.509 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,129] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40600400[Running, pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 9080]
21:18:32.510 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,267] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@18212cea[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 15017]
21:18:32.612 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
21:18:32.612 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [free,99] - [null] CredentialService is freed
21:18:32.626 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,189] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
21:18:33.112 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:18:33.379 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
